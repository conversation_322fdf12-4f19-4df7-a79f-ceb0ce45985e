//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';

// ignore: camel_case_types
class SectionTaskPhotos extends DataModelCollectionItem {
  @override
  String getDisplayName() => "TaskPhotos";

  SectionTaskPhotos(String id, DataModelItem parent) : super(id, parent);

  // ignore: non_constant_identifier_names
  late PhotoAttribute attributePhotos = PhotoAttribute(
      parent: this,
      displayName: "Photos",
      databaseName: "GT_Q212",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeDescription = StringAttribute(
      parent: this,
      displayName: "Description",
      databaseName: "GT_Q210",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late LocationAttribute attributeGIS_Location = LocationAttribute(
      parent: this,
      displayName: "GIS Location",
      databaseName: "GT_Q211",
      areCommentsRequired: false);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributePhotos,
      attributeDescription,
      attributeGIS_Location,
    ]);
    return children;
  }
}
