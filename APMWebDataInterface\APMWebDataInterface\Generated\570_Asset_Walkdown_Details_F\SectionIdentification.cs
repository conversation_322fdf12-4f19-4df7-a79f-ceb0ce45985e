//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section570_Asset_Walkdown_Details_F
{
  public class SectionIdentification : DataModelItem {

    public override String DisplayName { 
      get {
        return "Identification";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public StringAttribute attributeName;
    public StringAttribute attributeNumber_or_Circuit_ID;
    public StringAttribute attributeAsset_Type;
    public StringAttribute attributeEquipment_Description;
    public StringAttribute attributeProduct_Handled;
    public DateAttribute attributeLast_known_inspection_date;
    public PredefinedValueAttribute attributeLocation;
    public StringAttribute attributeLine_from_what_equipment_ID;
    public LocationAttribute attributeStart_GIS_Location;
    public StringAttribute attributeLine_to_what_eqiupment_ID;
    public LocationAttribute attributeEnd_GIS_Location;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionIdentification";

    public SectionIdentification(DataModelItem parent) : base(parent)
    {
            
        attributeName = new StringAttribute(this, displayName: "Name", databaseName: "570AW_Q005"); 
     
        attributeNumber_or_Circuit_ID = new StringAttribute(this, displayName: "Number or Circuit ID", databaseName: "570AW_Q006", isQueryable: true); 
     
        attributeAsset_Type = new StringAttribute(this, displayName: "Asset Type", databaseName: "570AW_Q007"); 
     
        attributeEquipment_Description = new StringAttribute(this, displayName: "Equipment Description", databaseName: "570AW_Q016"); 
     
        attributeProduct_Handled = new StringAttribute(this, displayName: "Product Handled", databaseName: "570AW_Q008"); 
     
        attributeLast_known_inspection_date = new DateAttribute(this, displayName: "Last known inspection date", databaseName: "570AW_Q009", areCommentsRequired: false); 
     
        attributeLocation = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("On-Plot (Facility)", null),
          new PredefinedValueOption("Off-Plot (Field)", null)
        }, false, this, "Location", databaseName: "570AW_Q010"); 
     
        attributeLine_from_what_equipment_ID = new StringAttribute(this, displayName: "Line from what equipment ID?", databaseName: "570AW_Q011"); 
     
        attributeStart_GIS_Location = new LocationAttribute(this, displayName: "Start GIS Location", databaseName: "570AW_Q012"); 
     
        attributeLine_to_what_eqiupment_ID = new StringAttribute(this, displayName: "Line to what eqiupment ID?", databaseName: "570AW_Q014"); 
     
        attributeEnd_GIS_Location = new LocationAttribute(this, displayName: "End GIS Location", databaseName: "570AW_Q015"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeName,
           attributeNumber_or_Circuit_ID,
           attributeAsset_Type,
           attributeEquipment_Description,
           attributeProduct_Handled,
           attributeLast_known_inspection_date,
           attributeLocation,
           attributeLine_from_what_equipment_ID,
           attributeStart_GIS_Location,
           attributeLine_to_what_eqiupment_ID,
           attributeEnd_GIS_Location,
        };
    }
  }
}
