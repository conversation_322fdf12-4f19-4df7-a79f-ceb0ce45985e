import 'package:api_inspection/app/Queries/download_progress.dart';
import 'package:api_inspection/generic/AppRoot.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class DownloadProgressIndicatorWidget extends StatefulWidget {
  const DownloadProgressIndicatorWidget({Key? key}) : super(key: key);

  @override
  State<DownloadProgressIndicatorWidget> createState() =>
      _DownloadProgressIndicatorWidgetState();
}

class _DownloadProgressIndicatorWidgetState
    extends State<DownloadProgressIndicatorWidget> {
  @override
  void initState() {
    AppRoot.global().photoDownloadProgressEvent.addListener(onFileResolved);

    super.initState();
  }

  Future<void> onFileResolved() async {
    // This can be called while the build method is running, which is not
    // good.  Futures in dart will get run after currently running sync code
    // is being executed.  This attempts to make sure we don't call `setState`
    // while the build method is "doing its thing."
    await Future.delayed(Duration.zero);
    if (mounted) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    AppRoot.global().photoDownloadProgressEvent.removeListener(onFileResolved);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var downloadProgress = DownloadProgress.getDownloadProgress();
    if (downloadProgress.downloadingCount > 0) {
      var percent = NumberFormat.percentPattern()
          .format(downloadProgress.progressPercent);
      var count = downloadProgress.downloadedCount;
      var total = downloadProgress.downloadableTotal;
      return Container(
          alignment: Alignment.topCenter,
          margin: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Padding(
                  padding: const EdgeInsets.only(bottom: 10),
                  child: Text(
                    'Downloading photos - $percent ($count / $total)',
                    style: const TextStyle(fontSize: 13, color: Colors.white),
                  )),
              LinearProgressIndicator(
                  value: downloadProgress.progressPercent,
                  semanticsLabel: 'Linear progress indicator')
            ],
          ));
    } else {
      return Container();
    }
  }
}
