import 'dart:developer';

import 'package:api_inspection/app/APMRoot.dart';
import 'package:api_inspection/generic/DataModelCommon/ConcretePhotoRoot.dart';
import 'package:api_inspection/generic/MediaControls/MediaEntry.dart';
import 'package:darq/darq.dart';

class DownloadProgressReport {
  final double progressPercent;
  final int downloadedCount;
  final int downloadableTotal;
  final int downloadingCount;
  DownloadProgressReport(this.progressPercent, this.downloadedCount,
      this.downloadableTotal, this.downloadingCount);
}

class DownloadProgress {
  static DownloadProgressReport getDownloadProgress() {
    var provider = APMRoot.global.newQueries;

    List<ConcretePhotoRoot> downloadablePhotoRoots = [
      ...APMRoot.global.queries.leakReports.leakReports,
      ...APMRoot.global.queries.locationQueries.locations,
      ...APMRoot.global.queries.projectQueries.projects,
      ...APMRoot.global.queries.businessUnitQueries.businessUnits,
      ...APMRoot.global.queries.clientQueries.clients,
      ...provider.assets,
      ...provider.workOrders,
      ...provider.workOrders.expand((wo) => wo.tasks)
    ].where((element) => element.getShouldDownloadPhotos()).toList();

    List<MediaEntry> downloadableFiles = downloadablePhotoRoots
        .expand((e) => e.mediaItems.getEntries())
        .toList();

    var filesDownloadedCount = downloadableFiles
        .count((element) => element.resolvedVersion == element.version);

    var filesCurrentlyBeingDownloaded =
        downloadableFiles.count((element) => element.isDownloading);

    if (downloadableFiles.isEmpty) return DownloadProgressReport(1.0, 0, 0, 0);

    var progress = filesDownloadedCount / downloadableFiles.length;
    var prettyProgress = '${(progress * 100).toStringAsFixed(1)}%';
    log('download progress: $prettyProgress ($filesDownloadedCount / ${downloadableFiles.length}), files being downloaded: $filesCurrentlyBeingDownloaded',
        name: 'DownloadProgress');
    return DownloadProgressReport(progress, filesDownloadedCount,
        downloadableFiles.length, filesCurrentlyBeingDownloaded);
  }
}
