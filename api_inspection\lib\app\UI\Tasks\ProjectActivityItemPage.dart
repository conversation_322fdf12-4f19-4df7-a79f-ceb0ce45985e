import 'package:api_inspection/generic/AppStyle.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/activity.dart';
import 'package:api_inspection/generic/UIControls/attributePadding.dart';

class ProjectActivityItemPage extends StatefulWidget {
  final ProjectActivity activity;

  const ProjectActivityItemPage(this.activity, {Key? key}) : super(key: key);

  @override
  _ProjectActivityItemPageState createState() =>
      _ProjectActivityItemPageState();
}

class _ProjectActivityItemPageState extends State<ProjectActivityItemPage> {
  List<Widget> buildHoursTable() {
    List<Widget> children = [
      Container(
          margin: const EdgeInsets.fromLTRB(120, 0, 20, 0),
          child: Row(children: [
            const SizedBox(
                width: 120,
                child: Text("Hours",
                    style: TextStyle(color: Colors.white, fontSize: 20))),
            Container(
                width: 100,
                alignment: Alignment.topLeft,
                child: const Text("Count",
                    style: TextStyle(color: Colors.white, fontSize: 20)))
          ]))
    ];

    List<Widget> activityChildren = [];
    for (var item in widget.activity.activities) {
      activityChildren.add(Container(
          margin: const EdgeInsets.all(10),
          width: 370,
          child: Row(
            children: [
              SizedBox(
                  width: 100,
                  child: Text(
                    item.name,
                    style: const TextStyle(color: Colors.white, fontSize: 16),
                  )),
              SizedBox(
                  width: 100,
                  child: SizedBox(
                      width: 100,
                      child: item.duration.buildWidget_EntryOnly())),
              Container(width: 20),
              Container(
                  width: 100,
                  alignment: Alignment.topLeft,
                  child: SizedBox(
                      width: 100, child: item.count.buildWidget_EntryOnly()))
            ],
          )));
    }

    children.add(Expanded(
        child: Container(
            margin: const EdgeInsets.all(10),
            color: Colors.blueGrey[900],
            child: Scrollbar(
                thumbVisibility: true,
                child: ListView(children: activityChildren)))));

    children.add(Container(
        margin: const EdgeInsets.fromLTRB(25, 0, 0, 10),
        child: Text("Total Hours " + widget.activity.getTotalHours().toString(),
            style: const TextStyle(color: Colors.white, fontSize: 20))));

    return children;
  }

  @override
  void dispose() {
    widget.activity.activitiesListener.removeListener(onActivitiesChanged);
    super.dispose();
  }

  @override
  void initState() {
    widget.activity.activitiesListener.addListener(onActivitiesChanged);
    super.initState();
  }

  void onActivitiesChanged() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> children = [
      AttributePadding.WithStdPadding(widget.activity.date.buildWidget()),
      AttributePadding.WithStdPadding(
          widget.activity.workOrderNumber.buildWidget()),
    ];

    children.addAll(buildHoursTable());

    return Scaffold(
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        appBar: AppBar(
          title: Text(
            "Activity",
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        body: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: children));
  }
}
