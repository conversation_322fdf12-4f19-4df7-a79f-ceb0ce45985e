//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Asset_Walkdown_Details_F
{
  public class SectionOperatingDesignConditions : DataModelItem {

    public override String DisplayName { 
      get {
        return "Operating/Design Conditions";
      }
    }

    #region [-- Sub Sections --]

    #region [-- SectionShellSide --]
    private SectionShellSide _sectionShellSide;
    public SectionShellSide sectionShellSide {
        get {
            if (_sectionShellSide == null) {
               _sectionShellSide = new SectionShellSide(this);
            }

            return _sectionShellSide;
        }
    }
    #endregion [-- SectionShellSide --]
    
    #region [-- SectionTubeSide --]
    private SectionTubeSide _sectionTubeSide;
    public SectionTubeSide sectionTubeSide {
        get {
            if (_sectionTubeSide == null) {
               _sectionTubeSide = new SectionTubeSide(this);
            }

            return _sectionTubeSide;
        }
    }
    #endregion [-- SectionTubeSide --]
    
    #region [-- SectionDimensions --]
    private SectionDimensions _sectionDimensions;
    public SectionDimensions sectionDimensions {
        get {
            if (_sectionDimensions == null) {
               _sectionDimensions = new SectionDimensions(this);
            }

            return _sectionDimensions;
        }
    }
    #endregion [-- SectionDimensions --]
    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public DoubleAttribute attributeOperating_Temperature;
    public PredefinedValueAttribute attributeOperation_Status;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionOperatingDesignConditions";

    public SectionOperatingDesignConditions(DataModelItem parent) : base(parent)
    {
            
        attributeOperating_Temperature = new DoubleAttribute(this, displayName: "Operating Temperature", databaseName: "510AW_Q305", areCommentsRequired: false, displayUnit: null, allowNegatives: true); 
     
        attributeOperation_Status = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("In-Service", null),
          new PredefinedValueOption("Out-Of-Service", null),
          new PredefinedValueOption("Standby", null)
        }, false, this, "Operation Status", databaseName: "510AW_Q356"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           sectionShellSide,
           sectionTubeSide,
           sectionDimensions,
           attributeOperating_Temperature,
           attributeOperation_Status,
        };
    }
  }
}
