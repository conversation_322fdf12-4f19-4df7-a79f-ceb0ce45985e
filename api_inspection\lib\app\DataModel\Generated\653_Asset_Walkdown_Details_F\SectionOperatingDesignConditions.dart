//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'SectionTankOutOfServiceRequirements.dart';
import 'SectionRegulatoryRequirements.dart';

// ignore: camel_case_types
class SectionOperatingDesignConditions extends DataModelSection {
  @override
  String getDisplayName() => "Operating/Design Conditions";
  SectionOperatingDesignConditions(DataModelItem? parent)
      : super(parent: parent, sectionName: "Operating/Design Conditions");

  // ignore: non_constant_identifier_names
  late StringAttribute attributeCurrent_service = StringAttribute(
      parent: this,
      displayName: "Current service",
      databaseName: "653AW_Q305",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeDesign_Temp = DoubleAttribute(
    parent: this,
    displayName: "Design Temp",
    databaseName: "653AW_Q306",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "",
  );

  // ignore: non_constant_identifier_names
  late IntegerAttribute attributeCurrent_Operating_Temperature =
      IntegerAttribute(
    parent: this,
    displayName: "Current Operating Temperature:",
    databaseName: "653AW_Q307",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "",
  );

  // ignore: non_constant_identifier_names
  late IntegerAttribute attributeCurrent_Fill_level_if_available =
      IntegerAttribute(
    parent: this,
    displayName: "Current Fill level if available:",
    databaseName: "653AW_Q308",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "",
  );

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeOperation_Status =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Operation Status",
          databaseName: "653AW_Q309",
          availableOptions: [
        PredefinedValueOption("In-Service", null, isCommentRequired: false),
        PredefinedValueOption("Out-Of-Service", null, isCommentRequired: false),
        PredefinedValueOption("Temp OOS for Insp", null,
            isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeIs_the_tank_equipped_with_VRU =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Is the tank equipped with VRU?",
          databaseName: "653AW_Q310",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Uknown", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeTank_equipped_with_Leak_Detection =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Tank equipped with Leak Detection?",
          databaseName: "653AW_Q311",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("N/A", null, isCommentRequired: false),
      ]);

// ignore: non_constant_identifier_names
  late SectionTankOutOfServiceRequirements sectionTankOutOfServiceRequirements =
      SectionTankOutOfServiceRequirements(this);

// ignore: non_constant_identifier_names
  late DataModelCollection<SectionRegulatoryRequirements>
      sectionRegulatoryRequirements =
      DataModelCollection<SectionRegulatoryRequirements>(
          "Regulatory Requirements", (parent, entry) {
    return SectionRegulatoryRequirements(
        entry.key, sectionRegulatoryRequirements);
  }, this);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      sectionTankOutOfServiceRequirements,
      sectionRegulatoryRequirements,
      attributeCurrent_service,
      attributeDesign_Temp,
      attributeCurrent_Operating_Temperature,
      attributeCurrent_Fill_level_if_available,
      attributeOperation_Status,
      attributeIs_the_tank_equipped_with_VRU,
      attributeTank_equipped_with_Leak_Detection,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionOperatingDesignConditions";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
