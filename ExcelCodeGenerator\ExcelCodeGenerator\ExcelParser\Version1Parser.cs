﻿using System.Collections.Generic;
using System.Data;
using System.Linq;
using CheckListGen;

namespace ExcelCodeGenerator.ExcelParser
{
    public class Version1Parser : IExcelParser
    {
        public bool IsTableInFormat(DataTable table)
        {
            return table.Rows[0][0].ToString() == "QuestionCode";
        }

        public ExcelForm ReadTable(DataTable table)
        {
            var tableName = table.TableName;

            var form = new ExcelForm
            {
                Sections = new List<ExcelSection>(),
                Name = tableName,
                DisplayName = tableName
            };

            foreach (DataRow row in table.Rows)
            {
                if (row[0]?.ToString() == "QuestionCode") continue;

                if (string.IsNullOrWhiteSpace(row[2].ToString()))
                    continue;

                var headerName = row[3]?.ToString();
                if (!string.IsNullOrWhiteSpace(headerName))
                {
                    var section = new ExcelSection
                    {
                        Name = headerName.Trim(),
                        DataName = row[1].ToString().Trim()
                    };


                    if (string.IsNullOrWhiteSpace(row[0]?.ToString()))
                    {
                        section.Id = row[2].ToString().Split('.')[0];
                        section.ParentId = "root";
                        section.Order = double.Parse(row[2].ToString().Split('.')[0].Substring(1));
                    }
                    else
                    {
                        section.ParentId = row[2].ToString().Split('.')[0];
                        section.Id = row[0]?.ToString().Trim();
                        section.Order = double.Parse(row[2].ToString().Split('.')[1].Substring(1));
                    }

                    form.Sections.Add(section);


                    continue;
                }

                if (string.IsNullOrWhiteSpace(row[1]?.ToString()))
                    continue;

                var excelQuestion = new ExcelQuestion();
                var question = new Question();
                excelQuestion.Question = question;

                question.DataName = row[1]?.ToString().Trim();
                excelQuestion.SectionId = row[2].ToString().Split('.')[0];
                question.Order = double.Parse(row[2].ToString().Split('.')[1].Substring(1));
                question.DisplayText = row[4]?.ToString().Trim();
                question.References = row[5]?.ToString().Split(',').Select(a => a.Trim()).ToList();
                question.Unit = row[6]?.ToString().Trim();
                question.DataType = row[7]?.ToString().Trim();
                question.Required = !string.IsNullOrWhiteSpace(row[8]?.ToString()) && Helpers.ParseBooleanEXT(row[8]?.ToString());
                question.EntryMethod = row[9]?.ToString().Trim();
                question.Choices = row[10]?.ToString().Split(',')
                    .Where(a => !string.IsNullOrWhiteSpace(a))
                    .Select(a =>
                    {
                        var indexStart = a.IndexOf('[');
                        var indexEnd = a.IndexOf(']');
                        if (indexStart == -1 || indexEnd == -1) return new Choice {Display = a.Trim()};
                        var length = indexEnd - indexStart - 1;
                        var decorator = a.Substring(indexStart + 1, length);
                        return new Choice
                        {
                            Display = a.Replace("[" + decorator + "]", "").Trim(),
                            Decorator = decorator.Trim()
                        };

                    })
                    .Where(a => !string.IsNullOrWhiteSpace(a.Display)).ToList();

                var devComments = row[11]?.ToString();
                question.DevComments = devComments;
                form.Questions.Add(excelQuestion);
            }

            return form;
        }

        public void AddPhotoQuestionToSection(ExcelSection section, ExcelForm form)
        {
            var excelQuestion = new ExcelQuestion();
            var question = new Question();
            excelQuestion.Question = question;

            question.DataName = "General Photos";
            question.DisplayText = "General Photos";
            excelQuestion.SectionId = section.Id;
            question.DataType = "photocollection";
            question.Order = 0;

            form.Questions.Add(excelQuestion);
        }
    }
}