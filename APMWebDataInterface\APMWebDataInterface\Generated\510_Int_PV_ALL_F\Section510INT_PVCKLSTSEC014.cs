//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Int_PV_ALL_F
{
  public class Section510INT_PVCKLSTSEC014 : DataModelItem {

    public override String DisplayName { 
      get {
        return "TUBE BUNDLE - HEX ONLY";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC014Q001;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC014Q002;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC014Q003;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC014Q004;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC014Q005;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC014Q006;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC014Q007;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC014Q008;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC014Q009;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC014Q010;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC014Q011;
    public PredefinedValueAttribute attributeAre_tierods_double_nutted;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC014Q013;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC014Q014;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC014Q015;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510INT_PVCKLSTSEC014";

    public Section510INT_PVCKLSTSEC014(DataModelItem parent) : base(parent)
    {
            
        attribute510INT_PVCKLSTSEC014Q001 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Erosion", null),
          new PredefinedValueOption("Yes: Pitting", null)
        }, false, this, "Were any corrosion cells, erosion or pitting noted on the tube sheet surfaces: (The dimensions and locations of any damage shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC014_Q001"); 
     
        attribute510INT_PVCKLSTSEC014Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Cracking", null),
          new PredefinedValueOption("Yes: Deformation", null),
          new PredefinedValueOption("Yes: Mechanical Damage", null)
        }, false, this, "Was any mechanical damage, deformation, or cracking noted on the tube sheet surfaces: (The dimensions and location of any damage shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC014_Q002"); 
     
        attribute510INT_PVCKLSTSEC014Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are the tube sheet sealing surfaces in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC014_Q003"); 
     
        attribute510INT_PVCKLSTSEC014Q004 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are any tubes plugged & vented: (The number and location of plugged tubes shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC014_Q004"); 
     
        attribute510INT_PVCKLSTSEC014Q005 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Other", null),
          new PredefinedValueOption("Yes: Deformation", null),
          new PredefinedValueOption("Yes: Mechanical Damage", null),
          new PredefinedValueOption("Yes: Knife Edged", null)
        }, false, this, "Are tube ends in acceptable condition for continued service: (If knife edged or damaged the number and location of damaged tubes shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC014_Q005"); 
     
        attribute510INT_PVCKLSTSEC014Q006 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are any tubes seal welded: (The number and location of seal welded  tubes shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC014_Q006"); 
     
        attribute510INT_PVCKLSTSEC014Q007 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Do any tubes require seal welding: (The number and location of tubes to be seal welded shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC014_Q007"); 
     
        attribute510INT_PVCKLSTSEC014Q008 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Cracking", null),
          new PredefinedValueOption("Yes: Deformation", null),
          new PredefinedValueOption("Yes: Mechanical Damage", null)
        }, false, this, "Was any mechanical damage, deformation, or cracking noted on the tube surfaces: (The dimensions and location of any damage shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC014_Q008"); 
     
        attribute510INT_PVCKLSTSEC014Q009 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Erosion", null),
          new PredefinedValueOption("Yes: Pitting", null)
        }, false, this, "Were there indications of corrosion, erosion or pitting of the tubes: (The dimensions and location of any damage shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC014_Q009"); 
     
        attribute510INT_PVCKLSTSEC014Q010 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are the tube u-bends in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC014_Q010"); 
     
        attribute510INT_PVCKLSTSEC014Q011 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are tierods and spacers in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC014_Q011"); 
     
        attributeAre_tierods_double_nutted = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are tierods double nutted:", databaseName: "510_INT-PV_CKLST_SEC014_Q012"); 
     
        attribute510INT_PVCKLSTSEC014Q013 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are transverse baffles / support plates in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC014_Q013"); 
     
        attribute510INT_PVCKLSTSEC014Q014 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are longitudinal baffles in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC014_Q014"); 
     
        attribute510INT_PVCKLSTSEC014Q015 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is impingement plate in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC014_Q015"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute510INT_PVCKLSTSEC014Q001,
           attribute510INT_PVCKLSTSEC014Q002,
           attribute510INT_PVCKLSTSEC014Q003,
           attribute510INT_PVCKLSTSEC014Q004,
           attribute510INT_PVCKLSTSEC014Q005,
           attribute510INT_PVCKLSTSEC014Q006,
           attribute510INT_PVCKLSTSEC014Q007,
           attribute510INT_PVCKLSTSEC014Q008,
           attribute510INT_PVCKLSTSEC014Q009,
           attribute510INT_PVCKLSTSEC014Q010,
           attribute510INT_PVCKLSTSEC014Q011,
           attributeAre_tierods_double_nutted,
           attribute510INT_PVCKLSTSEC014Q013,
           attribute510INT_PVCKLSTSEC014Q014,
           attribute510INT_PVCKLSTSEC014Q015,
        };
    }
  }
}
