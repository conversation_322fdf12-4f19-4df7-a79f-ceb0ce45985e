import 'package:api_inspection/generic/AppStyle.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';

typedef GetDisplayValue<T> = String Function(T item);

class SelectorWithOther extends StatefulWidget {
  final List<String> options;
  final GetDisplayValue<String> getDisplayValueFunc;
  final Function(String?) onItemSelected;
  final String? initialSelectedOption;

  final Color buttonColor;
  const SelectorWithOther(
      this.options, this.getDisplayValueFunc, this.onItemSelected,
      {Key? key,
      this.buttonColor = const Color.fromARGB(255, 41, 45, 52),
      this.initialSelectedOption})
      : super(key: key);

  @override
  _SelectorWithOtherState createState() => _SelectorWithOtherState();
}

Color selectedColor = const Color.fromARGB(255, 4, 188, 242);
Color unselectedColor = const Color.fromARGB(255, 122, 122, 122);

class _SelectorWithOtherState extends State<SelectorWithOther> {
  String? selectedOption;
  bool otherSelected = false;
  TextEditingController otherController = TextEditingController();

  @override
  void initState() {
    selectedOption = widget.initialSelectedOption;
    otherController.addListener(onOtherChanged);
    super.initState();
  }

  @override
  void dispose() {
    otherController.removeListener(onOtherChanged);

    super.dispose();
  }

  void onOtherChanged() {
    selectedOption = otherController.text;
    widget.onItemSelected.call(selectedOption);
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> optionWidgets = [];
    for (var option in widget.options) {
      Color borderColor =
          selectedOption == option ? selectedColor : unselectedColor;
      optionWidgets.add(SizedBox(
          width: 250,
          height: 65,
          child: TeamToggleButton.withText(
            widget.getDisplayValueFunc(option),
            () => {
              setState(() {
                otherSelected = false;
                if (selectedOption == option) {
                  selectedOption = null;
                } else {
                  selectedOption = option;
                }
                widget.onItemSelected.call(selectedOption);
              })
            },
            borderColor: borderColor,
            color: widget.buttonColor,
          )));
    }

    Color otherBorderColor = otherSelected ? selectedColor : unselectedColor;
    optionWidgets.add(SizedBox(
        width: 250,
        height: 65,
        child: TeamToggleButton.withText(
          "Other",
          () => {
            setState(() {
              otherSelected = true;
              if (selectedOption == otherController.text) {
                selectedOption = null;
              } else {
                selectedOption = otherController.text;
              }
              widget.onItemSelected.call(selectedOption);
            })
          },
          borderColor: otherBorderColor,
          color: widget.buttonColor,
        )));

    if (otherSelected) {
      optionWidgets.add(TextField(
          controller: otherController,
          decoration: AppStyle.global.textFieldDecoration,
          style: const TextStyle(color: Colors.white)));
    }
    return Column(children: optionWidgets);
  }
}
