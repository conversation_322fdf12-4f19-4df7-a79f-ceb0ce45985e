﻿using System;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.DataModel.LeakReport
{

  public class LeakReportPhoto : DataModelCollectionItem {
    public override string DisplayName => "Leak Report Photo";

    internal LeakReportPhoto(DataModelItem parent, String id) : base(id, parent)
    {
      photos = new PhotoAttribute(parent: this, displayName: "Photos");
      
      description = new StringAttribute(parent: this, displayName: "Description");
      comment = new StringAttribute(parent: this, displayName: "Comment");
      areaOfInterestCoordinate = new LocationAttribute(parent: this, displayName: "Area of Interest GIS position");
      upstreamTieInCoordinate = new LocationAttribute(parent: this, displayName: "Upstream tie-in GIS location");
      downstreamTieInCoordinate = new LocationAttribute(parent: this, displayName: "Downstream tie-in GIS location");
      utHighMeasurment = new DoubleAttribute(parent: this, displayName: "UT High Measurement", allowNegatives: true);
      utLowMeasurment = new DoubleAttribute(parent: this, displayName: "UT Low Measurement", allowNegatives: true);

    }

    public PhotoAttribute photos;

    public StringAttribute description;
    public StringAttribute comment;
    public LocationAttribute areaOfInterestCoordinate;
    public LocationAttribute upstreamTieInCoordinate;
    public LocationAttribute downstreamTieInCoordinate;
    public DoubleAttribute utHighMeasurment;
    public DoubleAttribute utLowMeasurment;

    public override DataModelItem[] GetChildren() {
      return new DataModelItem[] {
        photos, description, comment, areaOfInterestCoordinate, upstreamTieInCoordinate, 
        downstreamTieInCoordinate, utHighMeasurment, utLowMeasurment
      };
    }


  }

}
