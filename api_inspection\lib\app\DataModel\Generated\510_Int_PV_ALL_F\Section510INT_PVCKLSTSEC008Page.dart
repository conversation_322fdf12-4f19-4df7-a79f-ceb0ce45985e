//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'Section510INT_PVCKLSTSEC008.dart';

// ignore: camel_case_types
class Section510INT_PVCKLSTSEC008Page extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final Section510INT_PVCKLSTSEC008 section510INT_PVCKLSTSEC008;

  const Section510INT_PVCKLSTSEC008Page(this.section510INT_PVCKLSTSEC008,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _Section510INT_PVCKLSTSEC008PageState();
  }
}

class _Section510INT_PVCKLSTSEC008PageState
    extends State<Section510INT_PVCKLSTSEC008Page> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.section510INT_PVCKLSTSEC008,
        title: "HEADS",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC008
                      .attribute510INT_PVCKLSTSEC008Q001
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC008
                      .attribute510INT_PVCKLSTSEC008Q002
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC008
                      .attribute510INT_PVCKLSTSEC008Q003
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC008
                      .attribute510INT_PVCKLSTSEC008Q004
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC008
                      .attribute510INT_PVCKLSTSEC008Q005
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC008
                      .attributeIs_the_head_to_shell_attachment_achieved_via_welding
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC008
                      .attribute510INT_PVCKLSTSEC008Q007
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC008
                      .attribute510INT_PVCKLSTSEC008Q008
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC008
                      .attribute510INT_PVCKLSTSEC008Q009
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC008
                      .attribute510INT_PVCKLSTSEC008Q010
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC008
                      .attribute510INT_PVCKLSTSEC008Q011
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
