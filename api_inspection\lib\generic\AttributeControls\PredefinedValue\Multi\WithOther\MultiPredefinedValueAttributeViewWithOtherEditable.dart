import 'package:api_inspection/generic/UIControls/listener.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';

class MultiPredefinedValueAttributeViewWithOtherEditable
    extends StatefulWidget {
  final MultiPredefinedValueAttribute _attribute;
  final ListenerWrapper updateListener;

  const MultiPredefinedValueAttributeViewWithOtherEditable(
      this._attribute, this.updateListener,
      {Key? key})
      : super(key: key);

  @override
  _MultiPredefinedValueAttributeViewWithOtherEditableState createState() =>
      _MultiPredefinedValueAttributeViewWithOtherEditableState();
}

class _MultiPredefinedValueAttributeViewWithOtherEditableState
    extends State<MultiPredefinedValueAttributeViewWithOtherEditable> {
  bool otherButtonSelected = false;

  TextEditingController? _controller;

  @override
  void initState() {
    var attribute = widget._attribute;
    widget.updateListener.addListener(updateAttributeValue);
    attribute.addListener(onAttributeChanged);
    super.initState();
  }

  @override
  void didUpdateWidget(
      covariant MultiPredefinedValueAttributeViewWithOtherEditable oldWidget) {
    oldWidget.updateListener.removeListener(updateAttributeValue);
    widget.updateListener.addListener(updateAttributeValue);
    super.didUpdateWidget(oldWidget);

    var attribute = oldWidget._attribute;

    attribute.removeListener(onAttributeChanged);
    updateControllerBasedOnValue();

    widget._attribute.addListener(onAttributeChanged);
  }

  @override
  void dispose() {
    widget.updateListener.removeListener(updateAttributeValue);
    updateAttributeValue();
    var attribute = widget._attribute;
    attribute.removeListener(onAttributeChanged);
    _controller?.dispose();
    super.dispose();
  }

  List<String> getCurrentOtherValues() {
    var attribute = widget._attribute;
    var currentValue = attribute.getValue();
    var possibleValues = attribute.getOptions();
    var otherValues = currentValue
            ?.where((element) => !possibleValues
                .any((possibleElement) => possibleElement.value == element))
            .toList() ??
        [];
    return otherValues;
  }

  void updateControllerBasedOnValue() {
    TextEditingController? controller = _controller;
    if (controller == null) {
      return;
    }

    var otherValues = getCurrentOtherValues();

    if (otherValues.isEmpty) {
      controller.text = "";
    } else {
      controller.text = otherValues.first;
    }
  }

  void onAttributeChanged() {
    setState(() {
      updateControllerBasedOnValue();
    });
  }

  void updateAttributeValue() {
    TextEditingController? controller = _controller;
    if (controller != null) {
      var batch = FirebaseFirestore.instance.batch();

      var otherValues = getCurrentOtherValues();

      var attribute = widget._attribute;
      if (controller.text != "") {
        if (otherValues.length == 1 && otherValues[0] == controller.text) {
          return;
        }
        for (var value in otherValues) {
          attribute.removeValue(value);
        }
        attribute.addValue(controller.text);

        attribute.saveItem(batch);
        batch.commit();

        return;
      }
      for (var value in otherValues) {
        attribute.removeValue(value);
      }
      attribute.saveItem(batch);
      batch.commit();
    }
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;

    if (_controller == null) {
      var otherValues = getCurrentOtherValues();
      var controllerValue = otherValues.isEmpty ? "" : otherValues.first;
      if (controllerValue != "") {
        otherButtonSelected = true;
      }
      _controller = TextEditingController(text: controllerValue);
    }

    Color selectedColor = const Color.fromARGB(255, 4, 188, 242);
    Color unselectedColor = const Color.fromARGB(255, 122, 122, 122);

    List<Widget> buttons = [];
    var attrValue = widget._attribute.getValue();
    for (var item in widget._attribute.getOptions()) {
      Color buttonColor;
      if (attrValue != null &&
          attrValue.any((element) => element == item.value)) {
        buttonColor = selectedColor;
      } else {
        buttonColor = unselectedColor;
      }

      buttons.add(SizedBox(
          width: (width - 55) / 2,
          child: TeamToggleButton.withText(
              item.value + " " + (widget._attribute.unit ?? ""),
              () => {
                    setState(() {
                      var currentValue = widget._attribute.getValue();
                      var batch = FirebaseFirestore.instance.batch();
                      if (currentValue != null &&
                          currentValue
                              .any((element) => element == item.value)) {
                        widget._attribute.removeValue(item.value);
                      } else {
                        widget._attribute.addValue(item.value);
                      }
                      widget._attribute.saveItem(batch);
                      batch.commit();
                    })
                  },
              borderColor: buttonColor)));
    }

    buttons.add(SizedBox(
        width: (width - 55) / 2,
        child: TeamToggleButton.withText(
            "Other",
            () => {
                  setState(() {
                    var others = getCurrentOtherValues();
                    var batch = FirebaseFirestore.instance.batch();
                    var attribute = widget._attribute;
                    for (var value in others) {
                      attribute.removeValue(value);
                    }

                    attribute.saveItem(batch);
                    batch.commit();

                    _controller!.text = "";
                    otherButtonSelected = !otherButtonSelected;
                  })
                },
            borderColor:
                otherButtonSelected ? selectedColor : unselectedColor)));

    if (otherButtonSelected) {
      return Container(
          margin: const EdgeInsets.fromLTRB(0, 10, 0, 10),
          child: Column(
            children: [
              Wrap(children: buttons),
              Focus(
                  onFocusChange: (hasFocus) {
                    updateAttributeValue();
                  },
                  child: TextField(
                    controller: _controller,
                    decoration: const InputDecoration(
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.white, width: 1.0),
                      ),
                      border: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.white, width: 1.0),
                      ),
                    ),
                    onSubmitted: (String value) {
                      updateAttributeValue();
                    },
                    style: const TextStyle(color: Colors.white),
                  ))
            ],
          ));
    } else {
      return Container(
          margin: const EdgeInsets.fromLTRB(0, 10, 0, 10),
          child: Column(
            children: [Wrap(children: buttons)],
          ));
    }
  }
}
