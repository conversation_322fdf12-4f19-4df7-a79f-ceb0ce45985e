import 'FirebaseDatabaseHelper.dart';
//import 'FirebaseWebDBHelper.dart';
import 'IDatabaseHelper.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

class DatabaseHelperManager{
  
  static IDatabaseHelper global() {
    if (kIsWeb){
      //return FirebaseWebDBHelper.global();
      return FirebaseDatabaseHelper.global();
    }
    else{
      return FirebaseDatabaseHelper.global();
    }
  }
}
