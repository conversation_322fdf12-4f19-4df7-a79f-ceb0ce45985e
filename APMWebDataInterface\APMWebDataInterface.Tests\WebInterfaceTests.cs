using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using APMWebDataInterface.DataModel;
using APMWebDataInterface.ExampleDataModel;
using APMWebDataInterface.Generated.section510_Asset_Walkdown_Details_F;
using APMWebDataInterface.Generated.section570_Asset_Walkdown_Details_F;
using APMWebDataInterface.Generated.section570_Ext_Pipe_F;
using APMWebDataInterface.Generated.section653_Asset_Walkdown_Details_F;
using CommonDataInterface;
using CommonDataInterface.Attributes;
using ExcelDataReader;
using Google.Cloud.Firestore;
using Newtonsoft.Json;
using NUnit.Framework;

namespace APMWebDataInterface.Tests
{
    public class TestResult
    {
        public bool Passed { get; set; }
        public string Error { get; set; }
    }


    public class WebInterfaceTests
    {
        //[Test]
        //public void AddProject()
        //{
        //  var driver = APM_WebDataInterface.Global;
        //  driver.Initialize();

        //  SemaphoreSlim semaphore =new SemaphoreSlim(0);
        //  TestResult result = null;

        //  Task task = new Task(async () => {
        //    var location = new Location();

        //    await location.SavePendingChanges("<EMAIL>");
        //    var project = new Project(location.id);
        //    project.name.SetValue("Unit Test Project");


        //    await project.SavePendingChanges("<EMAIL>");


        //    semaphore.Release();
        //  });
        //  task.Start();

        //  semaphore.Wait();
        //  if (!result.Passed) {
        //    Assert.That(false, result.Error);
        //  }
        //}
        
        private void RunTestWithFramework(Func<Project, APM_WebDataInterface, Task<TestResult>> test,
            string projectName = "Unit Test Project")
        {
            var driver = APM_WebDataInterface.Global;

            SemaphoreSlim semaphore = new SemaphoreSlim(0);
            TestResult result = null;

            Task task = new Task(async () =>
            {
                await driver.Initialize();
                var locations = await driver.GetLocations("<EMAIL>");
                var location = locations.Select(a => a.id).ToArray();
                var projects = await driver.GetProjectsAtLocation(location, "<EMAIL>");

                var project = projects.FirstOrDefault(a =>
                    string.Equals(a.name?.GetValue(), projectName, StringComparison.InvariantCultureIgnoreCase));

                result = await test(project, driver);


                semaphore.Release();
            });
            task.Start();

            semaphore.Wait();
            if (!result.Passed)
            {
                Assert.That(false, result.Error);
            }
        }

        [Test]
        public void testingAddAsset()
        {
            RunTestWithFramework(async (project, driver) =>
            {
                var location = project.locationId;
                var asset = new Asset("Vessel", location);
                await asset.SavePendingChanges("<EMAIL>");


                return new TestResult { Passed = true };
            }, projectName: "Unit Test Project");
        }

        [Test]
        public void testingCompleteTask_DataCopiedOver()
        {
            RunTestWithFramework(async (project, driver) =>
            {
                var assets = await driver.GetAssetsForProjects(new[] { project.id }, "<EMAIL>");
                var asset = assets[0];
                var workOrders = await driver.GetWorkOrders(new[] { project.id }, "<EMAIL>");
                var workOrder = workOrders.FirstOrDefault(a => a.id == "c8d1ed7c-665c-4385-a37f-ae60ca5c2fbe");

                var task = workOrder.tasks.FirstOrDefault(a => a.taskType == "Asset Walkdown");
                task.ChangeStatus(APMTask.Statuses.InProgress);

                task.ChangeStatus(APMTask.Statuses.Completed);

                await workOrder.SavePendingChanges("<EMAIL>");
                Console.WriteLine(workOrder?.DisplayName);
                return new TestResult { Passed = true };
            }, projectName: "Bako Launch Test");
        }

        [Test]
        public void testingAddTask()
        {
            RunTestWithFramework(async (project, driver) =>
            {
                var assets = await driver.GetAssetsForProjects(new[] { project.id }, "<EMAIL>");
                var asset = assets[0];
                var workOrders = await driver.GetWorkOrders(new[] { project.id }, "<EMAIL>");


                var workOrder = new WorkOrder(asset, project.id);
                var newTask = workOrder.AddNewTask(WorkOrder.TaskTypes.ExternalVisual);

                await workOrder.SavePendingChanges("<EMAIL>");

                System.Threading.Thread.Sleep(1);

                var workOrders2 = await driver.GetWorkOrders(new[] { project.id }, "<EMAIL>");
                var workOrder2 = workOrders2.FirstOrDefault(a => a.id == workOrder.id);
                var task2 = workOrder2?.tasks.FirstOrDefault(a => a.id == newTask.id);

                if (workOrder2 == null || task2 == null)
                    return new TestResult { Passed = false };

                return new TestResult { Passed = true };
            }, projectName: "Brendan Project");
        }

        [Test]
        public void testingSetSomething()
        {
            RunTestWithFramework(async (project, driver) =>
            {
                var locations = await driver.GetLocations("<EMAIL>");
                var projects =
                    await driver.GetProjectsAtLocation(locations.Select(a => a.id).ToArray(), "<EMAIL>");
                var workOrders = await driver.GetWorkOrders(projects.Select(a => a.id).ToArray(), "<EMAIL>");
                var workOrder = workOrders[0];

                var task = workOrder.tasks[0];

                task.taskDetails.supervisor.SetValue("NewGUY:" +
                                                     new string((Guid.NewGuid().ToString().Take(4).ToArray())));

                await workOrder.SavePendingChanges("<EMAIL>");
                return new TestResult { Passed = true };
            });
        }

        [Test]
        public void testSetAreaOnAsset()
        {
            RunTestWithFramework(async (project, driver) =>
            {
                var newAsset = new Asset("Vessel", project.locationId);
                var walkDown = newAsset.walkDown as Section510_Asset_Walkdown_Details_F;

                walkDown.sectionIdentification.attributeNumber_or_ID.SetValue("ID:" +
                                                                              new string(Guid.NewGuid().ToString()
                                                                                  .Take(6).ToArray()));
                walkDown.sectionIdentification.attributeName.SetValue("Name " +
                                                                      new string(Guid.NewGuid().ToString().Take(6)
                                                                          .ToArray()));
                walkDown.sectionIdentification.attributeAsset_Type.SetValue("Pressure Vessel " +
                                                                            new string(Guid.NewGuid().ToString().Take(6)
                                                                                .ToArray()));

                await newAsset.SavePendingChanges("<EMAIL>");

                project.assetIds.AddValue(newAsset.id);
                await project.SavePendingChanges("<EMAIL>");

                return new TestResult { Passed = true };
            });
        }

        [Test]
        public void Test1()
        {
            var str = "ABC|DEF|GHI|JKL";
            var data = Encoding.UTF8.GetBytes(str);
            int[] intArray2 = data.Select(a => (int)a).ToArray();


            int[] intArray = new int[(int)Math.Ceiling(data.Length / 4.0)];


            Buffer.BlockCopy(data, 0, intArray, 0, data.Length);


            Console.Write(intArray2.Select(a => a.ToString()).Aggregate((a, b) => a + " " + b));
        }

        [Test]
        public void testCreateProject()
        {
            RunTestWithFramework(async (project, driver) =>
            {
                var location = new Location();
                await location.SavePendingChanges("<EMAIL>");

                var project1 = new Project(location.id);
                project1.name.SetValue("Brendan Project");
                await project1.SavePendingChanges("<EMAIL>");

                return new TestResult
                {
                    Passed = true
                };
            }, projectName: "Unit Test Project");
        }

        [Test]
        public void testCreateClient()
        {
            RunTestWithFramework(async (project, driver) =>
            {
                var client = new Client();
                await client.SavePendingChanges("<EMAIL>");

                return new TestResult
                {
                    Passed = true
                };
            });
        }

        [Test]
        public void testCreateBusinessUnit()
        {
            RunTestWithFramework(async (project, driver) =>
            {
                var client = driver.GetClients(new[] { "a040b6ad-5a0a-474c-9d0e-c378f13c5f17" }).FirstOrDefault();
                var buinessUnit = await client.CreateBusinessUnit("testing,testing", "<EMAIL>");

                return new TestResult
                {
                    Passed = true
                };
            });
        }

        [Test]
        public async Task getAllProjects()
        {
            var driver = APM_WebDataInterface.Global;
            await driver.Initialize();
            var projects = driver.GetProjects("<EMAIL>");

            Console.Write(projects);
        }

        [Test]
        public async Task TestGetStuff()
        {
            var driver = APM_WebDataInterface.Global;
            await driver.Initialize();
            var users = driver.GetUsers();
            var assets = await driver.GetAssetsForProjects(new[] { "0060ce5f-badd-4a8b-8105-ddc2450e83d8" }, "<EMAIL>");
            var workOrders =
                await driver.GetWorkOrders(new[] {"0060ce5f-badd-4a8b-8105-ddc2450e83d8"}, "<EMAIL>");
            Assert.That(users.Length, Is.GreaterThan(0));
            Assert.That(assets.Length, Is.GreaterThan(0));
            Assert.That(workOrders, Has.Length.GreaterThan(0));
        }

        [Test]
        public async Task getAllLocations()
        {
            var driver = APM_WebDataInterface.Global;
            await driver.Initialize();
            var locations = driver.GetLocations("<EMAIL>");

            Console.Write(locations);
        }

        [Test]
        public async Task getAllWorkOrders()
        {
            var driver = APM_WebDataInterface.Global;
            await driver.Initialize();
            var workOrders = await driver.GetWorkOrders(
                new[]
                {
                    "0a0bfd15-256a-429c-b2da-7ee04dc61177", "151381ab-1d93-453d-be29-ec88377ae0cc",
                    "2097ce0a-a4da-43d7-8962-de58ba96fab6", "22aa64b7-ea5c-40b0-9c35-3a027916701b",
                    "24ee4233-c7f0-47bd-9a3c-3f9f1e4dd77b", "2f6d4a22-9085-4b86-a570-bec3ea6aad43",
                    "314e1dc8-86d9-4777-b255-809fcca82764", "373ef9e2-3a12-4910-95e2-da1993a5eab6",
                    "374c4cc1-f386-4595-9825-74f483dade28"
                }, "<EMAIL>");
            Console.Write(workOrders);
        }

        [Test]
        public async Task modifyWorkOrderToRestrictedBusinessUnit()
        {
            var driver = APM_WebDataInterface.Global;
            await driver.Initialize();
            var workOrders = await driver.GetWorkOrders(
                new[]
                {
                    "0a0bfd15-256a-429c-b2da-7ee04dc61177", "151381ab-1d93-453d-be29-ec88377ae0cc",
                    "2097ce0a-a4da-43d7-8962-de58ba96fab6", "22aa64b7-ea5c-40b0-9c35-3a027916701b",
                    "24ee4233-c7f0-47bd-9a3c-3f9f1e4dd77b", "2f6d4a22-9085-4b86-a570-bec3ea6aad43",
                    "314e1dc8-86d9-4777-b255-809fcca82764", "373ef9e2-3a12-4910-95e2-da1993a5eab6",
                    "374c4cc1-f386-4595-9825-74f483dade28"
                }, "<EMAIL>");
            var wo = workOrders.FirstOrDefault();
            wo.businessUnitId.SetValue("763e134d-e976-4967-9d14-92deb3aadbaa");
            await wo.SavePendingChanges("<EMAIL>");
            Console.Write(workOrders);
        }

        [Test]
        public async Task modifyWorkOrdersFromRestrictedBusinessUnit()
        {
            var driver = APM_WebDataInterface.Global;
            await driver.Initialize();
            var workOrders = await driver.GetWorkOrders(new[]
            {
                "02f813a6-c2fa-42c4-a1ad-e2fb3b498857",
                "07252c15-72a6-43dd-a22c-6c16db64dfe3",
                "07c4b705-1556-404c-900a-da4280d33a81",
                "0ccfa9cf-ff89-4ca1-a1f8-aeeabc4be76f",
                "0ec6f01b-b08c-49ef-8eb2-5aaeaa87f0ce",
                "0f80aa8e-3fae-4e7d-b444-c4c733f7260e",
                "10394fef-3fd4-432c-80a8-6ed3285e272f",
                "12df01a0-3bcd-431f-9bd7-4c08b0328edc",
                "16fbde1f-1073-41a2-8eeb-6c12a99975ed"
            }, "<EMAIL>");

            var wo = workOrders.FirstOrDefault();
            wo.businessUnitId.SetValue("3a29da1d-c777-468d-8e1f-1c525836fe7d");
            await wo.SavePendingChanges("<EMAIL>");

            Console.Write(workOrders);
        }

        [Test]
        public async Task modifyWorkOrdersWithRestrictedBusinessUnit()
        {
            var driver = APM_WebDataInterface.Global;
            await driver.Initialize();
            var workOrders = await driver.GetWorkOrders(new[]
            {
                "02f813a6-c2fa-42c4-a1ad-e2fb3b498857",
                "07252c15-72a6-43dd-a22c-6c16db64dfe3",
                "07c4b705-1556-404c-900a-da4280d33a81",
                "0ccfa9cf-ff89-4ca1-a1f8-aeeabc4be76f",
                "0ec6f01b-b08c-49ef-8eb2-5aaeaa87f0ce",
                "0f80aa8e-3fae-4e7d-b444-c4c733f7260e",
                "10394fef-3fd4-432c-80a8-6ed3285e272f",
                "12df01a0-3bcd-431f-9bd7-4c08b0328edc",
                "16fbde1f-1073-41a2-8eeb-6c12a99975ed"
            }, "<EMAIL>");

            var wo = workOrders.FirstOrDefault();
            wo.facilityName.SetValue("Test");
            await wo.SavePendingChanges("<EMAIL>");

            Console.Write(workOrders);
        }

        [Test]
        public async Task createWorkOrder()
        {
            var driver = APM_WebDataInterface.Global;
            await driver.Initialize();
            var workOrder = new WorkOrder(new Asset("category", "locationId"), "someprojectid");
            workOrder.businessUnitId.SetValue("3a29da1d-c777-468d-8e1f-1c525836fe7d");
            await workOrder.SavePendingChanges("test");
        }

        [Test]
        public async Task getAllClients()
        {
            var driver = APM_WebDataInterface.Global;
            await driver.Initialize();
            var clients = driver.GetClients();

            Console.Write(clients);
        }

        [Test]
        public async Task getAllBusinessUnits()
        {
            var driver = APM_WebDataInterface.Global;
            await driver.Initialize();
            var businessUnits = driver.GetBusinessUnits();

            Console.Write(businessUnits);
        }

        [Test]
        public async Task getBusinessUnitsByClientId()
        {
            var driver = APM_WebDataInterface.Global;
            await driver.Initialize();
            var businessUnits = driver.GetBusinessUnitsByClientId("a040b6ad-5a0a-474c-9d0e-c378f13c5f17");

            Console.Write(businessUnits);
        }

        [Test]
        public async Task getAssetssByProjects()
        {
            var driver = APM_WebDataInterface.Global;
            await driver.Initialize();
            var assets = await driver.GetAssetsForProjects(new[]
                {
                    "0060ce5f-badd-4a8b-8105-ddc2450e83d8",
                    "020af681-fe98-451a-8518-60b3e7756e88",
                    "02a033c7-fa30-4c79-a4e3-f72d1b891a68",
                    "02a3524c-4d62-4340-ad92-59b3fac26360",
                    "034303fc-87ef-47ec-b51d-abe7d55a497a",
                    "03795e59-d703-4961-a4eb-598d424dcfdf",
                    "05e876c0-437a-48ba-aa14-599ef8377885",
                    "09ba828c-056f-45c0-9f56-a646f8ec25da",
                    "09eff1ff-e319-4e13-a8ac-2d2c109cb576",
                    "0a0bfd15-256a-429c-b2da-7ee04dc61177",
                    "0df88f5c-e917-46d1-8531-dd1a8e7d3895",
                    "1040f569-50aa-4f63-b045-63fc083b5569",
                    "11f14c9c-81ec-4d92-8079-8b68f2d4b588",
                    "12bd5eaf-77d7-4629-91b2-82c259c2a17b",
                    "134725f7-a912-464a-b3b7-fa7bd6f63555",
                    "151381ab-1d93-453d-be29-ec88377ae0cc",
                    "1593c15a-88e9-4abd-a9aa-246062ca284b",
                    "1972347a-0af4-42c7-a306-8c4eb72bd426",
                    "1a0e1987-65b4-424e-b721-8de00fdc73cf",
                    "1b879431-c246-410a-9e12-27f77ba945b7",
                    "1bf54d2e-03ab-4822-ac8d-b3e4389dc1c9",
                    "1ca1fccd-806f-45c2-bc23-c909f5f41348",
                    "1d42dfcd-8ea1-423a-aad3-5f754e41d286",
                    "1e68c137-981f-4bbf-be83-41a5dfbc5a70",
                    "1ed42df0-139e-4a42-b0c1-2ebdd9e45d95",
                    "1efde2c4-5c80-491b-95ff-e5a3d5a5d6f5",
                    "20241035-855a-4f0a-b805-f0793c055b3c",
                    "204d7822-8287-40a8-b6e0-2d980f1f0c1c",
                    "207a72d4-a8dd-4d81-a9d8-bee252987b77",
                    "2097ce0a-a4da-43d7-8962-de58ba96fab6",
                    "20b3026d-56f1-426a-8848-9b62585bde45",
                    "20e60a55-42f0-49ca-980e-0bbb6e7d0ca2",
                    "2183d40f-302d-4fbd-bdee-bd208cf8ad96",
                    "22280866-7f16-4cd7-b05c-5a3338de888c",
                    "228a785e-28f5-4aa0-9d07-182e4b4373ba",
                    "22aa64b7-ea5c-40b0-9c35-3a027916701b",
                    "2310a5de-c769-4deb-a7c2-c631972ba4e3",
                    "24030d58-46c8-4f53-8428-27ae8e4d25d9",
                    "2408b642-af5c-43d2-9d85-6539e94c439d",
                    "24ee4233-c7f0-47bd-9a3c-3f9f1e4dd77b",
                    "26285f3b-8c61-4549-8c78-ba65e5943cbe",
                    "26400775-e5ec-4b04-bd11-0d8ff2e29038",
                    "27f3e01e-724f-4d96-804e-663221f1cc25",
                    "28b00df9-b8f7-4ce4-b714-0d0dd6dfcae0",
                    "28f600ba-c014-41df-9288-85d6ac37a6f0",
                    "29cfccc9-babd-4f43-9dda-3746807c0ac8",
                    "2cab5db1-9cd4-4a8d-baad-aebda3a83d02",
                    "2d1a1c2f-d059-4856-abc3-121f07dc5c49",
                    "2d8a7e97-b7c4-4c85-8e9c-4d43d1dad265",
                    "2dc35048-4a5f-49f7-8961-1e0677b8f2a7",
                    "2f0e23d3-2447-4ee9-bbd7-9ce0c18c46c9",
                    "2f6d4a22-9085-4b86-a570-bec3ea6aad43",
                    "3069f7ea-e89c-4d3c-bbd0-d56f10821077",
                    "31457a5e-ba5b-48a8-a911-4c1ea7dc3d95",
                    "314e1dc8-86d9-4777-b255-809fcca82764",
                    "3216a141-a1e2-4c8c-9267-b3cc3782f9aa",
                    "3236ef7b-2353-4bc0-be0f-2e4a6e9e36d6",
                    "3339c63b-4372-4aac-a397-cac8fd4f293d",
                    "335d00b6-9758-46fa-ac2e-6b8b9ba9f7fc",
                    "33822966-9aa2-413a-9cd0-8d0fb844e5d6",
                    "3387b6cb-4432-4afb-8f26-82de7ec80d17",
                    "34524b57-7df4-48b7-b3fe-69a3da0d276e",
                    "346f5618-1886-4d8b-befc-cc1e04ef4a5a",
                    "347b65e2-3028-4753-828a-de5a2ba5a3d1",
                    "3513dfbe-8563-4b47-bd7f-644595a8a73a",
                    "361d6c95-37b0-4f29-91a0-50c6f9410f7d",
                    "3719cd49-2680-427c-9722-434a83231f06",
                    "373ef9e2-3a12-4910-95e2-da1993a5eab6",
                    "374c4cc1-f386-4595-9825-74f483dade28",
                    "38c93496-15d9-4e29-baa1-5b09351d0963",
                    "3a39c39e-1a10-4b8e-973d-af41cfe36357",
                    "3ae42c7d-d1e3-4ab5-b88c-5ed4669a8365",
                    "3bf53890-136f-41cf-8f29-9eeec35a54ae",
                    "3c7d1357-f5ec-4053-a4cd-81cbec45cd46",
                    "3dd4ede8-39c4-48f7-aa2c-b716aeafce1e",
                    "3e67d859-8194-47f6-a54d-8d3e287f8f6f",
                    "3f2bb1ed-22b1-4f9b-8598-4c99bdfaf011",
                    "3fbbcb38-6fc5-47a2-9970-e64759a29db3",
                    "4044ab7a-b429-46b8-8cf0-1f6f0b52759a",
                    "40a178b5-ecfb-49d3-94bf-b98a99f78e9e",
                    "411f13b0-1fe4-4ded-89f0-dd5f05ea5e52",
                    "41842357-faa3-4d85-8f49-a38251f158c4",
                    "41aff751-ffe2-4b4a-a241-a9385b620fbb",
                    "42099560-e95b-431b-9884-5af135a3a283",
                    "429135a5-22c0-489c-a6fb-9cbe5affe8a7",
                    "42c7c73c-4735-4964-b60f-ec3dbaf0e2f8",
                    "46bea055-6be6-456f-bc9d-ca770f97dd57",
                    "472f9b98-061d-48f4-83ad-6a40c634f1b4",
                    "4c558545-ccdd-4895-bb82-dea0ae0f1860",
                    "4e27bd6e-ce87-4af9-ac1c-7f5ba0f49229",
                    "4f4d15f0-6184-4923-8956-d623d47c1390",
                    "4f5b084f-d042-4c6b-97ad-c3574c9cecab",
                    "51019d0e-5b1b-4264-85a7-09846597fedd",
                    "5296ee2b-c6eb-44d4-99c6-8208bd998b09",
                    "55366676-73a5-429c-9840-d42f1659d9b0",
                    "55bfb289-2c69-495a-9955-2902b1da22e8",
                    "569a0a60-ab8e-4298-9f6e-d43097245141",
                    "56c47153-0cb0-47b1-9b6b-a596010dce15",
                    "57ada772-e369-4e59-acbb-60c4839f76d0",
                    "58cf8b84-0e1f-4fd1-8b91-ccafde004c48",
                    "5d7c3a69-bdb8-49f4-a818-d69af05b5df0",
                    "5efc0f16-848c-4887-b362-f4e74e11913b",
                    "5f2481e3-9d73-46d7-8183-34c080158f8f",
                    "5ffe0af6-c18d-41e1-8fdd-cc4f5d1f8d3f",
                    "61ea0f90-a2a6-4020-b746-37187fe0abb7",
                    "61f9f200-610b-4c94-b3b5-84a2e5cdcb47",
                    "621d3c3e-4f4f-4576-b428-85bedbed47b3",
                    "62401fd4-5dfc-47c4-8022-a6ead7e6b0d9",
                    "62806e65-ed70-4f87-810b-2ca11439afe7",
                    "63c8178b-65a0-4fc9-bb89-36b83a35c735",
                    "642d5e9d-50de-427c-b454-8f37ac2416ae",
                    "6440da90-fb51-426c-a1d5-42065f2eaa32",
                    "64832097-138c-47d3-bcd6-b8aee0980197",
                    "6615094b-7ba2-4648-908f-5e45ee3868bd",
                    "66b1fcff-096f-4ac8-9a1b-654f5846d10e",
                    "672bcee9-6f6d-41ce-a8ea-40a8b33f1a26",
                    "672fdff4-ceb4-4914-9736-16a4c4c6b2b1",
                    "685c777a-ed24-43a8-9d9b-55d45c6a6f37",
                    "689b1f02-60fa-4da5-95af-43dcb721c751",
                    "6a411906-2ca5-4323-9d58-9afe6fb959e8",
                    "6a834ace-df0c-4745-a96c-71efe49b4d54",
                    "6c3052f5-c21b-4028-a85a-eab281c5d565",
                    "6c47a7df-b762-44d0-9bab-495690b1e666",
                    "6cba3027-266b-499f-a2fe-33a77be09c94",
                    "6cd06b2f-e451-4e83-bfe8-923196733181",
                    "6dabed5f-2f1b-416b-bf20-6d510a35e1ab",
                    "6eb86a73-caf3-426e-a5eb-ecf697e59a11",
                    "6fc8c4a3-f4a2-4c6e-a573-43b8e57a056d",
                    "710b8c98-96c4-4e5d-ba12-946a2fc6f79b",
                    "71ba1634-132b-4518-81ec-eba84ac6ae98",
                    "734b04bb-adcf-4eab-9b8a-84b988417665",
                    "73826346-bc5f-4e9c-8eda-59ea092321f1",
                    "7430a0de-f3f4-4f2e-8bfa-b2c7a56375a7",
                    "74eec1b6-8cdf-4535-8913-d48d1ef5978d",
                    "754c05f7-edb6-4ea5-b533-99671fe7a56a",
                    "76f38bfc-6421-4605-ab55-1903cf10d197",
                    "772c089d-e831-4b05-8fb9-22945d0d1b54",
                    "773ac044-63b8-48e1-a0cf-c074b545183d",
                    "7759a4dc-fd34-4fad-ac01-f35d8a5fa578",
                    "77a62db1-9494-4de3-8b3c-cda2dc0e266a",
                    "77c8d012-24ab-41ce-86e9-f1f5aa7ea455",
                    "78f948b2-6f2b-453a-b384-7bd01069744a",
                    "79aee1b2-bc12-4033-aa8e-9a9d0db79bde",
                    "7a6da61e-301e-4352-afea-ede14fc8bc2b",
                    "7a7aa254-c3e9-43a9-ba01-0bedb5da9644",
                    "7b69f439-f36e-4e94-a094-79471aaf33ca",
                    "7bb563da-a4fa-4362-9a0c-9dc0995e26e5",
                    "7bd78656-12f3-40ec-96fa-113e4fb9c03a",
                    "7bf5cd7d-cffc-4eb7-945a-f1c911483053",
                    "7bff6108-7230-4886-af48-3405d8e58a2e",
                    "7c73361a-0454-4a0e-9953-a01ac818b534",
                    "7cb8b6b0-6b28-4ffe-a115-01840a39bc27",
                    "7cea8403-a3ae-4972-bbcd-750088c675a9",
                    "7d607f2c-695b-4dc4-a08f-dc4aa7488575",
                    "7e310afa-e302-43ed-86e6-955eeca45b47",
                    "7f14c964-5d53-447c-8926-0c599b179bde",
                    "7f36834b-8d68-47a2-87d4-d114c378a3b2",
                    "813e7627-e212-45ac-8759-a0baba0458df",
                    "8238284e-4f5c-4ec9-9d86-43e75158bcb4",
                    "8407af07-ee9f-43a8-86ed-a04b95218791",
                    "84bda143-1e31-4427-b1fc-7312e3c01472",
                    "85d48dc1-b6c3-4fd5-8cc9-afc1db19b887",
                    "8665c5be-c005-4dab-adea-9b3b89686934",
                    "87aa4fb7-a7e6-4a9a-83aa-6b3d37dd77ce",
                    "88b7894a-1fc4-4b51-9f9a-1186cf873ea9",
                    "8bf3b40c-bb7b-4cee-b9e3-3275ac9a04c9",
                    "8c224384-8f74-40f6-a834-8fd371d140cb",
                    "8d027832-e617-4041-bc1c-35460bb4d1c2",
                    "8d2e5946-f1a5-414f-8b2e-5bb05f7bbe0f",
                    "8fcc0672-9e2c-4497-be96-ac8370592645",
                    "933dc933-e0ab-4db6-a274-f9141efb438d",
                    "94ceb86b-2852-4cbc-938f-153c88339f97",
                    "97426a3a-d0b8-40dd-ba43-740864c9867d",
                    "977c14b8-26a6-43b6-9f94-4dc4bc06658d",
                    "97a0c304-1f89-4742-bbd3-c2fffc8286f7",
                    "998b190a-b051-4591-946c-ba1414959485",
                    "9a283601-7136-4e57-96b4-bec0724f05d3",
                    "9cb9c110-0bec-4afc-9dd5-73f22c8c3ab3",
                    "9d5fc72b-88ca-4e84-8826-a228c87b3bd2",
                    "9e5c8375-c454-47b7-b280-c855a827dd12",
                    "9f02aafa-6d0f-4893-9651-bd03931c5694",
                    "9fa5525a-dcff-46a0-b56f-58f517b984a8",
                    "a001c928-6dc4-4afc-86cd-c0d65bdc88a5",
                    "a00d5bff-2f4d-4073-be9c-7dd36a6c005c",
                    "a0c3b1f1-9b70-4b9a-a1e2-fc8b53c81699",
                    "a1c6c08d-09be-4501-bdfe-17c679f988c5",
                    "a21b4706-118e-422d-afda-6969ae022e6e",
                    "a23af618-7625-483c-a3f8-2c4688b4d06a",
                    "a292b865-2081-4903-8d7a-4a9354846be3",
                    "a2cbbf27-4c8d-4c30-9f0b-a7fe32c7db8b",
                    "a37d0c5b-aba6-4bbc-9542-15d954695e45",
                    "a40c5796-8eaa-42be-a088-a8a06bd862d3",
                    "a4c211b7-d51d-4426-b3e4-1598b4704586",
                    "a5709d2b-6d47-4007-af43-38529457be41",
                    "a61c3c12-4b62-4fba-a666-fba04b98d737",
                    "a8647951-a7a3-4569-a81d-3d281c4d5cdb",
                    "a86af4ee-784d-4067-9810-f3577c57ed7c",
                    "a941711a-126c-4e83-a66f-4790d158a4cf",
                    "a96f2a09-65f6-4dad-aec7-960bee2dd22c",
                    "a98b1149-dd18-4f0b-b9a8-dac5321c999f",
                    "ab1316ca-781d-476c-87c2-501503dd5f5d",
                    "acd3bf65-ab6f-4387-9617-7fa1ff22855b",
                    "ace169b3-5593-4eda-9096-6b2f1a636212",
                    "ae676d12-fb16-4ccc-997c-3975b44de661",
                    "ae936c6c-0da6-4a25-a951-78f280636c5b",
                    "af8ac4f5-2534-4f82-8420-d188e8cb6f74",
                    "b0ab8950-3a38-4261-8058-95670bbbb97c",
                    "b18190ee-59da-4f8e-b52b-c93d70cb5223",
                    "b27a3116-dea1-4498-9f12-af1e380abcbc",
                    "b2ce52c2-8663-4361-a3f3-ca2ab1601270",
                    "b2f8109c-3420-4722-af35-ad63c0089ae9",
                    "b4277fa3-ac05-4d01-aa07-b642b83dfb09",
                    "b5d215ff-f4c5-456d-a9a8-9d0ebb11e14e",
                    "b6361aee-b5cb-4b18-840e-78c1cf712ee2",
                    "b66341ac-bc46-417a-9ad7-e0c9bd886eb8",
                    "b688c8d7-385c-45fb-b86a-61873f94ff03",
                    "b7f4d2ec-9607-4651-a6c2-d2089ae738ba",
                    "b832eba8-bece-45da-93a2-1e216e228c96",
                    "b8f3a29f-ebf8-457d-9796-a6c30294c120",
                    "b901b498-5348-4ad5-9de9-7332d50a188e",
                    "b943db50-c845-4fa3-9e5e-cc1b2d76e4a0",
                    "ba8b46dd-bb91-4b8b-8782-413c5459cc3a",
                    "bab158ad-71a1-4436-a281-960798a5a212",
                    "bb8177fc-e1b0-409f-9d67-51d7f61b5d43",
                    "bc810b27-18ac-4343-8fb9-68a227d23858",
                    "bcc11df7-14c2-4359-a4f0-8fec1d3314b1",
                    "bd2d2000-019e-40e6-9933-0628918cb289",
                    "bd72e986-8f67-46e8-bedd-4eded5031873",
                    "bdb30b82-fa7b-45d2-982c-3c1d8fe83db1",
                    "be61431d-f353-465b-b046-8c6e0bd2f32f",
                    "be66b576-0102-4439-8864-5741b240d0a4",
                    "beec5f30-d653-47b0-bcf3-a83c6e2ade88",
                    "bf81f342-7053-4c5d-9c3e-4a8a0d074198",
                    "c19a030a-8bde-410c-a2e4-24b895eaa3f3",
                    "c2b04a80-2b0f-4127-8f07-a7a79201a458",
                    "c2ef4962-82ca-44ef-9673-cd60884b7b96",
                    "c3a4b994-601d-4be1-aa91-9e9d3fb307fb",
                    "c5e5bfd1-d3c3-4851-99bb-6ab95486703d",
                    "c63f419f-8b65-4ca2-9b8a-a1ca7c9edb3f",
                    "c6f3c1e9-5d3e-4831-8eb4-3988c0301358",
                    "c7eefa99-f020-471b-afb6-e1b52af16dec",
                    "c8eeceda-7572-4a79-8122-1f5f95d7fb1c",
                    "cb074d62-930d-4452-802a-7a811d1bd4ac",
                    "cb3a7c2f-a312-44f2-a7f1-1a69d63cd831",
                    "cb6548ac-75df-4144-8e33-b4c64aaa2302",
                    "ccc0dc7d-fb7f-437c-a556-5cdf6f67a0dc",
                    "cdca972b-9072-4427-b04f-de61fd4be07b",
                    "cdffc50b-c5de-4222-9d48-e8178faef1a7",
                    "cec49553-87f6-4909-a093-4e13771068bf",
                    "d1558942-4d35-48d2-8a27-d361eb2d56c7",
                    "d22a3e32-fd3e-4d9f-8c6a-1b8a02c64e8c",
                    "d27448ce-3ecb-49e8-a650-bb5184cf2555",
                    "d3769211-b2ed-4b33-bc89-461baff6d895",
                    "d3dd90d0-13ab-4fb3-b820-42e3484f813a",
                    "d51ea925-0aa7-4c4e-9545-116c02e68e29",
                    "d62e932d-514d-4666-9501-81a0dc8a8288",
                    "d81943ce-5bbf-4e02-a656-9c40c4e4bc6d",
                    "dab1d11e-b6e0-4734-90ea-9eca9bf0538c",
                    "db7b2808-e952-419a-a251-7442f079e7f6",
                    "dba45b9f-138f-4ea4-a34e-f93d30a11de0",
                    "dc1a7c16-2544-429d-94b0-3b000020de16",
                    "dc75ce04-5389-42d6-8fbf-588e34d7b395",
                    "dca84c3c-6e2f-4c33-9e45-2700ec308592",
                    "df4ed95d-607b-48e7-b0ba-7c1debc18eb9",
                    "dfc4af29-8fdb-437b-a7e2-b9897a85e26d",
                    "e26fc79a-742b-434d-8fe1-1ca491d7ac8b",
                    "e2d3819c-f70e-41dc-9c09-cbca5f87a564",
                    "e38dde96-3a57-4b0d-b250-f8eba1da7c53",
                    "e47763b6-2581-4f08-afdc-dfbfe092d4e0",
                    "e47fea90-40d9-4502-aa81-18743384e2bb",
                    "e4e8ecb4-3010-4cbe-b16f-dc013c3b44a6",
                    "e73c9e06-a7ef-4038-a563-a57d00a9451f",
                    "e8bd8c70-91d4-459e-8bfa-da1620af10cb",
                    "e907ffd9-7b5b-4faf-b9fa-bd5d14f247cc",
                    "e95810cb-ba69-4b89-8011-d4cececcc4ab",
                    "e9b19518-0c63-4495-a3e1-57d403a45271",
                    "e9eb391d-3ea0-4eba-bdb7-7906871e2fd2",
                    "eaecb3b0-69a3-4690-8320-f455e9ab567b",
                    "eb954b84-dd05-4cdf-92a1-5ce19d9a13e7",
                    "ec82234b-33e0-466f-8012-dad20dd7f947",
                    "eca3c1c9-adb1-4fed-86b3-bae7871429e2",
                    "ed478f4c-c5d5-49f3-9024-9f5d7a253032",
                    "ed8a6ec6-4fb6-48bc-bbfb-8fae46f3934e",
                    "ed97fa1c-3a90-44fd-b0ed-7cff6750a2a5",
                    "ed9ed7df-db7f-4ab6-9913-3f8b1ed29ada",
                    "ee41a5fd-d30f-4c37-b640-d859cc313a9c",
                    "eea09c85-5620-4c5d-9943-84d343264b1b",
                    "eed8bf1b-1764-451e-97d4-af27f5f7399b",
                    "ef5162d1-0c99-455e-83c7-177442c93fdc",
                    "ef68dc69-408a-4420-9b76-c5010f1a0bb1",
                    "f0666767-c911-42ef-8b6f-b65260083453",
                    "f0d9e615-03bc-4ce4-9e85-95a0429a283b",
                    "f12693dc-d6f2-4815-b7a6-6b7294a17d39",
                    "f2a1950d-d298-4f2c-902d-07d2debe5ab2",
                    "f303031b-40d3-4317-aed4-46c1a9272728",
                    "f30e206d-0eaa-4ea1-8b0e-ea51417a438e",
                    "f4f9a761-ee35-4673-9dde-3f1eadfeca75",
                    "f5d29522-ae52-4d69-abfb-a4c84ccd86e9",
                    "f6393a17-364a-4f0b-badf-426269edb173",
                    "f76d9dc5-8481-4290-91e9-c5ae195976cd",
                    "f8fa4c67-c1b9-475c-ac6b-8b9bec07f88c",
                    "fb3e4b9d-a3e4-41e3-ab99-37b011352e3c",
                    "fbee24fe-75bc-4b19-982b-9fac5aa7c42d",
                    "fc11544b-7486-44e2-84de-540124bbb1d6",
                    "fceb8231-1f3b-419b-8151-40bf68fc558d",
                    "fe4a0ad2-969f-4bdf-8e72-c84bba69a4d6",
                    "fe80e847-0ce6-4645-9d34-143c6a733391",
                    "fe956454-26cc-48c9-96ec-78dfa6bb80de",
                    "fff4941b-8c2a-43fa-9c6c-404ab889fbe6"
                }, "<EMAIL>");


            Console.Write(assets);
        }

        [Test]
        public async Task changeClientName()
        {
            var driver = APM_WebDataInterface.Global;
            var passed = false;
            string error = null;
            await driver.Initialize();
            var client = driver.GetClients(new[] { "24aad4de-7c64-4b5d-97b6-7ccbb0c8de54" }).FirstOrDefault();
            client.Name.SetValue("Clam");
            await client.SavePendingChanges("<EMAIL>");
        }
        
        [Test]
        public async Task addBusinessUnitToUser()
        {
            var driver = APM_WebDataInterface.Global;
            var passed = false;
            string error = null;
            await driver.Initialize();
            var users = driver.GetUsers();
            var portalUser = users.FirstOrDefault(user => user.Email == "<EMAIL>");
            //portalUser.BusinessUnitIds.SetValue(new []{"763e134d-e976-4967-9d14-92deb3aadbaa"});
            //await portalUser.SavePendingChanges("<EMAIL>");
            portalUser.BusinessUnitIds.SetValue(new[] { "3a29da1d-c777-468d-8e1f-1c525836fe7d", "80c01c1d-7296-4549-ad02-23a89ba5a856" });
            await portalUser.SavePendingChanges("<EMAIL>");
            //portalUser.BusinessUnitIds.SetValue(new[] { "763e134d-e976-4967-9d14-92deb3aadbaa" });
            //await portalUser.SavePendingChanges("<EMAIL>");
            //var technician = users.FirstOrDefault(user => user.Email == "<EMAIL>");
            //technician.BusinessUnitIds.SetValue(new[] {"763e134d-e976-4967-9d14-92deb3aadbaa"});
            //technician.SavePendingChanges("<EMAIL>");
            //technician.BusinessUnitIds.SetValue(new[] { "3a29da1d-c777-468d-8e1f-1c525836fe7d", "80c01c1d-7296-4549-ad02-23a89ba5a856" });
            //await technician.SavePendingChanges("<EMAIL>");
        }
        
        //[Test]
        public async Task RemoveAssets()
        {
            // Email used for all operations that require it
            const string email = "<EMAIL>";

            // Ids of assets we want to remove
            var assetDbIds = new[]
                {"564524bb-3ad8-4971-907c-e517b74638b4", "a4cec04d-19cd-49df-8445-768fd83e10cd"};

            // Initialize the driver
            var driver = APM_WebDataInterface.Global;
            await driver.Initialize(APM_WebDataInterface.Databases.production);

            // Get the assets
            var assets = await driver.GetAssets(assetDbIds, email);

            // Get the projects that these assets are associated with
            var projects = driver.GetProjects(email)
                .Where(p => p.assetIds.CurrentValue.Any(id => assetDbIds.Contains(id)));

            // Get any work orders using these assets
            var projectWorkOrders = await driver.GetWorkOrders(projects.Select(p => p.id).ToArray(), email);
            var assetWorkOrders = projectWorkOrders.Where(wo => assetDbIds.Contains(wo.asset.id));

            // Get any locations these assets are associated with
            var locations = await driver.GetLocations(email);
            var assetLocations = locations.Where(l => assets.Select(a => a.locationId).Contains(l.id));

            // Remove assets from projects
            foreach (var project in projects)
            {
                foreach (var assetDbId in assetDbIds)
                {
                    project.assetIds.RemoveValue(assetDbId);
                }

                await project.SavePendingChanges(email);
            }

            // Remove asset card from location
            foreach (var asset in assets)
            {
                var location = assetLocations.FirstOrDefault(l => asset.locationId == l.id);
                // Empty out the asset cards (seems easier
                await driver.DatabaseContextManager.ContextForNonListenerAction(db =>
                    db.Collection("locations").Document(location.id)
                        .UpdateAsync("AssetCards." + asset.id, FieldValue.Delete));
            }

            // Delete work orders and tasks
            foreach (var workOrder in assetWorkOrders)
            {
                foreach (var task in workOrder.tasks)
                {
                    await driver.DatabaseContextManager.ContextForNonListenerAction(db =>
                        db.Collection("tasks").Document(task.id).DeleteAsync());
                }

                await driver.DatabaseContextManager.ContextForNonListenerAction(db =>
                    db.Collection("workorders").Document(workOrder.id).DeleteAsync());
            }

            // Delete the assets
            foreach (var asset in assets)
            {
                await driver.DatabaseContextManager.ContextForNonListenerAction(db =>
                    db.Collection("assets").Document(asset.id).DeleteAsync());
            }
        }

        //[Test]
        //public async Task Migrate570PipeScheduleAndPipeSizeFieldsFromPredefinedToMultiPredefined()
        //{
        //    // Email used for all operations that require it
        //    const string email = "<EMAIL>";

        //    // TODO: MAKE SURE THIS IS WHAT YOU WANT IT TO BE!
        //    const APM_WebDataInterface.Databases env = APM_WebDataInterface.Databases.testing;

        //    // Initialize the driver
        //    var driver = APM_WebDataInterface.Global;
        //    await driver.Initialize(env);

        //    // TODO: I don't know how to get all projects...This uses my effective business unit ids
        //    var projects = driver.GetProjects(email);
        //    var workOrders = await driver.GetWorkOrders(projects.Select(p => p.id).ToArray(), email);
        //    var tasks = workOrders.SelectMany(w => w.tasks.Where(t => t.asset.assetCategory == "Piping")).Cast<APMTaskGeneric<Section570_Ext_Pipe_F>>();
        //    foreach (var task in tasks)
        //    {
        //        var walkDown = task.asset.walkDown as Section570_Asset_Walkdown_Details_F;
        //        walkDown.sectionGeneralInformation.attributePipe_Size.ValueChangeLog;
        //    }
        //}
        
        [Test]
        public async Task Migrate570PipeScheduleAndPipeSizeFieldsFromPredefinedToMultiPredefined()
        {
            // Email used for all operations that require it
            const string email = "<EMAIL>";

            // TODO: MAKE SURE THIS IS WHAT YOU WANT IT TO BE!
            var env = APM_WebDataInterface.Databases.testing;

            // Initialize the driver
            var driver = APM_WebDataInterface.Global;
            await driver.Initialize(env);
        }

        [Test]
        public async Task updateNonVerifiedUserTest()
        {
            var driver = APM_WebDataInterface.Global;
            var passed = false;
            string error = null;
            await driver.Initialize();
            var users = driver.GetUsers();
            var nonverifieduser = users.FirstOrDefault(user => user.Email == "<EMAIL>");
            nonverifieduser.BusinessUnitIds.AddValue("TestBU");
            await nonverifieduser.SavePendingChanges("<EMAIL>");
            Console.Write(nonverifieduser);
        }
        
        [Test]
        public void testUpdateAsset()
        {
            RunTestWithFramework(async (project, driver) =>
            {
                var asset = new Asset("Vessel", project.locationId);
                var newName = "New Asset From Unit Test: " + new string(Guid.NewGuid().ToString().Take(6).ToArray());
                (asset.walkDown as Section510_Asset_Walkdown_Details_F).sectionIdentification.attributeName
                    .SetValue(newName);

                await asset.SavePendingChanges("<EMAIL>");


                // associate asset with project
                project.assetIds.AddValue(asset.id);
                await project.SavePendingChanges("<EMAIL>");

                var assets = await driver.GetAssetsForProjects(new[] { project.id }, "<EMAIL>");

                if (assets.Any(a =>
                        a.assetCategory == "Vessel" &&
                        (a.walkDown as Section510_Asset_Walkdown_Details_F).sectionIdentification.attributeName
                        .GetValue() == newName))
                {
                    return new TestResult { Passed = true };
                }
                else
                {
                    return new TestResult
                    {
                        Passed = false,
                        Error = "Was unable to find asset after insert"
                    };
                }
            }, projectName: "Unit Test Creation Project");
        }
        
        [Test]
        public void testFindAsset()
        {
            RunTestWithFramework(async (project, driver) =>
            {
                var assets = await driver.GetAssetsForProjects(new[] { project.id }, "<EMAIL>");
                if (assets.Length > 0 && assets.All(a => a != null))
                {
                    return new TestResult
                    {
                        Passed = true,
                    };
                }

                return new TestResult
                {
                    Passed = false,
                    Error = "Was unable to find asset after insert"
                };
            });
        }
        
        [Test]
        public void testCreateAsset()
        {
            RunTestWithFramework(async (project, driver) =>
            {
                var asset = new Asset("Vessel", project.locationId);
                var newName = "New Asset From Unit Test: " + new string(Guid.NewGuid().ToString().Take(6).ToArray());
                (asset.walkDown as Section510_Asset_Walkdown_Details_F).sectionIdentification.attributeName
                    .SetValue(newName);

                await asset.SavePendingChanges("<EMAIL>");


                // associate asset with project
                project.assetIds.AddValue(asset.id);
                await project.SavePendingChanges("<EMAIL>");

                var assets = await driver.GetAssetsForProjects(new[] { project.id }, "<EMAIL>");

                if (assets.Any(a =>
                        a.assetCategory == "Vessel" &&
                        (a.walkDown as Section510_Asset_Walkdown_Details_F).sectionIdentification.attributeName
                        .GetValue() == newName))
                {
                    return new TestResult { Passed = true };
                }
                else
                {
                    return new TestResult
                    {
                        Passed = false,
                        Error = "Was unable to find asset after insert"
                    };
                }
            });
        }

        [Test]
        public void assignToMe()
        {
            var driver = APM_WebDataInterface.Global;

            SemaphoreSlim semaphore = new SemaphoreSlim(0);

            var assetId = "7eaa5311-16b9-460e-867b-c6fa92b22bb2";

            bool passed = false;
            string error = null;
            Task task = new Task(async () =>
            {
                await driver.Initialize();
                var locations = await driver.GetLocations("<EMAIL>");
                var locationIds = locations.Select(a => a.id).ToArray();
                var projects = await driver.GetProjectsAtLocation(locationIds, "<EMAIL>");

                var project = projects.FirstOrDefault(a => a.name.GetValue().Equals("Test_QA_Project1"));

                var assets = await driver.GetAssetsForProjects(new[] { project.id }, "<EMAIL>");
                var asset = assets.FirstOrDefault(a => a.unit.GetValue() == "1515");

                var workOrders = await driver.GetWorkOrders(new[] { project.id }, "<EMAIL>");

                var assetWorkOrder = workOrders.Where(a => a.asset.id == asset.id).SelectMany(a => a.tasks);
                var walkdown = assetWorkOrder.FirstOrDefault(a => a.taskType == "Asset Walkdown");
                var workOrder = workOrders.FirstOrDefault(a => a.tasks.Contains(walkdown));

                walkdown.assignedUsers = new string[] { "<EMAIL>" };

                await workOrder.SavePendingChanges("<EMAIL>");

                semaphore.Release();
            });
            task.Start();

            semaphore.Wait();
            if (!passed)
            {
                Assert.That(false, error);
            }
        }

        [Test]
        public void unAssignToMe()
        {
            var driver = APM_WebDataInterface.Global;

            SemaphoreSlim semaphore = new SemaphoreSlim(0);

            var assetId = "7eaa5311-16b9-460e-867b-c6fa92b22bb2";

            bool passed = false;
            string error = null;
            Task task = new Task(async () =>
            {
                await driver.Initialize();
                var locations = await driver.GetLocations("<EMAIL>");
                var locationIds = locations.Select(a => a.id).ToArray();
                var projects = await driver.GetProjectsAtLocation(locationIds, "<EMAIL>");

                var project = projects.FirstOrDefault(a => a.name.GetValue().Equals("Test_QA_Project1"));

                var assets = await driver.GetAssetsForProjects(new[] { project.id }, "<EMAIL>");
                var asset = assets.FirstOrDefault(a => a.unit.GetValue() == "1515");

                var workOrders = await driver.GetWorkOrders(new[] { project.id }, "<EMAIL>");

                var assetWorkOrder = workOrders.Where(a => a.asset.id == asset.id).SelectMany(a => a.tasks);
                var walkdown = assetWorkOrder.FirstOrDefault(a => a.taskType == "Asset Walkdown");
                var workOrder = workOrders.FirstOrDefault(a => a.tasks.Contains(walkdown));

                walkdown.assignedUsers = new string[] { };

                await workOrder.SavePendingChanges("<EMAIL>");

                semaphore.Release();
            });
            task.Start();

            semaphore.Wait();
            if (!passed)
            {
                Assert.That(false, error);
            }
        }

        [Test]
        public void fullTestExample254()
        {
            var driver = APM_WebDataInterface.Global;

            SemaphoreSlim semaphore = new SemaphoreSlim(0);

            var assetId = "7eaa5311-16b9-460e-867b-c6fa92b22bb2";

            bool passed = false;
            string error = null;
            Task task = new Task(async () =>
            {
                await driver.Initialize();
                var locations = await driver.GetLocations("<EMAIL>");
                var locationIds = locations.Select(a => a.id).ToArray();
                var projects = await driver.GetProjectsAtLocation(locationIds, "<EMAIL>");

                var project = projects.FirstOrDefault(a => a.name.GetValue() == "Unit Test Project");
                var assets = await driver.GetAssetsForProjects(new[] { project.id }, "<EMAIL>");
                var workOrders = await driver.GetWorkOrders(new[] { project.id }, "<EMAIL>");


                var asset = assets.FirstOrDefault(a => a.id == assetId);
                var assetTasks = workOrders.Where(a => a.asset.id == asset.id).SelectMany(a => a.tasks).ToArray();
                var tasks = assetTasks.Where(a => a.taskType == "Asset Walkdown").ToArray();

                var task = tasks[0];

                var newPhotoData = File.ReadAllBytes("C:\\Users\\<USER>\\Pictures\\Saved Pictures\\Picture 1.jpg");


                task.clientCostCode.AddPhoto(new PendingMediaEntry()
                { ImageData = newPhotoData, MediaName = "TestPhoto", Extension = "jpg" });

                await task.SavePendingChanges("<EMAIL>");
                //await task.ResolvePhotos();


                Console.WriteLine(tasks.Length);


                semaphore.Release();
            });
            task.Start();

            semaphore.Wait();
            if (!passed)
            {
                Assert.That(false, error);
            }
        }
        
        IEnumerable<AttributeBase> getAllAttributes(DataModelItem item)
        {
            List<AttributeBase> attributes = new List<AttributeBase>();
            foreach (var child in item.GetChildren())
            {
                if (child is AttributeBase attr)
                {
                    attributes.Add(attr);
                }
                else
                {
                    attributes.AddRange(getAllAttributes(child));
                }
            }

            return attributes.ToArray();
        }

        [Test]
        public void fullTestExample444()
        {
            var driver = APM_WebDataInterface.Global;

            SemaphoreSlim semaphore = new SemaphoreSlim(0);

            bool passed = false;
            string error = null;
            Task task = new Task(async () =>
            {
                await driver.Initialize();
                var locations = await driver.GetLocations("<EMAIL>");
                var locationIds = locations.Select(a => a.id).ToArray();
                var projects = await driver.GetProjectsAtLocation(locationIds, "<EMAIL>");

                var project = projects.FirstOrDefault(a => a.name.GetValue().Equals("Brendan Project regression"));

                var assets = await driver.GetAssetsAtLocation(locationIds, "<EMAIL>");
                var asset = assets.FirstOrDefault(a => a.unit.GetValue() == "5151");

                var workOrders = await driver.GetWorkOrders(new[] { project.id }, "<EMAIL>");


                var allAttributes = getAllAttributes(asset);
                var photoAttributes = allAttributes.Where(a => a.Photos.Any()).ToArray();

                var assetWorkOrder = workOrders.Where(a => a.asset.id == asset.id).SelectMany(a => a.tasks);
                var walkdown = assetWorkOrder.FirstOrDefault(a => a.taskType == "Asset Walkdown");
                var workOrder = workOrders.FirstOrDefault(a => a.tasks.Contains(walkdown));

                var photoSection = (walkdown.getTaskData() as Section510_Asset_Walkdown_Details_F)
                    .sectionGeneralInformation.sectionPhotos;


                walkdown.ChangeStatus(APMTask.Statuses.Completed);
                await workOrder.SavePendingChanges("<EMAIL>");

                var asJson = Newtonsoft.Json.JsonConvert.SerializeObject(walkdown);
                var asJson2 = Newtonsoft.Json.JsonConvert.SerializeObject(asset);

                Console.WriteLine(photoAttributes.Length.ToString());
                passed = true;

                Thread.Sleep(15000);
                semaphore.Release();
            });
            task.Start();

            semaphore.Wait();
            if (!passed)
            {
                Assert.That(false, error);
            }
        }

        [Test]
        public void saveAssets()
        {
            var driver = APM_WebDataInterface.Global;

            SemaphoreSlim semaphore = new SemaphoreSlim(0);

            bool passed = false;
            string error = null;
            Task task = new Task(async () =>
            {
                await driver.Initialize();
                var locations = await driver.GetLocations("<EMAIL>");
                var locationIds = locations.Select(a => a.id).ToArray();

                var assets = await driver.GetAssetsAtLocation(locationIds, "<EMAIL>");

                foreach (var asset in assets)
                {
                    await asset.SavePendingChanges("<EMAIL>");
                }


                passed = true;

                semaphore.Release();
            });
            task.Start();

            semaphore.Wait();
            if (!passed)
            {
                Assert.That(false, error);
            }
        }

        [Test]
        public void fullTestExample2()
        {
            var driver = APM_WebDataInterface.Global;

            SemaphoreSlim semaphore = new SemaphoreSlim(0);

            bool passed = false;
            string error = null;
            Task task = new Task(async () =>
            {
                await driver.Initialize();
                var locations = await driver.GetLocations("<EMAIL>");
                var locationIds = locations.Select(a => a.id).ToArray();
                var projects = await driver.GetProjectsAtLocation(locationIds, "<EMAIL>");

                var project = projects.FirstOrDefault(a => a.name.GetValue() == "2022-06");

                var workOrders = await driver.GetWorkOrders(new[] { project.id }, "<EMAIL>");
                var workOrder = workOrders.FirstOrDefault(a => a.id == "e726c2f6-a6ce-405e-a821-9e84cf6e214a");

                // APMTaskGeneric<Section570_Asset_Walkdown_Details_F> walkdown = workOrder.tasks.FirstOrDefault(a => a.taskType == "Asset Walkdown") as APMTaskGeneric<Section570_Asset_Walkdown_Details_F>;
                APMTaskGeneric<Section570_Asset_Walkdown_Details_F> walkdown =
                    workOrder.tasks.FirstOrDefault(a => a.id == "71d47b4f-7004-4ce6-9b6f-99f2ac1157c9") as
                        APMTaskGeneric<Section570_Asset_Walkdown_Details_F>;
                var assetWalkdown = workOrder.asset.walkDown as Section570_Asset_Walkdown_Details_F;

                var value = new string(Guid.NewGuid().ToString().Take(6).ToArray());
                walkdown.taskData.sectionIdentification.attributeProduct_Handled.SetValue("Testing:" + value);

                await walkdown.SavePendingChanges("<EMAIL>");

                walkdown.ChangeStatus(APMTask.Statuses.Completed);

                await workOrder.SavePendingChanges("<EMAIL>");

                if (string.Equals(assetWalkdown.sectionIdentification.attributeProduct_Handled.GetValue(),
                        "Testing:" + value, StringComparison.InvariantCultureIgnoreCase))
                {
                    passed = true;
                }

                semaphore.Release();
            });
            task.Start();

            semaphore.Wait();
            if (!passed)
            {
                Assert.That(false, error);
            }
        }
        
        private async Task<APMTask> getWalkdown(string projectId)
        {
            Stopwatch stopwatch = new Stopwatch();
            var driver = APM_WebDataInterface.Global;
            stopwatch.Start();
            var assets = driver.GetAssetsForProjects(new[] { projectId }, "<EMAIL>");
            stopwatch.Stop();
            Console.WriteLine("Get Assets: " + (stopwatch.ElapsedMilliseconds / 1000.0));
            //var assets = await driver.GetAssetsForProjects(new[] {project.id});
            //var assets = await driver.GetAssets(new[] { "a0320381-c3ee-445e-a897-08b4bed226e1" });
            var asset = assets.Result.FirstOrDefault(a => a.unit.GetValue() == "some u unit 999");
            stopwatch.Restart();
            var workOrders = await driver.GetWorkOrders(new[] { projectId }, "<EMAIL>");
            stopwatch.Stop();
            Console.WriteLine("GetWorkOrders: " + (stopwatch.ElapsedMilliseconds / 1000.0));

            var workOrder = workOrders.FirstOrDefault(a => a.asset.id == asset.id);

            var walkdown = workOrder.tasks.FirstOrDefault(a => a.taskType == "Asset Walkdown");
            return walkdown;
        }

        [Test]
        public void testJsonSize()
        {
            var driver = APM_WebDataInterface.Global;
            SemaphoreSlim semaphore = new SemaphoreSlim(0);
            Stopwatch stopwatch = new Stopwatch();

            bool passed = false;
            string error = null;
            Task task = new Task(async () =>
            {
                await driver.Initialize();

                var locations = await driver.GetLocations("<EMAIL>");
                var projects =
                    await driver.GetProjectsAtLocation(locations.Select(a => a.id).ToArray(), "<EMAIL>");

                var project = projects.FirstOrDefault(a =>
                    a.name?.GetValue() != null && a.name.GetValue()
                        .Equals("Unit Test Project", StringComparison.InvariantCultureIgnoreCase));


                var workOrders = await driver.GetWorkOrders(new[] { "882af46b-0325-4846-9994-9aadee948f2d" },
                    "<EMAIL>");
                var workOrder = workOrders.FirstOrDefault(a => a.id == "d6de72da-2801-4392-b1db-d757be420af9");

                if (workOrder.asset.walkDown is Section510_Asset_Walkdown_Details_F walkdown1)
                {
                    var entries = walkdown1.sectionComponents.sectionHeads.GetEntries();
                    entries[0].attributeNumber.SetValue("100");
                    await workOrder.asset.SavePendingChanges("<EMAIL>");
                }

                var serialized1 = JsonConvert.SerializeObject(workOrder, Formatting.Indented);


                var walkdown = await getWalkdown(project.id);

                int transportSize = JsonConvert.SerializeObject(walkdown.workOrder, Formatting.Indented).Length;
                Console.WriteLine("WorkOrder Size No Photos: " + transportSize);

                transportSize = JsonConvert
                    .SerializeObject(WorkOrderListViewModel.Build(walkdown.workOrder, "<EMAIL>"),
                        Formatting.Indented).Length;
                Console.WriteLine("WorkOrder Item Size No Photos: " + transportSize);

                walkdown.workOrder.ExcludeHistoryFromJson();
                foreach (var task in walkdown.workOrder.tasks)
                    task.ExcludeHistoryFromJson();

                var transport = JsonConvert.SerializeObject(walkdown.workOrder, Formatting.Indented,
                    new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                transportSize = JsonConvert.SerializeObject(walkdown.workOrder, Formatting.Indented).Length;
                Console.WriteLine("WorkOrder Size No Photos, nor history: " + transportSize);

                transportSize = JsonConvert.SerializeObject(walkdown.workOrder, Formatting.Indented,
                    new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore }).Length;
                Console.WriteLine("WorkOrder Size Ignore Nulls " + transportSize);

                transportSize = JsonConvert.SerializeObject(walkdown, Formatting.Indented).Length;
                Console.WriteLine("Transport Size No Photos: " + transportSize);

                stopwatch.Start();
                await walkdown.ResolvePhotosWithResize(1000, 1000, 70);
                stopwatch.Stop();
                Console.WriteLine("ResolvePhotosWithResize: " + (stopwatch.ElapsedMilliseconds / 1000.0));

                transportSize = JsonConvert.SerializeObject(walkdown, Formatting.Indented).Length;
                Console.WriteLine("Transport Size: " + transportSize);


                walkdown = await getWalkdown(project.id);
                stopwatch.Restart();
                await walkdown.ResolvePhotosWithResize(1000, 1000, 70);
                stopwatch.Stop();
                Console.WriteLine("ResolvePhotosWithResize: " + (stopwatch.ElapsedMilliseconds / 1000.0));

                transportSize = JsonConvert.SerializeObject(walkdown, Formatting.Indented).Length;
                Console.WriteLine("Transport Size: " + transportSize);


                walkdown = await getWalkdown(project.id);


                transportSize = JsonConvert.SerializeObject(walkdown, Formatting.Indented).Length;
                Console.WriteLine("Transport Size No Photos: " + transportSize);

                stopwatch.Restart();
                await walkdown.ResolvePhotos();
                stopwatch.Stop();
                Console.WriteLine("ResolvePhotosWithResize: " + (stopwatch.ElapsedMilliseconds / 1000.0));

                transportSize = JsonConvert.SerializeObject(walkdown, Formatting.Indented).Length;
                Console.WriteLine("Transport Size: " + transportSize);


                passed = true;
                semaphore.Release();

                return;
            });
            task.Start();

            semaphore.Wait();
            if (!passed)
            {
                Assert.That(false, error);
            }
        }

        [Test]
        public async Task getLeakReports()
        {
            var driver = APM_WebDataInterface.Global;
            var passed = false;
            string error = null;
            await driver.Initialize();
            var leakReports = await driver.GetLeakReports("<EMAIL>");
        }

        [Test]
        public void testLeakReportJson()
        {
            RunTestWithFramework(async (project, driver) =>
            {
                var leakReports = await driver.GetLeakReports("<EMAIL>");

                var report = leakReports.FirstOrDefault(a => a.leakReportPhotos.GetEntries().Any());
                foreach (var photoGroup in report.leakReportPhotos.GetEntries())
                {
                    foreach (var media in photoGroup.photos.Photos)
                    {
                        await media.ResolveWithResize(325, 325, 95);
                    }
                }

                var photos = report.report.GetAllPhotos(shareMedia: false).Concat(
                    report.workDetail.GetAllPhotos(shareMedia: false));
                foreach (var photo in photos)
                {
                    await photo.MediaEntry.ResolveWithResize(325, 325, 95);
                }


                var package = new LeakReportPackage
                {
                    LeakReport = report,
                    Photos = photos.ToArray()
                };

                string fileName = "LeakReport";

                report.GetAllPhotos(shareMedia: false);

                string withNulls = JsonConvert.SerializeObject(package, Formatting.Indented);
                string withoutNulls = JsonConvert.SerializeObject(package, Formatting.Indented,
                    new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });


                System.IO.File.WriteAllText("C:\\Temp\\Json\\" + fileName + "_withNulls.json", withNulls);
                System.IO.File.WriteAllText("C:\\Temp\\Json\\" + fileName + "_withoutNulls.json", withoutNulls);
                return new TestResult() { Passed = true };
            });
        }
        
        [Test]
        public void buildReportJson()
        {
            string projectId = "b832eba8-bece-45da-93a2-1e216e228c96";
            string workOrderId = "c8d1ed7c-665c-4385-a37f-ae60ca5c2fbe";
            string taskId = "f55274b8-8490-4127-af6b-eca388cc573b";

            var driver = APM_WebDataInterface.Global;
            SemaphoreSlim semaphore = new SemaphoreSlim(0);

            bool passed = false;
            string error = null;
            Task task = new Task(async () =>
            {
                await driver.Initialize();

                var workOrders = await driver.GetWorkOrders(new[] { projectId }, "<EMAIL>");

                var locations = await driver.GetLocations("<EMAIL>");
                var workOrder = workOrders.FirstOrDefault(a => a.id == workOrderId);
                var task = workOrder.tasks.FirstOrDefault(a => a.id == taskId);
                var asset = workOrder.asset;
                var project = driver.GetProjects("<EMAIL>").FirstOrDefault(a => a.id == projectId);

                // for (int width = 200; width < 600; width += 25)
                {
                    var photos = task.GetAllPhotos(shareMedia: false).Concat(asset.GetAllPhotos(shareMedia: false))
                        .Concat(workOrder.GetAllPhotos(shareMedia: false)).ToArray();
                    var resolveTasks = photos.Select(a => a.MediaEntry.ResolveWithResize(325, 325, 90, false))
                        .ToArray();
                    foreach (var resolve in resolveTasks)
                    {
                        await resolve;
                    }

                    var reportPackage = new SingleTaskReportPackage
                    {
                        Asset = asset,
                        Project = project,
                        Task = task,
                        Location = locations.FirstOrDefault(a => a.id == project.locationId),
                        Photos = photos.ToArray(),
                        WorkOrder = workOrder
                    };

                    string withNulls = JsonConvert.SerializeObject(reportPackage, Formatting.Indented);
                    string withoutNulls = JsonConvert.SerializeObject(reportPackage, Formatting.Indented,
                        new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });

                    string fileName = "Rotated_NoPadding";

                    System.IO.File.WriteAllText("C:\\Temp\\Json\\" + fileName + ".json", withoutNulls);
                    Console.WriteLine(fileName);
                }


                passed = true;

                semaphore.Release();
            });
            task.Start();

            semaphore.Wait();
            if (!passed)
            {
                Assert.That(false, error);
            }
        }

        [Test]
        public void findInfoFromAPMNumber()
        {
            string projectId = "4e27bd6e-ce87-4af9-ac1c-7f5ba0f49229";
            string workOrderId = "08d5a450-040c-4579-a395-886dd90aa6b5";
            string taskId = "f158660a-6f12-4e55-8a4f-1a279e4a470f";

            var driver = APM_WebDataInterface.Global;
            SemaphoreSlim semaphore = new SemaphoreSlim(0);

            bool passed = false;
            string error = null;
            Task task = new Task(async () =>
            {
                await driver.Initialize();

                var projects = driver.GetProjects("<EMAIL>");
                var workOrders = await driver.GetWorkOrders(
                    projects.Where(a => a.name.GetValue() != null && a.name.GetValue().Equals("2022-08"))
                        .Select(a => a.id).ToArray(), "<EMAIL>");

                var locations = await driver.GetLocations("<EMAIL>");

                var workOrder = workOrders.FirstOrDefault(a =>
                    a.tasks.Any(a => a.taskAPMNumber.GetValue().Equals("APM-TSK-2994")));
                var task = workOrder.tasks.FirstOrDefault(a => a.taskAPMNumber.GetValue().Equals("APM-TSK-2994"));

                var asset = workOrder.asset;
                var project = driver.GetProjects("<EMAIL>").FirstOrDefault(a => a.id == projectId);


                var photos = task.GetAllPhotos(shareMedia: false)
                    .Concat(asset.GetAllPhotos(shareMedia: false))
                    .Concat(workOrder.GetAllPhotos(shareMedia: false))
                    .ToArray();

                var resolveTasks = photos.Select(a => a.MediaEntry.ResolveWithResize(325, 325, 95)).ToArray();
                foreach (var resolve in resolveTasks)
                {
                    await resolve;
                }

                var reportPackage = new SingleTaskReportPackage
                {
                    Asset = asset,
                    Project = project,
                    Task = task,
                    Location = locations.FirstOrDefault(a => a.id == project.locationId),
                    Photos = photos.ToArray(),
                    WorkOrder = workOrder
                };

                string withNulls = JsonConvert.SerializeObject(reportPackage, Formatting.Indented);
                string withoutNulls = JsonConvert.SerializeObject(reportPackage, Formatting.Indented,
                    new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });

                string fileName = "Broken Photos";

                System.IO.File.WriteAllText("C:\\Temp\\Json\\" + fileName + "_withNulls.json", withNulls);
                System.IO.File.WriteAllText("C:\\Temp\\Json\\" + fileName + "_withoutNulls.json", withoutNulls);
                Console.WriteLine(fileName);


                passed = true;

                semaphore.Release();
            });
            task.Start();

            semaphore.Wait();
            if (!passed)
            {
                Assert.That(false, error);
            }
        }
        
        [Test]
        public void createProjectTest2()
        {
            var driver = APM_WebDataInterface.Global;
            SemaphoreSlim semaphore = new SemaphoreSlim(0);

            bool passed = false;
            string error = null;
            Task task = new Task(async () =>
            {
                await driver.Initialize(APM_WebDataInterface.Databases.testing);


                passed = true;

                semaphore.Release();
            });
            task.Start();

            semaphore.Wait();
            if (!passed)
            {
                Assert.That(false, error);
            }
        }
        
        [Test]
        public void createProjectTest()
        {
            var driver = APM_WebDataInterface.Global;
            SemaphoreSlim semaphore = new SemaphoreSlim(0);

            bool passed = false;
            string error = null;
            Task task = new Task(async () =>
            {
                await driver.Initialize();

                var location = new Location();
                await location.SavePendingChanges("<EMAIL>");
                var project = new Project(location.id);
                project.name.SetValue("Chevron Test");
                await project.SavePendingChanges("<EMAIL>");
                passed = true;

                semaphore.Release();
            });
            task.Start();

            semaphore.Wait();
            if (!passed)
            {
                Assert.That(false, error);
            }
        }

        [Test]
        public void fullTestExample4()
        {
            var driver = APM_WebDataInterface.Global;
            SemaphoreSlim semaphore = new SemaphoreSlim(0);

            bool passed = false;
            string error = null;
            Task task = new Task(async () =>
            {
                await driver.Initialize();

                string workOrderId = "7b0a8a1e-5a95-463d-8556-3f64f5244048";
                var projectId = "882af46b-0325-4846-9994-9aadee948f2d";


                var workOrders = await driver.GetWorkOrders(new[] { projectId }, "<EMAIL>");
                var workOrder = workOrders.FirstOrDefault(a => a.id == workOrderId);
                var questions = workOrder.tasks
                    .SelectMany(a => a.getTaskData().GetChildren().SelectMany(a => a.GetChildren()))
                    .OfType<AttributeBase>();
                var photoQuestion = questions.FirstOrDefault(a => a.Photos.Length > 0);
                var photo = photoQuestion.Photos[0];
                photo.Description.SetValue("New Description");

                var task = photo.Parent.Parent as DataModelRoot;
                await task.SavePendingChanges("<EMAIL>");


                passed = true;

                semaphore.Release();
            });
            task.Start();

            semaphore.Wait();
            if (!passed)
            {
                Assert.That(false, error);
            }
        }
        
        private string getGuidShort()
        {
            return new string(Guid.NewGuid().ToString().Take(5).ToArray());
        }

        [Test]
        public void updateProjectMany()
        {
            var driver = APM_WebDataInterface.Global;
            SemaphoreSlim semaphore = new SemaphoreSlim(0);

            int j = 0;
            bool passed = false;
            string error = null;
            Task task = new Task(async () =>
            {
                await driver.Initialize();
                //var driverA = new APM_WebDataInterface();
                //var driverB = new APM_WebDataInterface();


                //await driverA.Initialize();
                //await driverB.Initialize();

                //for (int i = 0; i < 80; i++) {
                //  var projectId = "Project:" + j;
                //  var db = driverA.FirestoreDB;

                //  SemaphoreSlim slim = new SemaphoreSlim(0);
                //  db.Collection("tasks").WhereEqualTo("ProjectId", projectId).Listen((snapshot) => {
                //    slim.Release();
                //  });
                //  slim.Wait();
                //}

                //for (int i = 0; i < 80; i++) {
                //  var projectId = "Project:" + j;
                //  var db = driverB.FirestoreDB;

                //  SemaphoreSlim slim = new SemaphoreSlim(0);
                //  db.Collection("tasks").WhereEqualTo("ProjectId", projectId).Listen((snapshot) => {
                //    slim.Release();
                //  });
                //  slim.Wait();
                //}


                List<double> timesInSeconds = new List<double>();
                for (int i = 0; i < 100; i++)
                {
                    Stopwatch stopwatch = new Stopwatch();
                    stopwatch.Start();
                    var projects = driver.GetProjects("<EMAIL>");
                    var project = projects.FirstOrDefault(a =>
                        a.name.GetValue().Equals("brian's project edittest2",
                            StringComparison.InvariantCultureIgnoreCase));
                    var contacts = project.clientDetails.contacts;
                    var contact = contacts.GetEntries()
                        .FirstOrDefault(a => a.GetId() == "bd0268bd-1a20-4c45-aadb-9d89c4933430");
                    var value = "New Name From Unit Test:" + getGuidShort();
                    contact.name.SetValue(value);

                    var locations = await driver.GetLocations("<EMAIL>");


                    var projectIds = projects.Select(a => a.id).ToArray().Reverse().ToArray();
                    for (j = 0; j < projectIds.Length; j++)
                    {
                        if (j == 39)
                        {
                            Console.Write("A");
                        }

                        var workOrders = await driver.GetWorkOrders(new[] { projectIds[j] }, "<EMAIL>");
                    }


                    var assets =
                        await driver.GetAssetsAtLocation(locations.Select(a => a.id).ToArray(), "<EMAIL>");
                    //foreach (var workOrder in workOrders) {
                    //  workOrder.ResolvePhotos();
                    //}

                    await project.SavePendingChanges("<EMAIL>");

                    var newActivity = project.activities.AddNewItem();
                    newActivity.date.SetValue(DateTime.Now);
                    newActivity.user.SetValue("<EMAIL>");


                    var asset = new Asset("Vessel", project.locationId);
                    project.assetIds.AddValue(asset.id);
                    asset.area.SetValue("Some Area: ");

                    await asset.SavePendingChanges("<EMAIL>");

                    await project.SavePendingChanges("<EMAIL>");
                    stopwatch.Stop();

                    timesInSeconds.Add(stopwatch.ElapsedMilliseconds / 1000.0);
                    Console.WriteLine("Elapsed Time: " + (stopwatch.ElapsedMilliseconds / 1000.0) + " seconds");
                }


                passed = true;

                semaphore.Release();
            });
            task.Start();

            semaphore.Wait();
            if (!passed)
            {
                Assert.That(false, error);
            }
        }
        
        [Test]
        public void updateProject1()
        {
            var driver = APM_WebDataInterface.Global;
            SemaphoreSlim semaphore = new SemaphoreSlim(0);

            bool passed = false;
            string error = null;
            Task task = new Task(async () =>
            {
                await driver.Initialize();

                var projects = driver.GetProjects("<EMAIL>");

                var project = projects.FirstOrDefault(a =>
                    a.name.GetValue().Equals("brian's project edittest2", StringComparison.InvariantCultureIgnoreCase));
                var contacts = project.clientDetails.contacts;

                Stopwatch stopwatch = new Stopwatch();
                stopwatch.Start();

                var newContact = contacts.AddNewItem();
                newContact.name.SetValue("Newly added contact");

                await project.SavePendingChanges("<EMAIL>");


                stopwatch.Stop();


                Console.WriteLine("Elapsed Time: " + (stopwatch.ElapsedMilliseconds / 1000.0) + " seconds");

                passed = true;

                semaphore.Release();
            });
            task.Start();

            semaphore.Wait();
            if (!passed)
            {
                Assert.That(false, error);
            }
        }
        
        [Test]
        public void fullTestExample3()
        {
            var driver = APM_WebDataInterface.Global;
            SemaphoreSlim semaphore = new SemaphoreSlim(0);

            bool passed = false;
            string error = null;
            Task task = new Task(async () =>
            {
                await driver.Initialize();
                var dbCounts = DatabaseCounts.Global;
                Console.Write(dbCounts.CurrentMaxProjectId);

                var locations = await driver.GetLocations("<EMAIL>");
                var locationIds = locations.Select(a => a.id).ToArray();
                var projects = await driver.GetProjectsAtLocation(locationIds, "<EMAIL>");

                //var newproject = new Project(locationIds[0]);
                // await newproject.SavePendingChanges("<EMAIL>");
                var assets =
                    await driver.GetAssetsForProjects(projects.Select(a => a.id).ToArray(), "<EMAIL>");


                var workOrders = await driver.GetWorkOrders(projects.Select(a => a.id).ToArray(), "<EMAIL>");

                var workOrder = workOrders.FirstOrDefault(a => a.tasks.Count > 0);
                workOrder.tasks.FirstOrDefault().assignedUsers = new[] { "Johnny" };

                await workOrder.SavePendingChanges("<EMAIL>");


                //var newTask = workOrder.AddNewTask(WorkOrder.TaskTypes.AssetWalkdown);
                //newTask.clientWorkOrderDescription.SetValue("Some Value");
                //newTask.clientWorkOrderNumber.SetValue("Some Value");

                //await workOrder.SavePendingChanges("<EMAIL>");


                //var json = JsonConvert.SerializeObject(workOrder);


                //var workOrders2 = await driver.GetWorkOrders(new[] {"3a8c19a3-d6dd-472c-b6a8-60f8b65525b5"});
                // var workOrder2 = workOrders2.FirstOrDefault(a => a.id == "e5788196-eafd-41ef-beae-44c0b3bc872f");
                //var task = workOrder2.tasks.CurrentEntries.FirstOrDefault();
                //task.assignedUsers


                passed = true;
                //var testVessel = assets.FirstOrDefault(a => a.unit.GetValue() == "Test Vessel 1");

                //string value = "Value from unit test: " + new String(Guid.NewGuid().ToString().Take(6).ToArray());
                //testVessel.area.SetValue(value);
                //await testVessel.SavePendingChanges("SomeUserEmail.com");

                //var assetsAfterUpdate = await driver.GetAssets(new[] {testVessel.id});
                //var assetAfterUpdate = assetsAfterUpdate[0];
                //if (assetAfterUpdate.area.GetValue() != value) {
                //  passed = false;
                //  error = "value was not correct after update";
                //}
                //else {
                //  passed = true;
                //}

                semaphore.Release();
            });
            task.Start();

            semaphore.Wait();
            if (!passed)
            {
                Assert.That(false, error);
            }
        }

        [Test]
        public void fullTestExample()
        {
            var driver = APM_WebDataInterface.Global;
            driver.Initialize();
            SemaphoreSlim semaphore = new SemaphoreSlim(0);

            bool passed = false;
            string error = null;
            Task task = new Task(async () =>
            {
                var locations = await driver.GetLocations("<EMAIL>");
                var locationIds = locations.Select(a => a.id).ToArray();
                var projects = await driver.GetProjectsAtLocation(locationIds, "<EMAIL>");

                var project = projects.FirstOrDefault(a =>
                    a.name.GetValue().Equals("Unit Test Project", StringComparison.InvariantCultureIgnoreCase));

                var assets = await driver.GetAssetsForProjects(new[] { project.id }, "<EMAIL>");
                var testVessel = assets.FirstOrDefault(a => a.unit.GetValue() == "Test Vessel 1");

                string value = "Value from unit test: " + new string(Guid.NewGuid().ToString().Take(6).ToArray());
                testVessel.area.SetValue(value);
                await testVessel.SavePendingChanges("SomeUserEmail.com");

                var assetsAfterUpdate = await driver.GetAssets(new[] { testVessel.id }, "<EMAIL>");
                var assetAfterUpdate = assetsAfterUpdate[0];
                if (assetAfterUpdate.area.GetValue() != value)
                {
                    passed = false;
                    error = "value was not correct after update";
                }
                else
                {
                    passed = true;
                }

                semaphore.Release();
            });
            task.Start();

            semaphore.Wait();
            if (!passed)
            {
                Assert.That(false, error);
            }
        }
        
        // Don't want this run if someone ends up running all tests
        // [Test]
        public async Task addBusinessUnitToRootItems()
        {
            var driver = APM_WebDataInterface.Global;
            var passed = false;
            string error = null;
            await driver.Initialize();
            var clients = driver.GetClients();
            var businessUnits = driver.GetBusinessUnits();
            var locations = await driver.GetLocations("<EMAIL>");
            var projects = driver.GetProjects("<EMAIL>");

            var randomNumber = new Random();
            var businessUnitCount = businessUnits.Length;
            var leakReports = await driver.GetLeakReports("<EMAIL>");
            foreach (var location in locations)
            {
                var businessUnitIdToAssign = businessUnits[randomNumber.Next(businessUnitCount)].id;
                // var businessUnitIdToAssign = location.businessUnitId.CurrentValue;
                if (businessUnitIdToAssign == null)
                    throw new NullReferenceException();
                location.businessUnitId.SetValue(businessUnitIdToAssign);
                await location.SavePendingChanges("<EMAIL>");
                foreach (var project in projects.Where(project => project.locationId == location.id))
                {
                    project.businessUnitId.SetValue(businessUnitIdToAssign);
                    await project.SavePendingChanges("<EMAIL>");

                    var assets = await driver.GetAssetsForProjects(new[] { project.id }, "<EMAIL>");
                    var workOrders = await driver.GetWorkOrders(new[] { project.id }, "<EMAIL>");
                    foreach (var asset in assets)
                    {
                        asset.businessUnitId.SetValue(businessUnitIdToAssign);
                        await asset.SavePendingChanges("<EMAIL>");
                    }

                    foreach (var workOrder in workOrders)
                    {
                        foreach (var task in workOrder.tasks)
                        {
                            task.businessUnitId.SetValue(businessUnitIdToAssign);
                            await task.SavePendingChanges("<EMAIL>");
                        }

                        workOrder.businessUnitId.SetValue(businessUnitIdToAssign);
                        await workOrder.SavePendingChanges("migration@test");
                    }
                }
            }

            foreach (var leakReport in leakReports)
            {
                var businessUnitIdToAssign = businessUnits[randomNumber.Next(businessUnitCount)].id;
                leakReport.businessUnitId.SetValue(businessUnitIdToAssign);
                await leakReport.SavePendingChanges("<EMAIL>");
            }

            Console.Write("Fin");
        }

        // Don't want this run if someone ends up running all tests
        //[Test]
        public async Task saveAssetsToUpdateAssetCards()
        {
            var driver = APM_WebDataInterface.Global;
            var passed = false;
            string error = null;
            await driver.Initialize();
            var locations = await driver.GetLocations("<EMAIL>");
            foreach (var location in locations)
            {
                var assets =
                    await driver.GetAssetsAtLocation(new[] { location.id }, "<EMAIL>");

                foreach (var asset in assets)
                {
                    await asset.SavePendingChanges("<EMAIL>");
                }
            }


            /*
            var driver = APM_WebDataInterface.Global;
            var passed = false;
            string error = null;
            await driver.Initialize();
            var projects = driver.GetProjects("<EMAIL>");
            var assets = await driver.GetAssetsForProjects(projects.Select(proj => proj.id).ToArray(), "<EMAIL>");

            foreach (var asset in assets)
            {
                await asset.SavePendingChanges("<EMAIL>");
            }

            */


            Console.Write("Fin");
        }
        
        string getAssetId(Asset asset)
        {
            if (asset.walkDown is Section510_Asset_Walkdown_Details_F walkDown510)
            {
                return walkDown510.sectionIdentification.attributeNumber_or_ID.GetValue();
            }

            if (asset.walkDown is Section570_Asset_Walkdown_Details_F walkDown570)
            {
                return walkDown570.sectionIdentification.attributeNumber_or_Circuit_ID.GetValue();
            }

            if (asset.walkDown is Section653_Asset_Walkdown_Details_F walkDown653)
            {
                return walkDown653.sectionIdentification.attributeNumber_or_ID.GetValue();
            }

            return null;
        }

        void setAssetFields(Asset asset, string assetId, string assetName, string assetSubCategory)
        {
            if (asset.walkDown is Section510_Asset_Walkdown_Details_F walkDown510)
            {
                walkDown510.sectionIdentification.attributeNumber_or_ID.SetValue(assetId);
                walkDown510.sectionIdentification.attributeName.SetValue(assetName);
                walkDown510.sectionIdentification.attributeAsset_Type.SetValue(assetSubCategory);
            }

            if (asset.walkDown is Section570_Asset_Walkdown_Details_F walkDown570)
            {
                walkDown570.sectionIdentification.attributeNumber_or_Circuit_ID.SetValue(assetId);
                walkDown570.sectionIdentification.attributeName.SetValue(assetName);
                walkDown570.sectionIdentification.attributeAsset_Type.SetValue(assetSubCategory);
            }

            if (asset.walkDown is Section653_Asset_Walkdown_Details_F walkDown653)
            {
                walkDown653.sectionIdentification.attributeNumber_or_ID.SetValue(assetId);
                walkDown653.sectionIdentification.attributeName.SetValue(assetName);
                walkDown653.sectionIdentification.attributeAsset_Type.SetValue(assetSubCategory);
            }
        }

        // [Test]
        public void importDataSet()
        {
            var driver = APM_WebDataInterface.Global;
            driver.Initialize();
            SemaphoreSlim semaphore = new SemaphoreSlim(0);

            bool passed = false;
            string error = null;
            Task task = new Task(async () =>
            {
                // var locations = await driver.GetLocations();
                // var location = new[] {locations[0].id};
                //String locationId = locations[0].id;
                //var projects = await driver.GetProjectsAtLocation(location);

                var location = new Location();
                await location.SavePendingChanges("ImportedFromExcel");

                string locationId = location.id;

                var project = new Project(location.id);

                project.name.SetValue("SJVBU_MI_Q4_2021");


                await project.SavePendingChanges("ImportedFromExcel");
                //var project = projects.FirstOrDefault(a => a.name.GetValue().Equals("SJVBU_MI_Q4_2021", StringComparison.InvariantCultureIgnoreCase));


                List<Asset> assets = new List<Asset>();
                List<APMTask> tasks = new List<APMTask>();
                Dictionary<Asset, List<WorkOrder>> workOrders = new Dictionary<Asset, List<WorkOrder>>();

                System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);

                using (var stream = File.Open("C:\\Users\\<USER>\\Documents\\Chevron_APM_Asset_List_draft_002.xlsx",
                           FileMode.Open, FileAccess.Read))
                {
                    using (var reader = ExcelReaderFactory.CreateReader(stream))
                    {
                        var result = reader.AsDataSet();

                        var table = result.Tables["SJVBU_MI_Q4_2021"];

                        foreach (System.Data.DataRow row in table.Rows)
                        {
                            var facility = row[0].ToString();
                            if (facility == "Facility" || facility == "" || facility == null)
                                continue;

                            var assetCategoryFromSpreadSheet = row[1].ToString();
                            var assetsubType = row[2].ToString();
                            var taskType = row[3].ToString();
                            var assetId = row[4].ToString();
                            var assetName = row[5].ToString();
                            var pmDue = row[6].ToString();
                            var workOrderId = row[7].ToString();
                            var workOrderDesc = row[8].ToString();
                            var costCode = row[9].ToString();

                            if (!(taskType == "Internal" || taskType == "External" || taskType == "" ||
                                  taskType == null))
                                continue;

                            string assetCategory = null;
                            if (assetCategoryFromSpreadSheet.Trim() == "Pressure Vessels")
                            {
                                assetCategory = "Vessel";
                            }
                            else if (assetCategoryFromSpreadSheet.Trim() == "Tanks")
                            {
                                assetCategory = "Tank";
                            }
                            else if (assetCategoryFromSpreadSheet.Trim() == "Piping")
                            {
                                assetCategory = "Piping";
                            }

                            if (assetCategory == null)
                                continue;

                            bool firstSeeingAsset = false;
                            var asset = assets.FirstOrDefault(a =>
                                string.Equals(getAssetId(a), assetId, StringComparison.InvariantCultureIgnoreCase));
                            if (asset == null)
                            {
                                firstSeeingAsset = true;
                                asset = new Asset(assetCategory, locationId);
                                setAssetFields(asset, assetId, assetName, assetsubType);
                                assets.Add(asset);

                                await asset.SavePendingChanges("ImportedFromExcel");
                            }

                            if (!workOrders.ContainsKey(asset))
                            {
                                workOrders[asset] = new List<WorkOrder>();
                            }
                            //var workOrder = workOrders[asset].FirstOrDefault(a => a.clientWorkOrderNumber.GetValue() == workOrderId);
                            //if (workOrder == null) {
                            //  workOrder = new WorkOrder(asset, project.id);
                            //  workOrder.facilityName.SetValue(facility);
                            //  workOrder.clientWorkOrderNumber.SetValue(workOrderId);
                            //  workOrder.clientCostCode.SetValue(costCode);
                            //  try {
                            //    workOrder.dueDate.SetValue(DateTime.Parse(pmDue));
                            //  }
                            //  catch (System.Exception ex) {
                            //    Console.Write("Failed to parse datetime: " + pmDue);
                            //    Console.Write(ex);
                            //  }
                            //  workOrder.clientWorkOrderDescription.SetValue(workOrderDesc);

                            //  workOrders[asset].Add(workOrder);
                            //}

                            //if (firstSeeingAsset) {
                            //  tasks.Add(workOrder.AddNewTask(WorkOrder.TaskTypes.AssetWalkdown));
                            //}

                            //if (taskType == "Internal") {
                            //  tasks.Add(workOrder.AddNewTask(WorkOrder.TaskTypes.InternalVisual));
                            //}

                            //if (taskType == "External") {
                            //  tasks.Add(workOrder.AddNewTask(WorkOrder.TaskTypes.ExternalVisual));
                            //}
                        }
                    }
                }


                foreach (var asset in assets)
                {
                    project.assetIds.AddValue(asset.id);
                }

                await project.SavePendingChanges("ImportedFromExcel");

                foreach (var item in workOrders.SelectMany(a => a.Value))
                {
                    await item.SavePendingChanges("ImportedFromExcel");
                }


                semaphore.Release();
            });
            task.Start();

            semaphore.Wait();
            if (!passed)
            {
                Assert.That(false, error);
            }
        }
    }
}


/*
 =============================================
 ==========    API DOCUMENTATION    ==========
 =============================================

Root level classes:
assets
locations
projects
users
workorders

 

=============================================
============     PHOTOS            ==========
=============================================


====== Add New Photo ========================
  var photoPath = "C:\\Temp\\photo.png";
  var photoBytes = System.IO.File.ReadAllBytes(photoPath);
  asset.assetPPE.sectionGeneralSiteConditions.attributeAre_there_any_on_site_leaks.AddPhoto(new PendingMediaEntry {
    MediaName = Guid.NewGuid().ToString(),
    Extension = "png", 
    ImageData =photoBytes
  });



====== Resolve photos ========================
  await project.ResolvePhotos();  // needs to be called on root level,  cannot be called on sub sections 
  var photoData = project.accountingDetails.workOrderNumber.Photos[0].ImageData;



=============================================
============     TASKS          =============
=============================================


==== Creation ===
var workOrders = await driver.GetWorkOrders(new[] {project.id});
var workOrder = workOrders.FirstOrDefault(a => a.asset.id == asset.id);
var newWalkdown = workOrder.AddNewTask(WorkOrder.TaskTypes.AssetWalkdown);
await workOrder.SavePendingChanges("<EMAIL>");



=== Changing task state ===
var workOrders = await driver.GetWorkOrders(new[] {project.id});
var workOrder = workOrders.FirstOrDefault(a => a.asset.id == asset.id);
workOrder.tasks.CurrentEntries[0].ChangeStatus(APMTask.Statuses.Completed);
await workOrder.SavePendingChanges("<EMAIL>");



================================================
==========   COLLECTIONS  ======================
================================================
var assets = await driver.GetAssetsForProjects(new[] {project.id});
var testVessel = assets.FirstOrDefault(a => a.unit.GetValue() == "Test Vessel 1");
var newHead = (testVessel.walkDown as Section510_Asset_Walkdown_Details_F).sectionComponents.sectionHeads.AddNewItem();
// Set some values on head
await testVessel.SavePendingChanges("<EMAIL>");




================================================
           Type Information
================================================

Asset.walkdown will be a walkdown type
the type of walkdown depends on the type of asset:
a vessel's walkdown will be of type: Section510_Asset_Walkdown_Details_F
a pipe's walkdown will be of type: Section570_Asset_Walkdown_Details_F
a tank's walkdown will be of type: Section653_Asset_Walkdown_Details_F

For a task:
task's inspection can be found by: 
task.getTaskData();

The type of the task data depends on task type and asset type:

the chart that determines the type is:  so the Asset Walkdown for a vessel will be the type: Section510_Asset_Walkdown_Details_F

      if (taskType.Equals("Asset Walkdown", StringComparison.InvariantCultureIgnoreCase)) {
        switch (asset.assetCategory){
          case "Vessel":
            return new APMTaskGeneric<Section510_Asset_Walkdown_Details_F>(workOrder, parent, id, "Asset Walkdown", (parent) => new Section510_Asset_Walkdown_Details_F(parent));
          case "Piping":
            return new APMTaskGeneric<Section570_Asset_Walkdown_Details_F>(workOrder, parent, id, "Asset Walkdown", (parent) => new Section570_Asset_Walkdown_Details_F(parent));
          case "Tank":
            return new APMTaskGeneric<Section653_Asset_Walkdown_Details_F>(workOrder, parent, id, "Asset Walkdown", (parent) => new Section653_Asset_Walkdown_Details_F(parent));
        }      
      }
      else if (taskType.Equals("External Visual", StringComparison.InvariantCultureIgnoreCase)) {
        switch (asset.assetCategory){
          case "Vessel":
            return new APMTaskGeneric<Section510_Ext_PV_ALL_F>(workOrder, parent, id, "External Visual", (parent) => new Section510_Ext_PV_ALL_F(parent));
          case "Piping":
            return new APMTaskGeneric<Section570_Ext_Pipe_F>(workOrder, parent, id, "External Visual", (parent) => new Section570_Ext_Pipe_F(parent));
          case "Tank":
            throw new NotImplementedException("This is not implemented for MVP,  tanks only supports walkdown");
        }
      }
      else if (taskType.Equals("Internal Visual", StringComparison.InvariantCultureIgnoreCase)) {
        switch (asset.assetCategory){
          case "Vessel":
            return new APMTaskGeneric<Section510_Int_PV_ALL_F>(workOrder, parent, id, "Internal Visual", (parent) => new Section510_Int_PV_ALL_F(parent));
          case "Piping":
            throw new NotImplementedException("This is not implemented for MVP,  piping only supports walkdown and external visuals");
          case "Tank":
            throw new NotImplementedException("This is not implemented for MVP,  tanks only supports walkdown");
        }












After you call savependingchanges,  any new changes for that attribute the changelog's last value's username should be the email you provided in savependingchange:
(a.walkDown as Section510_Asset_Walkdown_Details_F).sectionIdentification.attributeName.ValueChangeLog.entries.Last().UserName
 *
 */