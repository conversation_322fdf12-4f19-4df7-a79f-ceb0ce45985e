//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Ext_PV_ALL_F
{
  public class Section510EXT_PVCKLSTSEC009 : DataModelItem {

    public override String DisplayName { 
      get {
        return "NOZZLES";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC009Q001;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC009Q002;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC009Q003;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC009Q004;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC009Q005;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC009Q006;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC009Q007;
    public PredefinedValueAttribute attributeDo_the_gaskets_show_signs_of_damage_or_leaking;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC009Q009;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC009Q010;
    public PredefinedValueAttribute attributeAre_nozzles_in_acceptable_condition_for_continued_service;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510EXT_PVCKLSTSEC009";

    public Section510EXT_PVCKLSTSEC009(DataModelItem parent) : base(parent)
    {
            
        attribute510EXT_PVCKLSTSEC009Q001 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Were indications of leaks or repair clamps noted at piping attachments: (Discoloration, wet surfaces, odors, etc.)", databaseName: "510_EXT-PV_CKLST_SEC009_Q001"); 
     
        attribute510EXT_PVCKLSTSEC009Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Were flange fasteners fully engaged: (Any fastener failing to do so is considered acceptably engaged if the lack of complete engagement is not more than one thread)", databaseName: "510_EXT-PV_CKLST_SEC009_Q002"); 
     
        attribute510EXT_PVCKLSTSEC009Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are the flange fasteners utilized in alignment with Client specification:", databaseName: "510_EXT-PV_CKLST_SEC009_Q003"); 
     
        attribute510EXT_PVCKLSTSEC009Q004 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are the flange gaskets utilized in alignment with Client specification:", databaseName: "510_EXT-PV_CKLST_SEC009_Q004"); 
     
        attribute510EXT_PVCKLSTSEC009Q005 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the Nozzle protective coating in acceptable condition for continued service:", databaseName: "510_EXT-PV_CKLST_SEC009_Q005"); 
     
        attribute510EXT_PVCKLSTSEC009Q006 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are nozzle re-pads and weep holes in acceptable condition for continued service:", databaseName: "510_EXT-PV_CKLST_SEC009_Q006"); 
     
        attribute510EXT_PVCKLSTSEC009Q007 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are nozzles and adjacent shell areas in acceptable condition for continued service:", databaseName: "510_EXT-PV_CKLST_SEC009_Q007"); 
     
        attributeDo_the_gaskets_show_signs_of_damage_or_leaking = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Do the gaskets show signs of damage or leaking:", databaseName: "510_EXT-PV_CKLST_SEC009_Q008"); 
     
        attribute510EXT_PVCKLSTSEC009Q009 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Cracking", null),
          new PredefinedValueOption("Yes: Distortion", null),
          new PredefinedValueOption("Yes: Mechanical Damage", null)
        }, false, this, "Was any mechanical damage, distortion, or cracking noted on the nozzle surfaces:  (Possibly caused by settling)", databaseName: "510_EXT-PV_CKLST_SEC009_Q009"); 
     
        attribute510EXT_PVCKLSTSEC009Q010 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Pitting", null)
        }, false, this, "Were any corrosion cells or pitting noted on the nozzle surfaces or fasteners:", databaseName: "510_EXT-PV_CKLST_SEC009_Q010"); 
     
        attributeAre_nozzles_in_acceptable_condition_for_continued_service = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are nozzles in acceptable condition for continued service:", databaseName: "510_EXT-PV_CKLST_SEC009_Q011"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute510EXT_PVCKLSTSEC009Q001,
           attribute510EXT_PVCKLSTSEC009Q002,
           attribute510EXT_PVCKLSTSEC009Q003,
           attribute510EXT_PVCKLSTSEC009Q004,
           attribute510EXT_PVCKLSTSEC009Q005,
           attribute510EXT_PVCKLSTSEC009Q006,
           attribute510EXT_PVCKLSTSEC009Q007,
           attributeDo_the_gaskets_show_signs_of_damage_or_leaking,
           attribute510EXT_PVCKLSTSEC009Q009,
           attribute510EXT_PVCKLSTSEC009Q010,
           attributeAre_nozzles_in_acceptable_condition_for_continued_service,
        };
    }
  }
}
