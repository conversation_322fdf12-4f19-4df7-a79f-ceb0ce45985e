//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Ext_PV_ALL_F
{
  public class Section510EXT_PVCKLSTSEC013 : DataModelItem {

    public override String DisplayName { 
      get {
        return "SHELL AND HEADS";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q001;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q002;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q003;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q004;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q005;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q006;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q007;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q008;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q009;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q010;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q011;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q012;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q013;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q014;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q015;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q016;
    public PredefinedValueAttribute attributeIs_the_head_to_shell_attachment_achieved_via_welding;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q018;
    public PredefinedValueAttribute attributeIs_the_head_to_shell_attachment_achieved_via_bolting;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q020;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q021;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q022;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q023;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q024;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q025;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510EXT_PVCKLSTSEC013";

    public Section510EXT_PVCKLSTSEC013(DataModelItem parent) : base(parent)
    {
            
        attribute510EXT_PVCKLSTSEC013Q001 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Erosion", null),
          new PredefinedValueOption("Yes: Pitting", null)
        }, false, this, "Were any corrosion, erosion, or pitting cells noted on the shell or head surfaces:  (The dimensions and locations of any damage shall be recorded)", databaseName: "510_EXT-PV_CKLST_SEC013_Q001"); 
     
        attribute510EXT_PVCKLSTSEC013Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Cracking", null),
          new PredefinedValueOption("Yes: Mechanical Damage", null)
        }, false, this, "Was any mechanical damage or cracking noted on the shell or head surfaces:  (The dimensions and location of any damage shall be recorded)", databaseName: "510_EXT-PV_CKLST_SEC013_Q002"); 
     
        attribute510EXT_PVCKLSTSEC013Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Deformation", null),
          new PredefinedValueOption("Yes: Hot Spots", null)
        }, false, this, "Does the shell or head have any deformations or hot spots:(Bulges, Blisters, Dimpling)", databaseName: "510_EXT-PV_CKLST_SEC013_Q003"); 
     
        attribute510EXT_PVCKLSTSEC013Q004 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Cracking", null)
        }, false, this, "Were there indications of weld corrosion or cracking in the welds or Heat Affected Zones of the pressure retaining welds:", databaseName: "510_EXT-PV_CKLST_SEC013_Q004"); 
     
        attribute510EXT_PVCKLSTSEC013Q005 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Erosion", null),
          new PredefinedValueOption("Yes: Pitting", null)
        }, false, this, "Were any corrosion, erosion, or pitting cells noted on the channel surfaces:  (The dimensions and locations of any damage shall be recorded)", databaseName: "510_EXT-PV_CKLST_SEC013_Q005"); 
     
        attribute510EXT_PVCKLSTSEC013Q006 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Cracking", null),
          new PredefinedValueOption("Yes: Mechanical Damage", null)
        }, false, this, "Was any mechanical damage or cracking noted on the channel surfaces:  (The dimensions and location of any damage shall be recorded)", databaseName: "510_EXT-PV_CKLST_SEC013_Q006"); 
     
        attribute510EXT_PVCKLSTSEC013Q007 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Deformation", null),
          new PredefinedValueOption("Yes: Hot Spots", null)
        }, false, this, "Does the channel surfaces have any deformations or hot spots: (Bulges , Blisters , Dimpling)", databaseName: "510_EXT-PV_CKLST_SEC013_Q007"); 
     
        attribute510EXT_PVCKLSTSEC013Q008 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Cracking", null)
        }, false, this, "Were there indications of weld corrosion or cracking in the welds or Heat Affected Zones of the channel pressure retaining welds:", databaseName: "510_EXT-PV_CKLST_SEC013_Q008"); 
     
        attribute510EXT_PVCKLSTSEC013Q009 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Deformation", null),
          new PredefinedValueOption("Yes: Hot Spots", null)
        }, false, this, "Do the head surfaces have any deformations or hot spots: (Bulges , Blisters , Dimpling)", databaseName: "510_EXT-PV_CKLST_SEC013_Q009"); 
     
        attribute510EXT_PVCKLSTSEC013Q010 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Cracking", null)
        }, false, this, "Were there indications of weld corrosion or cracking in the welds or Heat Affected Zones of the head pressure retaining welds:", databaseName: "510_EXT-PV_CKLST_SEC013_Q010"); 
     
        attribute510EXT_PVCKLSTSEC013Q011 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Erosion", null),
          new PredefinedValueOption("Yes: Pitting", null)
        }, false, this, "Were any corrosion, erosion, or pitting cells noted on the head surfaces:  (The dimensions and locations of any damage shall be recorded)", databaseName: "510_EXT-PV_CKLST_SEC013_Q011"); 
     
        attribute510EXT_PVCKLSTSEC013Q012 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Cracking", null),
          new PredefinedValueOption("Yes: Mechanical Damage", null)
        }, false, this, "Was any mechanical damage, or cracking noted on the head surfaces:  (The dimensions and location of any damage shall be recorded)", databaseName: "510_EXT-PV_CKLST_SEC013_Q012"); 
     
        attribute510EXT_PVCKLSTSEC013Q013 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Cracking", null)
        }, false, this, "Were there indications of weld corrosion or cracking in the welds or Heat Affected Zones of the pressure retaining welds:", databaseName: "510_EXT-PV_CKLST_SEC013_Q013"); 
     
        attribute510EXT_PVCKLSTSEC013Q014 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was any excessive vibration noted that could propagate failure of weld(s) or supports:", databaseName: "510_EXT-PV_CKLST_SEC013_Q014"); 
     
        attribute510EXT_PVCKLSTSEC013Q015 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Were any temperature variances or pressure limitations noted at time of inspection:", databaseName: "510_EXT-PV_CKLST_SEC013_Q015"); 
     
        attribute510EXT_PVCKLSTSEC013Q016 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was any environmental deterioration or impacts from objects noted on the heads or shell:", databaseName: "510_EXT-PV_CKLST_SEC013_Q016"); 
     
        attributeIs_the_head_to_shell_attachment_achieved_via_welding = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the head to shell attachment achieved via welding:", databaseName: "510_EXT-PV_CKLST_SEC013_Q017"); 
     
        attribute510EXT_PVCKLSTSEC013Q018 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the head to shell weld in acceptable condition for continued service:", databaseName: "510_EXT-PV_CKLST_SEC013_Q018"); 
     
        attributeIs_the_head_to_shell_attachment_achieved_via_bolting = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the head to shell attachment achieved via bolting:", databaseName: "510_EXT-PV_CKLST_SEC013_Q019"); 
     
        attribute510EXT_PVCKLSTSEC013Q020 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the head to shell bolting in acceptable condition for continued service:", databaseName: "510_EXT-PV_CKLST_SEC013_Q020"); 
     
        attribute510EXT_PVCKLSTSEC013Q021 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the channel to shell bolting in acceptable condition for continued service? (Not all channel to shells are bolted)", databaseName: "510_EXT-PV_CKLST_SEC013_Q021"); 
     
        attribute510EXT_PVCKLSTSEC013Q022 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the channel to head bolting in acceptable condition for continued service? (Not all channel to heads are bolted)", databaseName: "510_EXT-PV_CKLST_SEC013_Q022"); 
     
        attribute510EXT_PVCKLSTSEC013Q023 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Active Leakage", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Evidence of Prior Leakage", null)
        }, false, this, "Was evidence of prior or active leakage noted to be originating from the shell or heads:", databaseName: "510_EXT-PV_CKLST_SEC013_Q023"); 
     
        attribute510EXT_PVCKLSTSEC013Q024 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the shell of the vessel in acceptable condition for continued service:", databaseName: "510_EXT-PV_CKLST_SEC013_Q024"); 
     
        attribute510EXT_PVCKLSTSEC013Q025 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are the heads of the vessel in acceptable condition for continued service:", databaseName: "510_EXT-PV_CKLST_SEC013_Q025"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute510EXT_PVCKLSTSEC013Q001,
           attribute510EXT_PVCKLSTSEC013Q002,
           attribute510EXT_PVCKLSTSEC013Q003,
           attribute510EXT_PVCKLSTSEC013Q004,
           attribute510EXT_PVCKLSTSEC013Q005,
           attribute510EXT_PVCKLSTSEC013Q006,
           attribute510EXT_PVCKLSTSEC013Q007,
           attribute510EXT_PVCKLSTSEC013Q008,
           attribute510EXT_PVCKLSTSEC013Q009,
           attribute510EXT_PVCKLSTSEC013Q010,
           attribute510EXT_PVCKLSTSEC013Q011,
           attribute510EXT_PVCKLSTSEC013Q012,
           attribute510EXT_PVCKLSTSEC013Q013,
           attribute510EXT_PVCKLSTSEC013Q014,
           attribute510EXT_PVCKLSTSEC013Q015,
           attribute510EXT_PVCKLSTSEC013Q016,
           attributeIs_the_head_to_shell_attachment_achieved_via_welding,
           attribute510EXT_PVCKLSTSEC013Q018,
           attributeIs_the_head_to_shell_attachment_achieved_via_bolting,
           attribute510EXT_PVCKLSTSEC013Q020,
           attribute510EXT_PVCKLSTSEC013Q021,
           attribute510EXT_PVCKLSTSEC013Q022,
           attribute510EXT_PVCKLSTSEC013Q023,
           attribute510EXT_PVCKLSTSEC013Q024,
           attribute510EXT_PVCKLSTSEC013Q025,
        };
    }
  }
}
