import 'package:api_inspection/generic/AppRoot.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/task.dart';
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'package:darq/darq.dart';
import 'package:api_inspection/generic/AppStyle.dart';

class TaskAssignmentPage extends StatefulWidget {
  final Task task;
  const TaskAssignmentPage(this.task, {Key? key}) : super(key: key);

  @override
  _TaskAssignmentPageState createState() => _TaskAssignmentPageState();
}

class _TaskAssignmentPageState extends State<TaskAssignmentPage> {
  String getTitle() {
    return "Task Assignment Page";
  }

  @override
  void initState() {
    super.initState();

    widget.task.addListener(onTaskChanged);
  }

  @override
  void dispose() {
    widget.task.removeListener(onTaskChanged);

    super.dispose();
  }

  void onTaskChanged() {
    setState(() {});
  }

  void changeAssignment() {
    var task = widget.task;
    var currentUser = AppRoot.global().currentUser!.email;
    setState(() {
      if (task.assignedUsers.contains(currentUser)) {
        task.assignedUsers.remove(currentUser);
      } else {
        task.assignedUsers.add(currentUser);
      }
    });

    var batch = FirebaseFirestore.instance.batch();
    task.saveItem(batch);

    batch.commit();

    task.workOrder.assignedUsers = task.workOrder.tasks
        .selectMany((element, index) => element.assignedUsers)
        .distinct()
        .toList();

    batch = FirebaseFirestore.instance.batch();
    task.workOrder.saveItem(batch);

    batch.commit();
  }

  @override
  Widget build(BuildContext context) {
    var task = widget.task;

    var currentUser = AppRoot.global().currentUser!.email;
    String assignmentLabel;
    if (task.assignedUsers.contains(currentUser)) {
      assignmentLabel = "Unassign task from me";
    } else {
      assignmentLabel = "Assign task to me";
    }

    List<Widget> assignedUserWidgets = [];
    for (var user in task.assignedUsers) {
      assignedUserWidgets.add(Text(user,
          style: const TextStyle(color: Colors.white, fontSize: 20)));
    }

    return Scaffold(
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        appBar: AppBar(
          title: Text(
            getTitle(),
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Container(
                margin: const EdgeInsets.all(20),
                child: const Text("Current Assignees",
                    style: TextStyle(color: Colors.white, fontSize: 22)),
              ),
              Expanded(
                child: Container(
                  color: Colors.blueGrey[800],
                  padding: const EdgeInsets.all(10),
                  margin: const EdgeInsets.fromLTRB(20, 0, 20, 10),
                  child: ListView(children: assignedUserWidgets),
                ),
              ),
              //task.name.buildWidget(),

              AttributePadding.WithStdPadding(
                TeamToggleButton.withText(assignmentLabel, changeAssignment),
              ),
            ],
          ),
        ));
  }
}
