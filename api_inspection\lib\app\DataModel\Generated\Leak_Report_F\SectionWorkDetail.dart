//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class SectionWorkDetail extends DataModelSection {
  @override
  String getDisplayName() => "Work Detail";
  SectionWorkDetail(DataModelItem? parent)
      : super(parent: parent, sectionName: "Work Detail");

  // ignore: non_constant_identifier_names
  late StringAttribute attributeClient = StringAttribute(
      parent: this,
      displayName: "Client",
      databaseName: "LR_Q005",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeCity = StringAttribute(
      parent: this,
      displayName: "City",
      databaseName: "LR_Q006",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeState = StringAttribute(
      parent: this,
      displayName: "State",
      databaseName: "LR_Q007",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributePostal_Code = StringAttribute(
      parent: this,
      displayName: "Postal Code",
      databaseName: "LR_Q008",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeArea = PredefinedValueAttribute(
      parent: this,
      hasOtherOption: true,
      displayName: "Area",
      databaseName: "LR_Q009",
      availableOptions: [
        PredefinedValueOption("Coalinga", null, isCommentRequired: false),
        PredefinedValueOption("Cymric", null, isCommentRequired: false),
        PredefinedValueOption("Kern River", null, isCommentRequired: false),
        PredefinedValueOption("Lost Hills", null, isCommentRequired: false),
        PredefinedValueOption("McKittrick", null, isCommentRequired: false),
        PredefinedValueOption("San Ardo", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeLease = StringAttribute(
      parent: this,
      displayName: "Lease",
      databaseName: "LR_Q010",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeFacility = StringAttribute(
      parent: this,
      displayName: "Facility",
      databaseName: "LR_Q011",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeJob_Description = StringAttribute(
      parent: this,
      displayName: "Job Description",
      databaseName: "LR_Q012",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeClient_Contact = StringAttribute(
      parent: this,
      displayName: "Client Contact",
      databaseName: "LR_Q013",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late PhoneNumberAttribute attributeClient_Contact_Number =
      PhoneNumberAttribute(
    parent: this,
    displayName: "Client Contact Number",
    databaseName: "LR_Q014",
    areCommentsRequired: false,
  );

  // ignore: non_constant_identifier_names
  late StringAttribute attributePurchase_OrderAFE = StringAttribute(
      parent: this,
      displayName: "Purchase Order/AFE",
      databaseName: "LR_Q015",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeClient_Cost_Code = StringAttribute(
      parent: this,
      displayName: "Client Cost Code",
      databaseName: "LR_Q016",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeClient_Work_Order = StringAttribute(
      parent: this,
      displayName: "Client Work Order",
      databaseName: "LR_Q017",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeTeam_District = StringAttribute(
      parent: this,
      displayName: "Team District",
      databaseName: "LR_Q018",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeTeam_Project_Number = StringAttribute(
      parent: this,
      displayName: "Team Project Number",
      databaseName: "LR_Q019",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeInspection_Reference = StringAttribute(
      parent: this,
      displayName: "Inspection Reference",
      databaseName: "LR_Q020",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeReference_EditionRevision = StringAttribute(
      parent: this,
      displayName: "Reference Edition/Revision",
      databaseName: "LR_Q021",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeInspection_Type =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: true,
          displayName: "Inspection Type",
          databaseName: "LR_Q022",
          availableOptions: [
        PredefinedValueOption("MT", null, isCommentRequired: false),
        PredefinedValueOption("PT", null, isCommentRequired: false),
        PredefinedValueOption("RT", null, isCommentRequired: false),
        PredefinedValueOption("UT", null, isCommentRequired: false),
        PredefinedValueOption("VT", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeInspected_By = StringAttribute(
      parent: this,
      displayName: "Inspected By",
      databaseName: "LR_Q023",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late DateAttribute attributeInspection_Date = DateAttribute(
      parent: this,
      displayName: "Inspection Date",
      databaseName: "LR_Q024",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeInspector_Certificate_Number = StringAttribute(
      parent: this,
      displayName: "Inspector Certificate Number",
      databaseName: "LR_Q025",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeReviewed_By = StringAttribute(
      parent: this,
      displayName: "Reviewed By:",
      databaseName: "LR_Q026",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeReviewer_Email = StringAttribute(
      parent: this,
      displayName: "Reviewer Email",
      databaseName: "LR_Q027",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeReviewer_Certificate_Number = StringAttribute(
      parent: this,
      displayName: "Reviewer Certificate Number",
      databaseName: "LR_Q028",
      areCommentsRequired: false);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeClient,
      attributeCity,
      attributeState,
      attributePostal_Code,
      attributeArea,
      attributeLease,
      attributeFacility,
      attributeJob_Description,
      attributeClient_Contact,
      attributeClient_Contact_Number,
      attributePurchase_OrderAFE,
      attributeClient_Cost_Code,
      attributeClient_Work_Order,
      attributeTeam_District,
      attributeTeam_Project_Number,
      attributeInspection_Reference,
      attributeReference_EditionRevision,
      attributeInspection_Type,
      attributeInspected_By,
      attributeInspection_Date,
      attributeInspector_Certificate_Number,
      attributeReviewed_By,
      attributeReviewer_Email,
      attributeReviewer_Certificate_Number,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionWorkDetail";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
