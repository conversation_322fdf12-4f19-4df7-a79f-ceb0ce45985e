import 'package:api_inspection/generic/AppStyle.dart';
import 'package:flutter/material.dart';

// ignore: must_be_immutable
class TeamToggleButton extends StatelessWidget {
  final Function() onPressed;
  late final Widget child;
  final Widget? iconWidget;
  final Color color;
  final Color borderColor;
  late double? height;

  TeamToggleButton.withText(String label, this.onPressed,
      {Key? key,
      this.color = const Color.fromARGB(255, 41, 45, 52),
      this.borderColor = const Color.fromARGB(255, 122, 122, 122),
      this.iconWidget,
      this.height})
      : super(key: key) {
    height ??= AppStyle.global.expandButtonSize;

    child = Text(label,
        textAlign: TextAlign.center,
        style: const TextStyle(color: Colors.white, fontSize: 16),
        softWrap: true);
  }

  TeamToggleButton(
      {Key? key,
      required this.child,
      required this.onPressed,
      this.color = const Color.fromARGB(255, 41, 45, 52),
      this.borderColor = const Color.fromARGB(255, 122, 122, 122),
      this.iconWidget,
      this.height})
      : super(key: key) {
    height ??= AppStyle.global.expandButtonSize;
  }

  @override
  Widget build(BuildContext context) {
    if (iconWidget != null) {
      return buildWithIcon(context);
    }

    TextButton button = TextButton(
        onPressed: onPressed,
        child: ClipRect(
          child: Container(
              decoration: BoxDecoration(
                color: color,
                borderRadius: const BorderRadius.all(Radius.circular(8)),
                border: Border.all(
                  color: borderColor,
                  width: 1,
                ),
              ),
              margin: const EdgeInsets.fromLTRB(0, 0, 0, 0),
              height: height,
              alignment: Alignment.center,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [Expanded(child: child)],
              )),
        ));
    return button;
  }

  Widget buildWithIcon(BuildContext context) {
    Widget? icon = iconWidget;

    if (icon == null) return const SizedBox(width: 0, height: 0);

    Widget image = Container(
        height: 50,
        width: 50,
        margin: const EdgeInsets.fromLTRB(20, 0, 20, 0),
        decoration: BoxDecoration(
          color: const Color.fromARGB(255, 122, 122, 122),
          borderRadius: const BorderRadius.all(Radius.circular(8)),
          border: Border.all(
            color: const Color.fromARGB(255, 122, 122, 122),
            width: 1,
          ),
        ),
        child: icon);

    TextButton button = TextButton(
        onPressed: onPressed,
        child: Container(
            decoration: BoxDecoration(
              color: color,
              borderRadius: const BorderRadius.all(Radius.circular(8)),
              border: Border.all(
                color: borderColor,
                width: 1,
              ),
            ),
            margin: const EdgeInsets.fromLTRB(0, 0, 0, 0),
            height: height,
            alignment: Alignment.center,
            child:
                //Container(clipBehavior: Clip.hardEdge, child:
                Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [image, Expanded(child: child)],
            ))
        //),
        );
    return button;
  }
}
