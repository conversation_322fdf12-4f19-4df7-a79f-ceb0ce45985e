import 'package:api_inspection/app/DataModel/Generated/510_Ext_PV_ALL_F/Section510_Ext_PV_ALL_F.dart';
import 'package:api_inspection/app/DataModel/Generated/510_Int_PV_ALL_F/Section510_Int_PV_ALL_F.dart';
import 'package:api_inspection/app/DataModel/Tasks/IMultiTopLevelInspection.dart';
import 'package:api_inspection/app/DataModel/Tasks/taskFormFactory.dart';
import 'package:api_inspection/app/DataModel/task.dart';
import 'package:api_inspection/app/UI/Tasks/TaskCompletionBarWrapper.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

class Section510_FullInspection extends DataModelSection
    implements IMultiTopLevelInspection {
  @override
  String getDisplayName() => "510 Full Inspection";
  Section510_FullInspection(DataModelItem? parent)
      : super(parent: parent, sectionName: "510-Int-PV-ALL");

  // ignore: non_constant_identifier_names
  late Section510_Int_PV_ALL_F internalInspection =
      Section510_Int_PV_ALL_F(this);

  // ignore: non_constant_identifier_names
  late Section510_Ext_PV_ALL_F externalInspection =
      Section510_Ext_PV_ALL_F(this);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([internalInspection, externalInspection]);
    return children;
  }

  @override
  String getDBName() {
    return "Section510_FullInspection";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry? entry) {
    return false;
  }

  @override
  List<Widget> buildFormButtons(BuildContext context, Task task) {
    return [
      AttributePadding.WithStdPadding(TeamToggleButton.withText("Internal", () {
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => TaskCompletionBarWrapper(
                    task: task,
                    child: TaskFormFactory.buildTaskDataForm(
                        internalInspection))));
      })),
      AttributePadding.WithStdPadding(TeamToggleButton.withText("External", () {
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => TaskCompletionBarWrapper(
                    task: task,
                    child: TaskFormFactory.buildTaskDataForm(
                        externalInspection))));
      })),
    ];
  }
}
