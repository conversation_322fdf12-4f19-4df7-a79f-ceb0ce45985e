import 'dart:async';
import 'dart:developer';

import 'package:api_inspection/app/APMRoot.dart';
import 'package:api_inspection/app/DataModel/asset.dart';
import 'package:api_inspection/app/DataModel/assetCard.dart';
import 'package:api_inspection/app/DataModel/workorder.dart';
import 'package:api_inspection/app/Queries/query_listeners_manager.dart';
import 'package:api_inspection/generic/AppRoot.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModel/user.dart';
import 'package:api_inspection/generic/DataModelCommon/DatabaseInterfaces/FirebaseDatabaseHelper.dart';
import 'package:api_inspection/generic/UIControls/listener.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:darq/darq.dart';

class NewQueries {
  /// Firestore db reference
  late FirebaseFirestore _db;

  /// Current user's email
  final String userEmail = AppRoot.global().currentUser!.email;

  /// Reference to the current user's nonverified user profile
  final UnverifiedUserProfile unverifiedUserProfile =
      AppRoot.global().currentUserNonVerified!;

  /// Instance that manages many `BatchQueryListenerWrapper`s
  late QueryListenersManager _manager;

  /// Work orders that we've cached at any given moment.
  Iterable<WorkOrder> get workOrders {
    return _manager.workOrders.values;
  }

  /// Assets that we've cached at any given moment.
  Iterable<Asset> get assets {
    return _manager.assets.values;
  }

  /// Broadcasts when work orders cache has been updated.  Can be used
  /// to signal that the UI needs updated, for example.
  ListenerWrapper get workOrderListener {
    return _manager.workOrderListener;
  }

  /// Broadcasts when assets cache has been updated.  Can be used to
  /// signal that the UI needs updated, for example.
  ListenerWrapper get assetListener {
    return _manager.assetListener;
  }

  /// Root subscription that drives visibility to what Assets, Work Orders,
  /// and Tasks we want to subscribe/listen to in our child listeners
  /// (`BatchQueryListenerWrapper`s).
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? _subscription;

  Future dispose() async {
    if (_subscription != null) {
      await _subscription!.cancel();
      _subscription = null;
    }

    await _manager.stop();

    APMRoot.global.queries.selectedProjects.selectedProjectsListener
        .removeListener(_onSelectedProjectsChanged);
  }

  NewQueries() {
    _db = FirebaseDatabaseHelper.global().databaseReference;
    _manager = QueryListenersManager(_db);

    APMRoot.global.queries.selectedProjects.selectedProjectsListener
        .addListener(_onSelectedProjectsChanged);
  }

  /// Initializes the class by beginning the root subscription/listener query,
  /// and updates the query manager with a fresh list of asset ids that drive
  /// what data we want to pull down.
  Future<void> initialize() async {
    _subscription = _db
        .collection('listened-assets')
        .where('userId', isEqualTo: userEmail)
        .snapshots()
        .listen((event) {
      log('===== LISTENED ASSETS =====');
      _manager.listenedAssetIds = {};
      _manager.listenedAssetIds.addAll(event.docs.map((e) => e.get('assetId')));

      _updateData();
    });
  }

  Future<void> pause() async {
    if (_subscription != null) {
      await _subscription!.cancel();
      _subscription = null;
    }

    _manager.stop();
  }

  Future<void> resume() async {
    return await initialize();
  }

  void _updateData() {
    Set<String> assetIds = {};
    assetIds.addAll(_manager.listenedAssetIds);
    assetIds.addAll(_manager.sessionAssetIds);

    _manager.removeUnwantedData();

    _manager.setIds('assets', assetIds);
    _manager.setIds('tasks', assetIds);
    _manager.setIds('workorders', assetIds);
  }

  void _onSelectedProjectsChanged() {
    var projects =
        APMRoot.global.queries.selectedProjects.getSelectedProjects();
    var assetIdsFromSelectedProjects = projects
        .where((project) => project.assetIds.getValue() != null)
        .selectMany(
            (project, index) => project.assetIds.getValue() as List<String>);

    _manager.sessionAssetIds = {};
    _manager.sessionAssetIds.addAll(assetIdsFromSelectedProjects);

    _updateData();
  }

  void addNewAsset(Asset asset) {
    if (!_manager.sessionAssetIds.contains(asset.id)) {
      _manager.sessionAssetIds.add(asset.id);
      _updateData();
    }
  }

  void addAssetIdToSession(String assetId) {
    if (!_manager.sessionAssetIds.contains(assetId)) {
      _manager.sessionAssetIds.add(assetId);
      _updateData();
    }
  }

  void addAssetToUserFavorited(AssetCard card) {
    unverifiedUserProfile.addFavoritedAsset(card.assetDBId);
    var helper = FirebaseDatabaseHelper.global();
    var batch = FirebaseFirestore.instance.batch();
    helper.updateItem('assets.${card.assetDBId}.ListeningUsers',
        FieldValue.arrayUnion([userEmail]), batch);
    batch.commit();
  }

  void removeAssetFromUserFavorited(AssetCard card) {
    unverifiedUserProfile.removeFavoritedAsset(card.assetDBId);
    var helper = FirebaseDatabaseHelper.global();
    var batch = FirebaseFirestore.instance.batch();
    helper.updateItem('assets.${card.assetDBId}.ListeningUsers',
        FieldValue.arrayRemove([userEmail]), batch);
    batch.commit();
  }

  Iterable<String> getAssetIdsExplicitlyListeningFor() {
    var assetIdsForCurrentSession = assets
        .where((asset) => _manager.sessionAssetIds.contains(asset.id))
        .map((asset) => asset.id);
    var assetIdsForWorkOrders =
        workOrders.map((workOrder) => workOrder.asset.id).distinct();
    return assetIdsForCurrentSession.concat(assetIdsForWorkOrders);
  }

  bool isAssetUserFavorited(AssetCard card) {
    return unverifiedUserProfile.favoritedAssets.contains(card.assetDBId);
  }
}
