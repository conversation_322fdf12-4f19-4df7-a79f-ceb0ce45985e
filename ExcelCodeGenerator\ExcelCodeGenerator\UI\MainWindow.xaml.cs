﻿using System;
using System.Windows;
using ExcelCodeGenerator;
using Microsoft.WindowsAPICodePack.Dialogs;

namespace CheckListGen
{
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
        }

        private void FindExcelFileClicked(object sender, RoutedEventArgs e)
        {
            var dialog = new CommonOpenFileDialog();
            dialog.Filters.Add(new CommonFileDialogFilter("Excel documents", ".xlsx"));

            if (dialog.ShowDialog() == CommonFileDialogResult.Ok) ExcelFilePath = dialog.FileName;
        }

        private void FindDartOutputPathClicked(object sender, RoutedEventArgs e)
        {
            var dialog = new CommonOpenFileDialog
            {
                IsFolderPicker = true
            };
            if (dialog.ShowDialog() == CommonFileDialogResult.Ok) DartOutputPath = dialog.FileName;
        }

        private void FindCSharpOutputPathClicked(object sender, RoutedEventArgs e)
        {
            var dialog = new CommonOpenFileDialog
            {
                IsFolderPicker = true
            };
            if (dialog.ShowDialog() == CommonFileDialogResult.Ok) CSharpOutputPath = dialog.FileName;
        }


        private void GenerateClicked(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(DartOutputPath))
            {
                MainOutput.Text = "Missing dart output directory";
                return;
            }

            if (string.IsNullOrWhiteSpace(CSharpOutputPath))
            {
                MainOutput.Text = "Missing c# output directory";
                return;
            }

            MainOutput.Text = "Starting Generation..";

            try
            {
                CodeGeneratorMain.GenerateCodeFromExcelTables(ExcelFilePath, DartOutputPath, CSharpOutputPath);

                MainOutput.Text = "Output successful";
            }
            catch (Exception ex)
            {
                MainOutput.Text = ex.ToString();
            }
        }

        #region [-- ExcelFilePath --]

        public string ExcelFilePath
        {
            get => (string) GetValue(ExcelFilePathProperty);
            set => SetValue(ExcelFilePathProperty, value);
        }

        public static readonly DependencyProperty ExcelFilePathProperty =
            DependencyProperty.Register("ExcelFilePath", typeof(string), typeof(MainWindow),
                new PropertyMetadata(Config.DefaultExcelFilePath));

        #endregion [-- ExcelFilePath --]

        #region [-- DartOutputPath --]

        public string DartOutputPath
        {
            get => (string) GetValue(DartOutputPathProperty);
            set => SetValue(DartOutputPathProperty, value);
        }

        public static readonly DependencyProperty DartOutputPathProperty =
            DependencyProperty.Register("DartOutputPath", typeof(string), typeof(MainWindow),
                new PropertyMetadata(Config.DefaultDartDirectory));

        #endregion [-- DartOutputPath --]

        #region [-- CSharpOutputPath --]

        public string CSharpOutputPath
        {
            get => (string) GetValue(CSharpOutputPathProperty);
            set => SetValue(CSharpOutputPathProperty, value);
        }

        public static readonly DependencyProperty CSharpOutputPathProperty =
            DependencyProperty.Register("CSharpOutputPath", typeof(string), typeof(MainWindow),
                new PropertyMetadata(Config.DefaultCSharpDirectory));

        #endregion [-- CSharpOutputPath --]
    }
}