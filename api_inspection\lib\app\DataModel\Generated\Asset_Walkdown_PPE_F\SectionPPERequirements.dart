//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class SectionPPERequirements extends DataModelSection {
  @override
  String getDisplayName() => "PPE Requirements";
  SectionPPERequirements(DataModelItem? parent)
      : super(parent: parent, sectionName: "PPE Requirements");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeHard_Hat = PredefinedValueAttribute(
      parent: this,
      hasOtherOption: false,
      displayName: "Hard Hat",
      databaseName: "PPEAW_Q001",
      availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeEye_Protection =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Eye Protection",
          databaseName: "PPEAW_Q010",
          availableOptions: [
        PredefinedValueOption("Clear Safety Glasses", null,
            isCommentRequired: false),
        PredefinedValueOption("Dark Safety Glasses", null,
            isCommentRequired: false),
        PredefinedValueOption("Safety Goggles", null, isCommentRequired: false),
        PredefinedValueOption("Face Shield", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeHearing_Protection =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Hearing Protection",
          databaseName: "PPEAW_Q015",
          availableOptions: [
        PredefinedValueOption("Ear Plugs", null, isCommentRequired: false),
        PredefinedValueOption("Ear Muffs", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeFire_Retardant_Clothing =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Fire Retardant Clothing",
          databaseName: "PPEAW_Q020",
          availableOptions: [
        PredefinedValueOption("Shirt/Jean", null, isCommentRequired: false),
        PredefinedValueOption("Overall", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeSafety_Gloves =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Safety Gloves",
          databaseName: "PPEAW_Q025",
          availableOptions: [
        PredefinedValueOption("Standard", null, isCommentRequired: false),
        PredefinedValueOption("Cut Resistant", null, isCommentRequired: false),
        PredefinedValueOption("Heat Resistant", null, isCommentRequired: false),
        PredefinedValueOption("Impact Resistant", null,
            isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeSnake_Chaps = PredefinedValueAttribute(
      parent: this,
      hasOtherOption: false,
      displayName: "Snake Chaps",
      databaseName: "PPEAW_Q030",
      availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeFoot_Protection =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Foot Protection",
          databaseName: "PPEAW_Q035",
          availableOptions: [
        PredefinedValueOption("Steel Toed Boots", null,
            isCommentRequired: false),
        PredefinedValueOption("Rubber Boots", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeChemical_Suit =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Chemical Suit",
          databaseName: "PPEAW_Q040",
          availableOptions: [
        PredefinedValueOption("Chemical Rated", null, isCommentRequired: false),
        PredefinedValueOption("Tyvek", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeFall_Protection =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Fall Protection",
          databaseName: "PPEAW_Q045",
          availableOptions: [
        PredefinedValueOption("Body Harness", null, isCommentRequired: false),
        PredefinedValueOption("Lanyard", null, isCommentRequired: false),
        PredefinedValueOption("Life Line", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeBreathing_Protection =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Breathing Protection",
          databaseName: "PPEAW_Q050",
          availableOptions: [
        PredefinedValueOption("Dust Mask", null, isCommentRequired: false),
        PredefinedValueOption("Half-face Respirator", null,
            isCommentRequired: false),
        PredefinedValueOption("Full-face Respirator", null,
            isCommentRequired: false),
        PredefinedValueOption("SCBA", null, isCommentRequired: false),
        PredefinedValueOption("Supplied Air", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeAtmosphere =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Atmosphere",
          databaseName: "PPEAW_Q055",
          availableOptions: [
        PredefinedValueOption("H2S Monitor", null, isCommentRequired: false),
        PredefinedValueOption("Cl Personal Monitor", null,
            isCommentRequired: false),
        PredefinedValueOption("Quad Gas Monitor", null,
            isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeHard_Hat,
      attributeEye_Protection,
      attributeHearing_Protection,
      attributeFire_Retardant_Clothing,
      attributeSafety_Gloves,
      attributeSnake_Chaps,
      attributeFoot_Protection,
      attributeChemical_Suit,
      attributeFall_Protection,
      attributeBreathing_Protection,
      attributeAtmosphere,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionPPERequirements";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
