import 'package:api_inspection/app/batch_helper.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';

// ignore: camel_case_types
class PredefinedValueAttributeView_YesNoNAEditable extends StatefulWidget {
  final PredefinedValueAttribute _attribute;
  final bool showPhotoControl;
  final bool showCommentsControl;

  const PredefinedValueAttributeView_YesNoNAEditable(this._attribute,
      {Key? key, this.showPhotoControl = true, this.showCommentsControl = true})
      : super(key: key);

  @override
  _PredefinedValueAttributeView_YesNoNAEditableState createState() =>
      _PredefinedValueAttributeView_YesNoNAEditableState();
}

// ignore: camel_case_types
class _PredefinedValueAttributeView_YesNoNAEditableState
    extends State<PredefinedValueAttributeView_YesNoNAEditable> {
  bool initialized = false;

  void initialize() {
    if (initialized) return;
    initialized = true;
    var attribute = widget._attribute;
    attribute.addListener(onAttributeChanged);
  }

  void onAttributeChanged() {
    setState(() {});
  }

  @override
  void dispose() {
    var attribute = widget._attribute;
    attribute.removeListener(onAttributeChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    initialize();

    double width = MediaQuery.of(context).size.width;

    Color selectedColor = const Color.fromARGB(255, 4, 188, 242);
    Color unselectedColor = const Color.fromARGB(255, 122, 122, 122);

    List<Widget> buttons = [];
    var attrValue = widget._attribute.getValue();
    var options = ["N/A", "No", "Yes"];
    for (var item in options) {
      Color buttonColor;
      if (item == attrValue) {
        buttonColor = selectedColor;
      } else {
        buttonColor = unselectedColor;
      }

      buttons.add(SizedBox(
          width: (width - 55) / 3,
          height: 85,
          child: TeamToggleButton.withText(
              item,
              () => {
                    setState(() {
                      var currentValue = widget._attribute.getValue();
                      if (currentValue == item) {
                        BatchHelper.saveAndCommitStringAttribute(
                            widget._attribute, null);
                      } else {
                        BatchHelper.saveAndCommitStringAttribute(
                            widget._attribute, item);
                      }
                    })
                  },
              borderColor: buttonColor)));
    }
    return Container(
        margin: const EdgeInsets.fromLTRB(0, 10, 0, 10),
        child: Wrap(children: buttons));
  }
}
