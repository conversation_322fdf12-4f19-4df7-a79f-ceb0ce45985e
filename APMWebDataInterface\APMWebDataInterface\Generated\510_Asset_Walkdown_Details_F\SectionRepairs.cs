//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Asset_Walkdown_Details_F
{
  public class SectionRepairs : DataModelCollectionItem {

    public override String DisplayName { 
      get {
        return "Repairs";
      }
    }
    
    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public DateAttribute attributeDate_Repaired_or_Altered;
    public StringAttribute attributeRepairAlteration_organization;
    public StringAttribute attributePurpose_of_repairalteration;
    public PredefinedValueAttribute attributeIs_NB_Form_R_1_Available;
    public PredefinedValueAttribute attributeIs_NB_Form_R_2_Available;
    public StringAttribute attributeNB_R_Certificate_Number;

    #endregion [-- Attributes --]

    public SectionRepairs(String id, DataModelItem parent) : base(id, parent)
    {
            
        attributeDate_Repaired_or_Altered = new DateAttribute(this, displayName: "Date Repaired or Altered", databaseName: "510AW_Q186", areCommentsRequired: false); 
     
        attributeRepairAlteration_organization = new StringAttribute(this, displayName: "Repair/Alteration organization", databaseName: "510AW_Q187"); 
     
        attributePurpose_of_repairalteration = new StringAttribute(this, displayName: "Purpose of repair/alteration", databaseName: "510AW_Q188"); 
     
        attributeIs_NB_Form_R_1_Available = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Is NB Form R-1 Available", databaseName: "510AW_Q189"); 
     
        attributeIs_NB_Form_R_2_Available = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Is NB Form R-2 Available", databaseName: "510AW_Q190"); 
     
        attributeNB_R_Certificate_Number = new StringAttribute(this, displayName: "NB 'R' Certificate Number", databaseName: "510AW_Q191"); 
    }   
    
    public override DataModelItem[] GetChildren() {
      var parentChildren = base.GetChildren();

      return parentChildren.Concat(new DataModelItem[] { 
        attributeDate_Repaired_or_Altered,
           attributeRepairAlteration_organization,
           attributePurpose_of_repairalteration,
           attributeIs_NB_Form_R_1_Available,
           attributeIs_NB_Form_R_2_Available,
           attributeNB_R_Certificate_Number,
      }).ToArray();
    }
  }
}
