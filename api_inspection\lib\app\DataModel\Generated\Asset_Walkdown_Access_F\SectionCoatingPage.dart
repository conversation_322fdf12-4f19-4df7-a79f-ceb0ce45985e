//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionCoating.dart';

// ignore: camel_case_types
class SectionCoatingPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionCoating sectionCoating;

  const SectionCoatingPage(this.sectionCoating, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionCoatingPageState();
  }
}

class _SectionCoatingPageState extends State<SectionCoatingPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionCoating,
        title: "Coating",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionCoating.attributeCoating_Type
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionCoating.attributeCoating_Condition
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionCoating.attributeCoating_Conditions_Observed
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionCoating.attributeCoating_Removal_Required
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
