//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section653_Asset_Walkdown_Details_F
{
  public class SectionManufacturerFabricator : DataModelItem {

    public override String DisplayName { 
      get {
        return "Manufacturer (Fabricator)";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public StringAttribute attributeName;
    public DateAttribute attributeDate;
    public StringAttribute attributeSerial_Number;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionManufacturerFabricator";

    public SectionManufacturerFabricator(DataModelItem parent) : base(parent)
    {
            
        attributeName = new StringAttribute(this, displayName: "Name", databaseName: "653AW_Q176"); 
     
        attributeDate = new DateAttribute(this, displayName: "Date", databaseName: "653AW_Q177", areCommentsRequired: false); 
     
        attributeSerial_Number = new StringAttribute(this, displayName: "Serial Number", databaseName: "653AW_Q178"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeName,
           attributeDate,
           attributeSerial_Number,
        };
    }
  }
}
