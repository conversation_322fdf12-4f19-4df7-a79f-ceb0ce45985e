import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';

class CosmosDbService {
  static final CosmosDbService _instance = CosmosDbService._internal();
  
  factory CosmosDbService() {
    return _instance;
  }
  
  CosmosDbService._internal();
  
  // Cosmos DB configuration - These would typically come from environment config
  final String _endpoint = 'https://YOUR_COSMOS_DB_ACCOUNT.documents.azure.com';
  final String _primaryKey = 'YOUR_COSMOS_DB_PRIMARY_KEY';
  final String _databaseId = 'YOUR_DATABASE_ID';
  final Map<String, String> _collections = {
    'inspections': 'inspections',
    'assets': 'assets',
    'users': 'users',
    // Add all your collections here
  };
  
  String? _authToken;
  
  // Get a document by ID
  Future<Map<String, dynamic>?> getDocument(String collectionId, String documentId) async {
    try {
      final String resourceLink = 'dbs/$_databaseId/colls/${_collections[collectionId]}/docs/$documentId';
      final response = await _executeRequest(resourceLink, 'GET');
      
      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        debugPrint('Failed to get document: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      debugPrint('Error getting document: $e');
      return null;
    }
  }
  
  // Query documents
  Future<List<Map<String, dynamic>>> queryDocuments(String collectionId, {String? query}) async {
    try {
      final String resourceLink = 'dbs/$_databaseId/colls/${_collections[collectionId]}/docs';
      
      // Default query to get all documents
      final String queryPayload = query ?? 'SELECT * FROM c';
      
      final response = await _executeRequest(
        resourceLink, 
        'POST', 
        additionalHeaders: {'x-ms-documentdb-isquery': 'true'},
        body: json.encode({'query': queryPayload})
      );
      
      if (response.statusCode == 200) {
        final Map<String, dynamic> body = json.decode(response.body);
        return List<Map<String, dynamic>>.from(body['Documents']);
      } else {
        debugPrint('Failed to query documents: ${response.statusCode} - ${response.body}');
        return [];
      }
    } catch (e) {
      debugPrint('Error querying documents: $e');
      return [];
    }
  }
  
  // Create a document
  Future<Map<String, dynamic>?> createDocument(String collectionId, Map<String, dynamic> document) async {
    try {
      final String resourceLink = 'dbs/$_databaseId/colls/${_collections[collectionId]}/docs';
      
      final response = await _executeRequest(
        resourceLink, 
        'POST', 
        body: json.encode(document)
      );
      
      if (response.statusCode == 201) {
        return json.decode(response.body);
      } else {
        debugPrint('Failed to create document: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      debugPrint('Error creating document: $e');
      return null;
    }
  }
  
  // Update a document
  Future<Map<String, dynamic>?> updateDocument(String collectionId, String documentId, Map<String, dynamic> document) async {
    try {
      final String resourceLink = 'dbs/$_databaseId/colls/${_collections[collectionId]}/docs/$documentId';
      
      final response = await _executeRequest(
        resourceLink, 
        'PUT', 
        body: json.encode(document)
      );
      
      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        debugPrint('Failed to update document: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      debugPrint('Error updating document: $e');
      return null;
    }
  }
  
  // Delete a document
  Future<bool> deleteDocument(String collectionId, String documentId) async {
    try {
      final String resourceLink = 'dbs/$_databaseId/colls/${_collections[collectionId]}/docs/$documentId';
      
      final response = await _executeRequest(resourceLink, 'DELETE');
      
      if (response.statusCode == 204) {
        return true;
      } else {
        debugPrint('Failed to delete document: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      debugPrint('Error deleting document: $e');
      return false;
    }
  }
  
  // Execute a request to Cosmos DB REST API with proper authentication
  Future<http.Response> _executeRequest(
    String resourceLink, 
    String method, 
    {Map<String, String>? additionalHeaders, String? body}
  ) async {
    final String date = DateTime.now().toUtc().toString();
    final String authHeader = _generateAuthHeader(resourceLink, method, date);
    
    final Map<String, String> headers = {
      'Authorization': authHeader,
      'x-ms-date': date,
      'x-ms-version': '2018-12-31',
      'Content-Type': 'application/json',
    };
    
    if (additionalHeaders != null) {
      headers.addAll(additionalHeaders);
    }
    
    final Uri uri = Uri.parse('$_endpoint/$resourceLink');
    
    switch (method) {
      case 'GET':
        return http.get(uri, headers: headers);
      case 'POST':
        return http.post(uri, headers: headers, body: body);
      case 'PUT':
        return http.put(uri, headers: headers, body: body);
      case 'DELETE':
        return http.delete(uri, headers: headers);
      default:
        throw Exception('Unsupported method: $method');
    }
  }
  
  // Generate the authorization header for Cosmos DB
  String _generateAuthHeader(String resourceLink, String verb, String date) {
    // This is a simplified version. In production, you would need to implement
    // the proper HMAC-SHA256 signature calculation as per Cosmos DB documentation
    // https://docs.microsoft.com/en-us/rest/api/cosmos-db/access-control-on-cosmosdb-resources
    
    final String type = 'master';
    final String version = '1.0';
    final String key = _primaryKey;
    
    // In a real implementation, we would:
    // 1. Create a string to sign (resourceType, resourceId, date, etc.)
    // 2. Create an HMAC-SHA256 hash of the string using the master key
    // 3. Base64 encode the hash
    
    // For now, return a placeholder:
    return 'type=$type&ver=$version&sig=SIGNATURE';
  }
}
