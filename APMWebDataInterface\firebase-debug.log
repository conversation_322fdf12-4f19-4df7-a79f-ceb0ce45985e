[debug] [2021-10-14T15:57:18.219Z] ----------------------------------------------------------------------
[debug] [2021-10-14T15:57:18.222Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js serve
[debug] [2021-10-14T15:57:18.222Z] CLI Version:   9.16.0
[debug] [2021-10-14T15:57:18.222Z] Platform:      win32
[debug] [2021-10-14T15:57:18.222Z] Node Version:  v14.17.1
[debug] [2021-10-14T15:57:18.223Z] Time:          Thu Oct 14 2021 09:57:18 GMT-0600 (Mountain Daylight Time)
[debug] [2021-10-14T15:57:18.223Z] ----------------------------------------------------------------------
[debug] 
[debug] [2021-10-14T15:57:18.300Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2021-10-14T15:57:18.300Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2021-10-14T15:57:18.301Z] [iam] checking project asset-performance-management for permissions ["firebase.projects.get"]
[debug] [2021-10-14T15:57:18.303Z] > refreshing access token with scopes: ["email","https://www.googleapis.com/auth/cloud-platform","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","openid"]
[debug] [2021-10-14T15:57:18.303Z] >>> HTTP REQUEST POST https://www.googleapis.com/oauth2/v3/token  
 <request body omitted>
[debug] [2021-10-14T15:57:18.399Z] <<< HTTP RESPONSE 200 {"date":"Thu, 14 Oct 2021 15:57:19 GMT","pragma":"no-cache","expires":"Mon, 01 Jan 1990 00:00:00 GMT","cache-control":"no-cache, no-store, max-age=0, must-revalidate","content-type":"application/json; charset=utf-8","vary":"X-Origin, Referer, Origin,Accept-Encoding","server":"scaffolding on HTTPServer2","x-xss-protection":"0","x-frame-options":"SAMEORIGIN","x-content-type-options":"nosniff","alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000,h3-T051=\":443\"; ma=2592000,h3-Q050=\":443\"; ma=2592000,h3-Q046=\":443\"; ma=2592000,h3-Q043=\":443\"; ma=2592000,quic=\":443\"; ma=2592000; v=\"46,43\"","accept-ranges":"none","transfer-encoding":"chunked"}
[debug] [2021-10-14T15:57:18.422Z] >>> HTTP REQUEST POST https://cloudresourcemanager.googleapis.com/v1/projects/asset-performance-management:testIamPermissions  
 {"permissions":["firebase.projects.get"]}
[debug] [2021-10-14T15:57:18.620Z] <<< HTTP RESPONSE 200 {"content-type":"application/json; charset=UTF-8","vary":"X-Origin, Referer, Origin,Accept-Encoding","date":"Thu, 14 Oct 2021 15:57:19 GMT","server":"ESF","cache-control":"private","x-xss-protection":"0","x-frame-options":"SAMEORIGIN","x-content-type-options":"nosniff","server-timing":"gfet4t7; dur=92","alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000,h3-T051=\":443\"; ma=2592000,h3-Q050=\":443\"; ma=2592000,h3-Q046=\":443\"; ma=2592000,h3-Q043=\":443\"; ma=2592000,quic=\":443\"; ma=2592000; v=\"46,43\"","accept-ranges":"none","transfer-encoding":"chunked"}
[debug] [2021-10-14T15:57:18.623Z] > refreshing access token with scopes: []
[debug] [2021-10-14T15:57:18.624Z] >>> HTTP REQUEST POST https://www.googleapis.com/oauth2/v3/token  
 <request body omitted>
[debug] [2021-10-14T15:57:18.686Z] <<< HTTP RESPONSE 200 {"pragma":"no-cache","cache-control":"no-cache, no-store, max-age=0, must-revalidate","expires":"Mon, 01 Jan 1990 00:00:00 GMT","date":"Thu, 14 Oct 2021 15:57:19 GMT","content-type":"application/json; charset=utf-8","vary":"X-Origin, Referer, Origin,Accept-Encoding","server":"scaffolding on HTTPServer2","x-xss-protection":"0","x-frame-options":"SAMEORIGIN","x-content-type-options":"nosniff","alt-svc":"h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000,h3-T051=\":443\"; ma=2592000,h3-Q050=\":443\"; ma=2592000,h3-Q046=\":443\"; ma=2592000,h3-Q043=\":443\"; ma=2592000,quic=\":443\"; ma=2592000; v=\"46,43\"","accept-ranges":"none","transfer-encoding":"chunked"}
[debug] [2021-10-14T15:57:18.700Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/asset-performance-management [none]
[debug] [2021-10-14T15:57:18.877Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/asset-performance-management 200
[debug] [2021-10-14T15:57:18.877Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/asset-performance-management {"projectId":"asset-performance-management","projectNumber":"375236612863","displayName":"Asset Performance Management","name":"projects/asset-performance-management","resources":{"hostingSite":"asset-performance-management","realtimeDatabaseInstance":"asset-performance-management-default-rtdb","storageBucket":"asset-performance-management.appspot.com","locationId":"us-central"},"state":"ACTIVE"}
[info] 
[info] === Serving from 'D:\Dev\Maggie\APMWebDataInterface'...
[info] 
[debug] [2021-10-14T15:57:18.882Z] >>> [apiv2][query] GET https://firebasehosting.googleapis.com/v1beta1/projects/asset-performance-management/sites 
[debug] [2021-10-14T15:57:19.101Z] <<< [apiv2][status] GET https://firebasehosting.googleapis.com/v1beta1/projects/asset-performance-management/sites 200
[debug] [2021-10-14T15:57:19.101Z] <<< [apiv2][body] GET https://firebasehosting.googleapis.com/v1beta1/projects/asset-performance-management/sites {"sites":[{"name":"projects/375236612863/sites/asset-performance-management","defaultUrl":"https://asset-performance-management.web.app","type":"DEFAULT_SITE"}]}
[debug] [2021-10-14T15:57:19.102Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/asset-performance-management/webApps/-/config [none]
[debug] [2021-10-14T15:57:19.296Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/asset-performance-management/webApps/-/config 200
[debug] [2021-10-14T15:57:19.296Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/asset-performance-management/webApps/-/config {"projectId":"asset-performance-management","appId":"1:375236612863:web:44b4e0058219b4abbc72ca","databaseURL":"https://asset-performance-management-default-rtdb.firebaseio.com","storageBucket":"asset-performance-management.appspot.com","locationId":"us-central","apiKey":"AIzaSyCy7hM82CoycZD_DGo62vLCbmktsBUsAjE","authDomain":"asset-performance-management.firebaseapp.com","messagingSenderId":"375236612863","measurementId":"G-BHMB8W7Z2W"}
[info] i  hosting: Serving hosting files from: public {"metadata":{"emulator":{"name":"hosting"},"message":"Serving hosting files from: \u001b[1mpublic\u001b[22m"}}
[info] +  hosting: Local server: http://localhost:5000 {"metadata":{"emulator":{"name":"hosting"},"message":"Local server: \u001b[4m\u001b[1mhttp://localhost:5000\u001b[22m\u001b[24m"}}
