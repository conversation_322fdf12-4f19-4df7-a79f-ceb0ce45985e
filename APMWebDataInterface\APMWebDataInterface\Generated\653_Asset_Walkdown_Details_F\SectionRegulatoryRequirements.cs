//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section653_Asset_Walkdown_Details_F
{
  public class SectionRegulatoryRequirements : DataModelCollectionItem {

    public override String DisplayName { 
      get {
        return "Regulatory Requirements";
      }
    }
    
    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public StringAttribute attributeJurisdiction_Regulatory_agency;

    #endregion [-- Attributes --]

    public SectionRegulatoryRequirements(String id, DataModelItem parent) : base(id, parent)
    {
            
        attributeJurisdiction_Regulatory_agency = new StringAttribute(this, displayName: "Jurisdiction-Regulatory agency", databaseName: "653AW_Q341"); 
    }   
    
    public override DataModelItem[] GetChildren() {
      var parentChildren = base.GetChildren();

      return parentChildren.Concat(new DataModelItem[] { 
        attributeJurisdiction_Regulatory_agency,
      }).ToArray();
    }
  }
}
