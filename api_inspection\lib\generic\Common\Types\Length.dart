enum LengthUnits {
  NauticalMiles,
  Cables,
  Fathoms,
  Leagues,
  Miles,
  Furlongs,
  Chains,
  Yards,
  Inches,
  MilliInches,
  Feet,
  Kilometers,
  Hectometers,
  Dekameters,
  Meters,
  Decimeters,
  Centimeters,
  Millimeters,
  Micrometers,
  Nanometers
}

extension LengthUnitsExt on LengthUnits {
  String abbreviation() {
    switch (this) {
      case LengthUnits.NauticalMiles:
        return "NauticalMiles";

      case LengthUnits.Cables:
        return "Cables";

      case LengthUnits.Fathoms:
        return "Fathoms";

      case LengthUnits.Leagues:
        return "Leagues";

      case LengthUnits.Miles:
        return "Miles";

      case LengthUnits.Furlongs:
        return "Furlongs";

      case LengthUnits.Chains:
        return "Chains";

      case LengthUnits.Yards:
        return "Yards";

      case LengthUnits.Inches:
        return "in.";

      case LengthUnits.MilliInches:
        return "mils";

      case LengthUnits.Feet:
        return "ft";

      case LengthUnits.Kilometers:
        return "km";

      case LengthUnits.Hectometers:
        return "Hectometers";

      case LengthUnits.Dekameters:
        return "Dekameters";

      case LengthUnits.Meters:
        return "m";

      case LengthUnits.Decimeters:
        return "Decimeters";

      case LengthUnits.Centimeters:
        return "cm";

      case LengthUnits.Millimeters:
        return "mm";

      case LengthUnits.Micrometers:
        return "μm";

      case LengthUnits.Nanometers:
        return "nm";
    }
  }
}

enum FormatType { SI, ImperialFeet, ImperialFeetInches }

class Length {
  late final double _lengthInMeters;

  Length.fromUnit(double len, LengthUnits unit) {
    switch (unit) {
      case LengthUnits.NauticalMiles:
        _lengthInMeters = _convertNauticalMilesToMeters(len);
        break;

      case LengthUnits.Cables:
        _lengthInMeters = _convertCablesToMeters(len);
        break;

      case LengthUnits.Fathoms:
        _lengthInMeters = _convertFathomsToMeters(len);
        break;

      case LengthUnits.Leagues:
        _lengthInMeters = _convertLeaguesToMeters(len);
        break;

      case LengthUnits.Miles:
        _lengthInMeters = _convertMilesToMeters(len);
        break;

      case LengthUnits.Furlongs:
        _lengthInMeters = _convertFurlongsToMeters(len);
        break;

      case LengthUnits.Chains:
        _lengthInMeters = _convertChainsToMeters(len);
        break;

      case LengthUnits.Yards:
        _lengthInMeters = _convertYardsToMeters(len);
        break;

      case LengthUnits.Inches:
        _lengthInMeters = _convertInchesToMeters(len);
        break;

      case LengthUnits.MilliInches:
        _lengthInMeters = _convertMilliInchesToMeters(len);
        break;

      case LengthUnits.Feet:
        _lengthInMeters = _convertFeetToMeters(len);
        break;

      case LengthUnits.Kilometers:
        _lengthInMeters = _convertKilometersToMeters(len);
        break;

      case LengthUnits.Hectometers:
        _lengthInMeters = _convertHectometersToMeters(len);
        break;

      case LengthUnits.Dekameters:
        _lengthInMeters = _convertDekametersToMeters(len);
        break;

      case LengthUnits.Meters:
        _lengthInMeters = len;
        break;

      case LengthUnits.Decimeters:
        _lengthInMeters = _convertDecimetersToMeters(len);
        break;

      case LengthUnits.Centimeters:
        _lengthInMeters = _convertCentimetersToMeters(len);
        break;

      case LengthUnits.Millimeters:
        _lengthInMeters = _convertMillimetersToMeters(len);
        break;

      case LengthUnits.Micrometers:
        _lengthInMeters = _convertMicrometersToMeters(len);
        break;

      case LengthUnits.Nanometers:
        _lengthInMeters = _convertNanometersToMeters(len);
        break;
    }
  }

  Length.fromNauticalMiles(double len) {
    _lengthInMeters = _convertNauticalMilesToMeters(len);
  }

  Length.fromCables(double len) {
    _lengthInMeters = _convertCablesToMeters(len);
  }

  Length.fromFathoms(double len) {
    _lengthInMeters = _convertFathomsToMeters(len);
  }

  Length.fromLeagues(double len) {
    _lengthInMeters = _convertLeaguesToMeters(len);
  }

  Length.fromMiles(double len) {
    _lengthInMeters = _convertMilesToMeters(len);
  }

  Length.fromFurlongs(double len) {
    _lengthInMeters = _convertNauticalMilesToMeters(len);
  }

  Length.fromChains(double len) {
    _lengthInMeters = _convertChainsToMeters(len);
  }

  Length.fromYards(double len) {
    _lengthInMeters = _convertYardsToMeters(len);
  }

  Length.fromInches(double len) {
    _lengthInMeters = _convertInchesToMeters(len);
  }

  Length.fromMilliInches(double len) {
    _lengthInMeters = _convertMilliInchesToMeters(len);
  }

  Length.fromFeet(double len) {
    _lengthInMeters = _convertFeetToMeters(len);
  }

  Length.fromKilometers(double len) {
    _lengthInMeters = _convertKilometersToMeters(len);
  }

  Length.fromHectometers(double len) {
    _lengthInMeters = _convertHectometersToMeters(len);
  }

  Length.fromDekameters(double len) {
    _lengthInMeters = _convertDekametersToMeters(len);
  }

  Length.fromMeters(double len) {
    _lengthInMeters = len;
  }

  Length.fromDecimeters(double len) {
    _lengthInMeters = _convertDecimetersToMeters(len);
  }

  Length.fromCentimeters(double len) {
    _lengthInMeters = _convertCentimetersToMeters(len);
  }

  Length.fromMillimeters(double len) {
    _lengthInMeters = _convertMillimetersToMeters(len);
  }

  Length.fromMicrometers(double len) {
    _lengthInMeters = _convertMicrometersToMeters(len);
  }

  Length.fromNanometers(double len) {
    _lengthInMeters = _convertNanometersToMeters(len);
  }

  double? getInUnit(LengthUnits unit) {
    switch (unit) {
      case LengthUnits.NauticalMiles:
        return inNauticalMiles;

      case LengthUnits.Cables:
        return inCables;

      case LengthUnits.Fathoms:
        return inFathoms;

      case LengthUnits.Leagues:
        return inLeagues;

      case LengthUnits.Miles:
        return inMiles;

      case LengthUnits.Furlongs:
        return inFurlongs;

      case LengthUnits.Chains:
        return inChains;

      case LengthUnits.Yards:
        return inYards;

      case LengthUnits.Inches:
        return inInches;

      case LengthUnits.MilliInches:
        return inMilliInches;

      case LengthUnits.Feet:
        return inFeet;

      case LengthUnits.Kilometers:
        return inKilometers;

      case LengthUnits.Hectometers:
        return inHectometers;

      case LengthUnits.Dekameters:
        return inDekameters;

      case LengthUnits.Meters:
        return inMeters;

      case LengthUnits.Decimeters:
        return inDecimeters;

      case LengthUnits.Centimeters:
        return inCentimeters;

      case LengthUnits.Millimeters:
        return inMillimeters;

      case LengthUnits.Micrometers:
        return inMicrometers;

      case LengthUnits.Nanometers:
        return inNanometers;
    }
  }

  double? get inMeters {
    return _lengthInMeters;
  }

  double? get inNauticalMiles {
    return _convertMetersToNauticalMiles(_lengthInMeters);
  }

  double? get inCables {
    return _convertMetersToCables(_lengthInMeters);
  }

  double? get inFathoms {
    return _convertMetersToFathoms(_lengthInMeters);
  }

  double? get inLeagues {
    return _convertMetersToLeagues(_lengthInMeters);
  }

  double? get inMiles {
    return _convertMetersToMiles(_lengthInMeters);
  }

  double? get inFurlongs {
    return _convertMetersToFurlongs(_lengthInMeters);
  }

  double? get inChains {
    return _convertMetersToChains(_lengthInMeters);
  }

  double? get inYards {
    return _convertMetersToYards(_lengthInMeters);
  }

  double? get inInches {
    return _convertMetersToInches(_lengthInMeters);
  }

  double? get inMilliInches {
    return _convertMetersToMilliInches(_lengthInMeters);
  }

  double? get inFeet {
    return _convertMetersToFeet(_lengthInMeters);
  }

  double? get inKilometers {
    return _convertMetersToKilometers(_lengthInMeters);
  }

  double? get inHectometers {
    return _convertMetersToHectometers(_lengthInMeters);
  }

  double? get inDekameters {
    return _convertMetersToDekameters(_lengthInMeters);
  }

  double? get inDecimeters {
    return _convertMetersToDecimeters(_lengthInMeters);
  }

  double? get inCentimeters {
    return _convertMetersToCentimeters(_lengthInMeters);
  }

  double? get inMillimeters {
    return _convertMetersToMillimeters(_lengthInMeters);
  }

  double? get inMicrometers {
    return _convertMetersToMicrometers(_lengthInMeters);
  }

  double? get inNanometers {
    return _convertMetersToNanometers(_lengthInMeters);
  }

  @override
  bool operator ==(Object other) =>
      other is Length &&
      other.runtimeType == runtimeType &&
      other._lengthInMeters == _lengthInMeters;

  @override
  int get hashCode => _lengthInMeters.hashCode;

  double _convertNauticalMilesToMeters(double len) {
    return len * 1852;
  }

  double _convertCablesToMeters(double len) {
    return len * 185.2;
  }

  double _convertFathomsToMeters(double len) {
    return len * 1.8288;
  }

  double _convertLeaguesToMeters(double len) {
    return len * 185.2;
  }

  double _convertMilesToMeters(double len) {
    return len * 1609.34;
  }

  double _convertFurlongsToMeters(double len) {
    return len * 201.168;
  }

  double _convertChainsToMeters(double len) {
    return len * 20.1168;
  }

  double _convertYardsToMeters(double len) {
    return len * 0.9144;
  }

  double _convertInchesToMeters(double len) {
    return len * 0.0254;
  }

  double _convertMilliInchesToMeters(double len) {
    return len * 2.54e-5;
  }

  double _convertFeetToMeters(double len) {
    return len * 0.3048;
  }

  double _convertKilometersToMeters(double len) {
    return len * 1000;
  }

  double _convertHectometersToMeters(double len) {
    return len * 100;
  }

  double _convertDekametersToMeters(double len) {
    return len * 10;
  }

  double _convertDecimetersToMeters(double len) {
    return len * 0.1;
  }

  double _convertCentimetersToMeters(double len) {
    return len * 0.01;
  }

  double _convertMillimetersToMeters(double len) {
    return len * 0.001;
  }

  double _convertMicrometersToMeters(double len) {
    return len * 1e-6;
  }

  double _convertNanometersToMeters(double len) {
    return len * 1e-9;
  }

  double _convertMetersToNauticalMiles(double len) {
    return len * 0.000539957;
  }

  double _convertMetersToCables(double len) {
    return len * 0.000539957;
  }

  double _convertMetersToFathoms(double len) {
    return len * 0.546807;
  }

  double _convertMetersToLeagues(double len) {
    return len * 0.000179986;
  }

  double _convertMetersToMiles(double len) {
    return len * 0.000621371;
  }

  double _convertMetersToFurlongs(double len) {
    return len * 0.00497096;
  }

  double _convertMetersToChains(double len) {
    return len * 0.0497097;
  }

  double _convertMetersToYards(double len) {
    return len * 1.09361;
  }

  double _convertMetersToInches(double len) {
    return len * 39.3701;
  }

  double _convertMetersToMilliInches(double len) {
    return len * 39370.08;
  }

  double _convertMetersToFeet(double len) {
    return len * 3.28084;
  }

  double _convertMetersToKilometers(double len) {
    return len * 0.001;
  }

  double _convertMetersToHectometers(double len) {
    return len * 0.01;
  }

  double _convertMetersToDekameters(double len) {
    return len * 0.1;
  }

  double _convertMetersToDecimeters(double len) {
    return len * 10;
  }

  double _convertMetersToCentimeters(double len) {
    return len * 100;
  }

  double _convertMetersToMillimeters(double len) {
    return len * 1000;
  }

  double _convertMetersToMicrometers(double len) {
    return len * 1000000;
  }

  double _convertMetersToNanometers(double len) {
    return len * 1e+9;
  }
}
