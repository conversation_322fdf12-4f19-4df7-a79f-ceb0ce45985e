//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section653_Asset_Walkdown_Details_F
{
  public class SectionOperatingDesignConditions : DataModelItem {

    public override String DisplayName { 
      get {
        return "Operating/Design Conditions";
      }
    }

    #region [-- Sub Sections --]

    #region [-- SectionTankOutOfServiceRequirements --]
    private SectionTankOutOfServiceRequirements _sectionTankOutOfServiceRequirements;
    public SectionTankOutOfServiceRequirements sectionTankOutOfServiceRequirements {
        get {
            if (_sectionTankOutOfServiceRequirements == null) {
               _sectionTankOutOfServiceRequirements = new SectionTankOutOfServiceRequirements(this);
            }

            return _sectionTankOutOfServiceRequirements;
        }
    }
    #endregion [-- SectionTankOutOfServiceRequirements --]
    
    #region [-- SectionRegulatoryRequirements --]
    private DataModelCollection<SectionRegulatoryRequirements> _sectionRegulatoryRequirements;
    public DataModelCollection<SectionRegulatoryRequirements> sectionRegulatoryRequirements {
        get {
            if (_sectionRegulatoryRequirements == null) {
              _sectionRegulatoryRequirements = new DataModelCollection<SectionRegulatoryRequirements>("Regulatory Requirements", (parent, entry) => {
                 return new SectionRegulatoryRequirements(entry.Key, _sectionRegulatoryRequirements);
              }, (parent, id) => {
                return new SectionRegulatoryRequirements(id, _sectionRegulatoryRequirements);
              }, this);
            }

            return _sectionRegulatoryRequirements;
        }
    }
    #endregion [-- SectionRegulatoryRequirements --]
    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public StringAttribute attributeCurrent_service;
    public DoubleAttribute attributeDesign_Temp;
    public IntegerAttribute attributeCurrent_Operating_Temperature;
    public IntegerAttribute attributeCurrent_Fill_level_if_available;
    public PredefinedValueAttribute attributeOperation_Status;
    public PredefinedValueAttribute attributeIs_the_tank_equipped_with_VRU;
    public PredefinedValueAttribute attributeTank_equipped_with_Leak_Detection;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionOperatingDesignConditions";

    public SectionOperatingDesignConditions(DataModelItem parent) : base(parent)
    {
            
        attributeCurrent_service = new StringAttribute(this, displayName: "Current service", databaseName: "653AW_Q305"); 
     
        attributeDesign_Temp = new DoubleAttribute(this, displayName: "Design Temp", databaseName: "653AW_Q306", areCommentsRequired: false, displayUnit: null, allowNegatives: true); 
     
        attributeCurrent_Operating_Temperature = new IntegerAttribute(this, displayName: "Current Operating Temperature:", databaseName: "653AW_Q307", areCommentsRequired: false, displayUnit: null, allowNegatives: true); 
     
        attributeCurrent_Fill_level_if_available = new IntegerAttribute(this, displayName: "Current Fill level if available:", databaseName: "653AW_Q308", areCommentsRequired: false, displayUnit: null, allowNegatives: true); 
     
        attributeOperation_Status = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("In-Service", null),
          new PredefinedValueOption("Out-Of-Service", null),
          new PredefinedValueOption("Temp OOS for Insp", null)
        }, false, this, "Operation Status", databaseName: "653AW_Q309"); 
     
        attributeIs_the_tank_equipped_with_VRU = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Uknown", null)
        }, false, this, "Is the tank equipped with VRU?", databaseName: "653AW_Q310"); 
     
        attributeTank_equipped_with_Leak_Detection = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("N/A", null)
        }, false, this, "Tank equipped with Leak Detection?", databaseName: "653AW_Q311"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           sectionTankOutOfServiceRequirements,
           sectionRegulatoryRequirements,
           attributeCurrent_service,
           attributeDesign_Temp,
           attributeCurrent_Operating_Temperature,
           attributeCurrent_Fill_level_if_available,
           attributeOperation_Status,
           attributeIs_the_tank_equipped_with_VRU,
           attributeTank_equipped_with_Leak_Detection,
        };
    }
  }
}
