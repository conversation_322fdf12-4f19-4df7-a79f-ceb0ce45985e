import 'package:api_inspection/generic/AttributeControls/String/StringAttributeViewEditable.dart';
import 'package:api_inspection/generic/AttributeControls/String/StringAttributeViewNonEditable.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
import 'package:flutter/material.dart';

import '../AttributeStructure.dart';

class StringAttributeView extends StatefulWidget {
  final StringAttribute _attribute;
  final IsEditableController? editingController;
  final bool showPhotos;
  final bool showComments;

  const StringAttributeView(this._attribute,
      {Key? key,
      this.editingController,
      this.showPhotos = true,
      this.showComments = true})
      : super(key: key);

  @override
  _StringAttributeViewState createState() => _StringAttributeViewState();
}

class _StringAttributeViewState extends State<StringAttributeView> {
  @override
  Widget build(BuildContext context) {
    return AttributeStructure(widget._attribute,
        showComments: widget.showComments,
        showPhotos: widget.showPhotos,
        editingController: widget.editingController,
        editingBuilder: (context, updateListener) {
      return StringAttributeViewEditable(widget._attribute, updateListener);
    }, nonEditingBuilder: (context) {
      return StringAttributeViewNonEditable(widget._attribute);
    });
  }
}
