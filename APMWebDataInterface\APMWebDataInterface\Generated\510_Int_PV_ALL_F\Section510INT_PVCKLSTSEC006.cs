//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Int_PV_ALL_F
{
  public class Section510INT_PVCKLSTSEC006 : DataModelItem {

    public override String DisplayName { 
      get {
        return "CHANNEL COVER - HEX ONLY";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC006Q001;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC006Q002;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC006Q003;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC006Q004;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC006Q005;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC006Q006;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC006Q007;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC006Q008;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510INT_PVCKLSTSEC006";

    public Section510INT_PVCKLSTSEC006(DataModelItem parent) : base(parent)
    {
            
        attribute510INT_PVCKLSTSEC006Q001 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Impingement", null),
          new PredefinedValueOption("Yes: Pitting", null)
        }, false, this, "Were any corrosion cells, impingement or pitting noted on the channel cover surfaces: (The dimensions and locations of any damage shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC006_Q001"); 
     
        attribute510INT_PVCKLSTSEC006Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Cracking", null),
          new PredefinedValueOption("Yes: Mechanical Damage", null)
        }, false, this, "Was any mechanical damage or cracking noted on the channel cover surfaces: (The dimensions and location of any damage shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC006_Q002"); 
     
        attribute510INT_PVCKLSTSEC006Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Erosion", null)
        }, false, this, "Were there indications of corrosion or erosion of the channel cover:", databaseName: "510_INT-PV_CKLST_SEC006_Q003"); 
     
        attribute510INT_PVCKLSTSEC006Q004 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Deformation", null),
          new PredefinedValueOption("Yes: Hot Spots", null)
        }, false, this, "Does the channel cover have any deformations or hot spots: (Bulges, Blisters, Dimpling)", databaseName: "510_INT-PV_CKLST_SEC006_Q004"); 
     
        attribute510INT_PVCKLSTSEC006Q005 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are channel cover penetrations and adjacent areas in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC006_Q005"); 
     
        attribute510INT_PVCKLSTSEC006Q006 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Impacts", null),
          new PredefinedValueOption("Yes: Mechanical Damage", null)
        }, false, this, "Was any mechanical damage or impacts from objects noted on the channel cover:", databaseName: "510_INT-PV_CKLST_SEC006_Q006"); 
     
        attribute510INT_PVCKLSTSEC006Q007 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are the channel cover to channel  flanged connection(s) in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC006_Q007"); 
     
        attribute510INT_PVCKLSTSEC006Q008 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the channel cover of the asset in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC006_Q008"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute510INT_PVCKLSTSEC006Q001,
           attribute510INT_PVCKLSTSEC006Q002,
           attribute510INT_PVCKLSTSEC006Q003,
           attribute510INT_PVCKLSTSEC006Q004,
           attribute510INT_PVCKLSTSEC006Q005,
           attribute510INT_PVCKLSTSEC006Q006,
           attribute510INT_PVCKLSTSEC006Q007,
           attribute510INT_PVCKLSTSEC006Q008,
        };
    }
  }
}
