import 'dart:developer';
import 'dart:typed_data';

import 'package:api_inspection/generic/DataModelCommon/DataModel/user.dart';
import 'package:api_inspection/environment.dart';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:api_inspection/generic/DataModelCommon/DatabaseInterfaces/FirebaseDatabaseHelper.dart';
import 'package:api_inspection/generic/UI/Login/EmailRegistrationPage.dart';
import 'package:api_inspection/generic/AppRoot.dart';
import 'package:api_inspection/generic/AppStyle.dart';
import 'package:encrypt/encrypt.dart' as Encrypt;
import 'dart:convert';
import 'package:package_info_plus/package_info_plus.dart';

//import 'dart:html';
class LoginPage extends StatefulWidget {
  final String title = "APM Login";
  final Future Function(BuildContext) onLoginSuccessful;

  const LoginPage(
      {required this.onLoginSuccessful,
      Key? key,
      this.launchedFromPopup = false})
      : super(key: key);
  final bool launchedFromPopup;

  @override
  _LoginPageState createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  bool authenticating = false;
  String? errorMessage;
  bool hidePassword = true;

  TextEditingController emailController = TextEditingController();
  TextEditingController passwordController = TextEditingController();

  bool initialized = false;
  String version = "Unknown";

  void hidePasswordClicked() {
    setState(() {
      hidePassword = !hidePassword;
    });
  }

  @override
  void initState() {
    super.initState();
    init();
  }

  void registerNewEmail() {
    Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => EmailRegistrationPage(
                    onLoginSuccessful: widget.onLoginSuccessful)))
        .then((value) => setState(() {}));
  }

  void startOfflineAuth() async {
    // Check mounted property to ensure object is still in scope.
    if (!mounted) return;

    setState(() {
      authenticating = true;
      errorMessage = null;
    });

    var savedEmail = emailController.text;

    var email = savedEmail.replaceAll(".", "|").toLowerCase().trim();

    var databaseReference = FirebaseDatabaseHelper.global().databaseReference;

    AppRoot.global()
        .sharedPreferences
        .setString("savedEmail", emailController.text.trim());

    var userProfile =
        await databaseReference.collection('users').doc(email).get();

    var profile = UserProfile(savedEmail.trim());
    profile.updateFromMap(userProfile.data());
    AppRoot.global().currentUser = profile;

    var unverifiedUserDoc =
        await databaseReference.collection('nonverifiedusers').doc(email).get();

    var unverifiedProfile = UnverifiedUserProfile(savedEmail.trim());
    unverifiedProfile.updateFromMap(unverifiedUserDoc.data());
    AppRoot.global().currentUserNonVerified = unverifiedProfile;

    if (profile.role == "admin" || profile.role == "technician") {
      AppRoot.global().startLoginAttempts(
          email: emailController.text, password: passwordController.text);

      await widget.onLoginSuccessful(context);
    } else {
      setState(() {
        authenticating = false;
        errorMessage =
            "This account does not have the required permissions to use APM.  \r\n\r\nPlease reach out to your administrator to have this resolved.";
      });
    }
  }

  void startAuth(String emailIn, String pw) async {
    try {
      // Check mounted property to ensure object is still in scope.
      if (!mounted) return;

      setState(() {
        authenticating = true;
        errorMessage = null;
      });
      FirebaseAuth auth = FirebaseAuth.instance;

      var user =
          await auth.signInWithEmailAndPassword(email: emailIn, password: pw);

      var savedEmail = user.user!.email!;

      var email = savedEmail.replaceAll(".", "|").toLowerCase().trim();

      var databaseReference = FirebaseDatabaseHelper.global().databaseReference;

      AppRoot.global()
          .sharedPreferences
          .setString("savedEmail", emailController.text.trim());

      var userProfile =
          await databaseReference.collection('users').doc(email).get();

      var profile = UserProfile(savedEmail.trim());
      profile.updateFromMap(userProfile.data());
      AppRoot.global().currentUser = profile;

      var unverifiedUserDoc = await databaseReference
          .collection('nonverifiedusers')
          .doc(email)
          .get();

      var unverifiedProfile = UnverifiedUserProfile(savedEmail.trim());
      unverifiedProfile.updateFromMap(unverifiedUserDoc.data());
      AppRoot.global().currentUserNonVerified = unverifiedProfile;

      if (profile.role == "admin" || profile.role == "technician") {
        var key = Encrypt.Key.fromUtf8("x!A%D*G-KaPdSgVkXp2s5v8y/B?E(H+M");
        var iv = Encrypt.IV.fromLength(16);

        var encrypter = Encrypt.Encrypter(Encrypt.AES(key));
        var encrypted = encrypter.encrypt(pw, iv: iv);

        AppRoot.global()
            .sharedPreferences
            .setString("savedCred", encrypted.base64);
        AppRoot.global()
            .sharedPreferences
            .setInt("savedCredTime", DateTime.now().millisecondsSinceEpoch);

        if (widget.launchedFromPopup) {
          Navigator.of(context).pop();
        } else {
          await widget.onLoginSuccessful(context);
        }
      } else {
        setState(() {
          authenticating = false;
          if (initialized) {
            errorMessage =
                "This account does not have the required permissions to use APM.  \r\n\r\nPlease reach out to your administrator to have this resolved.";
          } else {
            initialized = false;
          }
        });
      }
    } on FirebaseAuthException catch (e) {
      if (e.code == "network-request-failed") {
        startOfflineAuth();
        return;
      }
      log(e.toString(), name: 'LoginPage');
      setState(() {
        authenticating = false;
        if (initialized) {
          if (e.code == "invalid-email" ||
              e.code == "wrong-password" ||
              e.code == "user-not-found") {
            errorMessage = "Invalid email or password";
          } else if (e.code == "user-disabled") {
            errorMessage = "The specified account has been disabled";
          } else {
            errorMessage = e.message;
          }
        }

        initialized = true;
      });
    }
  }

  void resetPassword() {
    try {
      FirebaseAuth.instance
          .sendPasswordResetEmail(email: emailController.text.trim());
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: const Color.fromARGB(255, 41, 45, 52),
            title: const Text(
              'Password reset',
              style: TextStyle(color: Colors.white),
            ),
            content: const Text("A password reset email has been sent.",
                style: TextStyle(color: Colors.white)),
            actions: [
              ElevatedButton(
                child: const Text('Ok', style: TextStyle(color: Colors.white)),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        },
      );
    } on FirebaseAuthException catch (e) {
      errorMessage = e.message;
    }
  }

  void init() async {
    PackageInfo.fromPlatform().then((PackageInfo packageInfo) {
      setState(() {
        version = packageInfo.version;
      });
    });

    String? savedEmail =
        AppRoot.global().sharedPreferences.get("savedEmail") as String?;
    String? savedPWBase64 =
        AppRoot.global().sharedPreferences.get("savedCred") as String?;

    Uint8List? savedPW =
        savedPWBase64 == null ? null : base64.decode(savedPWBase64);

    int? savedPWTimeMilli =
        AppRoot.global().sharedPreferences.get("savedCredTime") as int?;

    DateTime? savedPWTime = savedPWTimeMilli == null
        ? null
        : DateTime.fromMillisecondsSinceEpoch(savedPWTimeMilli);
    if (savedEmail != null) {
      emailController.text = savedEmail;
    }

    if (savedPW == null ||
        savedPWTime == null ||
        savedEmail == null ||
        DateTime.now().difference(savedPWTime) > const Duration(days: 1)) {
      if (savedPW != null) {
        AppRoot.global().sharedPreferences.remove("savedCred");
      }
      initialized = true;
      return;
    }
    try {
      var key = Encrypt.Key.fromUtf8("x!A%D*G-KaPdSgVkXp2s5v8y/B?E(H+M");
      var iv = Encrypt.IV.fromLength(16);

      var encrypter = Encrypt.Encrypter(Encrypt.AES(key));
      var savedPWEncrypt = Encrypt.Encrypted(savedPW);
      //var encrypted = encrypter.encrypt(plainText, iv: iv);
      var decrypted = encrypter.decrypt(savedPWEncrypt, iv: iv);
      startAuth(savedEmail, decrypted);
    } catch (e) {
      initialized = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!initialized) {
      return Scaffold(
          appBar: AppBar(
            title: const Text(
              "Asset Performance Management",
              style: TextStyle(fontSize: 20),
            ),
            toolbarHeight: 55,
          ),
          backgroundColor: const Color.fromARGB(255, 24, 28, 32),
          body: Container(
              alignment: Alignment.center,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "Version: " + version,
                    style: const TextStyle(color: Colors.white, fontSize: 26),
                  ),
                  const Text(
                    "Loading",
                    style: TextStyle(color: Colors.white, fontSize: 26),
                  ),
                  const Text(
                    "please wait",
                    style: TextStyle(color: Colors.white, fontSize: 18),
                  )
                ],
              )));
    }

    var height = MediaQuery.of(context).size.height;
    // ignore: unused_local_variable
    var width = MediaQuery.of(context).size.width;

    var imageHeight = height / 6;
    var logoHeight = height / 16;
    Color backgroundColor = const Color.fromARGB(255, 24, 28, 32);
    Widget header;
    if (AppRoot.global().environment == EnvironmentValue.production) {
      header = SizedBox(
        height: imageHeight,
        child: Stack(
          fit: StackFit.expand,
          children: [
            FittedBox(
                clipBehavior: Clip.hardEdge,
                fit: BoxFit.fitWidth,
                child: Image.asset("assets/home_background.png")),
            Align(
                alignment: Alignment.topLeft,
                child: Container(
                    margin: const EdgeInsets.all(10),
                    height: logoHeight,
                    child: Image.asset("assets/logo.png")))
          ],
        ),
      );
    } else {
      header = SizedBox(
        height: imageHeight,
        child: Stack(
          fit: StackFit.expand,
          children: [
            Container(
              alignment: Alignment.bottomCenter,
              child: Container(
                  padding: const EdgeInsets.fromLTRB(40, 5, 40, 5),
                  color: Colors.deepOrange[900]!,
                  child: const Text("Development",
                      style: TextStyle(color: Colors.white, fontSize: 30))),
            ),
            Align(
                alignment: Alignment.topLeft,
                child: Container(
                    margin: const EdgeInsets.all(10),
                    height: logoHeight,
                    child: Image.asset("assets/logo.png")))
          ],
        ),
      );
    }

    if (authenticating) {
      return Scaffold(
          appBar: AppBar(
            toolbarHeight: 0,
          ),
          backgroundColor: backgroundColor,
          body: SingleChildScrollView(
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                header,
                Container(
                  alignment: Alignment.center,
                  margin: const EdgeInsets.all(25),
                  child: const Text("Asset Performance Management",
                      style: TextStyle(
                          fontSize: 18,
                          color: Colors.white,
                          fontWeight: FontWeight.w500)),
                ),
                Container(
                  alignment: Alignment.center,
                  margin: const EdgeInsets.all(25),
                  child: const Text("Authenticating..",
                      style: TextStyle(
                          fontSize: 18,
                          color: Colors.white,
                          fontWeight: FontWeight.w500)),
                )
              ])));
    }

    Widget errorField;
    String? error = errorMessage;
    Widget resetPasswordWidget;
    if (error == null) {
      errorField = Container();
      resetPasswordWidget = Container();
    } else {
      errorField = Container(
        margin: const EdgeInsets.fromLTRB(20, 10, 20, 5),
        alignment: Alignment.bottomCenter,
        child: Text(error,
            style: const TextStyle(
                fontSize: 18, color: Colors.red, fontWeight: FontWeight.w500)),
      );
      resetPasswordWidget = Container(
        alignment: Alignment.center,
        child: TextButton(
            onPressed: resetPassword,
            child: Container(
                width: 200,
                height: 24,
                margin: const EdgeInsets.all(10),
                alignment: Alignment.center,
                child: const Text(
                  "Reset Password",
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ))),
      );
    }

    var textFieldDecoration = const InputDecoration(
      contentPadding: EdgeInsets.fromLTRB(10, 3, 10, 3),
      enabledBorder: OutlineInputBorder(
        borderSide: BorderSide(color: Colors.white, width: 1.0),
      ),
      border: OutlineInputBorder(
        borderSide: BorderSide(color: Colors.white, width: 1.0),
      ),
    );

    return Scaffold(
        appBar: AppBar(
          toolbarHeight: 0,
        ),
        backgroundColor: backgroundColor,
        body: SingleChildScrollView(
            child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            header,
            Container(
                alignment: Alignment.center,
                margin: const EdgeInsets.fromLTRB(25, 25, 25, 0),
                child: const FittedBox(
                  fit: BoxFit.contain,
                  child: Text("Asset Performance Management",
                      style: TextStyle(
                          fontSize: 18,
                          color: Colors.white,
                          fontWeight: FontWeight.w500)),
                )),
            Container(
              alignment: Alignment.center,
              margin: const EdgeInsets.fromLTRB(0, 0, 0, 10),
              child: Text("Version " + version,
                  style: const TextStyle(fontSize: 14, color: Colors.white)),
            ),
            errorField,
            Container(
              alignment: Alignment.center,
              child: Container(
                alignment: Alignment.topLeft,
                width: 300,
                margin: const EdgeInsets.all(5),
                child: const Text("Email",
                    style: TextStyle(
                        fontSize: 18,
                        color: Colors.white,
                        fontWeight: FontWeight.w500)),
              ),
            ),
            Center(
              child: Container(
                constraints:
                    BoxConstraints(minHeight: AppStyle.global.textboxSizeLarge),
                width: 300,
                child: TextField(
                    minLines: 1,
                    maxLines: 3,
                    decoration: textFieldDecoration,
                    style: const TextStyle(color: Colors.white),
                    key: const ValueKey('emailtextbox'),
                    controller: emailController),
              ),
            ),
            Container(
              alignment: Alignment.center,
              child: Container(
                alignment: Alignment.topLeft,
                width: 300,
                margin: const EdgeInsets.all(5),
                child: const Text(
                  "Password",
                  style: TextStyle(
                      fontSize: 18,
                      color: Colors.white,
                      fontWeight: FontWeight.w500),
                ),
              ),
            ),
            Center(
                child: SizedBox(
                    width: 300,
                    height: AppStyle.global.textboxSizeLarge,
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                              autocorrect: false,
                              enableSuggestions: false,
                              keyboardType: TextInputType.visiblePassword,
                              decoration: textFieldDecoration,
                              obscureText: hidePassword,
                              style: const TextStyle(color: Colors.white),
                              key: const ValueKey('passwordtextbox'),
                              controller: passwordController),
                        ),
                        IconButton(
                            onPressed: hidePasswordClicked,
                            icon: Icon(
                                hidePassword
                                    ? Icons.remove_red_eye_outlined
                                    : Icons.remove_red_eye,
                                color: Colors.white)),
                      ],
                    ))),
            Container(
              alignment: Alignment.center,
              margin: const EdgeInsets.all(20),
              child: ElevatedButton(
                  onPressed: () {
                    startAuth(
                        emailController.text.trim(), passwordController.text);
                  },
                  child: Container(
                      width: 100,
                      height: AppStyle.global.buttonSizeRegular,
                      margin: const EdgeInsets.all(10),
                      alignment: Alignment.center,
                      child: const Text(
                        "Login",
                        style: TextStyle(
                            fontSize: 16, fontWeight: FontWeight.bold),
                        key: ValueKey('loginbtn'),
                      ))),
            ),
            resetPasswordWidget,
            Container(
              alignment: Alignment.center,
              child: TextButton(
                  onPressed: registerNewEmail,
                  child: Container(
                      width: 200,
                      height: AppStyle.global.buttonSizeRegular,
                      margin: const EdgeInsets.all(10),
                      alignment: Alignment.center,
                      child: const Text(
                        "Register New Email",
                        style: TextStyle(
                            fontSize: 16, fontWeight: FontWeight.bold),
                      ))),
            ),
          ],
        )));
  }
}
