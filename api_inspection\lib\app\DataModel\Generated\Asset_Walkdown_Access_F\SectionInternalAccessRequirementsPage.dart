//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionInternalAccessRequirements.dart';

// ignore: camel_case_types
class SectionInternalAccessRequirementsPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionInternalAccessRequirements sectionInternalAccessRequirements;

  const SectionInternalAccessRequirementsPage(
      this.sectionInternalAccessRequirements,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionInternalAccessRequirementsPageState();
  }
}

class _SectionInternalAccessRequirementsPageState
    extends State<SectionInternalAccessRequirementsPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionInternalAccessRequirements,
        title: "Internal Access Requirements",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionInternalAccessRequirements
                      .attributeAre_there_inspection_openings
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionInternalAccessRequirements
                      .attributeInspection_opening_Types
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionInternalAccessRequirements
                      .attributeSize_of_all_accessible_openings
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionInternalAccessRequirements
                      .attributeVentilation_requirements
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
