﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.29709.97
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "API_Inspection_ImageFunctions", "API_Inspection_ImageFunctions\API_Inspection_ImageFunctions.csproj", "{9C64637B-0A8C-4872-BA8A-2C3D3FF02FCE}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{9C64637B-0A8C-4872-BA8A-2C3D3FF02FCE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9C64637B-0A8C-4872-BA8A-2C3D3FF02FCE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9C64637B-0A8C-4872-BA8A-2C3D3FF02FCE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9C64637B-0A8C-4872-BA8A-2C3D3FF02FCE}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {D0E0B682-F280-41FD-B997-5F65AE5638DE}
	EndGlobalSection
EndGlobal
