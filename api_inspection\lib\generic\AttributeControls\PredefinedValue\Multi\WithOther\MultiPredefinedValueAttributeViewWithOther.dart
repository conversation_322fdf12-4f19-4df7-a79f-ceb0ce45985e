import 'package:api_inspection/generic/AttributeControls/AttributeStructure.dart';
import 'package:api_inspection/generic/AttributeControls/PredefinedValue/Multi/WithOther/MultiPredefinedValueAttributeViewWithOtherEditable.dart';
import 'package:api_inspection/generic/AttributeControls/PredefinedValue/Multi/MultiPredefinedValueAttributeViewNonEditable.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
import 'package:flutter/material.dart';

class MultiPredefinedValueAttributeViewWithOther extends StatefulWidget {
  final MultiPredefinedValueAttribute _attribute;
  final IsEditableController? editingController;
  final bool showPhotos;
  final bool showComments;

  const MultiPredefinedValueAttributeViewWithOther(this._attribute,
      {Key? key,
      this.editingController,
      this.showPhotos = true,
      this.showComments = true})
      : super(key: key);

  @override
  _MultiPredefinedValueAttributeViewWithOtherState createState() =>
      _MultiPredefinedValueAttributeViewWithOtherState();
}

class _MultiPredefinedValueAttributeViewWithOtherState
    extends State<MultiPredefinedValueAttributeViewWithOther> {
  @override
  Widget build(BuildContext context) {
    return AttributeStructure(widget._attribute,
        showComments: widget.showComments,
        showPhotos: widget.showPhotos,
        editingController: widget.editingController,
        editingBuilder: (context, updateListener) {
      return MultiPredefinedValueAttributeViewWithOtherEditable(
          widget._attribute, updateListener);
    }, nonEditingBuilder: (context) {
      return MultiPredefinedValueAttributeViewNonEditable(widget._attribute);
    });
  }
}
