import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/Generated/Asset_Walkdown_Access_F/SectionAsset_Walkdown_Access_FPage.dart';
import 'package:api_inspection/app/DataModel/Generated/Asset_Walkdown_PPE_F/SectionAsset_Walkdown_PPE_FPage.dart';
import 'package:api_inspection/app/DataModel/asset.dart';
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'package:api_inspection/generic/AppStyle.dart';

import 'CreateTaskPage.dart';

class AssetEditPage extends StatefulWidget {
  final Asset asset;
  const AssetEditPage(this.asset, {Key? key}) : super(key: key);

  @override
  _AssetEditPageState createState() => _AssetEditPageState();
}

class _AssetEditPageState extends State<AssetEditPage> {
  String getTitle() {
    return "Asset Edit Page";
  }

  void showAssetWalkdown() {
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => widget.asset.buildWalkdownPage()));
  }

  void showAssetPPE() {
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) =>
                SectionAsset_Walkdown_PPE_FPage(widget.asset.assetPPE)));
  }

  void showAssetAccess() {
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) =>
                SectionAsset_Walkdown_Access_FPage(widget.asset.assetAccess)));
  }

  void showErrorPrompt(String message) {
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: const Color.fromARGB(255, 41, 45, 52),
            title: Row(
              children: [
                Container(
                    margin: const EdgeInsets.all(5),
                    child:
                        const Icon(Icons.error, color: Colors.red, size: 32)),
                const Text(
                  'Error',
                  style: TextStyle(color: Colors.white),
                )
              ],
            ),
            content: Text(message, style: const TextStyle(color: Colors.white)),
            actions: [
              ElevatedButton(
                child: const Text('Ok', style: TextStyle(color: Colors.white)),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        });
  }

  var textFieldDecoration = const InputDecoration(
    enabledBorder: OutlineInputBorder(
      borderSide: BorderSide(color: Colors.white, width: 1.0),
    ),
    border: OutlineInputBorder(
      borderSide: BorderSide(color: Colors.white, width: 1.0),
    ),
  );

  @override
  Widget build(BuildContext context) {
    var asset = widget.asset;

    return Scaffold(
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        appBar: AppBar(
          title: Text(
            getTitle(),
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Expanded(
                  child: ListView(
                children: [
                  AttributePadding.WithStdPadding(
                    TeamToggleButton.withText(
                        'Asset Details', showAssetWalkdown),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamToggleButton.withText('Asset Access', showAssetAccess),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamToggleButton.withText('Asset PPE', showAssetPPE),
                  ),
                  AttributePadding.WithStdPadding(
                    widget.asset.area.buildWidget(),
                  ),
                  AttributePadding.WithStdPadding(
                    widget.asset.unit.buildWidget(),
                  )
                ],
              )),
              Align(
                  alignment: Alignment.centerRight,
                  child: Container(
                      margin: const EdgeInsets.all(10),
                      height: 50,
                      child: ElevatedButton(
                          onPressed: () async {
                            Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) =>
                                            CreateTaskPage(asset)))
                                .then((value) => setState(() {}));
                          },
                          child: const Text("Create New Task"))))
            ],
          ),
        ));
  }
}
