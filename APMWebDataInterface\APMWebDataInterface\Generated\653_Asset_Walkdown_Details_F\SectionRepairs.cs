//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section653_Asset_Walkdown_Details_F
{
  public class SectionRepairs : DataModelCollectionItem {

    public override String DisplayName { 
      get {
        return "Repairs";
      }
    }
    
    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public DateAttribute attributeDate_Repaired_or_Altered;
    public StringAttribute attributeRepairAlteration_organization;
    public StringAttribute attributePurpose_of_repairalteration;

    #endregion [-- Attributes --]

    public SectionRepairs(String id, DataModelItem parent) : base(id, parent)
    {
            
        attributeDate_Repaired_or_Altered = new DateAttribute(this, displayName: "Date Repaired or Altered", databaseName: "653AW_Q186", areCommentsRequired: false); 
     
        attributeRepairAlteration_organization = new StringAttribute(this, displayName: "Repair/Alteration organization", databaseName: "653AW_Q187"); 
     
        attributePurpose_of_repairalteration = new StringAttribute(this, displayName: "Purpose of repair/alteration", databaseName: "653AW_Q188"); 
    }   
    
    public override DataModelItem[] GetChildren() {
      var parentChildren = base.GetChildren();

      return parentChildren.Concat(new DataModelItem[] { 
        attributeDate_Repaired_or_Altered,
           attributeRepairAlteration_organization,
           attributePurpose_of_repairalteration,
      }).ToArray();
    }
  }
}
