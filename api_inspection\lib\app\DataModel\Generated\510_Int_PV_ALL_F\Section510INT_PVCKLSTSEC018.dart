//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class Section510INT_PVCKLSTSEC018 extends DataModelSection {
  @override
  String getDisplayName() => "AUXILIARY EQUIPMENT";
  Section510INT_PVCKLSTSEC018(DataModelItem? parent)
      : super(parent: parent, sectionName: "AUXILIARY EQUIPMENT");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC018Q001 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are auxiliary equipment items in acceptable condition for continued service:  (Gauge connections, sight glasses, float wells, etc.)",
          databaseName: "510_INT-PV_CKLST_SEC018_Q001",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC018Q002 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are impingement plates and adjacent shell areas in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC018_Q002",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC018Q003 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are there any threaded connections associated with the vessel:",
          databaseName: "510_INT-PV_CKLST_SEC018_Q003",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeAre_threaded_connections_acceptably_engaged_and_leak_free =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are threaded connections acceptably engaged and leak free:",
          databaseName: "510_INT-PV_CKLST_SEC018_Q004",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC018Q005 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is threaded connection piping constructed from schedule 80 or greater piping:",
          databaseName: "510_INT-PV_CKLST_SEC018_Q005",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeAre_threaded_connections_acceptable_for_continued_service =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are threaded connections acceptable for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC018_Q006",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC018Q007 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Were drain connections verified to be free of any foreign material that may cause plugging:",
          databaseName: "510_INT-PV_CKLST_SEC018_Q007",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attribute510INT_PVCKLSTSEC018Q001,
      attribute510INT_PVCKLSTSEC018Q002,
      attribute510INT_PVCKLSTSEC018Q003,
      attributeAre_threaded_connections_acceptably_engaged_and_leak_free,
      attribute510INT_PVCKLSTSEC018Q005,
      attributeAre_threaded_connections_acceptable_for_continued_service,
      attribute510INT_PVCKLSTSEC018Q007,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section510INT_PVCKLSTSEC018";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
