//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'Section570EXTCKLSTSEC001.dart';

// ignore: camel_case_types
class Section570EXTCKLSTSEC001Page extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final Section570EXTCKLSTSEC001 section570EXTCKLSTSEC001;

  const Section570EXTCKLSTSEC001Page(this.section570EXTCKLSTSEC001, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _Section570EXTCKLSTSEC001PageState();
  }
}

class _Section570EXTCKLSTSEC001PageState
    extends State<Section570EXTCKLSTSEC001Page> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.section570EXTCKLSTSEC001,
        title: "GENERAL INFORMATION",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC001
                      .attributeIs_the_asset_appropriately_prepared_for_inspection
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC001.attribute570EXTCKLSTSEC001Q002
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC001.attribute570EXTCKLSTSEC001Q003
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC001.attribute570EXTCKLSTSEC001Q004
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC001.attribute570EXTCKLSTSEC001Q005
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
