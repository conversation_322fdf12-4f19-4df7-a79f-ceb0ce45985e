﻿using APMWebDataInterface.DataModel.LeakReport;
using APMWebDataInterface.DataModel.LeakReport;
using APMWebDataInterface.ExampleDataModel;
using CommonDataInterface;

namespace APMWebDataInterface
{

  public class FullPackage
  {
    public Asset Asset { get; set; }
    public Project Project { get; set; }

    public APMTask InternalTask { get; set; }
    public APMTask ExternalTask { get; set; }
    
    public WorkOrder WorkOrder { get; set; }
    public Location Location { get; set; }

    public FlatPhotoHolder[] Photos { get; set; }
  }
  
  public class ExternalPackage
  {
    public Asset Asset { get; set; }
    public Project Project { get; set; }

    public APMTask ExternalTask { get; set; }

    public WorkOrder WorkOrder { get; set; }
    public Location Location { get; set; }

    public FlatPhotoHolder[] Photos { get; set; }
  }
  
  public class LeakReportPackage
  {
    public LeakReport LeakReport { get; set; }
    public FlatPhotoHolder[] Photos { get; set; }
  }

  public class SingleTaskReportPackage
  {
    public Asset Asset { get; set; }
    public Project Project { get; set; }

    public APMTask Task { get; set; }

    public WorkOrder WorkOrder { get; set; }
    public Location Location { get; set; }

    public FlatPhotoHolder[] Photos { get; set; }
  }
}
