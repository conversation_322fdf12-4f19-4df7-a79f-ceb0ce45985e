import 'package:api_inspection/generic/AttributeControls/AttributeStructure.dart';
import 'package:api_inspection/generic/AttributeControls/PredefinedValue/Single/Default/PredefinedValueAttributeView.dart';
import 'package:api_inspection/generic/AttributeControls/PredefinedValue/Single/WithOther/PredefinedValueAttributeViewWithOther.dart';
import 'package:api_inspection/generic/AttributeControls/PredefinedValue/Single/YesNoNa/PredefinedValueAttributeView_YesNoNA.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:flutter/material.dart';
import 'StringAttribute.dart';
import 'package:collection/collection.dart';

class PredefinedValueOption {
  String value;
  String? description;

  bool isCommentRequired;

  PredefinedValueOption(this.value, this.description,
      {this.isCommentRequired = false});
}

class PredefinedValueAttribute extends StringAttribute {
  bool hasOtherOption;
  String? unit;
  late List<PredefinedValueOption> availableOptions;

  List<PredefinedValueOption> getOptions() {
    return availableOptions;
  }

  @override
  String getPreviewText() {
    var value = getValue();
    return value == null ? "" : value + (unit == null ? "" : " " + unit!);
  }

  @override
  bool getAreCommentsRequired() {
    var currentValue = getValue();
    if (currentValue != null) {
      var option = getOptions()
          .firstWhereOrNull((element) => element.value == currentValue);
      if (option != null && option.isCommentRequired) return true;
    }
    return false;
  }

  PredefinedValueAttribute(
      {required this.availableOptions,
      required this.hasOtherOption,
      required DataModelItem parent,
      required String displayName,
      Widget? iconWidget,
      bool areCommentsRequired = false,
      String? databaseName,
      this.unit})
      : super(
            parent: parent,
            displayName: displayName,
            iconWidget: iconWidget,
            areCommentsRequired: areCommentsRequired,
            databaseName: databaseName);

  @override
  Widget buildWidget(
      {IsEditableController? editingController,
      bool showPhotos = true,
      bool showComments = true}) {
    if (hasOtherOption) {
      return PredefinedValueAttributeViewWithOther(this,
          showPhotos: showPhotos,
          showComments: showComments,
          editingController: editingController);
    } else {
      return PredefinedValueAttributeView(this,
          showPhotos: showPhotos,
          showComments: showComments,
          editingController: editingController);
    }
  }
}

// ignore: camel_case_types
class PredefinedValueAttribute_YesNoNA extends PredefinedValueAttribute {
  PredefinedValueAttribute_YesNoNA(
      {required List<PredefinedValueOption> availableOptions,
      required DataModelItem parent,
      required String displayName,
      Widget? iconWidget,
      bool areCommentsRequired = false,
      String? databaseName})
      : super(
            availableOptions: availableOptions,
            hasOtherOption: false,
            parent: parent,
            displayName: displayName,
            iconWidget: iconWidget,
            areCommentsRequired: areCommentsRequired,
            databaseName: databaseName);

  @override
  Widget buildWidget(
      {IsEditableController? editingController,
      bool showPhotos = true,
      bool showComments = true}) {
    return PredefinedValueAttributeView_YesNoNA(
      this,
      editingController: editingController,
      showPhotos: showPhotos,
      showComments: showComments,
    );
  }
}
