﻿using System;
using System.Linq;

namespace CommonDataInterface.Attributes
{
  public class BooleanAttribute : SingleAttributeBase<bool?>
  {
    private bool TryCastStringToBool(String str, out bool? casted)
    {
      String[] trueValues = new []{"y", "t", "true", "yes"};
      String[] falseValues = new []{"n", "f", "false", "no"};

      if (String.IsNullOrWhiteSpace(str)) {
        casted = null;
        return true;
      }

      if (trueValues.Any(a => str.Equals(a, StringComparison.InvariantCultureIgnoreCase))) {
        casted = true;
        return true;
      }
      
      if (falseValues.Any(a => str.Equals(a, StringComparison.InvariantCultureIgnoreCase))) {
        casted = false;
        return true;
      }

      if (bool.TryParse(str, out var casted1)) {
        casted = casted1;
        return true;
      }

      casted = null;
      return false;
    }


    public override bool IsValueEqualTo(String other)
    {
      if (TryCastStringToBool(other, out var casted)) {
        var currentValue = GetValue();
        return casted == currentValue;
      }
      else {
        throw new Exception("Unable to interpret boolean: " + other);
      }
    }

    public override void SetGenericValueTo(String other)
    {
      if (TryCastStringToBool(other, out var casted)) {
        SetValue(casted);
      }
      else {
        throw new Exception("Unable to interpret boolean: " + other);
      }
    }


    public override String AttributeType => "Boolean";

    public bool? GetValue() {
      if (this.GetValueChangeLog().entries.Count == 0)
        return null;
      return this.GetValueChangeLog().entries.Last().Value;
    }

    public void SetValue(bool? value){
      if (GetValue() == value)
        return;
        
      GetValueChangeLog().PendingChange = new PendingChange<bool?>{Value = value};

      NotifyListeners();
    }
    
    public BooleanAttribute(DataModelItem parent, String displayName, String databaseName = null, bool areCommentsRequired = false) :
      base(parent, displayName, databaseName, areCommentsRequired)
    {

    }
    
    public override String GetPreviewText(){
      var value = GetValue();
      if (value == null)
        return "";
      if (value == true){
        return "Yes";
      }
      return "No";
    }

  }
}