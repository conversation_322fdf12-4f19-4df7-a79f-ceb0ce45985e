﻿using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.DataModel.Activity
{
  
  public class TaskActivity : DataModelCollectionItem {
    
    public override string DisplayName => "Task Activity";

    public TaskActivity(DataModelItem parent, String id) : base(id, parent)
    {
      date = new DateAttribute(parent: this, displayName: "Date");
      activities = new List<DoubleAttribute>{
        new DoubleAttribute(parent: this, allowNegatives: false, displayName: "Permitting"),
        new DoubleAttribute(parent: this, allowNegatives: false, displayName: "Job Setup"),
        new DoubleAttribute(parent: this, allowNegatives: false, displayName: "Lunch"),
        new DoubleAttribute(parent: this, allowNegatives: false, displayName: "Post CleanUp"),
        new DoubleAttribute(parent: this, allowNegatives: false, displayName: "FW-VT"),
        new DoubleAttribute(parent: this, allowNegatives: false, displayName: "FW-API 510"),
        new DoubleAttribute(parent: this, allowNegatives: false, displayName: "FW-API 570"),
        new DoubleAttribute(parent: this, allowNegatives: false, displayName: "FW-API 653"),
        new DoubleAttribute(parent: this, allowNegatives: false, displayName: "FW-Other"),
      };
      user = new StringAttribute(displayName: "User", isQueryable:true, parent:this);

    }

    public DateAttribute date;
    public StringAttribute user;

    public List<DoubleAttribute> activities;

    double GetTotalHours(){
      double time = 0;
      foreach (var activity in activities){
        var activityTime = activity.GetValue();
        if (activityTime != null)
          time += activityTime.Value;
      }
      return time;
    }

    public override DataModelItem[] GetChildren()
    {
      return base.GetChildren().Concat(new DataModelItem[]{date}).Concat(activities).ToArray();
    }
    
  }
}
