{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\ImageResizing\\apm-mobile\\APMWebDataInterface\\MaggieDataInterface\\MaggieDataInterface.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\ImageResizing\\apm-mobile\\APMWebDataInterface\\CommonDataInterface\\CommonDataInterface.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\ImageResizing\\apm-mobile\\APMWebDataInterface\\CommonDataInterface\\CommonDataInterface.csproj", "projectName": "CommonDataInterface", "projectPath": "C:\\Users\\<USER>\\Desktop\\ImageResizing\\apm-mobile\\APMWebDataInterface\\CommonDataInterface\\CommonDataInterface.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\ImageResizing\\apm-mobile\\APMWebDataInterface\\CommonDataInterface\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "dependencies": {"Azure.Storage.Blobs": {"target": "Package", "version": "[12.13.0, )"}, "FirebaseAuthentication.net": {"target": "Package", "version": "[3.7.2, )"}, "Google.Cloud.Firestore": {"target": "Package", "version": "[3.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.1, )"}, "SixLabors.ImageSharp": {"target": "Package", "version": "[2.1.3, )"}, "SixLabors.ImageSharp.Drawing": {"target": "Package", "version": "[1.0.0-beta13, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\ImageResizing\\apm-mobile\\APMWebDataInterface\\MaggieDataInterface\\MaggieDataInterface.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\ImageResizing\\apm-mobile\\APMWebDataInterface\\MaggieDataInterface\\MaggieDataInterface.csproj", "projectName": "MaggieDataInterface", "projectPath": "C:\\Users\\<USER>\\Desktop\\ImageResizing\\apm-mobile\\APMWebDataInterface\\MaggieDataInterface\\MaggieDataInterface.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\ImageResizing\\apm-mobile\\APMWebDataInterface\\MaggieDataInterface\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net5.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\ImageResizing\\apm-mobile\\APMWebDataInterface\\CommonDataInterface\\CommonDataInterface.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\ImageResizing\\apm-mobile\\APMWebDataInterface\\CommonDataInterface\\CommonDataInterface.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net5.0": {"targetAlias": "net5.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}}