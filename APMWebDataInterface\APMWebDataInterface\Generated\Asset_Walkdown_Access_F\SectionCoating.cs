//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionAsset_Walkdown_Access_F
{
  public class SectionCoating : DataModelItem {

    public override String DisplayName { 
      get {
        return "Coating";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public MultiPredefinedValueAttribute attributeCoating_Type;
    public MultiPredefinedValueAttribute attributeCoating_Condition;
    public MultiPredefinedValueAttribute attributeCoating_Conditions_Observed;
    public PredefinedValueAttribute attributeCoating_Removal_Required;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionCoating";

    public SectionCoating(DataModelItem parent) : base(parent)
    {
            
        attributeCoating_Type = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Paint", null),
          new PredefinedValueOption("FBE", null),
          new PredefinedValueOption("Bitumen", null),
          new PredefinedValueOption("Wrap", null),
          new PredefinedValueOption("None", null)
        }, true, this, "Coating Type", databaseName: "AWA_Q106"); 
     
        attributeCoating_Condition = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Acceptable", null),
          new PredefinedValueOption("Concern", null),
          new PredefinedValueOption("N/A", null)
        }, true, this, "Coating Condition", databaseName: "AWA_Q107"); 
     
        attributeCoating_Conditions_Observed = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Smooth", null),
          new PredefinedValueOption("Chalking", null),
          new PredefinedValueOption("Chipping", null),
          new PredefinedValueOption("Peeling", null),
          new PredefinedValueOption("Blistering", null),
          new PredefinedValueOption("Holiday", null)
        }, true, this, "Coating Conditions Observed", databaseName: "AWA_Q108"); 
     
        attributeCoating_Removal_Required = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Coating Removal Required?", databaseName: "AWA_Q109"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeCoating_Type,
           attributeCoating_Condition,
           attributeCoating_Conditions_Observed,
           attributeCoating_Removal_Required,
        };
    }
  }
}
