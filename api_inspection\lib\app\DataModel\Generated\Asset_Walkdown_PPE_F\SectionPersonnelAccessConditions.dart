//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'SectionStandingWater.dart';
import 'SectionOvergrownvegetation.dart';

// ignore: camel_case_types
class SectionPersonnelAccessConditions extends DataModelSection {
  @override
  String getDisplayName() => "Personnel Access Conditions";
  SectionPersonnelAccessConditions(DataModelItem? parent)
      : super(parent: parent, sectionName: "Personnel Access Conditions");

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeConditions_observed_on_site =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: true,
          displayName: "Conditions observed on site",
          databaseName: "PPEAW_Q100",
          availableOptions: [
        PredefinedValueOption("Clean", null, isCommentRequired: false),
        PredefinedValueOption("Dirty", null, isCommentRequired: false),
        PredefinedValueOption("Debris", null, isCommentRequired: false),
        PredefinedValueOption("Spills", null, isCommentRequired: false),
        PredefinedValueOption("Dry", null, isCommentRequired: false),
        PredefinedValueOption("Mud", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributePower_available =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Power available",
          databaseName: "PPEAW_Q113",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeWater_available =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Water available?",
          databaseName: "PPEAW_Q114",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

// ignore: non_constant_identifier_names
  late SectionStandingWater sectionStandingWater = SectionStandingWater(this);
  // ignore: non_constant_identifier_names
  late SectionOvergrownvegetation sectionOvergrownvegetation =
      SectionOvergrownvegetation(this);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      sectionStandingWater,
      sectionOvergrownvegetation,
      attributeConditions_observed_on_site,
      attributePower_available,
      attributeWater_available,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionPersonnelAccessConditions";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
