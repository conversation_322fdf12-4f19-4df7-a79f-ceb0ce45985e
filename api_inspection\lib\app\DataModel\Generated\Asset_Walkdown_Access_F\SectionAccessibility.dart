//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'SectionAerialLiftRequirements.dart';

// ignore: camel_case_types
class SectionAccessibility extends DataModelSection {
  @override
  String getDisplayName() => "Accessibility";
  SectionAccessibility(DataModelItem? parent)
      : super(parent: parent, sectionName: "Accessibility");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute
      attributeFixed_Equipment_LaddersStairwaysPlatforms_Installed =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Fixed Equipment Ladders/Stairways/Platforms Installed?",
          databaseName: "AWA_Q205",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeAll_components_under_4_ft_in_height =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "All components under 4 ft in height?",
          databaseName: "AWA_Q206",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeLadder_Requirements =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: true,
          displayName: "Ladder Requirements",
          databaseName: "AWA_Q207",
          availableOptions: [
        PredefinedValueOption("Step Ladder", null, isCommentRequired: true),
        PredefinedValueOption("Extension Ladder", null,
            isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeScaffolding_required =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Scaffolding required?",
          databaseName: "AWA_Q240",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: true),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeRope_access_required =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Rope access required?",
          databaseName: "AWA_Q241",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: true),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

// ignore: non_constant_identifier_names
  late SectionAerialLiftRequirements sectionAerialLiftRequirements =
      SectionAerialLiftRequirements(this);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      sectionAerialLiftRequirements,
      attributeFixed_Equipment_LaddersStairwaysPlatforms_Installed,
      attributeAll_components_under_4_ft_in_height,
      attributeLadder_Requirements,
      attributeScaffolding_required,
      attributeRope_access_required,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionAccessibility";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
