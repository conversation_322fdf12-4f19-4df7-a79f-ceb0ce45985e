import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';

import '../AttributeStructure.dart';

class PhotoAttributeView extends StatelessWidget {
  final PhotoAttribute _attribute;
  final IsEditableController? editingController;
  final bool showComments;

  const PhotoAttributeView(this._attribute,
      {Key? key, this.editingController, this.showComments = true})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AttributeStructure(_attribute,
        editingBuilder: (context, updateListener) {
      return Container();
    }, nonEditingBuilder: (context) {
      return Container();
    }, editingController: editingController, showComments: showComments);
  }
}
