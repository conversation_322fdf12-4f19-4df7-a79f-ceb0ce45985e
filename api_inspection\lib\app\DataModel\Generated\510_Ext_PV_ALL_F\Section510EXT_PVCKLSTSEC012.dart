//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class Section510EXT_PVCKLSTSEC012 extends DataModelSection {
  @override
  String getDisplayName() => "PROTECTIVE COATING AND INSULATION";
  Section510EXT_PVCKLSTSEC012(DataModelItem? parent)
      : super(parent: parent, sectionName: "PROTECTIVE COATING AND INSULATION");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC012Q001 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Do the asset supports have a protective coating system applied:",
          databaseName: "510_EXT-PV_CKLST_SEC012_Q001",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeDoes_the_asset_have_a_protective_coating_system_applied =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Does the asset have a protective coating system applied:",
          databaseName: "510_EXT-PV_CKLST_SEC012_Q002",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC012Q003 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the asset insulated: (The type of insulation system, metal jacket with underlying insulation type, blanket, etc., shall be recorded)",
          databaseName: "510_EXT-PV_CKLST_SEC012_Q003",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC012Q004 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the asset heat traced and in acceptable condition for continued service:  (The type of heat tracing, electrical or steam, shall be recorded)",
          databaseName: "510_EXT-PV_CKLST_SEC012_Q004",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeIs_the_insulation_jacketing_properly_sealed__oriented =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the insulation jacketing properly sealed & oriented:",
          databaseName: "510_EXT-PV_CKLST_SEC012_Q005",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeWas_condensation_noted_on_the_exterior_of_the_asset =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Was condensation noted on the exterior of the asset:",
          databaseName: "510_EXT-PV_CKLST_SEC012_Q006",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC012Q007 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Does the asset operate within the CUI range: (10 °F (–12 °C) and 350 °F (177 °C) for carbon and low alloy steels, 140 °F (60 °C) and 350 °F (177 °C) for austenitic stainless steels, and 280 °F (138 °C) and  350 °F (177 °C) for duplex stainless steels or in intermittent service",
          databaseName: "510_EXT-PV_CKLST_SEC012_Q007",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attributeWas_any_evidence_of_CUI_noted =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Was any evidence of CUI noted:",
          databaseName: "510_EXT-PV_CKLST_SEC012_Q008",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attributeAre_CML_ports_installed =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Are CML ports installed:",
          databaseName: "510_EXT-PV_CKLST_SEC012_Q009",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeAre_all_CML_port_hole_covers_present_and_properly_sealed =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are all CML port hole covers present and properly sealed:",
          databaseName: "510_EXT-PV_CKLST_SEC012_Q010",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeDoes_the_vessel_have_fireproofing_applied =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Does the vessel have fireproofing applied:",
          databaseName: "510_EXT-PV_CKLST_SEC012_Q011",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC012Q012 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the fireproofing applied to asset in acceptable condition for continued service:  (Any crack over 0.250” in width, and any crack which has displacement or bulging of the concrete fireproofing material should be investigated for corrosion under fireproofing)(CUF)",
          databaseName: "510_EXT-PV_CKLST_SEC012_Q012",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC012Q013 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the insulation system in acceptable condition for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC012_Q013",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC012Q014 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the protective coating system in acceptable condition for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC012_Q014",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attribute510EXT_PVCKLSTSEC012Q001,
      attributeDoes_the_asset_have_a_protective_coating_system_applied,
      attribute510EXT_PVCKLSTSEC012Q003,
      attribute510EXT_PVCKLSTSEC012Q004,
      attributeIs_the_insulation_jacketing_properly_sealed__oriented,
      attributeWas_condensation_noted_on_the_exterior_of_the_asset,
      attribute510EXT_PVCKLSTSEC012Q007,
      attributeWas_any_evidence_of_CUI_noted,
      attributeAre_CML_ports_installed,
      attributeAre_all_CML_port_hole_covers_present_and_properly_sealed,
      attributeDoes_the_vessel_have_fireproofing_applied,
      attribute510EXT_PVCKLSTSEC012Q012,
      attribute510EXT_PVCKLSTSEC012Q013,
      attribute510EXT_PVCKLSTSEC012Q014,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section510EXT_PVCKLSTSEC012";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
