import 'package:flutter/material.dart';
import 'package:flutter_appauth/flutter_appauth.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AzureAuthService {
  static final AzureAuthService _instance = AzureAuthService._internal();
  
  factory AzureAuthService() {
    return _instance;
  }
  
  AzureAuthService._internal();
  
  final FlutterAppAuth _appAuth = const FlutterAppAuth();
  final String _clientId = 'YOUR_CLIENT_ID'; // Replace with your Azure AD B2C client ID
  final String _redirectUrl = 'YOUR_REDIRECT_URL'; // Replace with your redirect URL
  final String _discoveryUrl = 'https://YOUR_TENANT_NAME.b2clogin.com/YOUR_TENANT_NAME.onmicrosoft.com/v2.0/.well-known/openid-configuration?p=B2C_1_signin'; // Replace with your B2C policy configuration

  // Storage key constants
  final String _accessTokenKey = 'access_token';
  final String _idTokenKey = 'id_token';
  final String _refreshTokenKey = 'refresh_token';
  final String _expirationTimeKey = 'expiration_time';
  
  String? _accessToken;
  String? _idToken;
  String? _refreshToken;
  DateTime? _expiryTime;
  
  bool get isLoggedIn => _accessToken != null && (_expiryTime?.isAfter(DateTime.now()) ?? false);
  
  String? get accessToken => _accessToken;
  String? get idToken => _idToken;
  String? get userId => _idToken; // Extract user ID from the token if needed
  
  // Initialize auth service and load saved tokens
  Future<void> init() async {
    final prefs = await SharedPreferences.getInstance();
    _accessToken = prefs.getString(_accessTokenKey);
    _idToken = prefs.getString(_idTokenKey);
    _refreshToken = prefs.getString(_refreshTokenKey);
    
    final expirationTimeMillis = prefs.getInt(_expirationTimeKey);
    if (expirationTimeMillis != null) {
      _expiryTime = DateTime.fromMillisecondsSinceEpoch(expirationTimeMillis);
    }
    
    // Auto refresh if token is expired or about to expire
    if (_refreshToken != null && _expiryTime != null) {
      if (_expiryTime!.difference(DateTime.now()).inMinutes < 5) {
        await _refreshTokens();
      }
    }
  }
  
  // Sign in with Azure AD B2C
  Future<bool> signIn() async {
    try {
      final AuthorizationTokenResponse? result = await _appAuth.authorizeAndExchangeCode(
        AuthorizationTokenRequest(
          _clientId,
          _redirectUrl,
          discoveryUrl: _discoveryUrl,
          scopes: ['openid', 'profile', 'email', 'offline_access'],
        ),
      );
      
      if (result != null) {
        _processAuthResult(result);
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('Error during sign in: $e');
      return false;
    }
  }
  
  // Sign out and clear tokens
  Future<void> signOut() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_accessTokenKey);
    await prefs.remove(_idTokenKey);
    await prefs.remove(_refreshTokenKey);
    await prefs.remove(_expirationTimeKey);
    
    _accessToken = null;
    _idToken = null;
    _refreshToken = null;
    _expiryTime = null;
  }
  
  // Refresh the access token using the refresh token
  Future<bool> _refreshTokens() async {
    try {
      if (_refreshToken == null) return false;
      
      final TokenResponse? response = await _appAuth.token(
        TokenRequest(
          _clientId,
          _redirectUrl,
          refreshToken: _refreshToken,
          discoveryUrl: _discoveryUrl,
          scopes: ['openid', 'profile', 'email', 'offline_access'],
        ),
      );
      
      if (response != null) {
        _processAuthResult(response);
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('Error refreshing token: $e');
      return false;
    }
  }
  
  // Process and save authentication result
  void _processAuthResult(TokenResponse result) async {
    _accessToken = result.accessToken;
    _idToken = result.idToken;
    _refreshToken = result.refreshToken;
    
    if (result.accessTokenExpirationDateTime != null) {
      _expiryTime = result.accessTokenExpirationDateTime;
    }
    
    // Save to shared preferences
    final prefs = await SharedPreferences.getInstance();
    if (_accessToken != null) prefs.setString(_accessTokenKey, _accessToken!);
    if (_idToken != null) prefs.setString(_idTokenKey, _idToken!);
    if (_refreshToken != null) prefs.setString(_refreshTokenKey, _refreshToken!);
    if (_expiryTime != null) prefs.setInt(_expirationTimeKey, _expiryTime!.millisecondsSinceEpoch);
  }
}
