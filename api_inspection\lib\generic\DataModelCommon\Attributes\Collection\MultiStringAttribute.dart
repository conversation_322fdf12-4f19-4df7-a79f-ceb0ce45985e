import 'package:api_inspection/generic/AttributeControls/AttributeStructure.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';

import '../AttributeBase.dart';
import '../ChangeLog/ChangeLogEntry.dart';

class MultiStringAttribute extends MultiAttributeBase<String> {
  List<String>? getValue() {
    if (valueChangeLog.entries.isEmpty) return null;
    return valueChangeLog.getCurrentEntries();
  }

  @override
  String getPreviewText() {
    var value = getValue();
    return value == null ? "" : value.join(", ");
  }

  @override
  bool hasData() {
    var value = getValue();
    return value != null && value.isNotEmpty;
  }

  bool areListsEqual(List<String>? str1, List<String>? str2) {
    if ((str1 == null || str1.isEmpty) && (str2 == null || str2.isEmpty)) {
      return true;
    }
    if (str1 == null || str2 == null) return false;
    if (str1.length != str2.length) return false;

    for (int i = 0; i < str1.length; i++) {
      if (!areStringsEqual(str1[i], str2[i])) {
        return false;
      }
    }
    return true;
  }

  void removeValue(String? value) {
    var currentValue = getValue();
    if (currentValue != null &&
        !currentValue.any((element) => areStringsEqual(element, value))) return;

    if (value == null || value.isEmpty) {
      throw "Cannot add a null or empty value";
    }

    var entry = ListChangeLogEntry<String>.newlyCreated(value, "Removed");
    valueChangeLog.addNewItem(entry);

    changedBySelfListeners.notifyListeners();
    notifyListeners();
  }

  void addValue(String? value) {
    var currentValue = getValue();
    if (currentValue != null &&
        currentValue.any((element) => areStringsEqual(element, value))) return;

    if (value == null || value.isEmpty) {
      throw "Cannot add a null or empty value";
    }

    var entry = ListChangeLogEntry<String>.newlyCreated(value, "Added");
    valueChangeLog.addNewItem(entry);

    changedBySelfListeners.notifyListeners();
    notifyListeners();
  }

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  MultiStringAttribute(DataModelItem parent, String displayName,
      {Widget? iconWidget,
      bool areCommentsRequired = false,
      String? databaseName})
      : super(parent, displayName, iconWidget, areCommentsRequired,
            databaseName) {
    valueChangeLog.setConversionMethod((entry) => entry.toString());
  }

  @override
  Widget buildWidget(
      {IsEditableController? editingController,
      bool showPhotos = true,
      bool showComments = true}) {
    return Container();
  }
}
