//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionComponents.dart';
import 'SectionShellCoursesPage.dart';
import 'SectionTankBottomFloorPage.dart';
import 'SectionTankRoofPage.dart';
import 'SectionNozzlesPage.dart';

// ignore: camel_case_types
class SectionComponentsPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionComponents sectionComponents;

  const SectionComponentsPage(this.sectionComponents, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionComponentsPageState();
  }
}

class _SectionComponentsPageState extends State<SectionComponentsPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionComponents,
        title: "Components",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionComponents.sectionShellCourses,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          SectionShellCoursesPage(
                                              widget.sectionComponents
                                                  .sectionShellCourses)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section:
                            widget.sectionComponents.sectionTankBottomFloor,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          SectionTankBottomFloorPage(widget
                                              .sectionComponents
                                              .sectionTankBottomFloor)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionComponents.sectionTankRoof,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => SectionTankRoofPage(
                                          widget.sectionComponents
                                              .sectionTankRoof)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionComponents.sectionNozzles,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => SectionNozzlesPage(
                                          widget.sectionComponents
                                              .sectionNozzles)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                ]),
          );
        });
  }
}
