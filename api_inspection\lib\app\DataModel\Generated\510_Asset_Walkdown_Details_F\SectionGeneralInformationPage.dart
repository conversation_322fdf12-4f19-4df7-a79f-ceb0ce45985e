//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionGeneralInformation.dart';
import 'SectionPhotosPage.dart';
import 'SectionDesignPage.dart';
import 'SectionInspectionPage.dart';
import 'SectionDataPlatePage.dart';
import 'SectionManufacturerPage.dart';
import 'SectionServicePage.dart';
import 'SectionInspectionOpeningsPage.dart';
import 'SectionRepairRecordPage.dart';

// ignore: camel_case_types
class SectionGeneralInformationPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionGeneralInformation sectionGeneralInformation;

  const SectionGeneralInformationPage(this.sectionGeneralInformation,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionGeneralInformationPageState();
  }
}

class _SectionGeneralInformationPageState
    extends State<SectionGeneralInformationPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionGeneralInformation,
        title: "General Information",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionGeneralInformation.sectionPhotos,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => SectionPhotosPage(
                                          widget.sectionGeneralInformation
                                              .sectionPhotos)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionGeneralInformation.sectionDesign,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => SectionDesignPage(
                                          widget.sectionGeneralInformation
                                              .sectionDesign)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section:
                            widget.sectionGeneralInformation.sectionInspection,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          SectionInspectionPage(
                                              widget.sectionGeneralInformation
                                                  .sectionInspection)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section:
                            widget.sectionGeneralInformation.sectionDataPlate,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          SectionDataPlatePage(
                                              widget.sectionGeneralInformation
                                                  .sectionDataPlate)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget
                            .sectionGeneralInformation.sectionManufacturer,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          SectionManufacturerPage(
                                              widget.sectionGeneralInformation
                                                  .sectionManufacturer)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section:
                            widget.sectionGeneralInformation.sectionService,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => SectionServicePage(
                                          widget.sectionGeneralInformation
                                              .sectionService)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(widget
                      .sectionGeneralInformation.attributeOrientation
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionGeneralInformation.attributeRT
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionGeneralInformation.attributeInstallation_Date
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionGeneralInformation.attributeIn_service_Date
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionGeneralInformation.attributePID_Number
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionGeneralInformation
                      .attributeConstructionDesign_Drawing_Number
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionGeneralInformation.attributeLowest_Flange_Rating
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionGeneralInformation.attributeHydro_Test_Pressure
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionGeneralInformation.attributeType_of_construction
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionGeneralInformation
                      .attributePost_Weld_Heat_Treatment
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionGeneralInformation
                      .attributeHas_the_equipment_been_de_rated_or_re_rated
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionGeneralInformation
                      .attributeIs_this_a_fired_pressure_vessel
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionGeneralInformation
                            .sectionInspectionOpenings,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          SectionInspectionOpeningsPage(widget
                                              .sectionGeneralInformation
                                              .sectionInspectionOpenings)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget
                            .sectionGeneralInformation.sectionRepairRecord,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          SectionRepairRecordPage(
                                              widget.sectionGeneralInformation
                                                  .sectionRepairRecord)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                ]),
          );
        });
  }
}
