//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'Section510INT_PVCKLSTSEC017.dart';

// ignore: camel_case_types
class Section510INT_PVCKLSTSEC017Page extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final Section510INT_PVCKLSTSEC017 section510INT_PVCKLSTSEC017;

  const Section510INT_PVCKLSTSEC017Page(this.section510INT_PVCKLSTSEC017,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _Section510INT_PVCKLSTSEC017PageState();
  }
}

class _Section510INT_PVCKLSTSEC017PageState
    extends State<Section510INT_PVCKLSTSEC017Page> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.section510INT_PVCKLSTSEC017,
        title: "STEEL SUPPORTS",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC017
                      .attribute510INT_PVCKLSTSEC017Q001
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC017
                      .attribute510INT_PVCKLSTSEC017Q002
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC017
                      .attribute510INT_PVCKLSTSEC017Q003
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC017
                      .attribute510INT_PVCKLSTSEC017Q004
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
