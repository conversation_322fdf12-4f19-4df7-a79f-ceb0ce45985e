//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Asset_Walkdown_Details_F
{
  public class SectionIdentification : DataModelItem {

    public override String DisplayName { 
      get {
        return "Identification";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public StringAttribute attributeName;
    public StringAttribute attributeNumber_or_ID;
    public StringAttribute attributeAsset_Type;
    public StringAttribute attributeEquipment_Description;
    public DateAttribute attributeLast_known_inspection_date;
    public PredefinedValueAttribute attributeLocation;
    public LocationAttribute attributeGIS_Location;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionIdentification";

    public SectionIdentification(DataModelItem parent) : base(parent)
    {
            
        attributeName = new StringAttribute(this, displayName: "Name", databaseName: "510AW_Q005"); 
     
        attributeNumber_or_ID = new StringAttribute(this, displayName: "Number or ID", databaseName: "510AW_Q006", isQueryable: true); 
     
        attributeAsset_Type = new StringAttribute(this, displayName: "Asset Type", databaseName: "510AW_Q007"); 
     
        attributeEquipment_Description = new StringAttribute(this, displayName: "Equipment Description", databaseName: "510AW_Q008"); 
     
        attributeLast_known_inspection_date = new DateAttribute(this, displayName: "Last known inspection date", databaseName: "510AW_Q009", areCommentsRequired: false); 
     
        attributeLocation = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("On-Plot (Facility)", null),
          new PredefinedValueOption("Off-Plot (Field)", null)
        }, false, this, "Location", databaseName: "510AW_Q010"); 
     
        attributeGIS_Location = new LocationAttribute(this, displayName: "GIS Location", databaseName: "510AW_Q011"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeName,
           attributeNumber_or_ID,
           attributeAsset_Type,
           attributeEquipment_Description,
           attributeLast_known_inspection_date,
           attributeLocation,
           attributeGIS_Location,
        };
    }
  }
}
