{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "camera_avfoundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\camera_avfoundation-0.9.8+5\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "cloud_firestore", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\cloud_firestore-3.4.9\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "connectivity_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\connectivity_plus-2.3.7\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "file_picker", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_picker-4.6.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "file_saver", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_saver-0.1.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_analytics", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_analytics-9.3.6\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_auth-3.10.0\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-1.23.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_crashlytics", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_crashlytics-2.8.11\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_appauth", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_appauth-4.2.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_uploader", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_uploader-3.0.0-beta.4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "gallery_saver", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\gallery_saver-2.3.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geolocator_apple", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geolocator_apple-2.2.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "map_launcher", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\map_launcher-2.4.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "open_file", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\open_file-3.2.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus-1.4.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_ios-2.0.11\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_ios-2.1.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\sqflite-2.1.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_ios", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_ios-6.0.17\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "camera_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\camera_android-0.10.0+2\\\\", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "cloud_firestore", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\cloud_firestore-3.4.9\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "connectivity_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\connectivity_plus-2.3.7\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "file_picker", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_picker-4.6.1\\\\", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "file_saver", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_saver-0.1.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_analytics", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_analytics-9.3.6\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_auth-3.10.0\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-1.23.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_crashlytics", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_crashlytics-2.8.11\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_appauth", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_appauth-4.2.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_plugin_android_lifecycle", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_plugin_android_lifecycle-2.0.7\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_uploader", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_uploader-3.0.0-beta.4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "gallery_saver", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\gallery_saver-2.3.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geolocator_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geolocator_android-4.1.3\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "map_launcher", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\map_launcher-2.4.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "open_file", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\open_file-3.2.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus-1.4.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_android-2.0.20\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_android-2.0.13\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\sqflite-2.1.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_android-6.0.19\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "cloud_firestore", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\cloud_firestore-3.4.9\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "connectivity_plus_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\connectivity_plus_macos-1.2.4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "file_saver", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_saver-0.1.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_analytics", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_analytics-9.3.6\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_auth-3.10.0\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-1.23.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_crashlytics", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_crashlytics-2.8.11\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_appauth", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\flutter_appauth-4.2.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geolocator_apple", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geolocator_apple-2.2.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus_macos-1.3.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_macos-2.0.6\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_macos-2.0.4\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\sqflite-2.1.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_macos", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_macos-3.0.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "connectivity_plus_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\connectivity_plus_linux-1.3.1\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "file_saver", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_saver-0.1.1\\\\", "native_build": true, "dependencies": ["path_provider_linux"], "dev_dependency": false}, {"name": "package_info_plus_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus_linux-1.0.5\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_linux-2.1.7\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_linux-2.1.1\\\\", "native_build": false, "dependencies": ["path_provider_linux"], "dev_dependency": false}, {"name": "url_launcher_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_linux-3.0.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "windows": [{"name": "connectivity_plus_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\connectivity_plus_windows-1.2.2\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "file_saver", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_saver-0.1.1\\\\", "native_build": true, "dependencies": ["path_provider_windows"], "dev_dependency": false}, {"name": "geolocator_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geolocator_windows-0.1.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus_windows-1.0.5\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_windows-2.0.7\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_windows-2.1.1\\\\", "native_build": false, "dependencies": ["path_provider_windows"], "dev_dependency": false}, {"name": "url_launcher_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_windows-3.0.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "web": [{"name": "camera_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\camera_web-0.3.0\\\\", "dependencies": [], "dev_dependency": false}, {"name": "cloud_firestore_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\cloud_firestore_web-2.8.8\\\\", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "connectivity_plus_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\connectivity_plus_web-1.2.3\\\\", "dependencies": [], "dev_dependency": false}, {"name": "file_picker", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_picker-4.6.1\\\\", "dependencies": [], "dev_dependency": false}, {"name": "file_saver", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\file_saver-0.1.1\\\\", "dependencies": [], "dev_dependency": false}, {"name": "firebase_analytics_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_analytics_web-0.4.2+5\\\\", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_auth_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_auth_web-4.5.0\\\\", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_core_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core_web-1.7.2\\\\", "dependencies": [], "dev_dependency": false}, {"name": "geolocator_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\geolocator_web-2.1.6\\\\", "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\package_info_plus_web-1.0.5\\\\", "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_web-2.0.4\\\\", "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\url_launcher_web-2.0.13\\\\", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "camera", "dependencies": ["camera_android", "camera_avfoundation", "camera_web", "flutter_plugin_android_lifecycle"]}, {"name": "camera_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "camera_avfoundation", "dependencies": []}, {"name": "camera_web", "dependencies": []}, {"name": "cloud_firestore", "dependencies": ["cloud_firestore_web", "firebase_core"]}, {"name": "cloud_firestore_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "connectivity_plus", "dependencies": ["connectivity_plus_linux", "connectivity_plus_macos", "connectivity_plus_web", "connectivity_plus_windows"]}, {"name": "connectivity_plus_linux", "dependencies": []}, {"name": "connectivity_plus_macos", "dependencies": []}, {"name": "connectivity_plus_web", "dependencies": []}, {"name": "connectivity_plus_windows", "dependencies": []}, {"name": "file_picker", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "file_saver", "dependencies": ["path_provider", "path_provider_windows", "path_provider_linux"]}, {"name": "firebase_analytics", "dependencies": ["firebase_analytics_web", "firebase_core"]}, {"name": "firebase_analytics_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_auth", "dependencies": ["firebase_auth_web", "firebase_core"]}, {"name": "firebase_auth_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "firebase_crashlytics", "dependencies": ["firebase_core"]}, {"name": "flutter_appauth", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "flutter_uploader", "dependencies": []}, {"name": "gallery_saver", "dependencies": ["path_provider"]}, {"name": "geolocator", "dependencies": ["geolocator_android", "geolocator_apple", "geolocator_web", "geolocator_windows"]}, {"name": "geolocator_android", "dependencies": []}, {"name": "geolocator_apple", "dependencies": []}, {"name": "geolocator_web", "dependencies": []}, {"name": "geolocator_windows", "dependencies": []}, {"name": "map_launcher", "dependencies": []}, {"name": "open_file", "dependencies": []}, {"name": "package_info_plus", "dependencies": ["package_info_plus_linux", "package_info_plus_macos", "package_info_plus_windows", "package_info_plus_web"]}, {"name": "package_info_plus_linux", "dependencies": []}, {"name": "package_info_plus_macos", "dependencies": []}, {"name": "package_info_plus_web", "dependencies": []}, {"name": "package_info_plus_windows", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_ios", "path_provider_linux", "path_provider_macos", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_ios", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_macos", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_ios", "shared_preferences_linux", "shared_preferences_macos", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_ios", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_macos", "dependencies": []}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sqflite", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}], "date_created": "2025-07-29 22:32:30.410596", "version": "3.32.8", "swift_package_manager_enabled": {"ios": false, "macos": false}}