﻿using System;
using Newtonsoft.Json;

namespace CommonDataInterface.Attributes
{
  public class PredefinedValueOption {
    public String Value { get; set; }
    public String Description { get; set; }
    
    public bool IsCommentRequired;
    public PredefinedValueOption(String value, String description, bool isCommentRequired = false)
    {
      this.IsCommentRequired = isCommentRequired;
      this.Value = value;
      this.Description = description;
    }
  }
}