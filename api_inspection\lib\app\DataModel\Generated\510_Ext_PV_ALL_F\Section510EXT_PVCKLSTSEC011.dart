//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class Section510EXT_PVCKLSTSEC011 extends DataModelSection {
  @override
  String getDisplayName() => "AUXILIARY EQUIPMENT";
  Section510EXT_PVCKLSTSEC011(DataModelItem? parent)
      : super(parent: parent, sectionName: "AUXILIARY EQUIPMENT");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC011Q001 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are there any temperature indicating devices connected to the asset:  (If yes, record temperature reading in the comment section & verify accuracy of the gauge by informing operations of temperature reading obtained versus the operating temperature of the asset)",
          databaseName: "510_EXT-PV_CKLST_SEC011_Q001",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC011Q002 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the pressure gauge exposed to high temperature from an external source or internal heat due to lack of protection by a proper siphon or trap:",
          databaseName: "510_EXT-PV_CKLST_SEC011_Q002",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC011Q003 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are auxiliary equipment items in acceptable condition for continued service:  (Gauge connections, sight glasses, float wells, etc.)",
          databaseName: "510_EXT-PV_CKLST_SEC011_Q003",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC011Q004 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are there any threaded connections associated with the asset:",
          databaseName: "510_EXT-PV_CKLST_SEC011_Q004",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeAre_threaded_connections_acceptably_engaged_and_leak_free =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are threaded connections acceptably engaged and leak free:",
          databaseName: "510_EXT-PV_CKLST_SEC011_Q005",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC011Q006 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is threaded connection piping constructed from schedule 80 or greater piping:",
          databaseName: "510_EXT-PV_CKLST_SEC011_Q006",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeAre_threaded_connections_acceptable_for_continued_service =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are threaded connections acceptable for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC011_Q007",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC011Q008 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Were drain connections verified to be free of any foreign material that may cause plugging:",
          databaseName: "510_EXT-PV_CKLST_SEC011_Q008",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attribute510EXT_PVCKLSTSEC011Q001,
      attribute510EXT_PVCKLSTSEC011Q002,
      attribute510EXT_PVCKLSTSEC011Q003,
      attribute510EXT_PVCKLSTSEC011Q004,
      attributeAre_threaded_connections_acceptably_engaged_and_leak_free,
      attribute510EXT_PVCKLSTSEC011Q006,
      attributeAre_threaded_connections_acceptable_for_continued_service,
      attribute510EXT_PVCKLSTSEC011Q008,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section510EXT_PVCKLSTSEC011";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
