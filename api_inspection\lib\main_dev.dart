import 'dart:async';
import 'dart:typed_data';

import 'package:api_inspection/firebase_options.dart';
import 'package:api_inspection/generic/AppRoot.dart';
import 'package:api_inspection/uploader_handler.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'app/myApp.dart';
import 'generic/MediaControls/IGallerySaver.dart';
import 'generic/MediaControls/MediaSynchronizer.dart';
import 'package:flutter/material.dart';
import 'app/AppScaffold.dart';
import 'environment.dart';
import 'package:gallery_saver/gallery_saver.dart';
import 'package:flutter_uploader/flutter_uploader.dart';

void main() async {
  runZonedGuarded<Future<void>>(() async {
    WidgetsFlutterBinding.ensureInitialized();
    AppRoot.global().gallerySaver = ConcreteGallerySaver();
    AppRoot.global().environment = EnvironmentValue.development;

    await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform(
            EnvironmentValue.development));
    FirebaseFirestore.instance.settings =
        const Settings(cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED);

    FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterFatalError;
    FlutterUploader().setBackgroundHandler(backgroundUploadHandler);
    await IMediaSynchronizer.getMediaSynchronizer().ensureInitialization();

    runApp(MyApp(
        environment: EnvironmentValue.development,
        buildMainWidget: (context) {
          return const TeamAppScaffold();
        }));
  },
      (error, stack) =>
          FirebaseCrashlytics.instance.recordError(error, stack, fatal: true));
}

// probably want to fix gallery saver so that it's not using this interface anymore
class ConcreteGallerySaver with IGallerySaver {
  @override
  Future<void> saveImageToGallery(
      {required Uint8List photoData,
      required int quality,
      required String name,
      required String path,
      required String albumName}) {
    return GallerySaver.saveImage(path, albumName: albumName);
  }
}
