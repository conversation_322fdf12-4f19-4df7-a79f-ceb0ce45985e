//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section570_Ext_Pipe_F
{
  public class Section570EXTCKLSTSEC001 : DataModelItem {

    public override String DisplayName { 
      get {
        return "GENERAL INFORMATION";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributeIs_the_asset_appropriately_prepared_for_inspection;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC001Q002;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC001Q003;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC001Q004;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC001Q005;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section570EXTCKLSTSEC001";

    public Section570EXTCKLSTSEC001(DataModelItem parent) : base(parent)
    {
            
        attributeIs_the_asset_appropriately_prepared_for_inspection = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the asset appropriately prepared for inspection:", databaseName: "570_EXT_CKLST_SEC001_Q001"); 
     
        attribute570EXTCKLSTSEC001Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was design data, previous inspection / examination reporting and or relevant  MOC / information pertaining to abnormal operating conditions made available prior to inspection:", databaseName: "570_EXT_CKLST_SEC001_Q002"); 
     
        attribute570EXTCKLSTSEC001Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was the visual inspection conducted within a distance of 6”-24” from the piping:", databaseName: "570_EXT_CKLST_SEC001_Q003"); 
     
        attribute570EXTCKLSTSEC001Q004 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was the visual inspection conducted within an angle of not less than 30 degrees with the piping:", databaseName: "570_EXT_CKLST_SEC001_Q004"); 
     
        attribute570EXTCKLSTSEC001Q005 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "During the visual inspection was a minimum light intensity of at least 100 foot-candles present:", databaseName: "570_EXT_CKLST_SEC001_Q005"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeIs_the_asset_appropriately_prepared_for_inspection,
           attribute570EXTCKLSTSEC001Q002,
           attribute570EXTCKLSTSEC001Q003,
           attribute570EXTCKLSTSEC001Q004,
           attribute570EXTCKLSTSEC001Q005,
        };
    }
  }
}
