import 'dart:async';
import 'dart:io';

import 'package:api_inspection/generic/DataModelCommon/DataModel/user.dart';
import 'package:api_inspection/generic/MediaControls/IGallerySaver.dart';
import 'package:api_inspection/generic/UI/Login/LoginPage.dart';
import 'package:api_inspection/generic/UIControls/PositionedTapDetector.dart';
import 'package:api_inspection/environment.dart';
import 'package:api_inspection/generic/UIControls/listener.dart';
import 'package:darq/darq.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

void showErrorPopup(BuildContext context, String title, String message) {
  showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color.fromARGB(255, 41, 45, 52),
          title: Row(
            children: [
              Container(
                  margin: const EdgeInsets.all(5),
                  child: const Icon(Icons.error, color: Colors.red, size: 32)),
              Expanded(
                  child: Text(
                title,
                style: const TextStyle(color: Colors.white),
              ))
            ],
          ),
          content: Text(message, style: const TextStyle(color: Colors.white)),
          actions: [
            ElevatedButton(
              child: const Text('Ok', style: TextStyle(color: Colors.white)),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      });
}

class AppRoot {
  static AppRoot? _global;
  static AppRoot global() {
    _global ??= AppRoot();
    return _global!;
  }

  Environment? environment;

  IGallerySaver? gallerySaver;

  UserProfile? currentUser;
  UnverifiedUserProfile? currentUserNonVerified;

  late SharedPreferences sharedPreferences;

  Iterable<Iterable<String>>? get businessIdsBatches {
    return currentUser?.businessUnitIds
        .getValue()
        ?.toList()
        .batch(10, includeTail: true);
  }

  Future<void> initPreferences() async {
    sharedPreferences = await SharedPreferences.getInstance();
  }

  ListenerWrapper photoDownloadProgressEvent = ListenerWrapper();

  String get azureResizedContainer {
    return environment == EnvironmentValue.development
        ? "/flutter-dev-resizedimages/"
        : "/flutter-resizedimages/";
  }

  String get resizedContainerSasToken {
    return environment == EnvironmentValue.development
        ? 'sv=2019-02-02&st=2022-06-27T12%3A59%3A12Z&se=2023-07-23T12%3A59%3A00Z&sr=c&sp=rl&sig=ZDTTltMtyKEpOQYrM23sGzDkp8yXNXzeufQ4j56gaAk%3D'
        : 'sv=2019-02-02&st=2022-06-27T12%3A57%3A32Z&se=2023-07-28T12%3A57%3A00Z&sr=c&sp=rl&sig=ZBBjzk%2BXEWT%2F1RaxiTIf%2FazvyNzdncCw65%2Fg%2BMDnMjM%3D';
  }

  String get azureContainer {
    return environment == EnvironmentValue.development
        ? "/flutter-dev/"
        : "/flutter/";
  }

  String get containerSasToken {
    return environment == EnvironmentValue.development
        ? 'sv=2022-11-02&ss=bfqt&srt=sco&sp=rwdlacupiytfx&se=2024-06-22T07:02:27Z&st=2023-06-21T23:02:27Z&spr=https&sig=fGc8QoqJrTrQ9Am0RprbEJ0%2Fk%2FO99%2FfSIxEiajGFyPk%3D'
        : 'sv=2022-11-02&ss=bfqt&srt=sco&sp=rwdlacupiytfx&se=2024-06-22T07:02:27Z&st=2023-06-21T23:02:27Z&spr=https&sig=fGc8QoqJrTrQ9Am0RprbEJ0%2Fk%2FO99%2FfSIxEiajGFyPk%3D';
  }

  late BuildContext mainBC;
  GlobalKey<NavigatorState>? mainNavKey;
  late BuildContext loginPageRootBC;
  bool Function(dynamic)? loginPagePopCondition;

  Future Function(BuildContext)? onInitialLoginSuccessful;

  void logOutWithExit(BuildContext context) async {
    await sharedPreferences.remove("savedCred");
    await sharedPreferences.remove("savedCredTime");

    if (Platform.isAndroid) {
      showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              backgroundColor: const Color.fromARGB(255, 41, 45, 52),
              title: Row(
                children: const [
                  Text(
                    'The application will now close',
                    style: TextStyle(color: Colors.white),
                  )
                ],
              ),
              content: const Text("Please restart the app to switch users",
                  style: TextStyle(color: Colors.white)),
              actions: [
                ElevatedButton(
                  child:
                      const Text('Ok', style: TextStyle(color: Colors.white)),
                  onPressed: () {
                    exit(0);
                  },
                ),
              ],
            );
          });
    } else {
      showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              backgroundColor: const Color.fromARGB(255, 41, 45, 52),
              title: Row(
                children: [
                  Container(
                      margin: const EdgeInsets.all(5),
                      child:
                          const Icon(Icons.error, color: Colors.red, size: 32)),
                  const Text(
                    'Please close the application',
                    style: TextStyle(color: Colors.white),
                  )
                ],
              ),
              content: const Text(
                  "Please close and restart the app to switch users.",
                  style: TextStyle(color: Colors.white)),
              actions: [
                ElevatedButton(
                  child:
                      const Text('Ok', style: TextStyle(color: Colors.white)),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ],
            );
          });
    }
  }

  void logOutClicked(BuildContext context) async {
    var logout = onLogOut;
    if (logout != null) await logout();
    FirebaseAuth auth = FirebaseAuth.instance;

    await auth.signOut();

    mainNavKey!.currentState!.pushAndRemoveUntil(
        MaterialPageRoute(
            builder: (context) =>
                LoginPage(onLoginSuccessful: onInitialLoginSuccessful!)),
        (_) => false);
  }

  void showMainMenu(BuildContext context, TapPosition position) async {
    var overlayPosition = RelativeRect.fromSize(
        Rect.fromLTWH(position.global.dx, position.global.dy, 48, 48),
        const Size(50, 100));
    final menuItem = await showMenu<int>(
        context: context,
        position: overlayPosition,
        items: [
          const PopupMenuItem(child: Text('Log Out'), value: 1),
        ]);

    switch (menuItem) {
      case 1:
        logOutWithExit(context);
        break;
    }
  }

  Widget buildMenuButtonWidget(BuildContext context) {
    return PositionedTapDetector(
        onTap: (position) {
          showMainMenu(context, position);
        },
        child: const SizedBox(
            width: 50,
            child: Icon(Icons.keyboard_control, color: Colors.white)));
  }

  void showPopupLogin() {
    showDialog(
      barrierDismissible: false,
      context: mainBC,
      builder: (BuildContext context) {
        return Container(
            margin: const EdgeInsets.all(25),
            child: LoginPage(
                onLoginSuccessful: (context) async {},
                launchedFromPopup: true));
      },
    );
  }

  Timer? loginAttemptTimer;

  Future Function()? onLogOut;

  Future<void> startLoginAttempts(
      {required String email, required String password}) async {
    loginAttemptTimer = Timer.periodic(const Duration(seconds: 15), (timer) {
      doLoginAttempt(email: email, password: password);
    });
  }

  void doLoginAttempt({required String email, required String password}) async {
    FirebaseAuth auth = FirebaseAuth.instance;

    try {
      await auth.signInWithEmailAndPassword(email: email, password: password);
    } on FirebaseAuthException catch (e) {
      if (e.code == "network-request-failed") {
        return;
      }
      var timer = loginAttemptTimer;
      if (timer != null) {
        timer.cancel();
        loginAttemptTimer = null;
      }

      showDialog(
        barrierDismissible: false,
        context: mainBC,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: const Color.fromARGB(255, 41, 45, 52),
            title: const Text(
              'We have found an issue with your login credentials',
              style: TextStyle(color: Colors.white),
            ),
            content: Text(e.code + "\r\n\r\nYou will have to re login.",
                style: const TextStyle(color: Colors.white)),
            actions: [
              ElevatedButton(
                child: const Text('Ok', style: TextStyle(color: Colors.white)),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        },
      ).then((value) => showPopupLogin());
    }
  }
}
