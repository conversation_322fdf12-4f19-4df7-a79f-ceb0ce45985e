import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';

class MultiPredefinedValueAttributeViewNonEditable extends StatefulWidget {
  final MultiPredefinedValueAttribute _attribute;

  const MultiPredefinedValueAttributeViewNonEditable(this._attribute,
      {Key? key})
      : super(key: key);

  @override
  _MultiPredefinedValueAttributeViewNonEditableState createState() =>
      _MultiPredefinedValueAttributeViewNonEditableState();
}

class _MultiPredefinedValueAttributeViewNonEditableState
    extends State<MultiPredefinedValueAttributeViewNonEditable> {
  @override
  void initState() {
    var attribute = widget._attribute;
    attribute.addListener(onAttributeChanged);
    super.initState();
  }

  @override
  void didUpdateWidget(
      covariant MultiPredefinedValueAttributeViewNonEditable oldWidget) {
    super.didUpdateWidget(oldWidget);

    var attribute = oldWidget._attribute;

    attribute.removeListener(onAttributeChanged);

    widget._attribute.addListener(onAttributeChanged);
  }

  @override
  void dispose() {
    var attribute = widget._attribute;
    attribute.removeListener(onAttributeChanged);
    super.dispose();
  }

  void onAttributeChanged() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> children = [];
    var value = widget._attribute.getValue();
    if (value != null) {
      for (var item in value) {
        children.add(Text(
          item,
          style: const TextStyle(color: Colors.white, fontSize: 16),
        ));
      }
    }
    return Container(
        margin: const EdgeInsets.fromLTRB(20, 10, 20, 10),
        alignment: Alignment.centerLeft,
        child: Column(
            crossAxisAlignment: CrossAxisAlignment.start, children: children));
  }
}
