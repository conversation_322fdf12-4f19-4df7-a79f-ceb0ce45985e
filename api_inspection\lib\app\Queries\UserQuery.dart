import 'dart:async';

import 'package:api_inspection/app/Queries/Queries.dart';
import 'package:api_inspection/generic/AppRoot.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModel/user.dart';
import 'package:api_inspection/generic/UIControls/listener.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class UserQuery {
  late Queries _parent;

  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? userSubscription;

  Future dispose() async {
    await userSubscription?.cancel();
    userListener.dispose();
    userSubscription = null;
  }

  ListenerWrapper userListener = ListenerWrapper();

  void initialize(Queries parent) {
    _parent = parent;

    _startUserQuery();
  }

  void _startUserQuery() {
    var dbRef = FirebaseFirestore.instance;

    userSubscription = dbRef
        .collection('users')
        .doc(AppRoot.global().currentUser!.id)
        .snapshots()
        .listen(_onUserUpdated);
  }

  void _onUserUpdated(DocumentSnapshot<Map<String, dynamic>> snapshot) {
    var profile = UserProfile(AppRoot.global()
        .currentUser!
        .id
        .replaceAll("|", ".")
        .toLowerCase()
        .trim());
    profile.updateFromMap(snapshot.data());
    AppRoot.global().currentUser = profile;
    userListener.notifyListeners();
  }
}
