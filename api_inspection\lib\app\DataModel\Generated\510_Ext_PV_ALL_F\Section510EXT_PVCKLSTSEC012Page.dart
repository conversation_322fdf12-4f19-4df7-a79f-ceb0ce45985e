//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'Section510EXT_PVCKLSTSEC012.dart';

// ignore: camel_case_types
class Section510EXT_PVCKLSTSEC012Page extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final Section510EXT_PVCKLSTSEC012 section510EXT_PVCKLSTSEC012;

  const Section510EXT_PVCKLSTSEC012Page(this.section510EXT_PVCKLSTSEC012,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _Section510EXT_PVCKLSTSEC012PageState();
  }
}

class _Section510EXT_PVCKLSTSEC012PageState
    extends State<Section510EXT_PVCKLSTSEC012Page> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.section510EXT_PVCKLSTSEC012,
        title: "PROTECTIVE COATING AND INSULATION",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC012
                      .attribute510EXT_PVCKLSTSEC012Q001
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC012
                      .attributeDoes_the_asset_have_a_protective_coating_system_applied
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC012
                      .attribute510EXT_PVCKLSTSEC012Q003
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC012
                      .attribute510EXT_PVCKLSTSEC012Q004
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC012
                      .attributeIs_the_insulation_jacketing_properly_sealed__oriented
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC012
                      .attributeWas_condensation_noted_on_the_exterior_of_the_asset
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC012
                      .attribute510EXT_PVCKLSTSEC012Q007
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC012
                      .attributeWas_any_evidence_of_CUI_noted
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC012
                      .attributeAre_CML_ports_installed
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC012
                      .attributeAre_all_CML_port_hole_covers_present_and_properly_sealed
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC012
                      .attributeDoes_the_vessel_have_fireproofing_applied
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC012
                      .attribute510EXT_PVCKLSTSEC012Q012
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC012
                      .attribute510EXT_PVCKLSTSEC012Q013
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC012
                      .attribute510EXT_PVCKLSTSEC012Q014
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
