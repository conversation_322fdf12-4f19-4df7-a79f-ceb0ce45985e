//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionFlexible_Task_Data_F
{
  public class SectionTaskDetails : DataModelItem {

    public override String DisplayName { 
      get {
        return "Task Details";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributeMethod;
    public PredefinedValueAttribute attributeSub_Method;
    public DateAttribute attributePlanned_Start_Date;
    public DateAttribute attributePlanned_End_Date;
    public DateAttribute attributeActual_Start_Date;
    public DateAttribute attributeWork_Completion_Date;
    public DateAttribute attributeDue_Date;
    public MultiPredefinedValueAttribute attributeTask_Assignees;
    public PredefinedValueAttribute attributeLead_Technician;
    public PredefinedValueAttribute attributeSupervisor;
    public StringAttribute attributeClient_Work_Order_Number;
    public StringAttribute attributeClient_Cost_Code;
    public StringAttribute attributePurchase_Order_AFE;
    public StringAttribute attributeClient_Work_Order_Description;
    public StringAttribute attributeWork_Summary;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionTaskDetails";

    public SectionTaskDetails(DataModelItem parent) : base(parent)
    {
            
        attributeMethod = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("PT Inspection", null),
          new PredefinedValueOption("MT Inspection", null),
          new PredefinedValueOption("UT Inspection", null),
          new PredefinedValueOption("RT Inspection", null),
          new PredefinedValueOption("EMT Inspection", null),
          new PredefinedValueOption("LKT Inspection", null),
          new PredefinedValueOption("PMI Testing", null),
          new PredefinedValueOption("Hardness Testing", null),
          new PredefinedValueOption("LTM Testing", null),
          new PredefinedValueOption("VT Inspection", null),
          new PredefinedValueOption("API 653 External", null),
          new PredefinedValueOption("API 653 Internal", null)
        }, false, this, "Method", databaseName: "GT_Q005"); 
     
        attributeSub_Method = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          
        }, false, this, "Sub-Method", databaseName: "GT_Q006"); 
     
        attributePlanned_Start_Date = new DateAttribute(this, displayName: "Planned Start Date", databaseName: "GT_Q007", areCommentsRequired: false); 
     
        attributePlanned_End_Date = new DateAttribute(this, displayName: "Planned End Date", databaseName: "GT_Q008", areCommentsRequired: false); 
     
        attributeActual_Start_Date = new DateAttribute(this, displayName: "Actual Start Date", databaseName: "GT_Q009", areCommentsRequired: false); 
     
        attributeWork_Completion_Date = new DateAttribute(this, displayName: "Work Completion Date", databaseName: "GT_Q010", areCommentsRequired: false); 
     
        attributeDue_Date = new DateAttribute(this, displayName: "Due Date", databaseName: "GT_Q011", areCommentsRequired: false); 
     
        attributeTask_Assignees = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          
        }, false, this, "Task Assignees", databaseName: "GT_Q012"); 
     
        attributeLead_Technician = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          
        }, false, this, "Lead Technician", databaseName: "GT_Q013"); 
     
        attributeSupervisor = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          
        }, false, this, "Supervisor", databaseName: "GT_Q014"); 
     
        attributeClient_Work_Order_Number = new StringAttribute(this, displayName: "Client Work Order Number", databaseName: "GT_Q015"); 
     
        attributeClient_Cost_Code = new StringAttribute(this, displayName: "Client Cost Code", databaseName: "GT_Q016"); 
     
        attributePurchase_Order_AFE = new StringAttribute(this, displayName: "Purchase Order AFE", databaseName: "GT_Q017"); 
     
        attributeClient_Work_Order_Description = new StringAttribute(this, displayName: "Client Work Order Description", databaseName: "GT_Q018"); 
     
        attributeWork_Summary = new StringAttribute(this, displayName: "Work Summary", databaseName: "GT_Q019"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeMethod,
           attributeSub_Method,
           attributePlanned_Start_Date,
           attributePlanned_End_Date,
           attributeActual_Start_Date,
           attributeWork_Completion_Date,
           attributeDue_Date,
           attributeTask_Assignees,
           attributeLead_Technician,
           attributeSupervisor,
           attributeClient_Work_Order_Number,
           attributeClient_Cost_Code,
           attributePurchase_Order_AFE,
           attributeClient_Work_Order_Description,
           attributeWork_Summary,
        };
    }
  }
}
