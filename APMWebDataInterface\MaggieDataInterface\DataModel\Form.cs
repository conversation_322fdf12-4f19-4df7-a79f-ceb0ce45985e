﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace MaggieDataInterface.DataModel
{
  public class Form : ConcretePhotoRoot
  {
    internal override String GetDBPath() => "forms." + GetDBName();

    public override string DisplayName => "Form";
    internal override string GetDBName()
    {
      return id;
    }
    
    public StringAttribute Name;
    public StringAttribute Category;

    public Form() : base(null)
    {
      InitializeAttributes();
    }

    public Form(string id) : base(id, null)
    {
      InitializeAttributes();
    }

    private void InitializeAttributes()
    {
      
      Questions = new DataModelCollection<Question>("Questions", (parent, entry) => {
        return new Question(parent, entry.Key);
      }, (parent, id) => {
        return new Question(parent, id);
      }, this);

      
      SubForms = new DataModelCollection<SubForm>("SubForms", (parent, entry) => {
        return new SubForm(parent, entry.Key);
      }, (parent, id) => {
        return new SubForm(parent, id);
      }, this);

      Name = new StringAttribute(parent: this, displayName:"Name");
      Category = new StringAttribute(parent: this, displayName:"Category");
    }
    
    public override DataModelItem[] GetChildren(){
      var parentChildren = base.GetChildren();

      return parentChildren.Concat(new DataModelItem[] { 
        Name, Category, Questions, SubForms
      }).ToArray();
    }
    
    public DataModelCollection<Question> Questions;
    public DataModelCollection<SubForm> SubForms;
  }

  public class Question : DataModelCollectionItem
  {
    public override string DisplayName => "Question";

    public StringAttribute Name;

    public IntegerAttribute Order;

    public Question(DataModelItem parent, String id) : base(id, parent)
    {
      Name = new StringAttribute(parent: this, displayName:"Name");
      Order = new IntegerAttribute(parent: this, displayName:"Order", allowNegatives:false);
    }

    
    public override DataModelItem[] GetChildren(){
      var parentChildren = base.GetChildren();

      return parentChildren.Concat(new DataModelItem[] { 
        Name, Order
      }).ToArray();
    }
  }

  public class SubForm : DataModelCollectionItem
  {
    public override string DisplayName => "SubForm";

    public StringAttribute Name;

    public IntegerAttribute Order;

    public SubForm(DataModelItem parent, String id) : base(id, parent)
    {
      Name = new StringAttribute(parent: this, displayName:"Name");
      Order = new IntegerAttribute(parent: this, displayName:"Order", allowNegatives:false);

      
      Questions = new DataModelCollection<Question>("Questions", (parent, entry) => {
        return new Question(parent, entry.Key);
      }, (parent, id) => {
        return new Question(parent, id);
      }, this);

      
      SubForms = new DataModelCollection<SubForm>("SubForms", (parent, entry) => {
        return new SubForm(parent, entry.Key);
      }, (parent, id) => {
        return new SubForm(parent, id);
      }, this);
    }

    
    public DataModelCollection<Question> Questions;
    public DataModelCollection<SubForm> SubForms;

    
    
    public override DataModelItem[] GetChildren(){
      var parentChildren = base.GetChildren();

      return parentChildren.Concat(new DataModelItem[] { 
        Name, Order, Questions, SubForms
      }).ToArray();
    }
  }
}
