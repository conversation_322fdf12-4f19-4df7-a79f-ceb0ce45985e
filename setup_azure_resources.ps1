# PowerShell script to create Azure resources for APM app migration
# Run this script in PowerShell to set up the required Azure resources

# Parameters - customize these values
$subscriptionId = "YOUR_SUBSCRIPTION_ID"
$location = "eastus"
$resourceGroupName = "apm-resources"
$cosmosDBAccountName = "apm-cosmos-db"
$databaseName = "apm-database"
$storageAccountName = "apmstorageaccount"
$containerName = "inspections"
$appInsightsName = "apm-app-insights"
$functionAppName = "apm-functions"
$servicePlanName = "apm-service-plan"

# Login to Azure
Write-Host "Logging in to Azure..."
az login

# Set the subscription
Write-Host "Setting subscription..."
az account set --subscription $subscriptionId

# Create a resource group
Write-Host "Creating resource group..."
az group create --name $resourceGroupName --location $location

# Create a Cosmos DB account
Write-Host "Creating Cosmos DB account..."
az cosmosdb create --name $cosmosDBAccountName --resource-group $resourceGroupName --locations regionName=$location

# Create a database
Write-Host "Creating database..."
az cosmosdb sql database create --account-name $cosmosDBAccountName --resource-group $resourceGroupName --name $databaseName

# Create containers for collections
$collections = @("inspections", "assets", "users")
foreach ($collection in $collections) {
    Write-Host "Creating collection $collection..."
    az cosmosdb sql container create `
        --account-name $cosmosDBAccountName `
        --resource-group $resourceGroupName `
        --database-name $databaseName `
        --name $collection `
        --partition-key-path "/id"
}

# Create a storage account
Write-Host "Creating storage account..."
az storage account create `
    --name $storageAccountName `
    --resource-group $resourceGroupName `
    --location $location `
    --sku Standard_LRS `
    --encryption-services blob

# Get storage account key
$storageKey = $(az storage account keys list --resource-group $resourceGroupName --account-name $storageAccountName --query [0].value -o tsv)

# Create a container
Write-Host "Creating blob container..."
az storage container create `
    --name $containerName `
    --account-name $storageAccountName `
    --account-key $storageKey

# Create Application Insights
Write-Host "Creating Application Insights..."
az monitor app-insights component create `
    --app $appInsightsName `
    --location $location `
    --resource-group $resourceGroupName `
    --application-type web

# Create App Service Plan
Write-Host "Creating App Service Plan..."
az appservice plan create `
    --name $servicePlanName `
    --resource-group $resourceGroupName `
    --location $location `
    --sku B1

# Create Function App
Write-Host "Creating Function App..."
az functionapp create `
    --name $functionAppName `
    --storage-account $storageAccountName `
    --consumption-plan-location $location `
    --resource-group $resourceGroupName `
    --functions-version 4

# Configure CORS for storage account
Write-Host "Configuring CORS for storage account..."
az storage cors add `
    --account-name $storageAccountName `
    --account-key $storageKey `
    --services b `
    --methods GET POST PUT DELETE OPTIONS `
    --origins '*' `
    --allowed-headers '*' `
    --exposed-headers '*' `
    --max-age 200

# Set function app settings
Write-Host "Configuring Function App settings..."
az functionapp config appsettings set `
    --name $functionAppName `
    --resource-group $resourceGroupName `
    --settings `
        "AzureWebJobsStorage=DefaultEndpointsProtocol=https;AccountName=$storageAccountName;AccountKey=$storageKey;EndpointSuffix=core.windows.net" `
        "FUNCTIONS_WORKER_RUNTIME=dotnet" `
        "CosmosDBConnection=$(az cosmosdb keys list --name $cosmosDBAccountName --resource-group $resourceGroupName --query primaryMasterKey -o tsv)" `
        "APPINSIGHTS_INSTRUMENTATIONKEY=$(az monitor app-insights component show --app $appInsightsName --resource-group $resourceGroupName --query instrumentationKey -o tsv)"

Write-Host "Setup complete! Azure resources have been created."
Write-Host "Next steps:"
Write-Host "1. Set up Azure AD B2C tenant and configure authentication"
Write-Host "2. Update your app code with the new connection strings"
Write-Host "3. Deploy your image resizing function to Azure Functions"
