//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionOvergrownvegetation.dart';

// ignore: camel_case_types
class SectionOvergrownvegetationPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionOvergrownvegetation sectionOvergrownvegetation;

  const SectionOvergrownvegetationPage(this.sectionOvergrownvegetation,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionOvergrownvegetationPageState();
  }
}

class _SectionOvergrownvegetationPageState
    extends State<SectionOvergrownvegetationPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionOvergrownvegetation,
        title: "Overgrown vegetation",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionOvergrownvegetation
                      .attributeIs_there_overgrown_vegation
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionOvergrownvegetation.attributeAbatement_required
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
