//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionLeak_Report_F
{
  public class SectionLeakReport : DataModelItem {

    public override String DisplayName { 
      get {
        return "Leak Report";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public StringAttribute attributeEquipment_ID;
    public PredefinedValueAttribute attributeEquipment_Description;
    public StringAttribute attributeEquipment_ID_at_line_START;
    public StringAttribute attributeEquipment_ID_at_line_END;
    public PredefinedValueAttribute attributePipe_Size;
    public PredefinedValueAttribute attributePipe_Schedule;
    public PredefinedValueAttribute attributeProcessService;
    public StringAttribute attributePipe_Cover;
    public IntegerAttribute attributeAffected_Length;
    public DoubleAttribute attributeDistance_between_tie_in_points;
    public MultiPredefinedValueAttribute attributeCorrosion_Type;
    public IntegerAttribute attributeEstimated_Loss_Rate;
    public IntegerAttribute attributeExisting_clamp_count_same_line;
    public IntegerAttribute attributeFeatureFitting_count_same_line;
    public StringAttribute attributeObservation_Summary;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionLeakReport";

    public SectionLeakReport(DataModelItem parent) : base(parent)
    {
            
        attributeEquipment_ID = new StringAttribute(this, displayName: "Equipment ID", databaseName: "LR_Q105"); 
     
        attributeEquipment_Description = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("On-Plot (Facility)", null),
          new PredefinedValueOption("Off-Plot (Field)", null)
        }, false, this, "Equipment Description", databaseName: "LR_Q106"); 
     
        attributeEquipment_ID_at_line_START = new StringAttribute(this, displayName: "Equipment ID at line START", databaseName: "LR_Q107"); 
     
        attributeEquipment_ID_at_line_END = new StringAttribute(this, displayName: "Equipment ID at line END", databaseName: "LR_Q108"); 
     
        attributePipe_Size = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("0.5", null),
          new PredefinedValueOption("0.75", null),
          new PredefinedValueOption("1", null),
          new PredefinedValueOption("1.25", null),
          new PredefinedValueOption("1.5", null),
          new PredefinedValueOption("2", null),
          new PredefinedValueOption("2.5", null),
          new PredefinedValueOption("3", null),
          new PredefinedValueOption("3.5", null),
          new PredefinedValueOption("4", null),
          new PredefinedValueOption("4.5", null),
          new PredefinedValueOption("5", null),
          new PredefinedValueOption("6", null),
          new PredefinedValueOption("8", null),
          new PredefinedValueOption("10", null),
          new PredefinedValueOption("12", null),
          new PredefinedValueOption("14", null),
          new PredefinedValueOption("16", null),
          new PredefinedValueOption("18", null),
          new PredefinedValueOption("20", null),
          new PredefinedValueOption("24", null),
          new PredefinedValueOption("30", null),
          new PredefinedValueOption("36", null),
          new PredefinedValueOption("42", null)
        }, false, this, "Pipe Size", databaseName: "LR_Q109", unit: "in"); 
     
        attributePipe_Schedule = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("5", null),
          new PredefinedValueOption("10", null),
          new PredefinedValueOption("20", null),
          new PredefinedValueOption("30", null),
          new PredefinedValueOption("40", null),
          new PredefinedValueOption("50", null),
          new PredefinedValueOption("60", null),
          new PredefinedValueOption("70", null),
          new PredefinedValueOption("80", null),
          new PredefinedValueOption("100", null),
          new PredefinedValueOption("120", null),
          new PredefinedValueOption("140", null),
          new PredefinedValueOption("160", null),
          new PredefinedValueOption("STD", null),
          new PredefinedValueOption("EH", null),
          new PredefinedValueOption("DBL.EH", null)
        }, false, this, "Pipe Schedule", databaseName: "LR_Q110"); 
     
        attributeProcessService = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Oil", null),
          new PredefinedValueOption("Gas", null),
          new PredefinedValueOption("Water", null),
          new PredefinedValueOption("Steam", null),
          new PredefinedValueOption("Air", null)
        }, true, this, "Process/Service", databaseName: "LR_Q111"); 
     
        attributePipe_Cover = new StringAttribute(this, displayName: "Pipe Cover", databaseName: "LR_Q112"); 
     
        attributeAffected_Length = new IntegerAttribute(this, displayName: "Affected Length", databaseName: "LR_Q113", areCommentsRequired: false, displayUnit: "ft", allowNegatives: true); 
     
        attributeDistance_between_tie_in_points = new DoubleAttribute(this, displayName: "Distance between tie-in-points", databaseName: "LR_Q114", areCommentsRequired: false, displayUnit: "ft", allowNegatives: true); 
     
        attributeCorrosion_Type = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("External", null),
          new PredefinedValueOption("Internal", null),
          new PredefinedValueOption("None", null)
        }, true, this, "Corrosion Type", databaseName: "LR_Q115"); 
     
        attributeEstimated_Loss_Rate = new IntegerAttribute(this, displayName: "Estimated Loss Rate", databaseName: "LR_Q116", areCommentsRequired: false, displayUnit: "bpd", allowNegatives: true); 
     
        attributeExisting_clamp_count_same_line = new IntegerAttribute(this, displayName: "Existing clamp count (same line)", databaseName: "LR_Q117", areCommentsRequired: false, displayUnit: null, allowNegatives: true); 
     
        attributeFeatureFitting_count_same_line = new IntegerAttribute(this, displayName: "Feature/Fitting count (same line)", databaseName: "LR_Q118", areCommentsRequired: false, displayUnit: null, allowNegatives: true); 
     
        attributeObservation_Summary = new StringAttribute(this, displayName: "Observation Summary", databaseName: "LR_Q119"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeEquipment_ID,
           attributeEquipment_Description,
           attributeEquipment_ID_at_line_START,
           attributeEquipment_ID_at_line_END,
           attributePipe_Size,
           attributePipe_Schedule,
           attributeProcessService,
           attributePipe_Cover,
           attributeAffected_Length,
           attributeDistance_between_tie_in_points,
           attributeCorrosion_Type,
           attributeEstimated_Loss_Rate,
           attributeExisting_clamp_count_same_line,
           attributeFeatureFitting_count_same_line,
           attributeObservation_Summary,
        };
    }
  }
}
