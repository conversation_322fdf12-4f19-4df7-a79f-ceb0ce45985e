import 'package:api_inspection/generic/DataModelCommon/DataModel/user.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'package:api_inspection/generic/AppRoot.dart';
import 'package:api_inspection/generic/AppStyle.dart';

class UserEditPage extends StatefulWidget {
  final UserProfile user;
  const UserEditPage(this.user, {Key? key}) : super(key: key);

  @override
  _UserEditPageState createState() => _UserEditPageState();
}

class _UserEditPageState extends State<UserEditPage> {
  void onRoleChanged(String newRole) {
    setState(() {
      widget.user.role = newRole;
      var batch = FirebaseFirestore.instance.batch();
      widget.user.saveItem(batch);

      batch.commit();
    });
  }

  Color selectedColor = const Color.fromARGB(255, 4, 188, 242);
  Color unselectedColor = const Color.fromARGB(255, 122, 122, 122);

  @override
  Widget build(BuildContext context) {
    var user = widget.user;
    Widget roleRow;

    if (user.email == AppRoot.global().currentUser!.email) {
      roleRow = Container(
        margin: const EdgeInsets.fromLTRB(20, 10, 20, 0),
        child: Text(
          AppRoot.global().currentUser!.role ?? "No Role Set",
          style: const TextStyle(color: Colors.white, fontSize: 22),
        ),
      );
    } else {
      var adminButton = TeamToggleButton(
          child: const Center(
              child: Text("Admin", style: TextStyle(color: Colors.white))),
          borderColor: user.role == "admin" ? selectedColor : unselectedColor,
          onPressed: () {
            onRoleChanged("admin");
          });
      var technicianButton = TeamToggleButton(
          child: const Center(
              child: Text(
            "Technician",
            style: TextStyle(color: Colors.white),
          )),
          borderColor:
              user.role == "technician" ? selectedColor : unselectedColor,
          onPressed: () {
            onRoleChanged("technician");
          });
      var noRoleButton = TeamToggleButton(
          child: const Center(
              child: Text(
            "None",
            style: TextStyle(color: Colors.white),
          )),
          borderColor: user.role == "none" ? selectedColor : unselectedColor,
          onPressed: () {
            onRoleChanged("none");
          });

      roleRow = Container(
          margin: const EdgeInsets.fromLTRB(20, 10, 20, 10),
          child: Wrap(
            children: [
              SizedBox(width: 120, height: 75, child: adminButton),
              SizedBox(width: 120, height: 75, child: technicianButton),
              SizedBox(width: 120, height: 75, child: noRoleButton),
            ],
          ));
    }

    return Scaffold(
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        appBar: AppBar(
          title: Text(
            user.email,
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        body: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Container(
                margin: const EdgeInsets.fromLTRB(20, 10, 20, 0),
                child: const Text(
                  "Role",
                  style: TextStyle(color: Colors.white, fontSize: 22),
                ),
              ),
              roleRow,
              AttributePadding.WithStdPadding(user.name.buildWidget()),
            ],
          ),
        ));
  }
}
