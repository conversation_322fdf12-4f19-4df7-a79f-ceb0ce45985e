﻿using System;
using System.Collections.Generic;

namespace CommonDataInterface.DatabaseHelpers
{
  public interface IDictionaryWrapper
  {
    public DataEntry[] GetEntries();


    public DataEntry this[String key] {
      get;
    }

    public bool ContainsKey(String key);

    public static IDictionaryWrapper Create(object obj)
    {
      if (obj is IDictionaryWrapper wrapper)
        return wrapper;

      if (obj is Dictionary<string, object> dictionary) {
        return new DictionaryWrapper_Dictionary(dictionary);
      }
      else if (obj is Newtonsoft.Json.Linq.JObject jObj) {
        return new DictionaryWrapper_JObject(jObj);
      }

      return null;
    }
  }
}