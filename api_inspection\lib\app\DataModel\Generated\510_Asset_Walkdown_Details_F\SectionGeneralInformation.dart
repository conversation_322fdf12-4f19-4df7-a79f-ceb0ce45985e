//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'SectionPhotos.dart';
import 'SectionDesign.dart';
import 'SectionInspection.dart';
import 'SectionDataPlate.dart';
import 'SectionManufacturer.dart';
import 'SectionService.dart';
import 'SectionInspectionOpenings.dart';
import 'SectionRepairRecord.dart';

// ignore: camel_case_types
class SectionGeneralInformation extends DataModelSection {
  @override
  String getDisplayName() => "General Information";
  SectionGeneralInformation(DataModelItem? parent)
      : super(parent: parent, sectionName: "General Information");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeOrientation = PredefinedValueAttribute(
      parent: this,
      hasOtherOption: false,
      displayName: "Orientation",
      databaseName: "510AW_Q140",
      availableOptions: [
        PredefinedValueOption("Horizontal", null, isCommentRequired: false),
        PredefinedValueOption("Vertical", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeRT = PredefinedValueAttribute(
      parent: this,
      hasOtherOption: false,
      displayName: "RT",
      databaseName: "510AW_Q141",
      availableOptions: [
        PredefinedValueOption("Spot", null, isCommentRequired: false),
        PredefinedValueOption("Full", null, isCommentRequired: false),
        PredefinedValueOption("None", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late DateAttribute attributeInstallation_Date = DateAttribute(
      parent: this,
      displayName: "Installation Date",
      databaseName: "510AW_Q142",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late DateAttribute attributeIn_service_Date = DateAttribute(
      parent: this,
      displayName: "In-service Date",
      databaseName: "510AW_Q143",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributePID_Number = StringAttribute(
      parent: this,
      displayName: "P&ID Number",
      databaseName: "510AW_Q144",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeConstructionDesign_Drawing_Number =
      StringAttribute(
          parent: this,
          displayName: "Construction/Design Drawing Number",
          databaseName: "510AW_Q145",
          areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeLowest_Flange_Rating =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Lowest Flange Rating",
          databaseName: "510AW_Q146",
          availableOptions: [
        PredefinedValueOption("150", null, isCommentRequired: false),
        PredefinedValueOption("300", null, isCommentRequired: false),
        PredefinedValueOption("400", null, isCommentRequired: false),
        PredefinedValueOption("600", null, isCommentRequired: false),
        PredefinedValueOption("900", null, isCommentRequired: false),
        PredefinedValueOption("1500", null, isCommentRequired: false),
        PredefinedValueOption("2500", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late IntegerAttribute attributeHydro_Test_Pressure = IntegerAttribute(
    parent: this,
    displayName: "Hydro Test Pressure",
    databaseName: "510AW_Q147",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "",
  );

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeType_of_construction =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: true,
          displayName: "Type of construction",
          databaseName: "510AW_Q148",
          availableOptions: [
        PredefinedValueOption("Welded", null, isCommentRequired: false),
        PredefinedValueOption("Presssure Welded", null,
            isCommentRequired: false),
        PredefinedValueOption("Brazed", null, isCommentRequired: false),
        PredefinedValueOption("Resistance Welded", null,
            isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributePost_Weld_Heat_Treatment =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Post Weld Heat Treatment",
          databaseName: "510AW_Q149",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Unknown", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute
      attributeHas_the_equipment_been_de_rated_or_re_rated =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Has the equipment been de-rated or re-rated?",
          databaseName: "510AW_Q150",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Unknown", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeIs_this_a_fired_pressure_vessel =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Is this a fired pressure vessel?",
          databaseName: "510AW_Q151",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

// ignore: non_constant_identifier_names
  late SectionPhotos sectionPhotos = SectionPhotos(this);
  // ignore: non_constant_identifier_names
  late SectionDesign sectionDesign = SectionDesign(this);
  // ignore: non_constant_identifier_names
  late SectionInspection sectionInspection = SectionInspection(this);
  // ignore: non_constant_identifier_names
  late SectionDataPlate sectionDataPlate = SectionDataPlate(this);
  // ignore: non_constant_identifier_names
  late SectionManufacturer sectionManufacturer = SectionManufacturer(this);
  // ignore: non_constant_identifier_names
  late SectionService sectionService = SectionService(this);
  // ignore: non_constant_identifier_names
  late SectionRepairRecord sectionRepairRecord = SectionRepairRecord(this);

// ignore: non_constant_identifier_names
  late DataModelCollection<SectionInspectionOpenings>
      sectionInspectionOpenings =
      DataModelCollection<SectionInspectionOpenings>("Inspection Openings",
          (parent, entry) {
    return SectionInspectionOpenings(entry.key, sectionInspectionOpenings);
  }, this);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      sectionPhotos,
      sectionDesign,
      sectionInspection,
      sectionDataPlate,
      sectionManufacturer,
      sectionService,
      sectionInspectionOpenings,
      sectionRepairRecord,
      attributeOrientation,
      attributeRT,
      attributeInstallation_Date,
      attributeIn_service_Date,
      attributePID_Number,
      attributeConstructionDesign_Drawing_Number,
      attributeLowest_Flange_Rating,
      attributeHydro_Test_Pressure,
      attributeType_of_construction,
      attributePost_Weld_Heat_Treatment,
      attributeHas_the_equipment_been_de_rated_or_re_rated,
      attributeIs_this_a_fired_pressure_vessel,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionGeneralInformation";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
