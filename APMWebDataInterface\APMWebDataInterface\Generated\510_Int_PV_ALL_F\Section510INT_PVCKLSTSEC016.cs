//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Int_PV_ALL_F
{
  public class Section510INT_PVCKLSTSEC016 : DataModelItem {

    public override String DisplayName { 
      get {
        return "MISCELLANEOUS";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC016Q001;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC016Q002;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC016Q003;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC016Q004;
    public PredefinedValueAttribute attributeAre_threaded_connections_acceptably_engaged_and_leak_free;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC016Q006;
    public PredefinedValueAttribute attributeAre_threaded_connections_acceptable_for_continued_service;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC016Q008;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510INT_PVCKLSTSEC016";

    public Section510INT_PVCKLSTSEC016(DataModelItem parent) : base(parent)
    {
            
        attribute510INT_PVCKLSTSEC016Q001 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Regardless of attachment type are all gasket sealing surfaces in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC016_Q001"); 
     
        attribute510INT_PVCKLSTSEC016Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Regardless of attachment type is all attachment hardware in acceptable condition for continued service: (nuts & bolts)", databaseName: "510_INT-PV_CKLST_SEC016_Q002"); 
     
        attribute510INT_PVCKLSTSEC016Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are auxiliary equipment connections and adjacent shell areas for gauge connections, thermal indicators etc. in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC016_Q003"); 
     
        attribute510INT_PVCKLSTSEC016Q004 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are there any threaded connections associated with the asset:", databaseName: "510_INT-PV_CKLST_SEC016_Q004"); 
     
        attributeAre_threaded_connections_acceptably_engaged_and_leak_free = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are threaded connections acceptably engaged and leak free:", databaseName: "510_INT-PV_CKLST_SEC016_Q005"); 
     
        attribute510INT_PVCKLSTSEC016Q006 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is threaded connection piping constructed from schedule 80 or greater piping:", databaseName: "510_INT-PV_CKLST_SEC016_Q006"); 
     
        attributeAre_threaded_connections_acceptable_for_continued_service = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are threaded connections acceptable for continued service:", databaseName: "510_INT-PV_CKLST_SEC016_Q007"); 
     
        attribute510INT_PVCKLSTSEC016Q008 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Were drain connections verified to be free of any foreign material that may cause plugging:", databaseName: "510_INT-PV_CKLST_SEC016_Q008"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute510INT_PVCKLSTSEC016Q001,
           attribute510INT_PVCKLSTSEC016Q002,
           attribute510INT_PVCKLSTSEC016Q003,
           attribute510INT_PVCKLSTSEC016Q004,
           attributeAre_threaded_connections_acceptably_engaged_and_leak_free,
           attribute510INT_PVCKLSTSEC016Q006,
           attributeAre_threaded_connections_acceptable_for_continued_service,
           attribute510INT_PVCKLSTSEC016Q008,
        };
    }
  }
}
