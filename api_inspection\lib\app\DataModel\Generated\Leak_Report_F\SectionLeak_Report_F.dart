//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'SectionWorkDetail.dart';
import 'SectionLeakReport.dart';
import 'SectionPhotos.dart';

// ignore: camel_case_types
class SectionLeak_Report_F extends DataModelSection {
  @override
  String getDisplayName() => "Leak Report";
  SectionLeak_Report_F(DataModelItem? parent)
      : super(parent: parent, sectionName: "Leak Report");

// ignore: non_constant_identifier_names
  late SectionWorkDetail sectionWorkDetail = SectionWorkDetail(this);
  // ignore: non_constant_identifier_names
  late SectionLeakReport sectionLeakReport = SectionLeakReport(this);
  // ignore: non_constant_identifier_names
  late SectionPhotos sectionPhotos = SectionPhotos(this);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      sectionWorkDetail,
      sectionLeakReport,
      sectionPhotos,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionLeak_Report_F";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
