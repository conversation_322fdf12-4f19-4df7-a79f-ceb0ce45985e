//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionWorkDetail.dart';

// ignore: camel_case_types
class SectionWorkDetailPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionWorkDetail sectionWorkDetail;

  const SectionWorkDetailPage(this.sectionWorkDetail, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionWorkDetailPageState();
  }
}

class _SectionWorkDetailPageState extends State<SectionWorkDetailPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionWorkDetail,
        title: "Work Detail",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionWorkDetail.attributeClient
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionWorkDetail.attributeCity
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionWorkDetail.attributeState
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionWorkDetail.attributePostal_Code
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionWorkDetail.attributeArea
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionWorkDetail.attributeLease
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionWorkDetail.attributeFacility
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionWorkDetail.attributeJob_Description
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionWorkDetail.attributeClient_Contact
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionWorkDetail.attributeClient_Contact_Number
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionWorkDetail.attributePurchase_OrderAFE
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionWorkDetail.attributeClient_Cost_Code
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionWorkDetail.attributeClient_Work_Order
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionWorkDetail.attributeTeam_District
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionWorkDetail.attributeTeam_Project_Number
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionWorkDetail.attributeInspection_Reference
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionWorkDetail.attributeReference_EditionRevision
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionWorkDetail.attributeInspection_Type
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionWorkDetail.attributeInspected_By
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionWorkDetail.attributeInspection_Date
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionWorkDetail.attributeInspector_Certificate_Number
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionWorkDetail.attributeReviewed_By
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionWorkDetail.attributeReviewer_Email
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionWorkDetail.attributeReviewer_Certificate_Number
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
