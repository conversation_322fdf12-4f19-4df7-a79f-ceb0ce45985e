//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Asset_Walkdown_Details_F
{
  public class SectionShellSide : DataModelItem {

    public override String DisplayName { 
      get {
        return "Shell Side";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public DoubleAttribute attributeDesign_MAWP;
    public DoubleAttribute attributeDesign_Temperature;
    public IntegerAttribute attributeOperating_Temperature;
    public DoubleAttribute attributeOperating_Pressure;
    public DoubleAttribute attributePRV_Set_Pressure;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionShellSide";

    public SectionShellSide(DataModelItem parent) : base(parent)
    {
            
        attributeDesign_MAWP = new DoubleAttribute(this, displayName: "Design MAWP", databaseName: "510AW_Q311", areCommentsRequired: false, displayUnit: "psi", allowNegatives: true); 
     
        attributeDesign_Temperature = new DoubleAttribute(this, displayName: "Design Temperature", databaseName: "510AW_Q312", areCommentsRequired: false, displayUnit: "F", allowNegatives: true); 
     
        attributeOperating_Temperature = new IntegerAttribute(this, displayName: "Operating Temperature", databaseName: "510AW_Q313", areCommentsRequired: false, displayUnit: "F", allowNegatives: true); 
     
        attributeOperating_Pressure = new DoubleAttribute(this, displayName: "Operating Pressure", databaseName: "510AW_Q314", areCommentsRequired: false, displayUnit: "psi", allowNegatives: true); 
     
        attributePRV_Set_Pressure = new DoubleAttribute(this, displayName: "PRV Set Pressure", databaseName: "510AW_Q315", areCommentsRequired: false, displayUnit: "psi", allowNegatives: true); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeDesign_MAWP,
           attributeDesign_Temperature,
           attributeOperating_Temperature,
           attributeOperating_Pressure,
           attributePRV_Set_Pressure,
        };
    }
  }
}
