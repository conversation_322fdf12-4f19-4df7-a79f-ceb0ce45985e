//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Ext_PV_ALL_F
{
  public class Section510EXT_PVCKLSTSEC007 : DataModelItem {

    public override String DisplayName { 
      get {
        return "GUY WIRES";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributeAre_guy_wires_utilized_to_support_the_asset;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC007Q002;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC007Q003;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC007Q004;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC007Q005;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC007Q006;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC007Q007;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC007Q008;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510EXT_PVCKLSTSEC007";

    public Section510EXT_PVCKLSTSEC007(DataModelItem parent) : base(parent)
    {
            
        attributeAre_guy_wires_utilized_to_support_the_asset = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are guy wires utilized to support the asset:", databaseName: "510_EXT-PV_CKLST_SEC007_Q001"); 
     
        attribute510EXT_PVCKLSTSEC007Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Cracking", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Distortion", null)
        }, false, this, "Is there any evidence of corrosion, distortion, and or cracking of the attachment welds or adjacent shell areas of the anchor point to asset connection:", databaseName: "510_EXT-PV_CKLST_SEC007_Q002"); 
     
        attribute510EXT_PVCKLSTSEC007Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are the guy wire connections to the asset and to each ground anchor point sufficiently tightened and tensioned correctly:", databaseName: "510_EXT-PV_CKLST_SEC007_Q003"); 
     
        attribute510EXT_PVCKLSTSEC007Q004 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are the termination points of the guy wires in acceptable condition for continued service: (I.e. concrete dead-man anchors)", databaseName: "510_EXT-PV_CKLST_SEC007_Q004"); 
     
        attribute510EXT_PVCKLSTSEC007Q005 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are wire rope clips in acceptable condition for continued service:  (I.e. no corrosion and correct clip orientation)", databaseName: "510_EXT-PV_CKLST_SEC007_Q005"); 
     
        attribute510EXT_PVCKLSTSEC007Q006 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the wire rope clip spacing correct:  (I.e. spaced at least six rope diameters apart)", databaseName: "510_EXT-PV_CKLST_SEC007_Q006"); 
     
        attribute510EXT_PVCKLSTSEC007Q007 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the number of wire rope clips appropriate based on the diameter of the wire rope:", databaseName: "510_EXT-PV_CKLST_SEC007_Q007"); 
     
        attribute510EXT_PVCKLSTSEC007Q008 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are the guy wires in acceptable condition for continued service:  (i.e. no corrosion or broken strands noted )", databaseName: "510_EXT-PV_CKLST_SEC007_Q008"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeAre_guy_wires_utilized_to_support_the_asset,
           attribute510EXT_PVCKLSTSEC007Q002,
           attribute510EXT_PVCKLSTSEC007Q003,
           attribute510EXT_PVCKLSTSEC007Q004,
           attribute510EXT_PVCKLSTSEC007Q005,
           attribute510EXT_PVCKLSTSEC007Q006,
           attribute510EXT_PVCKLSTSEC007Q007,
           attribute510EXT_PVCKLSTSEC007Q008,
        };
    }
  }
}
