//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionInternalAccessConditions.dart';
import 'SectionInternalAccessRequirementsPage.dart';
import 'SectionCleaningRequirementsPage.dart';
import 'SectionInternalCoatingLinerPage.dart';

// ignore: camel_case_types
class SectionInternalAccessConditionsPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionInternalAccessConditions sectionInternalAccessConditions;

  const SectionInternalAccessConditionsPage(
      this.sectionInternalAccessConditions,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionInternalAccessConditionsPageState();
  }
}

class _SectionInternalAccessConditionsPageState
    extends State<SectionInternalAccessConditionsPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionInternalAccessConditions,
        title: "Internal Access Conditions",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionInternalAccessConditions
                      .attributeIs_the_asset_out_of_service
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionInternalAccessConditions
                            .sectionInternalAccessRequirements,
                        onPressed: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      SectionInternalAccessRequirementsPage(widget
                                          .sectionInternalAccessConditions
                                          .sectionInternalAccessRequirements))).then(
                              (value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionInternalAccessConditions
                            .sectionCleaningRequirements,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          SectionCleaningRequirementsPage(widget
                                              .sectionInternalAccessConditions
                                              .sectionCleaningRequirements)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionInternalAccessConditions
                            .sectionInternalCoatingLiner,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          SectionInternalCoatingLinerPage(widget
                                              .sectionInternalAccessConditions
                                              .sectionInternalCoatingLiner)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                ]),
          );
        });
  }
}
