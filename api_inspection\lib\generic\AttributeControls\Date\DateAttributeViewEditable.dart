import 'package:api_inspection/app/batch_helper.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';

class DateAttributeViewEditable extends StatefulWidget {
  final DateAttribute _attribute;
  const DateAttributeViewEditable(this._attribute, {Key? key})
      : super(key: key);

  @override
  _DateAttributeViewEditableState createState() =>
      _DateAttributeViewEditableState();
}

class _DateAttributeViewEditableState extends State<DateAttributeViewEditable> {
  @override
  void initState() {
    DateAttribute attribute = widget._attribute;
    attribute.addListener(onAttributeChanged);

    super.initState();
  }

  void onAttributeChanged() {
    setState(() {});
  }

  @override
  void dispose() {
    DateAttribute attribute = widget._attribute;
    attribute.removeListener(onAttributeChanged);
    super.dispose();
  }

  void selectDateClicked() async {
    var date = await showDatePicker(
        context: context,
        initialDate: DateTime.now(),
        firstDate: DateTime(1900),
        lastDate: DateTime(2100));
    if (date != null) {
      setState(() {
        widget._attribute.setValue(date);
        BatchHelper.saveAndCommit(widget._attribute);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget buttonText;
    if (widget._attribute.getValue() == null) {
      buttonText = Text("Select a date",
          style: TextStyle(color: Colors.grey[300], fontSize: 16));
    } else {
      buttonText =
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Text(widget._attribute.getPreviewText(),
            style: const TextStyle(color: Colors.white, fontSize: 16)),
        IconButton(
            onPressed: () {
              setState(() {
                widget._attribute.setValue(null);
                BatchHelper.saveAndCommit(widget._attribute);
              });
            },
            icon: const Icon(
              Icons.clear,
              color: Colors.white,
              size: 34,
            ))
      ]);
    }

    var attributeTextField = Container(
      margin: const EdgeInsets.fromLTRB(20, 10, 10, 10),
      child: Column(crossAxisAlignment: CrossAxisAlignment.stretch, children: [
        Text(
          widget._attribute.displayName,
          style: const TextStyle(color: Colors.white, fontSize: 16),
          textAlign: TextAlign.start,
        ),
        GestureDetector(
          onTap: selectDateClicked,
          key: const ValueKey('DateField'),
          child: Container(
              margin: const EdgeInsets.fromLTRB(0, 0, 10, 0),
              height: 60,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.white, width: 1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Container(
                margin: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                alignment: Alignment.centerLeft,
                child: buttonText,
              )),
        )
      ]),
    );

    return attributeTextField;
  }
}
