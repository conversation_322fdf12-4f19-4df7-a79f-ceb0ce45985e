﻿using System;
using System.Linq;

namespace CommonDataInterface.Attributes
{
  public class DoubleAttribute : SingleAttributeBase<double?> {
    
    public override bool IsValueEqualTo(String other)
    {
      var currentValue = GetValue();
      if (String.IsNullOrWhiteSpace(other)) {
        return currentValue == null;
      }

      if (double.TryParse(other, out var parsed)) {
        if (currentValue == null)
          return false;

        return Math.Abs(currentValue.Value - parsed) < 0.000001;
      }

      throw new Exception("Could not parse as integer: " + other);
    }

    public override void SetGenericValueTo(String other)
    { 
      if (String.IsNullOrWhiteSpace(other)) {
        SetValue(null);
        return;
      }

      if (double.TryParse(other, out var parsed)) {
        SetValue(parsed);
        return;
      }

      throw new Exception("Could not parse as integer: " + other);
    }


    public override String AttributeType => "Double";
    public double? GetValue() {
      if (this.GetValueChangeLog().entries.Count == 0)
        return null;
      return this.GetValueChangeLog().entries.Last().Value;
    }

    public void SetValue(double? value){
      var currentValue = GetValue();
      if (value == null && currentValue == null) {
        return;
      }

      if (currentValue != null && value != null) {
        if (Math.Abs(currentValue.Value - value.Value) < 0.000001)
          return;
      }

      this.GetValueChangeLog().PendingChange = new PendingChange<double?>{Value = value};
      
      NotifyListeners();
    }
    
    public bool allowNegatives { get; set; }
    public String unit { get; set; }

    public DoubleAttribute(DataModelItem parent, String displayName, bool allowNegatives, String databaseName = null, bool areCommentsRequired = false, String displayUnit = null)
      : base(parent, displayName, databaseName, areCommentsRequired)
    {
      this.unit = displayUnit;
      GetValueChangeLog().SetConversionMethod(convertDynamicToDouble);
    }

    double? convertDynamicToDouble(object obj)
    {
      if (double.TryParse(obj?.ToString(), out var result)) {
        return result;
      }

      return null;
    }

    

    
    public override String GetPreviewText(){
      var value = GetValue();
      return value == null ? "" : value.ToString();
    }
      
  }
}