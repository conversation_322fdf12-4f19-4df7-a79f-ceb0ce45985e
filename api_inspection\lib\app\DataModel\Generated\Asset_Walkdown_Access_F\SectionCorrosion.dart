//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class SectionCorrosion extends DataModelSection {
  @override
  String getDisplayName() => "Corrosion";
  SectionCorrosion(DataModelItem? parent)
      : super(parent: parent, sectionName: "Corrosion");

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeCorrosion_identified =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: true,
          displayName: "Corrosion identified?",
          databaseName: "AWA_Q121",
          availableOptions: [
        PredefinedValueOption("Rust", null, isCommentRequired: false),
        PredefinedValueOption("Scale", null, isCommentRequired: false),
        PredefinedValueOption("Oxidation", null, isCommentRequired: false),
        PredefinedValueOption("None", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeCorrosion_removal_recommendation =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: true,
          displayName: "Corrosion removal recommendation",
          databaseName: "AWA_Q122",
          availableOptions: [
        PredefinedValueOption("Wire Brush", null, isCommentRequired: false),
        PredefinedValueOption("Power Tools", null, isCommentRequired: false),
        PredefinedValueOption("Sandblasting", null, isCommentRequired: false),
        PredefinedValueOption("None", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeCorrosion_identified,
      attributeCorrosion_removal_recommendation,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionCorrosion";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
