//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'Section510_Ext_PV_ALL_F.dart';
import 'Section510EXT_PVCKLSTSEC001Page.dart';
import 'Section510EXT_PVCKLSTSEC002Page.dart';
import 'Section510EXT_PVCKLSTSEC003Page.dart';
import 'Section510EXT_PVCKLSTSEC004Page.dart';
import 'Section510EXT_PVCKLSTSEC005Page.dart';
import 'Section510EXT_PVCKLSTSEC006Page.dart';
import 'Section510EXT_PVCKLSTSEC007Page.dart';
import 'Section510EXT_PVCKLSTSEC008Page.dart';
import 'Section510EXT_PVCKLSTSEC009Page.dart';
import 'Section510EXT_PVCKLSTSEC010Page.dart';
import 'Section510EXT_PVCKLSTSEC011Page.dart';
import 'Section510EXT_PVCKLSTSEC012Page.dart';
import 'Section510EXT_PVCKLSTSEC013Page.dart';
import 'Section510EXT_PVCKLSTSEC014Page.dart';

// ignore: camel_case_types
class Section510_Ext_PV_ALL_FPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final Section510_Ext_PV_ALL_F section510_Ext_PV_ALL_F;

  const Section510_Ext_PV_ALL_FPage(this.section510_Ext_PV_ALL_F, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _Section510_Ext_PV_ALL_FPageState();
  }
}

class _Section510_Ext_PV_ALL_FPageState
    extends State<Section510_Ext_PV_ALL_FPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.section510_Ext_PV_ALL_F,
        title: "510-Ext-PV-ALL",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Ext_PV_ALL_F
                            .section510EXT_PVCKLSTSEC001,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510EXT_PVCKLSTSEC001Page(widget
                                              .section510_Ext_PV_ALL_F
                                              .section510EXT_PVCKLSTSEC001)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Ext_PV_ALL_F
                            .section510EXT_PVCKLSTSEC002,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510EXT_PVCKLSTSEC002Page(widget
                                              .section510_Ext_PV_ALL_F
                                              .section510EXT_PVCKLSTSEC002)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Ext_PV_ALL_F
                            .section510EXT_PVCKLSTSEC003,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510EXT_PVCKLSTSEC003Page(widget
                                              .section510_Ext_PV_ALL_F
                                              .section510EXT_PVCKLSTSEC003)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Ext_PV_ALL_F
                            .section510EXT_PVCKLSTSEC004,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510EXT_PVCKLSTSEC004Page(widget
                                              .section510_Ext_PV_ALL_F
                                              .section510EXT_PVCKLSTSEC004)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Ext_PV_ALL_F
                            .section510EXT_PVCKLSTSEC005,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510EXT_PVCKLSTSEC005Page(widget
                                              .section510_Ext_PV_ALL_F
                                              .section510EXT_PVCKLSTSEC005)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Ext_PV_ALL_F
                            .section510EXT_PVCKLSTSEC006,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510EXT_PVCKLSTSEC006Page(widget
                                              .section510_Ext_PV_ALL_F
                                              .section510EXT_PVCKLSTSEC006)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Ext_PV_ALL_F
                            .section510EXT_PVCKLSTSEC007,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510EXT_PVCKLSTSEC007Page(widget
                                              .section510_Ext_PV_ALL_F
                                              .section510EXT_PVCKLSTSEC007)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Ext_PV_ALL_F
                            .section510EXT_PVCKLSTSEC008,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510EXT_PVCKLSTSEC008Page(widget
                                              .section510_Ext_PV_ALL_F
                                              .section510EXT_PVCKLSTSEC008)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Ext_PV_ALL_F
                            .section510EXT_PVCKLSTSEC009,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510EXT_PVCKLSTSEC009Page(widget
                                              .section510_Ext_PV_ALL_F
                                              .section510EXT_PVCKLSTSEC009)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Ext_PV_ALL_F
                            .section510EXT_PVCKLSTSEC010,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510EXT_PVCKLSTSEC010Page(widget
                                              .section510_Ext_PV_ALL_F
                                              .section510EXT_PVCKLSTSEC010)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Ext_PV_ALL_F
                            .section510EXT_PVCKLSTSEC011,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510EXT_PVCKLSTSEC011Page(widget
                                              .section510_Ext_PV_ALL_F
                                              .section510EXT_PVCKLSTSEC011)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Ext_PV_ALL_F
                            .section510EXT_PVCKLSTSEC012,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510EXT_PVCKLSTSEC012Page(widget
                                              .section510_Ext_PV_ALL_F
                                              .section510EXT_PVCKLSTSEC012)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Ext_PV_ALL_F
                            .section510EXT_PVCKLSTSEC013,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510EXT_PVCKLSTSEC013Page(widget
                                              .section510_Ext_PV_ALL_F
                                              .section510EXT_PVCKLSTSEC013)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Ext_PV_ALL_F
                            .section510EXT_PVCKLSTSEC014,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510EXT_PVCKLSTSEC014Page(widget
                                              .section510_Ext_PV_ALL_F
                                              .section510EXT_PVCKLSTSEC014)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                ]),
          );
        });
  }
}
