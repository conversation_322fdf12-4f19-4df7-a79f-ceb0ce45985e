import 'package:api_inspection/app/batch_helper.dart';
import 'package:api_inspection/generic/Common/Types/TimeSpan.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/TimeSpanAttribute.dart';
import 'package:api_inspection/generic/UIControls/listener.dart';
import 'package:flutter/material.dart';

class TimeSpanAttributeViewEditable extends StatefulWidget {
  final TimeSpanAttribute _attribute;
  final ListenerWrapper updateListener;

  const TimeSpanAttributeViewEditable(this._attribute, this.updateListener,
      {Key? key})
      : super(key: key);

  @override
  _TimeSpanAttributeViewEditableState createState() =>
      _TimeSpanAttributeViewEditableState();
}

class _TimeSpanAttributeViewEditableState
    extends State<TimeSpanAttributeViewEditable> {
  TextEditingController? _secondsController;
  TextEditingController? _minutesController;
  TextEditingController? _minutesandSecondsController;

  void updateAttributeValue() {
    var secondsController = _secondsController;
    var minutesController = _minutesController;
    var minutesandSecondsController = _minutesandSecondsController;

    double? parsedValue;

    if (widget._attribute.timeSpan == TimeSpan.Seconds) {
      if (secondsController != null) {
        parsedValue = double.tryParse(secondsController.text);
        widget._attribute.setValue(parsedValue);
      }
    } else if (widget._attribute.timeSpan == TimeSpan.MinutesandSeconds) {
      if (minutesController != null || minutesandSecondsController != null) {
        var parsedMinutes = double.tryParse(minutesController!.text);
        var parsedSeconds = double.tryParse(minutesandSecondsController!.text);
        widget._attribute
            .setValueForMinutesAndSeconds(parsedMinutes, parsedSeconds);
      }
    }

    BatchHelper.saveAndCommit(widget._attribute);
  }

  bool initialized = false;
  void initialize() {
    if (initialized) return;
    initialized = true;
    TimeSpanAttribute attribute = widget._attribute;
    attribute.addListener(onAttributeChanged);
  }

  bool areValuesEqual(String? str, int? value) {
    if (str == null && value == null) return true;
    if (str != null && str.isEmpty && value == null) return true;
    if (str == null || value == null) return false;

    int? strAsValue = int.tryParse(str);
    return strAsValue == value;
  }

  String doubleToString(double? value) {
    if (value == null) {
      return "";
    }
    return value.toString();
  }

  void onAttributeChanged() {
    setState(() {
      var secondsController = _secondsController;
      var minutesController = _minutesController;
      var minutesandSecondsController = _minutesandSecondsController;

      if (secondsController != null &&
          secondsController.text !=
              widget._attribute.getValueinSeconds()?.toString()) {
        String? latvalue = widget._attribute.getValueinSeconds()?.toString();
        secondsController.text = latvalue ?? "";
      }
      if (minutesController != null &&
          minutesController.text !=
              widget._attribute.getValueinMinutes()?.toString()) {
        String? longValue = widget._attribute.getValueinMinutes()?.toString();
        minutesController.text = longValue ?? "";
      }
      if (minutesandSecondsController != null &&
          minutesandSecondsController.text !=
              widget._attribute.getValueinSecondsAndMinutes()?.toString()) {
        String? longValue =
            widget._attribute.getValueinSecondsAndMinutes()?.toString();
        minutesandSecondsController.text = longValue ?? "";
      }
    });
  }

  @override
  void dispose() {
    widget.updateListener.removeListener(updateAttributeValue);

    TimeSpanAttribute attribute = widget._attribute;
    attribute.removeListener(onAttributeChanged);
    _secondsController!.dispose();
    _minutesController!.dispose();
    _minutesandSecondsController!.dispose();
    super.dispose();
  }

  @override
  void initState() {
    widget.updateListener.addListener(updateAttributeValue);
    super.initState();
  }

  @override
  void didUpdateWidget(covariant TimeSpanAttributeViewEditable oldWidget) {
    oldWidget.updateListener.removeListener(updateAttributeValue);
    widget.updateListener.addListener(updateAttributeValue);
    super.didUpdateWidget(oldWidget);
  }

  void onTextFieldFocusChanged(bool hasFocus) {
    if (!hasFocus) {
      updateAttributeValue();
    }
  }

  Widget buildTextField(String title, TextEditingController? controller) {
    var textDecorator = const InputDecoration(
      enabledBorder: OutlineInputBorder(
        borderSide: BorderSide(color: Colors.white, width: 1.0),
      ),
      border: OutlineInputBorder(
        borderSide: BorderSide(color: Colors.white, width: 1.0),
      ),
    );

    return Row(children: [
      Container(
          margin: const EdgeInsets.fromLTRB(0, 0, 0, 10),
          child: Focus(
            onFocusChange: onTextFieldFocusChanged,
            child: TextField(
              style: const TextStyle(color: Colors.white),
              controller: controller,
              keyboardType: const TextInputType.numberWithOptions(
                  signed: true, decimal: true),
              decoration: textDecorator,
              onSubmitted: (String value) {
                updateAttributeValue();
              },
            ),
          )),
      Container(
        alignment: Alignment.center,
        child: Text(title,
            style: const TextStyle(color: Colors.white, fontSize: 16)),
      ),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> children = [];

    if (widget._attribute.timeSpan == TimeSpan.Seconds) {
      _secondsController ??= TextEditingController(
          text: widget._attribute.getValueinSeconds()?.toString());
      children = [buildTextField("seconds", _secondsController)];
    } else if (widget._attribute.timeSpan == TimeSpan.MinutesandSeconds) {
      _minutesController ??= TextEditingController(
          text: widget._attribute.getValueinMinutes()?.toString());
      _minutesandSecondsController ??= TextEditingController(
          text: widget._attribute.getValueinSecondsAndMinutes()?.toString());
      children = [
        buildTextField("Latitude", _minutesController),
        buildTextField("Longitude", _minutesandSecondsController)
      ];
    }

    return Container(
        margin: const EdgeInsets.fromLTRB(20, 10, 20, 10),
        child: Column(children: children));
  }
}
