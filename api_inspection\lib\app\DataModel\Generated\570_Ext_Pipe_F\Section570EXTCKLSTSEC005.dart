//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class Section570EXTCKLSTSEC005 extends DataModelSection {
  @override
  String getDisplayName() => "PRESSURE RELIEF (PRD/PRV)";
  Section570EXTCKLSTSEC005(DataModelItem? parent)
      : super(parent: parent, sectionName: "PRESSURE RELIEF (PRD/PRV)");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC005Q001 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Has an inspection & test of Relief been performed within the required interval:  (i.e. 5 years for typical process services,10 years for clean (non-fouling) and noncorrosive services)",
          databaseName: "570_EXT_CKLST_SEC005_Q001",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC005Q002 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is Relief set at or below the max allowable operating pressure (MAOP) of the piping:  (Data plate & calibration tag information shall be recorded)",
          databaseName: "570_EXT_CKLST_SEC005_Q002",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute570EXTCKLSTSEC005Q003 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Were any corrosion, erosion, or pitting cells noted on the installed PRD / PRV:",
          databaseName: "570_EXT_CKLST_SEC005_Q003",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Corrosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Erosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Pitting", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC005Q004 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Were any crack like indications noted on the installed PRD / PRV:",
          databaseName: "570_EXT_CKLST_SEC005_Q004",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeWas_any_mechanical_damage_noted_on_the_PRD__PRV =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Was any mechanical damage noted on the PRD / PRV",
          databaseName: "570_EXT_CKLST_SEC005_Q005",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeIs_the_Relief_attachment_achieved_via_flanging =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Is the Relief attachment achieved via flanging:",
          databaseName: "570_EXT_CKLST_SEC005_Q006",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC005Q007 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Were flange fasteners fully engaged: (Any fastener is considered acceptably engaged if the lack of complete engagement is not more than one thread)",
          databaseName: "570_EXT_CKLST_SEC005_Q007",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC005Q008 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are the flange fasteners utilized in alignment with Client specification:",
          databaseName: "570_EXT_CKLST_SEC005_Q008",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC005Q009 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are the flange gaskets utilized in alignment with Client specification:",
          databaseName: "570_EXT_CKLST_SEC005_Q009",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC005Q010 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the Relief attachment achieved via threaded connection(s):",
          databaseName: "570_EXT_CKLST_SEC005_Q010",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC005Q011 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Were the Relief threaded connection(s) in acceptable condition for continued service:",
          databaseName: "570_EXT_CKLST_SEC005_Q011",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeIs_the_Relief_valve_vent_piping_routed_to_a_safe_location =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the Relief valve vent piping routed to a safe location:",
          databaseName: "570_EXT_CKLST_SEC005_Q012",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC005Q013 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Was the Relief valve vent piping verified to be free of any foreign material that may cause plugging:",
          databaseName: "570_EXT_CKLST_SEC005_Q013",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC005Q014 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Was the bellows vent verified to be free of any foreign material that may cause plugging:",
          databaseName: "570_EXT_CKLST_SEC005_Q014",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeIs_the_spring_tamper_car_seal_intact =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Is the spring tamper car seal intact:",
          databaseName: "570_EXT_CKLST_SEC005_Q015",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC005Q016 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the manual operation leaver in acceptable condition for continued service:",
          databaseName: "570_EXT_CKLST_SEC005_Q016",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeIs_Relief_alignment_acceptable =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Is Relief alignment acceptable:",
          databaseName: "570_EXT_CKLST_SEC005_Q017",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC005Q018 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Were the Relief supports in acceptable condition for continued service:",
          databaseName: "570_EXT_CKLST_SEC005_Q018",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeWas_any_excessive_vibration_of_the_Relief_noted =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Was any excessive vibration of the Relief noted:",
          databaseName: "570_EXT_CKLST_SEC005_Q019",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeWas_the_rupture_device_orientation_correct =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Was the rupture device orientation correct:",
          databaseName: "570_EXT_CKLST_SEC005_Q020",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeAre_associated_block_valves_car_sealed_in_the_open_position =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are associated block valves car sealed in the open position:",
          databaseName: "570_EXT_CKLST_SEC005_Q021",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute570EXTCKLSTSEC005Q022 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Was evidence of prior or active leakage noted originating from PRD / PRV:",
          databaseName: "570_EXT_CKLST_SEC005_Q022",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Active Leakage", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Evidence of Prior Leakage", null,
            isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC005Q023 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the PRD / PRV in acceptable condition for continued service:",
          databaseName: "570_EXT_CKLST_SEC005_Q023",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attribute570EXTCKLSTSEC005Q001,
      attribute570EXTCKLSTSEC005Q002,
      attribute570EXTCKLSTSEC005Q003,
      attribute570EXTCKLSTSEC005Q004,
      attributeWas_any_mechanical_damage_noted_on_the_PRD__PRV,
      attributeIs_the_Relief_attachment_achieved_via_flanging,
      attribute570EXTCKLSTSEC005Q007,
      attribute570EXTCKLSTSEC005Q008,
      attribute570EXTCKLSTSEC005Q009,
      attribute570EXTCKLSTSEC005Q010,
      attribute570EXTCKLSTSEC005Q011,
      attributeIs_the_Relief_valve_vent_piping_routed_to_a_safe_location,
      attribute570EXTCKLSTSEC005Q013,
      attribute570EXTCKLSTSEC005Q014,
      attributeIs_the_spring_tamper_car_seal_intact,
      attribute570EXTCKLSTSEC005Q016,
      attributeIs_Relief_alignment_acceptable,
      attribute570EXTCKLSTSEC005Q018,
      attributeWas_any_excessive_vibration_of_the_Relief_noted,
      attributeWas_the_rupture_device_orientation_correct,
      attributeAre_associated_block_valves_car_sealed_in_the_open_position,
      attribute570EXTCKLSTSEC005Q022,
      attribute570EXTCKLSTSEC005Q023,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section570EXTCKLSTSEC005";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
