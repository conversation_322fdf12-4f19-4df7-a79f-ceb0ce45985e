﻿using System.Collections.Generic;
using System.Data;
using System.Linq;
using CheckListGen;

namespace ExcelCodeGenerator.ExcelParser
{
    public class Version2Parser : IExcelParser
    {
        public bool IsTableInFormat(DataTable table)
        {
            return table.Rows[0][0].ToString() == "Type";
        }

        public ExcelForm ReadTable(DataTable table)
        {
            var tableName = table.TableName;

            var form = new ExcelForm
            {
                Sections = new List<ExcelSection>(),
                Name = tableName,
                DisplayName = tableName
            };

            var order = 1;

            foreach (DataRow row in table.Rows)
            {
                if (row[0]?.ToString() == "Type") continue;

                var type = row[0].ToString();
                switch (type)
                {
                    case "Q":
                        form.Questions.Add(ReadQuestionFromTableV2(row, order++));
                        break;
                    case "IS":
                    case "IPS":
                        form.Sections.Add(ReadSectionFromTableV2(row, order++, true, false));
                        break;
                    case "S":
                    case "PS":
                        form.Sections.Add(ReadSectionFromTableV2(row, order++, false, false));
                        break;
                    case "C":
                        form.Sections.Add(ReadSectionFromTableV2(row, order++, false, true));
                        break;
                    case "IC":
                        form.Sections.Add(ReadSectionFromTableV2(row, order++, true, true));
                        break;
                }
            }

            return form;
        }

        private static ExcelSection ReadSectionFromTableV2(DataRow row, int order, bool inline, bool collection)
        {
            var headerName = row[3]?.ToString();
            var section = new ExcelSection
            {
                Name = headerName.Trim(),
                DataName = Helpers.CleanupVariableName(headerName)
            };
            var parent = row[2]?.ToString();

            section.ParentId = string.IsNullOrWhiteSpace(parent) ? "root" : parent;
            section.Id = row[1].ToString();

            section.Decorators = row[9]?.ToString().Split(',').ToArray();

            section.Order = order;
            section.IsCollection = collection;
            section.IsInline = inline;
            return section;
        }

        private static ExcelQuestion ReadQuestionFromTableV2(DataRow row, int order)
        {
            var excelQuestion = new ExcelQuestion();
            var question = new Question();
            excelQuestion.Question = question;

            var questionDataName = row[1]?.ToString().Trim();
            question.DataName = string.IsNullOrWhiteSpace(questionDataName)
                ? Helpers.CleanupVariableName(row[3]?.ToString())
                : questionDataName;
            excelQuestion.SectionId = row[2]?.ToString();
            question.Order = order;
            question.DisplayText = row[3]?.ToString().Trim();

            question.Unit = row[7]?.ToString().Trim();
            question.DataType = row[5]?.ToString().Trim();
            var required = row[4]?.ToString();
            question.Required = !string.IsNullOrWhiteSpace(required) && Helpers.ParseBooleanEXT(required);

            question.Choices = row[6]?.ToString().Split(',')
                .Where(a => !string.IsNullOrWhiteSpace(a))
                .Select(a =>
                {
                    var indexStart = a.IndexOf('[');
                    var indexEnd = a.IndexOf(']');
                    if (indexStart == -1 || indexEnd == -1) return new Choice {Display = a.Trim()};
                    var length = indexEnd - indexStart - 1;
                    var decorator = a.Substring(indexStart + 1, length);
                    return new Choice
                    {
                        Display = a.Replace("[" + decorator + "]", "").Trim(),
                        Decorator = decorator.Trim()
                    };
                })
                .Where(a => !string.IsNullOrWhiteSpace(a.Display)).ToList();

            question.Decorators = row[9]?.ToString().Split(',').ToList();
            question.DevComments = row[10]?.ToString();
            return excelQuestion;
        }
    }
}