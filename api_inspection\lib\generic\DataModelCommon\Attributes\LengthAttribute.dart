import 'package:api_inspection/generic/AttributeControls/AttributeStructure.dart';
import 'package:api_inspection/generic/AttributeControls/Length/LengthAttributeView.dart';
import 'package:api_inspection/generic/Common/Types/Length.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

import 'AttributeBase.dart';
import 'ChangeLog/ChangeLogEntry.dart';

class LengthAttribute extends SingleAttributeBase<double> {
  LengthUnits displayUnit;

  @override
  bool hasData() {
    return getValue() != null;
  }

  Length? getValue() {
    if (valueChangeLog.entries.isEmpty) return null;
    var lastValue = valueChangeLog.entries.last.value;
    if (lastValue == null) return null;
    return Length.fromMeters(lastValue);
  }

  void setValue(Length? value) {
    if (getValue() == value) return;

    double? dbValue = value?.inMeters;

    var entry = ChangeLogEntry<double>.newlyCreated(dbValue);
    valueChangeLog.addNewItem(entry);

    notifyListeners();
  }

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  bool allowNegatives;

  LengthAttribute(
      {required DataModelItem parent,
      required String displayName,
      required this.allowNegatives,
      required this.displayUnit,
      Widget? iconWidget,
      bool areCommentsRequired = false,
      String? databaseName})
      : super(parent, displayName, iconWidget, areCommentsRequired,
            databaseName) {
    valueChangeLog.setConversionMethod(convertDynamicToDouble);
  }

  double? convertDynamicToDouble(dynamic dyn) {
    if (dyn is int) {
      return dyn.toDouble();
    }
    return dyn as double?;
  }

  @override
  Widget buildWidget(
      {IsEditableController? editingController,
      bool showPhotos = true,
      bool showComments = true}) {
    return LengthAttributeView(
      this,
      editingController: editingController,
      showPhotos: showPhotos,
      showComments: showComments,
    );
  }

  @override
  String getPreviewText() {
    var value = getValue();
    return value == null
        ? ""
        : value.toString() + " " + displayUnit.abbreviation();
  }
}
