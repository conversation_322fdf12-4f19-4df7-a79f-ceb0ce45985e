//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class Section510INT_PVCKLSTSEC001 extends DataModelSection {
  @override
  String getDisplayName() => "GENERAL INFORMATION";
  Section510INT_PVCKLSTSEC001(DataModelItem? parent)
      : super(parent: parent, sectionName: "GENERAL INFORMATION");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeIs_the_asset_appropriately_prepared_for_internal_inspection =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the asset appropriately prepared for internal inspection:",
          databaseName: "510_INT-PV_CKLST_SEC001_Q001",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late BooleanAttribute attribute510INT_PVCKLSTSEC001Q002 = BooleanAttribute(
      parent: this,
      displayName:
          "Are the internal areas of the asset free of debris and product residue:",
      databaseName: "510_INT-PV_CKLST_SEC001_Q002",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC001Q003 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Was design data, previous inspection / examination reporting and or relevant  MOC / information pertaining to abnormal operating conditions made available prior to inspection:",
          databaseName: "510_INT-PV_CKLST_SEC001_Q003",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeIs_the_asset_appropriately_prepared_for_internal_inspection,
      attribute510INT_PVCKLSTSEC001Q002,
      attribute510INT_PVCKLSTSEC001Q003,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section510INT_PVCKLSTSEC001";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
