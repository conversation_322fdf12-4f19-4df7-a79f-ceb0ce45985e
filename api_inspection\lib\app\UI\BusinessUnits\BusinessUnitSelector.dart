import 'package:flutter/material.dart';

import '../../APMRoot.dart';
import '../../DataModel/businessUnit.dart';

class BusinessUnitSelector extends StatefulWidget {
  final List<BusinessUnit> businessUnits;

  BusinessUnitSelector({Key? key, required this.businessUnits})
      : super(key: key) {
    businessUnits.sort((a, b) {
      var aName = a.name.getValue();
      var bName = b.name.getValue();
      if (aName == null && bName == null) return 0;
      if (aName == null && bName != null) return -1;
      if (aName != null && bName == null) return 1;

      return a.name.getValue()!.compareTo(b.name.getValue()!);
    });
  }

  @override
  State<StatefulWidget> createState() {
    return BusinessUnitSelectorState();
  }
}

class BusinessUnitSelectorState extends State<BusinessUnitSelector> {
  String? selectedBusinessUnitId;

  @override
  Widget build(BuildContext context) {
    var businessUnitItems =
        widget.businessUnits.map<DropdownMenuItem<String>>((BusinessUnit bu) {
      return DropdownMenuItem<String>(
        value: bu.id,
        child: Text(bu.name.getValue() ?? '?'),
      );
    }).toList();

    var initialValue = getInitialValue(businessUnitItems);

    return Container(
        padding: const EdgeInsets.fromLTRB(12.0, 18.0, 12.0, 6.0),
        constraints:
            BoxConstraints(maxWidth: MediaQuery.of(context).size.width / 2),
        child: DropdownButton<String>(
          hint: const Text('None', style: TextStyle(color: Colors.white)),
          underline: Container(),
          style: const TextStyle(
              fontSize: 14.0,
              color: Colors.black,
              overflow: TextOverflow.ellipsis),
          value: initialValue,
          icon: const Icon(Icons.arrow_downward),
          isExpanded: true,
          onChanged: (String? newValue) {
            setState(() {
              selectedBusinessUnitId = newValue;
              APMRoot.global.queries.businessUnitQueries
                  .setSelectedBusinessUnitId(newValue);
            });
          },
          selectedItemBuilder: (BuildContext context) {
            return widget.businessUnits.map((BusinessUnit bu) {
              return Center(
                  child: Text(bu.name.getValue() ?? '',
                      style: const TextStyle(
                          fontSize: 12.0, color: Colors.white)));
            }).toList();
          },
          items: businessUnitItems,
          isDense: true,
          alignment: AlignmentDirectional.centerEnd,
        ));
  }

  String? getInitialValue(List<DropdownMenuItem<String>> businessUnitItems) {
    var selected = APMRoot.global.selectedBusinessUnitId;
    var options = businessUnitItems.map((item) => item.value);
    return options.isEmpty
        ? null
        : (selected != null && options.contains(selected))
            ? selected
            : options.first;
  }
}
