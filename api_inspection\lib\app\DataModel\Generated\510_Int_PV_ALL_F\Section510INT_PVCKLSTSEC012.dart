//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class Section510INT_PVCKLSTSEC012 extends DataModelSection {
  @override
  String getDisplayName() => "PROTECTIVE COATING";
  Section510INT_PVCKLSTSEC012(DataModelItem? parent)
      : super(parent: parent, sectionName: "PROTECTIVE COATING");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeDoes_the_vessel_have_a_protective_coating_system_applied =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Does the vessel have a protective coating system applied:",
          databaseName: "510_INT-PV_CKLST_SEC012_Q001",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC012Q002 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Do vessel supports and or miscellaneous components have a protective coating system applied:",
          databaseName: "510_INT-PV_CKLST_SEC012_Q002",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC012Q003 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the protective coating system in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC012_Q003",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeDoes_the_vessel_have_a_protective_coating_system_applied,
      attribute510INT_PVCKLSTSEC012Q002,
      attribute510INT_PVCKLSTSEC012Q003,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section510INT_PVCKLSTSEC012";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
