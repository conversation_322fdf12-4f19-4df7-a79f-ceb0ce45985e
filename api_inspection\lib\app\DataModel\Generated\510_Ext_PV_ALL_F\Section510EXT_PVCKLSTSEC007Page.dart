//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'Section510EXT_PVCKLSTSEC007.dart';

// ignore: camel_case_types
class Section510EXT_PVCKLSTSEC007Page extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final Section510EXT_PVCKLSTSEC007 section510EXT_PVCKLSTSEC007;

  const Section510EXT_PVCKLSTSEC007Page(this.section510EXT_PVCKLSTSEC007,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _Section510EXT_PVCKLSTSEC007PageState();
  }
}

class _Section510EXT_PVCKLSTSEC007PageState
    extends State<Section510EXT_PVCKLSTSEC007Page> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.section510EXT_PVCKLSTSEC007,
        title: "GUY WIRES",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC007
                      .attributeAre_guy_wires_utilized_to_support_the_asset
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC007
                      .attribute510EXT_PVCKLSTSEC007Q002
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC007
                      .attribute510EXT_PVCKLSTSEC007Q003
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC007
                      .attribute510EXT_PVCKLSTSEC007Q004
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC007
                      .attribute510EXT_PVCKLSTSEC007Q005
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC007
                      .attribute510EXT_PVCKLSTSEC007Q006
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC007
                      .attribute510EXT_PVCKLSTSEC007Q007
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC007
                      .attribute510EXT_PVCKLSTSEC007Q008
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
