//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';

// ignore: camel_case_types
class SectionPipe extends DataModelCollectionItem {
  @override
  String getDisplayName() => "Pipe";

  SectionPipe(String id, DataModelItem parent) : super(id, parent);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeLine_No_ = StringAttribute(
      parent: this,
      displayName: "Line No.",
      databaseName: "570AW_Q306",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late PhotoAttribute attributePhotos = PhotoAttribute(
      parent: this,
      displayName: "Photos",
      databaseName: "570AW_Q313",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeMaterial_Spec_and_Grade = StringAttribute(
      parent: this,
      displayName: "Material Spec and Grade",
      databaseName: "570AW_Q307",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeAllowable_Stress_at_Temperature =
      DoubleAttribute(
    parent: this,
    displayName: "Allowable Stress at Temperature",
    databaseName: "570AW_Q308",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "psi",
  );

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeNominal_Thickness_schedule = DoubleAttribute(
    parent: this,
    displayName: "Nominal Thickness (schedule)",
    databaseName: "570AW_Q309",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "in",
  );

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeCorrosion_Allowance = DoubleAttribute(
    parent: this,
    displayName: "Corrosion Allowance",
    databaseName: "570AW_Q310",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "in",
  );

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeJoint_Efficiency = DoubleAttribute(
    parent: this,
    displayName: "Joint Efficiency",
    databaseName: "570AW_Q311",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "",
  );

  // ignore: non_constant_identifier_names
  late StringAttribute attributePipe_Spec_Number = StringAttribute(
      parent: this,
      displayName: "Pipe Spec Number",
      databaseName: "570AW_Q312",
      areCommentsRequired: false);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeLine_No_,
      attributePhotos,
      attributeMaterial_Spec_and_Grade,
      attributeAllowable_Stress_at_Temperature,
      attributeNominal_Thickness_schedule,
      attributeCorrosion_Allowance,
      attributeJoint_Efficiency,
      attributePipe_Spec_Number,
    ]);
    return children;
  }
}
