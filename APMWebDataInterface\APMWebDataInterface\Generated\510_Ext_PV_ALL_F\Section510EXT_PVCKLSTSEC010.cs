//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Ext_PV_ALL_F
{
  public class Section510EXT_PVCKLSTSEC010 : DataModelItem {

    public override String DisplayName { 
      get {
        return "GROUNDING";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributeIs_the_asset_grounded_to_the_earth;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC010Q002;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC010Q003;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510EXT_PVCKLSTSEC010";

    public Section510EXT_PVCKLSTSEC010(DataModelItem parent) : base(parent)
    {
            
        attributeIs_the_asset_grounded_to_the_earth = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the asset grounded to the earth:", databaseName: "510_EXT-PV_CKLST_SEC010_Q001"); 
     
        attribute510EXT_PVCKLSTSEC010Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the bonding clip, attachment weld and surrounding base metal in acceptable condition for continued service:", databaseName: "510_EXT-PV_CKLST_SEC010_Q002"); 
     
        attribute510EXT_PVCKLSTSEC010Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the grounding system in acceptable condition for continued service:", databaseName: "510_EXT-PV_CKLST_SEC010_Q003"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeIs_the_asset_grounded_to_the_earth,
           attribute510EXT_PVCKLSTSEC010Q002,
           attribute510EXT_PVCKLSTSEC010Q003,
        };
    }
  }
}
