//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionAsset_Walkdown_Access_F
{
  public class SectionExternalSurfaceConditions : DataModelItem {

    public override String DisplayName { 
      get {
        return "External Surface Conditions";
      }
    }

    #region [-- Sub Sections --]

    #region [-- SectionCoating --]
    private SectionCoating _sectionCoating;
    public SectionCoating sectionCoating {
        get {
            if (_sectionCoating == null) {
               _sectionCoating = new SectionCoating(this);
            }

            return _sectionCoating;
        }
    }
    #endregion [-- SectionCoating --]
    
    #region [-- SectionCorrosion --]
    private SectionCorrosion _sectionCorrosion;
    public SectionCorrosion sectionCorrosion {
        get {
            if (_sectionCorrosion == null) {
               _sectionCorrosion = new SectionCorrosion(this);
            }

            return _sectionCorrosion;
        }
    }
    #endregion [-- SectionCorrosion --]
    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionExternalSurfaceConditions";

    public SectionExternalSurfaceConditions(DataModelItem parent) : base(parent)
    {
            
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           sectionCoating,
           sectionCorrosion,
        };
    }
  }
}
