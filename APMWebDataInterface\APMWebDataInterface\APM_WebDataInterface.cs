using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using APMWebDataInterface.DataModel;
using APMWebDataInterface.DataModel.LeakReport;
using APMWebDataInterface.Exceptions;
using Azure.Storage.Blobs;
using CommonDataInterface;
using CommonDataInterface.Attributes;
using Firebase.Auth;
using Google.Cloud.Firestore;
using Google.Cloud.Firestore.V1;
using Grpc.Core;

namespace APMWebDataInterface.ExampleDataModel
{
    public class DatabaseContextManager : IDatabaseContextManager
    {
        public APM_WebDataInterface.Databases Database { get; private set; }

        private class ManagedDatabaseContext
        {
            private FirestoreDb _firestoreDB;

            public int NumberOfListeners = 0;

            public FirestoreDb FirestoreDB
            {
                get
                {
                    if (_firestoreDB == null)
                    {
                        throw new Exception(
                            "Must call APM_WebDataInterface.Global.Initialize() before calling methods inside nuget package");
                    }

                    return _firestoreDB;
                }
            }

            private async Task<FirestoreDb> CreateFirestoreDbWithEmailAuthentication(string emailAddress,
                string password, string firebaseApiKey, string firebaseProjectId)
            {
                // Create a custom authentication mechanism for Email/Password authentication
                // If the authentication is successful, we will get back the current authentication token and the refresh token
                // The authentication expires every hour, so we need to use the obtained refresh token to obtain a new authentication token as the previous one expires
                var authProvider = new FirebaseAuthProvider(new FirebaseConfig(firebaseApiKey));
                var auth = await authProvider.SignInWithEmailAndPasswordAsync(emailAddress, password);
                var callCredentials = CallCredentials.FromInterceptor(async (context, metadata) =>
                {
                    if (auth.IsExpired()) auth = await auth.GetFreshAuthAsync();
                    if (string.IsNullOrEmpty(auth.FirebaseToken)) return;

                    metadata.Clear();
                    metadata.Add("authorization", $"Bearer {auth.FirebaseToken}");
                });
                var credentials = ChannelCredentials.Create(new SslCredentials(), callCredentials);

                // Create a custom Firestore Client using custom credentials
                var client = await new FirestoreClientBuilder
                {
                    ChannelCredentials = credentials, 
                    Settings = FirestoreSettings.GetDefault()
                }.BuildAsync();
                //var grpcChannel = new Channel("firestore.googleapis.com", credentials);
                //var grcpClient = new Firestore.FirestoreClient(grpcChannel);

                //var firestoreClient = new FirestoreClientImpl(grcpClient, FirestoreSettings.GetDefault());

                return await FirestoreDb.CreateAsync(firebaseProjectId, client);
            }


            public async Task Initialize(
                APM_WebDataInterface.Databases database = APM_WebDataInterface.Databases.testing)
            {
                if (_firestoreDB == null)
                {
                    if (database == APM_WebDataInterface.Databases.testing)
                    {
                        _firestoreDB = await CreateFirestoreDbWithEmailAuthentication("<EMAIL>",
                            "testDB24686543", "AIzaSyCy7hM82CoycZD_DGo62vLCbmktsBUsAjE",
                            "asset-performance-management");
                    }
                    else if (database == APM_WebDataInterface.Databases.production)
                    {
                        _firestoreDB = await CreateFirestoreDbWithEmailAuthentication("<EMAIL>",
                            "LFu/PQG+2$:-NCtk", "AIzaSyCFa-e0RUpUjF4mqSPVxNiMY_UZ0yfTZa8", "apm-prod-da61a");
                    }
                    else if (database == APM_WebDataInterface.Databases.localEmulator)
                    {
                        //string project = ConfigurationManager.AppSettings["firestore_projectid"];
                        Environment.SetEnvironmentVariable("FIRESTORE_EMULATOR_HOST", "localhost:8080");
                        _firestoreDB = await new FirestoreDbBuilder
                        {
                            ProjectId = "asset-performance-management",
                            EmulatorDetection = Google.Api.Gax.EmulatorDetection.EmulatorOnly
                        }.BuildAsync();
                    }
                }
            }
        }


        private List<ManagedDatabaseContext> _databaseContexts = new List<ManagedDatabaseContext>();

        public async Task ContextForNewListener(Action<FirestoreDb> action)
        {
            ManagedDatabaseContext context;
            if (_databaseContexts.All(a => a.NumberOfListeners > 75))
            {
                context = new ManagedDatabaseContext();
                await context.Initialize(Database);
                _databaseContexts.Add(context);
            }
            else
            {
                context = _databaseContexts.FirstOrDefault(a => a.NumberOfListeners <= 75);
            }


            context.NumberOfListeners++;
            action(context.FirestoreDB);
        }

        public async Task<T> ContextForNonListenerAction<T>(Func<FirestoreDb, Task<T>> action)
        {
            ManagedDatabaseContext context = _databaseContexts.FirstOrDefault();

            return await action(context.FirestoreDB);
        }


        public async Task<T> ContextForNonListenerAction<T>(Func<FirestoreDb, T> action)
        {
            ManagedDatabaseContext context = _databaseContexts.FirstOrDefault();

            return action(context.FirestoreDB);
        }

        public DatabaseContextManager(APM_WebDataInterface.Databases database = APM_WebDataInterface.Databases.testing)
        {
            Database = database;
        }


        public async Task Initialize()
        {
            if (_databaseContexts.Count == 0)
            {
                var initialContext = new ManagedDatabaseContext();
                await initialContext.Initialize(Database);
                _databaseContexts.Add(initialContext);
            }
        }
    }

    public class APM_WebDataInterface
    {
        public APM_WebDataInterface()
        {
        }

        private static APM_WebDataInterface _global;

        public static APM_WebDataInterface Global
        {
            get
            {
                if (_global == null)
                {
                    _global = new APM_WebDataInterface();
                }

                return _global;
            }
        }


        private FirestoreDb CreateFirestoreDbWithEmailAuthentication(string emailAddress,
            string password, string firebaseApiKey, string firebaseProjectId)
        {
            // Create a custom authentication mechanism for Email/Password authentication
            // If the authentication is successful, we will get back the current authentication token and the refresh token
            // The authentication expires every hour, so we need to use the obtained refresh token to obtain a new authentication token as the previous one expires
            var authProvider = new FirebaseAuthProvider(new FirebaseConfig(firebaseApiKey));
            var auth = authProvider.SignInWithEmailAndPasswordAsync(emailAddress, password).Result;
            var callCredentials = CallCredentials.FromInterceptor(async (context, metadata) =>
            {
                if (auth.IsExpired()) auth = await auth.GetFreshAuthAsync();
                if (string.IsNullOrEmpty(auth.FirebaseToken)) return;

                metadata.Clear();
                metadata.Add("authorization", $"Bearer {auth.FirebaseToken}");
            });
            var credentials = ChannelCredentials.Create(new SslCredentials(), callCredentials);

            var builder = new FirestoreClientBuilder
            {
                ChannelCredentials = credentials
            };
            var client = builder.Build();

            return FirestoreDb.Create(firebaseProjectId, client);
        }


        private DatabaseContextManager _firestoreDBContexts;

        public DatabaseContextManager DatabaseContextManager
        {
            get
            {
                if (_firestoreDBContexts == null)
                {
                    throw new Exception(
                        "Must call APM_WebDataInterface.Global.Initialize() before calling methods inside nuget package");
                }

                return _firestoreDBContexts;
            }
        }

        internal BlobContainerClient BlobContainerClient { get; set; }
        private bool _isInitialized = false;

        public enum Databases
        {
            testing,
            production,
            localEmulator
        };

        public async Task Initialize(Databases database = Databases.testing)
        {
            if (_isInitialized)
                return;
            _isInitialized = true;

            const string connectionString = "DefaultEndpointsProtocol=https;AccountName=aznascpiadevsa;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net";
            var blobContainerName = database == Databases.production ? "flutter" : "flutter-dev";
            BlobContainerClient = new BlobContainerClient(connectionString, blobContainerName);

            _firestoreDBContexts = new DatabaseContextManager(database);
            await _firestoreDBContexts.Initialize();
            DataManager.ContextManager = _firestoreDBContexts;
            BlobStorageHelper.BlobContainerClient = BlobContainerClient;

            await StartClientsQuery();
            await StartBusinessUnitsQuery();
            await StartUsersQuery();
            await StartNonVerifiedUsersQuery();
            await StartLocationsQuery();
            await StartProjectsQuery();
            await StartListeningToCountQuery();
        }

        private List<DocumentSnapshot> _leakReports = new List<DocumentSnapshot>();
        private Task _leakReportQuery = null;

        private async Task StartLeakReportsQuery(string email)
        {
            SemaphoreSlim semaphore = new SemaphoreSlim(0);

            await DatabaseContextManager.ContextForNewListener(database => database.Collection("leakreports").Listen(
                (snapshot) =>
                {
                    List<DocumentSnapshot> reports = new List<DocumentSnapshot>();
                    foreach (var item in snapshot.Documents)
                    {
                        reports.Add(item);
                    }

                    _leakReports = reports;


                    semaphore.Release();
                }));
            await semaphore.WaitAsync();
        }

        public async Task<List<LeakReport>> GetLeakReports(string email)
        {
            _leakReportQuery ??= StartLeakReportsQuery(email);

            await _leakReportQuery;

            var effectiveBusinessUnitIds = GetEffectiveBusinessUnitIds(email);

            var reports = new List<LeakReport>();

            if (effectiveBusinessUnitIds == null)
                return reports;

            foreach (var doc in _leakReports.Where(leakReportDoc =>
                         IsBusinessUnitContained(leakReportDoc, effectiveBusinessUnitIds)))
            {
                var createdBy = doc.ContainsField("createdBy") ? doc.GetValue<string>("createdBy") : "Unknown";
                var status = doc.ContainsField("status") ? doc.GetValue<string>("status") : null;

                var leakReport = new LeakReport(doc.Id, createdBy, status);

                leakReport.UpdateFromMap(doc.ToDictionary());
                reports.Add(leakReport);
                
            }


            return reports;
        }



        private async Task StartListeningToCountQuery()
        {
            SemaphoreSlim semaphore = new SemaphoreSlim(0);

            await DatabaseContextManager.ContextForNewListener(database => database.Collection("utility").Listen(
                async (snapshot) =>
                {
                    var countsDocument = snapshot.Documents.FirstOrDefault(a => a.Id == "Counts");

                    if (countsDocument == null)
                    {
                        DatabaseCounts.Global.CurrentMaxWorkOrderId = 1000;
                        DatabaseCounts.Global.CurrentMaxProjectId = 1000;
                        DatabaseCounts.Global.CurrentMaxTaskId = 1000;
                        await DatabaseCounts.Global.Save();
                    }
                    else
                    {
                        DatabaseCounts.Global.UpdateFromMap(countsDocument.ToDictionary());
                    }

                    semaphore.Release();
                }));
            await semaphore.WaitAsync();
        }


        private List<DocumentSnapshot> _clients = new List<DocumentSnapshot>();

        private async Task StartClientsQuery()
        {
            SemaphoreSlim semaphore = new SemaphoreSlim(0);
            await DatabaseContextManager.ContextForNewListener(database => database.Collection("clients").Listen(
                (snapshot) =>
                {
                    List<DocumentSnapshot> clients = new List<DocumentSnapshot>();
                    foreach (var item in snapshot.Documents)
                    {
                        clients.Add(item);
                    }

                    _clients = clients;
                    semaphore.Release();
                }));
            await semaphore.WaitAsync();
        }

        private List<DocumentSnapshot> _businessUnits = new List<DocumentSnapshot>();

        private async Task StartBusinessUnitsQuery()
        {
            SemaphoreSlim semaphore = new SemaphoreSlim(0);
            await DatabaseContextManager.ContextForNewListener(database => database.Collection("businessUnits").Listen(
                (snapshot) =>
                {
                    List<DocumentSnapshot> businessUnits = new List<DocumentSnapshot>();
                    foreach (var item in snapshot.Documents)
                    {
                        businessUnits.Add(item);
                    }

                    _businessUnits = businessUnits;
                    semaphore.Release();
                }));
            await semaphore.WaitAsync();
        }

        private List<DocumentSnapshot> _users = new List<DocumentSnapshot>();

        private async Task StartUsersQuery()
        {
            var semaphore = new SemaphoreSlim(0);
            await DatabaseContextManager.ContextForNewListener(database => database.Collection("users").Listen(
                (snapshot) =>
                {
                    var users = snapshot.Documents.ToList();

                    _users = users;
                    semaphore.Release();
                }));
            await semaphore.WaitAsync();
        }

        private List<DocumentSnapshot> _nonverifiedusers = new List<DocumentSnapshot>();

        private async Task StartNonVerifiedUsersQuery()
        {
            SemaphoreSlim semaphore = new SemaphoreSlim(0);
            await DatabaseContextManager.ContextForNewListener(database => database.Collection("nonverifiedusers")
                .Listen((snapshot) =>
                {
                    List<DocumentSnapshot> nonverfiedusers = new List<DocumentSnapshot>();
                    foreach (var item in snapshot.Documents)
                    {
                        nonverfiedusers.Add(item);
                    }

                    _nonverifiedusers = nonverfiedusers;
                    semaphore.Release();
                }));
            await semaphore.WaitAsync();
        }

        private List<DocumentSnapshot> _locations = new List<DocumentSnapshot>();

        private async Task StartLocationsQuery()
        {
            var semaphore = new SemaphoreSlim(0);
            await DatabaseContextManager.ContextForNewListener(database => database.Collection("locations").Listen(
                (snapshot) =>
                {
                    var locations = snapshot.Documents.ToList();

                    _locations = locations;
                    semaphore.Release();
                }));
            await semaphore.WaitAsync();
        }


        private List<DocumentSnapshot> _projects = new List<DocumentSnapshot>();

        private async Task StartProjectsQuery()
        {
            SemaphoreSlim semaphore = new SemaphoreSlim(0);

            await DatabaseContextManager.ContextForNewListener(database => database.Collection("projects").Listen(
                (snapshot) =>
                {
                    List<DocumentSnapshot> projects = new List<DocumentSnapshot>();
                    foreach (var item in snapshot.Documents)
                    {
                        projects.Add(item);
                    }

                    _projects = projects;
                    semaphore.Release();
                }));
            await semaphore.WaitAsync();
        }

        private object _queryLockObj = new object();

        private Dictionary<string, List<DocumentSnapshot>> _assetsForLocationId =
            new Dictionary<string, List<DocumentSnapshot>>();

        private object _initialAssetsForLocationQueryLock = new object();
        private Dictionary<string, Task> _initialAssetsForLocationQuery = new Dictionary<string, Task>();

        private async Task StartAssetsQueryForLocation(String locationId)
        {
            lock (_queryLockObj)
            {
                bool alreadyRan;

                lock (_initialAssetsForLocationQueryLock)
                {
                    alreadyRan = _initialAssetsForLocationQuery.ContainsKey(locationId);
                }

                if (!alreadyRan)
                {
                    lock (_initialAssetsForLocationQueryLock)
                    {
                        _initialAssetsForLocationQuery[locationId] = new Task(() =>
                        {
                            SemaphoreSlim semaphoreAssets = new SemaphoreSlim(0);


                            DatabaseContextManager.ContextForNewListener(database => database.Collection("assets")
                                .WhereEqualTo("LocationId", locationId).Listen((snapshot) =>
                                {
                                    var snapshots = new List<DocumentSnapshot>();
                                    foreach (var item in snapshot.Documents)
                                    {
                                        snapshots.Add(item);
                                    }

                                    _assetsForLocationId[locationId] = snapshots;

                                    semaphoreAssets.Release();
                                }));

                            semaphoreAssets.Wait();
                        });
                        _initialAssetsForLocationQuery[locationId].Start();
                    }
                }
            }

            Task task;
            lock (_initialAssetsForLocationQueryLock)
            {
                task = _initialAssetsForLocationQuery[locationId];
            }

            await task;
        }

        private object _workOrdersForProjectIdLock = new object();

        private Dictionary<string, List<DocumentSnapshot>> _workOrdersForProjectId =
            new Dictionary<string, List<DocumentSnapshot>>();

        private object _initialWorkOrdersForProjectQueryLock = new object();
        private Dictionary<string, Task> _initialWorkOrdersForProjectQuery = new Dictionary<string, Task>();

        private async Task StartWorkOrdersQueryForProject(String projectId)
        {
            lock (_queryLockObj)
            {
                bool alreadyRan;
                lock (_initialWorkOrdersForProjectQueryLock)
                {
                    alreadyRan = _initialWorkOrdersForProjectQuery.ContainsKey(projectId);
                }

                if (!alreadyRan)
                {
                    lock (_initialWorkOrdersForProjectQueryLock)
                    {
                        _initialWorkOrdersForProjectQuery[projectId] = new Task(() =>
                        {
                            SemaphoreSlim semaphore = new SemaphoreSlim(0);


                            DatabaseContextManager.ContextForNewListener(database => database.Collection("workorders")
                                .WhereEqualTo("ProjectId", projectId).Listen((snapshot) =>
                                {
                                    var workOrders = new List<DocumentSnapshot>();
                                    foreach (var item in snapshot.Documents)
                                    {
                                        workOrders.Add(item);
                                    }

                                    lock (_workOrdersForProjectIdLock)
                                    {
                                        _workOrdersForProjectId[projectId] = workOrders;
                                    }

                                    semaphore.Release();
                                }));

                            semaphore.Wait();
                        });

                        _initialWorkOrdersForProjectQuery[projectId].Start();
                    }
                }
            }

            Task task;
            lock (_initialWorkOrdersForProjectQueryLock)
            {
                task = _initialWorkOrdersForProjectQuery[projectId];
            }

            await task;
        }


        private object _tasksForProjectIdLock = new object();

        private Dictionary<string, List<DocumentSnapshot>> _tasksForProjectId =
            new Dictionary<string, List<DocumentSnapshot>>();


        private object _initialTasksForProjectQueryLock = new object();
        private Dictionary<string, Task> _initialTasksForProjectQuery = new Dictionary<string, Task>();

        private async Task StartTasksQueryForProject(String projectId)
        {
            lock (_queryLockObj)
            {
                bool alreadyRan;
                lock (_initialTasksForProjectQueryLock)
                {
                    alreadyRan = _initialTasksForProjectQuery.ContainsKey(projectId);
                }

                if (!alreadyRan)
                {
                    lock (_initialTasksForProjectQueryLock)
                    {
                        _initialTasksForProjectQuery[projectId] = new Task(() =>
                        {
                            SemaphoreSlim semaphore = new SemaphoreSlim(0);

                            try
                            {
                                DatabaseContextManager.ContextForNewListener(database => database.Collection("tasks")
                                    .WhereEqualTo("ProjectId", projectId).Listen((snapshot) =>
                                    {
                                        var tasks = new List<DocumentSnapshot>();
                                        foreach (var item in snapshot.Documents)
                                        {
                                            tasks.Add(item);
                                        }

                                        lock (_tasksForProjectIdLock)
                                        {
                                            _tasksForProjectId[projectId] = tasks;
                                        }

                                        semaphore.Release();
                                    }));
                            }
                            catch (Exception ex)
                            {
                                Console.Write(ex);
                            }


                            semaphore.Wait();
                        });

                        _initialTasksForProjectQuery[projectId].Start();
                    }
                }
            }

            Task task;
            lock (_initialTasksForProjectQueryLock)
            {
                task = _initialTasksForProjectQuery[projectId];
            }

            await task;
        }


        public UserProfile[] GetUsers()
        {
            if (!_isInitialized)
                throw new Exception("Must call Initialize before calling methods inside nuget package");

            var usersSnapshot = _users;
            var nonVerifiedUsersSnapshot = _nonverifiedusers;

            var users = new List<UserProfile>();
            foreach (var item in usersSnapshot)
            {
                var user = new UserProfile(item.Id.Replace("|", "."));
                user.UpdateFromMap(item.ToDictionary());
                users.Add(user);
            }

            foreach (var item in nonVerifiedUsersSnapshot)
            {
                var email = item.Id.Replace("|", ".");
                if (users.Any(a => a.Email == email)) continue;
                var user = new UserProfile(email, false);
                users.Add(user);
            }

            return users.ToArray();
        }

        public async Task<WorkOrder[]> GetWorkOrders(string[] projectIds, string email)
        {
            if (!_isInitialized)
                throw new Exception("Must call Initialize before calling methods inside nuget package");

            var effectiveBusinessUnitIds = GetEffectiveBusinessUnitIds(email);
            if (effectiveBusinessUnitIds == null)
                return Array.Empty<WorkOrder>();

            var projects = GetProjects(projectIds, email);
            projectIds = projects.Select(project => project.id).ToArray();

            var locationIds = projects.Select(a => a.locationId).Distinct().ToArray();

            var queryTasks = locationIds.Select(StartAssetsQueryForLocation).ToList();
            queryTasks.AddRange(projectIds.Select(StartWorkOrdersQueryForProject));
            queryTasks.AddRange(projectIds.Select(StartTasksQueryForProject));

            foreach (var task in queryTasks)
            {
                await task;
            }

            var assets = BuildAssetsFromDocuments(locationIds.SelectMany(a => _assetsForLocationId[a])
                .Where(assetDoc => IsBusinessUnitContained(assetDoc, effectiveBusinessUnitIds)).ToList());

            List<DocumentSnapshot> workOrderDocs;

            lock (_workOrdersForProjectIdLock)
            {
                workOrderDocs = projectIds.SelectMany(a => _workOrdersForProjectId[a]).Where(workOrderDoc =>
                    IsBusinessUnitContained(workOrderDoc, effectiveBusinessUnitIds)).ToList();
            }

            List<DocumentSnapshot> taskDocs;
            lock (_tasksForProjectIdLock)
            {
                taskDocs = projectIds.SelectMany(a => _tasksForProjectId[a])
                    .Where(taskDoc => IsBusinessUnitContained(taskDoc, effectiveBusinessUnitIds)).ToList();
            }

            var workOrders = new List<WorkOrder>();
            foreach (var doc in workOrderDocs)
            {
                var assetId = doc.GetValue<string>("AssetId");
                var projectId = doc.GetValue<string>("ProjectId");

                var asset = assets.FirstOrDefault(a => a.id == assetId);

                if (asset == null || projectId == null) continue;

                var workOrder = new WorkOrder(doc.Id, asset, projectId);

                workOrder.UpdateFromMap(doc.ToDictionary());
                workOrders.Add(workOrder);
            }

            foreach (var doc in taskDocs)
            {
                try
                {
                    var workOrderId = doc.ToDictionary()["WorkOrderId"] as string;
                    var taskType = doc.ToDictionary()["TaskType"] as string;
                    var workOrder = workOrders.FirstOrDefault(a => a.id == workOrderId);
                    if (workOrder == null)
                        continue;
                    var task = APMTask.Create(workOrder, doc.Id, taskType);
                    task.UpdateFromMap(doc.ToDictionary());
                    workOrder.tasks.Add(task);
                }
                catch (UnexpectedTaskTypeException ex)
                {
                    Console.WriteLine("Inspection type encountered: " + ex.TypeEncountered + Environment.NewLine +
                                      "This could be a newer type not yet supported in the package, or a legacy type that is unexpectedly failing.");
                }
            }

            return workOrders.ToArray();
        }


        /// <summary>
        /// Will return all assets that have been associated with the project
        /// </summary>
        /// <returns></returns>
        public async Task<Asset[]> GetAssetsForProjects(string[] projectIds, string email)
        {
            var projects = GetProjects(projectIds, email);

            var assetsAtLocations = await GetAssetsAtLocation(projects.Select(a => a.locationId).Distinct().ToArray(), email);

            var allAssetIds = projects.SelectMany(a => a.assetIds.GetValue()).Distinct().ToArray();
            return allAssetIds.Select(b => assetsAtLocations.FirstOrDefault(c => b == c.id)).Where(a => a != null)
                .ToArray();
        }

        /// <summary>
        /// Will return all assets at the locations
        /// </summary>
        /// <returns></returns>
        public async Task<Asset[]> GetAssetsAtLocation(string[] locationIds, string email)
        {
            if (!_isInitialized)
                throw new Exception("Must call Initialize before calling methods inside nuget package");

            var effectiveBusinessUnitIds = GetEffectiveBusinessUnitIds(email);
            if (effectiveBusinessUnitIds == null)
                return Array.Empty<Asset>();

            var queryTasks = locationIds.Select(StartAssetsQueryForLocation).ToList();

            foreach (var task in queryTasks)
            {
                await task;
            }

            var assets = locationIds.SelectMany(a => _assetsForLocationId[a])
                .Where(assetDoc => IsBusinessUnitContained(assetDoc, effectiveBusinessUnitIds)).ToList();

            return BuildAssetsFromDocuments(assets).ToArray();
        }

        private List<Asset> BuildAssetsFromDocuments(List<DocumentSnapshot> documents)
        {
            var assets = new List<Asset>();
            foreach (var item in documents)
            {
                if (!item.ContainsField("AssetCategory"))
                    continue;
                if (!item.ContainsField("LocationId"))
                    continue;
                var locationId = item.GetValue<string>("LocationId");

                var asset = new Asset(item.GetValue<string>("AssetCategory"), item.Id, locationId);
                asset.UpdateFromMap(item.ToDictionary());
                assets.Add(asset);
            }

            return assets;
        }

        /// <summary>
        /// Not cached, if you have the projectId, or assetID and have already search for that,
        /// please use those queries instead
        /// </summary>
        /// <param name="assetIds"></param>
        /// <returns></returns>

        // TODO: THIS WILL CURRENTLY FAIL IF WE WE ARE TRYING TO SEARCH FOR MORE THAN 10 ASSET IDS
        public async Task<Asset[]> GetAssets(string[] assetIds, string email)
        {
            if (!_isInitialized)
                throw new Exception("Must call Initialize before calling methods inside nuget package");

            if (!assetIds.Any())
                return Array.Empty<Asset>();

            var child = await DatabaseContextManager.ContextForNonListenerAction(database =>
                database.Collection("assets").WhereIn(FieldPath.DocumentId, assetIds));

            var snapShot = await child.GetSnapshotAsync();

            var assets = new List<Asset>();

            var effectiveBusinessUnitIds = GetEffectiveBusinessUnitIds(email);
            if (effectiveBusinessUnitIds == null)
                return assets.ToArray();

            foreach (var item in snapShot.Documents.Where(assetDoc =>
                         IsBusinessUnitContained(assetDoc, effectiveBusinessUnitIds)))
            {
                if (!item.ContainsField("AssetCategory"))
                    continue;
                if (!item.ContainsField("LocationId"))
                    continue;
                var locationId = item.GetValue<String>("LocationId");

                var asset = new Asset(item.GetValue<String>("AssetCategory"), item.Id, locationId);
                asset.UpdateFromMap(item.ToDictionary());
                assets.Add(asset);
            }

            return assets.ToArray();
        }


        // This should probably be restricted by user role? Wait for feedback
        public Client[] GetClients()
        {
            if (!_isInitialized)
                throw new Exception("Must call Initialize before calling methods inside the nuget package");

            var snapshot = _clients;
            List<Client> clients = new List<Client>();
            foreach (var item in snapshot)
            {
                var client = new Client(item.Id);
                client.UpdateFromMap(item.ToDictionary());
                clients.Add(client);
            }

            return clients.ToArray();
        }


        // This should probably be restricted by user role? Wait for feedback
        // but do we want people to be able to read and modify clients that they don't have access to?
        public Client[] GetClients(string[] clientIds)
        {
            if (!_isInitialized)
                throw new Exception("Must call Initialize before calling methods inside the nuget package");

            var snapshot = _clients;
            List<Client> clients = new List<Client>();
            foreach (var item in snapshot.Where(a => clientIds.Contains(a.Id)))
            {
                var client = new Client(item.Id);
                client.UpdateFromMap(item.ToDictionary());
                clients.Add(client);
            }

            return clients.ToArray();
        }

        // This should probably be restricted by user role? Wait for feedback
        public BusinessUnit[] GetBusinessUnits()
        {
            if (!_isInitialized)
                throw new Exception("Must call Initialize before calling methods inside the nuget package");

            var snapshot = _businessUnits;
            List<BusinessUnit> businessUnits = new List<BusinessUnit>();
            foreach (var item in snapshot)
            {
                var businessUnit = new BusinessUnit(item.Id);
                businessUnit.UpdateFromMap(item.ToDictionary());
                businessUnits.Add(businessUnit);
            }

            return businessUnits.ToArray();
        }

        // This should probably be restricted by user role? Wait for feedback
        public BusinessUnit[] GetBusinessUnits(string[] businessUnitIds)
        {
            if (!_isInitialized)
                throw new Exception("Must call Initialize before calling methods inside the nuget package");

            var snapshot = _businessUnits;
            List<BusinessUnit> businessUnits = new List<BusinessUnit>();
            foreach (var item in snapshot.Where(a => businessUnitIds.Contains(a.Id)))
            {
                var businessUnit = new BusinessUnit(item.Id);
                businessUnit.UpdateFromMap(item.ToDictionary());
                businessUnits.Add(businessUnit);
            }

            return businessUnits.ToArray();
        }


        // This should probably be restricted by user role? Wait for feedback
        public BusinessUnit[] GetBusinessUnitsByClientId(string clientId)
        {
            if (!_isInitialized)
                throw new Exception("Must call Initialize before calling methods inside the nuget package");

            var snapshot = _businessUnits;
            List<BusinessUnit> businessUnits = new List<BusinessUnit>();
            foreach (var item in snapshot.Where(a => a?.GetValue<string>("ClientId") == clientId))
            {
                var businessUnit = new BusinessUnit(item.Id);
                businessUnit.UpdateFromMap(item.ToDictionary());
                businessUnits.Add(businessUnit);
            }

            return businessUnits.ToArray();
        }

        public Project[] GetProjects(string email)
        {
            if (!_isInitialized)
                throw new Exception("Must call Initialize before calling methods inside nuget package");

            var snapshot = _projects;

            List<Project> projects = new List<Project>();

            var effectiveBusinessUnitIds = GetEffectiveBusinessUnitIds(email);
            if (effectiveBusinessUnitIds == null)
                return projects.ToArray();

            foreach (var item in snapshot.Where(projectDoc =>
                         IsBusinessUnitContained(projectDoc, effectiveBusinessUnitIds)))
            {
                var project = new Project(item.Id, item.GetValue<string>("LocationId"));
                project.UpdateFromMap(item.ToDictionary());
                projects.Add(project);
            }


            return projects.ToArray();
        }

        public Project[] GetProjects(String[] projectIds, string email)
        {
            if (!_isInitialized)
                throw new Exception("Must call Initialize before calling methods inside nuget package");

            var snapshot = _projects;

            var effectiveBusinessUnitIds = GetEffectiveBusinessUnitIds(email);

            List<Project> projects = new List<Project>();

            if (effectiveBusinessUnitIds == null)
                return projects.ToArray();

            foreach (var item in snapshot.Where(a => projectIds.Contains(a.Id)).Where(projectDoc =>
                         IsBusinessUnitContained(projectDoc, effectiveBusinessUnitIds)))
            {
                var project = new Project(item.Id, item.GetValue<string>("LocationId"));
                project.UpdateFromMap(item.ToDictionary());
                projects.Add(project);
            }

            return projects.ToArray();
        }

        public async Task<Project[]> GetProjectsAtLocation(String[] locationIds, string email)
        {
            if (!_isInitialized)
                throw new Exception("Must call Initialize before calling methods inside nuget package");

            List<Project> projects = new List<Project>();

            var effectiveBusinessUnitIds = GetEffectiveBusinessUnitIds(email);
            if (effectiveBusinessUnitIds == null)
                return projects.ToArray();

            foreach (var item in _projects.Where(projectDoc =>
                         IsBusinessUnitContained(projectDoc, effectiveBusinessUnitIds)))
            {
                var locationId = item.GetValue<string>("LocationId");
                if (!locationIds.Contains(locationId))
                    continue;
                var project = new Project(item.Id, locationId);
                project.UpdateFromMap(item.ToDictionary());
                projects.Add(project);
            }

            return projects.ToArray();
        }

        public async Task<Location[]> GetLocations(string email)
        {
            if (!_isInitialized)
                throw new Exception("Must call Initialize before calling methods inside nuget package");

            var snapShot = _locations;

            List<Location> locations = new List<Location>();

            var effectiveBusinessUnitIds = GetEffectiveBusinessUnitIds(email);
            if (effectiveBusinessUnitIds == null)
                return locations.ToArray();

            foreach (var item in snapShot.Where(locationDoc =>
                         IsBusinessUnitContained(locationDoc, effectiveBusinessUnitIds)))
            {
                var location = new Location(item.Id);
                location.UpdateFromMap(item.ToDictionary());
                locations.Add(location);
            }

            return locations.ToArray();
        }

        private String[] GetEffectiveBusinessUnitIds(string email)
        {
         
            if (email == "<EMAIL>")
                return GetBusinessUnits().Select(businessUnitDoc => businessUnitDoc.id).ToArray();

            var userSnapshot = _users.SingleOrDefault(user => string.Equals(user.Id.Replace("|", "."), email, StringComparison.InvariantCultureIgnoreCase));
            if (userSnapshot == null)
                return null;

            if (!userSnapshot.ContainsField("EffectiveBusinessUnitIds"))
                return null;

            return userSnapshot.GetValue<String[]>("EffectiveBusinessUnitIds");
        }

        private bool IsBusinessUnitContained(DocumentSnapshot document, String[] businessUnits)
        {
            return document.ContainsField("BusinessUnitId.Value") &&
                   businessUnits.Contains(document.GetValue<String>("BusinessUnitId.Value"));
        }

        internal void AuthorizeWriteToRootObject(StringAttribute businessUnitId, string email)
        {
            //check string attribute display name to make sure it is a business unit id
            if (businessUnitId.DisplayName != "BusinessUnitId")
                throw new BusinessUnitRestrictionException("Attribute is not a business id string attribute");


            var effectiveBusinessUnitIds = GetEffectiveBusinessUnitIds(email);
            if (effectiveBusinessUnitIds == null)
                throw new BusinessUnitRestrictionException("Users effective business units is null");

            if (businessUnitId.CurrentValue != businessUnitId.CurrentPendingOrValue)
            {
                if (string.IsNullOrWhiteSpace(businessUnitId.PendingValue))
                {
                    // need custom exception for business unit id restrictions
                    throw new BusinessUnitRestrictionException(
                        "Business Unit Id cannot be changed to null or whitespace");
                }


                if (string.IsNullOrWhiteSpace(businessUnitId.CurrentValue))
                {
                    if (!effectiveBusinessUnitIds.Contains(businessUnitId.PendingValue))
                    {
                        throw new BusinessUnitRestrictionException(
                            "User is not authorized to create a root object on this business unit");
                    }
                }else if (!effectiveBusinessUnitIds.Contains(businessUnitId.CurrentValue))
                {
                    throw new BusinessUnitRestrictionException(
                        "User is not authorized to change the root object from this business unit id");
                }else if (!effectiveBusinessUnitIds.Contains(businessUnitId.PendingValue))
                {
                    throw new BusinessUnitRestrictionException(
                        "User is not authorized to change the root object to this business unit id");
                }
            }else if (!effectiveBusinessUnitIds.Contains(businessUnitId.CurrentValue))
            {
                throw new BusinessUnitRestrictionException(
                    "User is not authorized to make changes to this root object");
            }
        }
    }
}