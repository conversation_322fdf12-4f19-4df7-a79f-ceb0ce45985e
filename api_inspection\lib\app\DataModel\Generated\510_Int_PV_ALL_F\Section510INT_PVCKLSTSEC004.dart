//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class Section510INT_PVCKLSTSEC004 extends DataModelSection {
  @override
  String getDisplayName() => "SHELL COVER - HEX ONLY";
  Section510INT_PVCKLSTSEC004(DataModelItem? parent)
      : super(parent: parent, sectionName: "SHELL COVER - HEX ONLY");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510INT_PVCKLSTSEC004Q001 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Were any corrosion cells, impingement or pitting noted on the shell cover surfaces: (The dimensions and locations of any damage shall be recorded)",
          databaseName: "510_INT-PV_CKLST_SEC004_Q001",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Corrosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Impingement", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Pitting", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510INT_PVCKLSTSEC004Q002 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Was any mechanical damage or cracking noted on the shell cover surfaces: (The dimensions and location of any damage shall be recorded)",
          databaseName: "510_INT-PV_CKLST_SEC004_Q002",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Cracking", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Mechanical Damage", null,
            isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510INT_PVCKLSTSEC004Q003 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Were there indications of weld corrosion or cracking in the welds or Heat Affected Zones of the shell cover pressure retaining welds:",
          databaseName: "510_INT-PV_CKLST_SEC004_Q003",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Weld Corrosion", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Weld Cracking", null,
            isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510INT_PVCKLSTSEC004Q004 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Does the shell cover have any deformations or hot spots: (Bulges, Blisters, Dimpling)",
          databaseName: "510_INT-PV_CKLST_SEC004_Q004",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Deformation", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Hot Spots", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC004Q005 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are shell cover penetrations and adjacent areas in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC004_Q005",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC004Q006 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Was any mechanical damage or impacts from objects noted on the shell cover:",
          databaseName: "510_INT-PV_CKLST_SEC004_Q006",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeIs_the_shell_cover_to_shell_attachment_achieved_via_welding =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the shell cover to shell attachment achieved via welding:",
          databaseName: "510_INT-PV_CKLST_SEC004_Q007",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC004Q008 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the shell cover to shell weld in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC004_Q008",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC004Q009 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the shell cover to shell attachment achieved via flanged connection(s):",
          databaseName: "510_INT-PV_CKLST_SEC004_Q009",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC004Q010 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the shell cover to shell  flanged connection(s) in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC004_Q010",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC004Q011 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the shell cover of the asset in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC004_Q011",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attribute510INT_PVCKLSTSEC004Q001,
      attribute510INT_PVCKLSTSEC004Q002,
      attribute510INT_PVCKLSTSEC004Q003,
      attribute510INT_PVCKLSTSEC004Q004,
      attribute510INT_PVCKLSTSEC004Q005,
      attribute510INT_PVCKLSTSEC004Q006,
      attributeIs_the_shell_cover_to_shell_attachment_achieved_via_welding,
      attribute510INT_PVCKLSTSEC004Q008,
      attribute510INT_PVCKLSTSEC004Q009,
      attribute510INT_PVCKLSTSEC004Q010,
      attribute510INT_PVCKLSTSEC004Q011,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section510INT_PVCKLSTSEC004";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
