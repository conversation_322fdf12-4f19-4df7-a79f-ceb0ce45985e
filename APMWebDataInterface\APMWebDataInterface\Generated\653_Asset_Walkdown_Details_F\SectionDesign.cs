//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section653_Asset_Walkdown_Details_F
{
  public class SectionDesign : DataModelItem {

    public override String DisplayName { 
      get {
        return "Design";
      }
    }

    #region [-- Sub Sections --]

    #region [-- SectionInspectionOpenings --]
    private DataModelCollection<SectionInspectionOpenings> _sectionInspectionOpenings;
    public DataModelCollection<SectionInspectionOpenings> sectionInspectionOpenings {
        get {
            if (_sectionInspectionOpenings == null) {
              _sectionInspectionOpenings = new DataModelCollection<SectionInspectionOpenings>("Inspection Openings", (parent, entry) => {
                 return new SectionInspectionOpenings(entry.Key, _sectionInspectionOpenings);
              }, (parent, id) => {
                return new SectionInspectionOpenings(id, _sectionInspectionOpenings);
              }, this);
            }

            return _sectionInspectionOpenings;
        }
    }
    #endregion [-- SectionInspectionOpenings --]
    
    #region [-- SectionInspection --]
    private SectionInspection _sectionInspection;
    public SectionInspection sectionInspection {
        get {
            if (_sectionInspection == null) {
               _sectionInspection = new SectionInspection(this);
            }

            return _sectionInspection;
        }
    }
    #endregion [-- SectionInspection --]
    
    #region [-- SectionDataPlate --]
    private SectionDataPlate _sectionDataPlate;
    public SectionDataPlate sectionDataPlate {
        get {
            if (_sectionDataPlate == null) {
               _sectionDataPlate = new SectionDataPlate(this);
            }

            return _sectionDataPlate;
        }
    }
    #endregion [-- SectionDataPlate --]
    
    #region [-- SectionManufacturerFabricator --]
    private SectionManufacturerFabricator _sectionManufacturerFabricator;
    public SectionManufacturerFabricator sectionManufacturerFabricator {
        get {
            if (_sectionManufacturerFabricator == null) {
               _sectionManufacturerFabricator = new SectionManufacturerFabricator(this);
            }

            return _sectionManufacturerFabricator;
        }
    }
    #endregion [-- SectionManufacturerFabricator --]
    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public StringAttribute attributeDesign_Code;
    public StringAttribute attributeCode_Year;
    public StringAttribute attributeAddendum;
    public DoubleAttribute attributeMaximum_Fill_Height;
    public DoubleAttribute attributeDiameter;
    public DoubleAttribute attributeHeight;
    public DoubleAttribute attributeTank_Volume_in_BBL;
    public MultiPredefinedValueAttribute attributeConstruction_Method;
    public PredefinedValueAttribute attributeOrientation;
    public PredefinedValueAttribute attributeRT;
    public DateAttribute attributeInstallation_Date;
    public DateAttribute attributeIn_service_Date;
    public StringAttribute attributePID_Number;
    public StringAttribute attributeConstructionDesign_Drawing_Number;
    public PredefinedValueAttribute attributeLowest_Flange_Rating;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionDesign";

    public SectionDesign(DataModelItem parent) : base(parent)
    {
            
        attributeDesign_Code = new StringAttribute(this, displayName: "Design Code", databaseName: "653AW_Q106"); 
     
        attributeCode_Year = new StringAttribute(this, displayName: "Code Year", databaseName: "653AW_Q107"); 
     
        attributeAddendum = new StringAttribute(this, displayName: "Addendum", databaseName: "653AW_Q108"); 
     
        attributeMaximum_Fill_Height = new DoubleAttribute(this, displayName: "Maximum Fill Height", databaseName: "653AW_Q109", areCommentsRequired: false, displayUnit: "ft", allowNegatives: true); 
     
        attributeDiameter = new DoubleAttribute(this, displayName: "Diameter", databaseName: "653AW_Q110", areCommentsRequired: false, displayUnit: "ft", allowNegatives: true); 
     
        attributeHeight = new DoubleAttribute(this, displayName: "Height", databaseName: "653AW_Q111", areCommentsRequired: false, displayUnit: "ft", allowNegatives: true); 
     
        attributeTank_Volume_in_BBL = new DoubleAttribute(this, displayName: "Tank Volume in BBL", databaseName: "653AW_Q112", areCommentsRequired: false, displayUnit: "BBL", allowNegatives: true); 
     
        attributeConstruction_Method = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Welded", null),
          new PredefinedValueOption("Bolted", null),
          new PredefinedValueOption("Riveted", null),
          new PredefinedValueOption("Fiberglass", null),
          new PredefinedValueOption("Plastic", null),
          new PredefinedValueOption("Wood", null)
        }, true, this, "Construction Method", databaseName: "653AW_Q113"); 
     
        attributeOrientation = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Horizontal", null),
          new PredefinedValueOption("Vertical", null)
        }, false, this, "Orientation", databaseName: "653AW_Q114"); 
     
        attributeRT = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Spot", null),
          new PredefinedValueOption("Full", null),
          new PredefinedValueOption("None", null)
        }, false, this, "RT", databaseName: "653AW_Q115"); 
     
        attributeInstallation_Date = new DateAttribute(this, displayName: "Installation Date", databaseName: "653AW_Q116", areCommentsRequired: false); 
     
        attributeIn_service_Date = new DateAttribute(this, displayName: "In-service Date", databaseName: "653AW_Q117", areCommentsRequired: false); 
     
        attributePID_Number = new StringAttribute(this, displayName: "P&ID Number", databaseName: "653AW_Q118"); 
     
        attributeConstructionDesign_Drawing_Number = new StringAttribute(this, displayName: "Construction/Design Drawing Number", databaseName: "653AW_Q119"); 
     
        attributeLowest_Flange_Rating = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("150", null),
          new PredefinedValueOption("300", null),
          new PredefinedValueOption("400", null),
          new PredefinedValueOption("600", null),
          new PredefinedValueOption("900", null),
          new PredefinedValueOption("1500", null),
          new PredefinedValueOption("2500", null)
        }, false, this, "Lowest Flange Rating", databaseName: "653AW_Q120"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           sectionInspectionOpenings,
           sectionInspection,
           sectionDataPlate,
           sectionManufacturerFabricator,
           attributeDesign_Code,
           attributeCode_Year,
           attributeAddendum,
           attributeMaximum_Fill_Height,
           attributeDiameter,
           attributeHeight,
           attributeTank_Volume_in_BBL,
           attributeConstruction_Method,
           attributeOrientation,
           attributeRT,
           attributeInstallation_Date,
           attributeIn_service_Date,
           attributePID_Number,
           attributeConstructionDesign_Drawing_Number,
           attributeLowest_Flange_Rating,
        };
    }
  }
}
