import 'package:api_inspection/generic/AttributeControls/AttributeStructure.dart';
import 'package:api_inspection/generic/AttributeControls/Integer/IntegerAttributeView.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';

import 'AttributeBase.dart';
import 'ChangeLog/ChangeLogEntry.dart';

class IntegerAttribute extends SingleAttributeBase<int> {
  String? displayUnit;
  int? minValue;
  int? maxValue;
  bool isValueWithinRange() {
    int? value = getValue();
    if (maxValue != null && minValue != null) {
      if (value == null) return true;
      if (value > maxValue! || value < minValue!) return false;
    }

    return true;
  }

  @override
  bool hasData() {
    return getValue() != null;
  }

  int? getValue() {
    if (valueChangeLog.entries.isEmpty) return null;
    return valueChangeLog.entries.last.value;
  }

  void setValue(int? value) {
    if (getValue() == value) return;

    var entry = ChangeLogEntry<int>.newlyCreated(value);
    valueChangeLog.addNewItem(entry);

    changedBySelfListeners.notifyListeners();
    notifyListeners();
  }

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  bool allowNegatives;

  IntegerAttribute(
      {required DataModelItem parent,
      required String displayName,
      required this.allowNegatives,
      String? displayUnit,
      Widget? iconWidget,
      bool areCommentsRequired = false,
      int? mininumValue,
      int? maximumValue,
      String? databaseName})
      : super(parent, displayName, iconWidget, areCommentsRequired,
            databaseName) {
    if (displayUnit != "") this.displayUnit = displayUnit;
    minValue = mininumValue;
    maxValue = maximumValue;

    valueChangeLog.setConversionMethod(convertDynamicToInteger);
  }

  int? convertDynamicToInteger(dynamic dyn) {
    if (dyn is int) {
      return dyn.toInt();
    }
    return int.tryParse(dyn.toString());
  }

  @override
  Widget buildWidget(
      {IsEditableController? editingController,
      bool showPhotos = true,
      bool showComments = true}) {
    return IntegerAttributeView(
      this,
      editingController: editingController,
      showPhotos: showPhotos,
      showComments: showComments,
    );
  }

  @override
  String getPreviewText() {
    var value = getValue();
    if (value == null) return "";
    var unit = displayUnit;
    if (unit == null) {
      return value.toString();
    }
    return value.toString() + " " + unit;
  }
}
