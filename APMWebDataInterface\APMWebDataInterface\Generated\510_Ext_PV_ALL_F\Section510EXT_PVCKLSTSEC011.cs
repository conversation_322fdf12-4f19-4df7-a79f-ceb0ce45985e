//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Ext_PV_ALL_F
{
  public class Section510EXT_PVCKLSTSEC011 : DataModelItem {

    public override String DisplayName { 
      get {
        return "AUXILIARY EQUIPMENT";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC011Q001;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC011Q002;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC011Q003;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC011Q004;
    public PredefinedValueAttribute attributeAre_threaded_connections_acceptably_engaged_and_leak_free;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC011Q006;
    public PredefinedValueAttribute attributeAre_threaded_connections_acceptable_for_continued_service;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC011Q008;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510EXT_PVCKLSTSEC011";

    public Section510EXT_PVCKLSTSEC011(DataModelItem parent) : base(parent)
    {
            
        attribute510EXT_PVCKLSTSEC011Q001 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are there any temperature indicating devices connected to the asset:  (If yes, record temperature reading in the comment section & verify accuracy of the gauge by informing operations of temperature reading obtained versus the operating temperature of the asset)", databaseName: "510_EXT-PV_CKLST_SEC011_Q001"); 
     
        attribute510EXT_PVCKLSTSEC011Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the pressure gauge exposed to high temperature from an external source or internal heat due to lack of protection by a proper siphon or trap:", databaseName: "510_EXT-PV_CKLST_SEC011_Q002"); 
     
        attribute510EXT_PVCKLSTSEC011Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are auxiliary equipment items in acceptable condition for continued service:  (Gauge connections, sight glasses, float wells, etc.)", databaseName: "510_EXT-PV_CKLST_SEC011_Q003"); 
     
        attribute510EXT_PVCKLSTSEC011Q004 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are there any threaded connections associated with the asset:", databaseName: "510_EXT-PV_CKLST_SEC011_Q004"); 
     
        attributeAre_threaded_connections_acceptably_engaged_and_leak_free = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are threaded connections acceptably engaged and leak free:", databaseName: "510_EXT-PV_CKLST_SEC011_Q005"); 
     
        attribute510EXT_PVCKLSTSEC011Q006 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is threaded connection piping constructed from schedule 80 or greater piping:", databaseName: "510_EXT-PV_CKLST_SEC011_Q006"); 
     
        attributeAre_threaded_connections_acceptable_for_continued_service = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are threaded connections acceptable for continued service:", databaseName: "510_EXT-PV_CKLST_SEC011_Q007"); 
     
        attribute510EXT_PVCKLSTSEC011Q008 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Were drain connections verified to be free of any foreign material that may cause plugging:", databaseName: "510_EXT-PV_CKLST_SEC011_Q008"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute510EXT_PVCKLSTSEC011Q001,
           attribute510EXT_PVCKLSTSEC011Q002,
           attribute510EXT_PVCKLSTSEC011Q003,
           attribute510EXT_PVCKLSTSEC011Q004,
           attributeAre_threaded_connections_acceptably_engaged_and_leak_free,
           attribute510EXT_PVCKLSTSEC011Q006,
           attributeAre_threaded_connections_acceptable_for_continued_service,
           attribute510EXT_PVCKLSTSEC011Q008,
        };
    }
  }
}
