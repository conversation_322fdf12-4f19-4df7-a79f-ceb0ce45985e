import 'package:api_inspection/generic/AppStyle.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/Generated/Asset_Walkdown_Access_F/SectionAsset_Walkdown_Access_FPage.dart';
import 'package:api_inspection/app/DataModel/Generated/Asset_Walkdown_PPE_F/SectionAsset_Walkdown_PPE_FPage.dart';
import 'package:api_inspection/app/DataModel/task.dart';
import 'package:api_inspection/app/UI/Tasks/TaskActivityTrackerPage.dart';
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
import 'package:api_inspection/generic/UIControls/attributePadding.dart';

import 'TaskDetailsPage.dart';

class TaskEditPage extends StatefulWidget {
  final Task task;
  final NavigatorState rootNavigator;
  const TaskEditPage(this.rootNavigator, this.task, {Key? key})
      : super(key: key);

  @override
  _TaskEditPageState createState() => _TaskEditPageState();
}

class _TaskEditPageState extends State<TaskEditPage> {
  @override
  void initState() {
    widget.task.setShouldDownloadPhotos(true);
    widget.task.asset.setShouldDownloadPhotos(true);
    super.initState();
  }

  String getTitle() {
    return "Task Edit Page";
  }

  void showAssetWalkdown() {
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => widget.task.asset.buildWalkdownPage()));
  }

  void showActivityTracker() {
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) =>
                TaskActivityTrackerPage(widget.task.activityTracker)));
  }

  void showTaskDetails() {
    Navigator.push(context,
        MaterialPageRoute(builder: (context) => TaskDetailsPage(widget.task)));
  }

  void showAssetPPE() {
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) =>
                SectionAsset_Walkdown_PPE_FPage(widget.task.asset.assetPPE)));
  }

  void showAssetAccess() {
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => SectionAsset_Walkdown_Access_FPage(
                widget.task.asset.assetAccess)));
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> columnWidgets = [
      AttributePadding.WithStdPadding(
        TeamToggleButton.withText('General Details', showTaskDetails),
      ),
      AttributePadding.WithStdPadding(
        TeamToggleButton.withText('Asset PPE', showAssetPPE),
      ),
      AttributePadding.WithStdPadding(
        TeamToggleButton.withText('Asset Access', showAssetAccess),
      ),
    ];

    if (widget.task.taskType != "Asset Walkdown") {
      columnWidgets.add(AttributePadding.WithStdPadding(
        TeamToggleButton.withText('Asset Details', showAssetWalkdown),
      ));
    }

    columnWidgets.addAll(widget.task.buildFormButtons(context));
    columnWidgets.addAll([
      AttributePadding.WithStdPadding(
        TeamToggleButton.withText('Activity Tracker', showActivityTracker),
      )
    ]);

    return Scaffold(
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        appBar: AppBar(
          leading: IconButton(
              icon: const Icon(Icons.arrow_back, size: 22, color: Colors.white),
              onPressed: () {
                widget.rootNavigator.pop();
              }),
          title: Text(
            getTitle(),
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        body: SingleChildScrollView(
            child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: columnWidgets,
        )));
  }
}
