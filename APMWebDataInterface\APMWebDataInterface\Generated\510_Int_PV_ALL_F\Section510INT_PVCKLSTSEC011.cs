//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Int_PV_ALL_F
{
  public class Section510INT_PVCKLSTSEC011 : DataModelItem {

    public override String DisplayName { 
      get {
        return "INTERNAL PIPING";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC011Q001;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC011Q002;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC011Q003;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510INT_PVCKLSTSEC011";

    public Section510INT_PVCKLSTSEC011(DataModelItem parent) : base(parent)
    {
            
        attribute510INT_PVCKLSTSEC011Q001 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Erosion", null),
          new PredefinedValueOption("Yes: Pitting", null)
        }, false, this, "Are there areas of corrosion, pitting, or erosion noted on the internal piping surfaces:  (The dimensions and location of any damage shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC011_Q001"); 
     
        attribute510INT_PVCKLSTSEC011Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Blistering", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Cracking", null),
          new PredefinedValueOption("Yes: Deformation", null),
          new PredefinedValueOption("Yes: Mechanical Damage", null)
        }, false, this, "Was any blistering, deformation, cracking, or other mechanical damage noted on the internal piping surfaces:  (The dimensions and location of any damage shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC011_Q002"); 
     
        attribute510INT_PVCKLSTSEC011Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the internal piping, including flanges, attachment hardware and supports, in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC011_Q003"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute510INT_PVCKLSTSEC011Q001,
           attribute510INT_PVCKLSTSEC011Q002,
           attribute510INT_PVCKLSTSEC011Q003,
        };
    }
  }
}
