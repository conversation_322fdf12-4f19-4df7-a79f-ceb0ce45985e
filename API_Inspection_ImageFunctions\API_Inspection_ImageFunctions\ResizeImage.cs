using Azure.Storage.Blobs;
using Microsoft.Azure.EventGrid.Models;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.EventGrid;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Formats;
using SixLabors.ImageSharp.Formats.Gif;
using SixLabors.ImageSharp.Formats.Jpeg;
using SixLabors.ImageSharp.Formats.Tiff;
using SixLabors.ImageSharp.Formats.Bmp;
using SixLabors.ImageSharp.Formats.Png;
using SixLabors.ImageSharp.PixelFormats;
using SixLabors.ImageSharp.Processing;
using System;
using System.IO;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace API_Inspection_ImageFunctions
{
  public static class ResizeImage
  {
    private static readonly string BLOB_STORAGE_CONNECTION_STRING = Environment.GetEnvironmentVariable("AzureWebJobsStorage");

    private static string GetBlobNameFromUrl(string bloblUrl)
    {
      var uri = new Uri(bloblUrl);
      var blobClient = new BlobClient(uri);
      return blobClient.Name;
    }

    private static IImageEncoder GetEncoder(string extension)
    {
      IImageEncoder encoder = null;

      extension = extension.Replace(".", "");

      var isSupported = Regex.IsMatch(extension, "bmp|gif|png|jpe?g|jpe|jfif|tif?f", RegexOptions.IgnoreCase);

      if (isSupported)
      {
        switch (extension.ToLower())
        {

          case "png":
            encoder = new PngEncoder()
            {
              CompressionLevel = (PngCompressionLevel)Convert.ToInt32(Environment.GetEnvironmentVariable("RESIZE_COMPRESSIONLEVEL"))
            };
            break;
          case "jpg":
            encoder = new JpegEncoder()
            {
              Quality = Convert.ToInt32(Environment.GetEnvironmentVariable("RESIZE_QUALITY"))
            };
            break;
          case "jpeg":
            encoder = new JpegEncoder()
            {
              Quality = Convert.ToInt32(Environment.GetEnvironmentVariable("RESIZE_QUALITY"))
            };
            break;
          case "jpe":
            encoder = new JpegEncoder()
            {
              Quality = Convert.ToInt32(Environment.GetEnvironmentVariable("RESIZE_QUALITY"))
            };
            break;
          case "jfif":
            encoder = new JpegEncoder()
            {
              Quality = Convert.ToInt32(Environment.GetEnvironmentVariable("RESIZE_QUALITY"))
            };
            break;
          case "tif":
            encoder = new TiffEncoder()
            {
              CompressionLevel = (SixLabors.ImageSharp.Compression.Zlib.DeflateCompressionLevel?)Convert.ToInt32(Environment.GetEnvironmentVariable("RESIZE_COMPRESSIONLEVEL"))
            };
            break;
          case "tiff":
            encoder = new TiffEncoder()
            {
              CompressionLevel = (SixLabors.ImageSharp.Compression.Zlib.DeflateCompressionLevel?)Convert.ToInt32(Environment.GetEnvironmentVariable("RESIZE_COMPRESSIONLEVEL"))
            };
            break;
          case "gif":
            encoder = new GifEncoder();
            break;
          case "bmp":
            encoder = new BmpEncoder();
            break;
          default:
            break;
        }
      }

      return encoder;
    }

    [FunctionName("ResizeImage")]
    public static async Task Run(
        [EventGridTrigger] EventGridEvent eventGridEvent,
        [Blob("{data.url}", FileAccess.Read)] Stream input,
        ILogger log)
    {
      try
      {
        if (input != null)
        {
          var createdEvent = ((JObject)eventGridEvent.Data).ToObject<StorageBlobCreatedEventData>();
          var extension = Path.GetExtension(createdEvent.Url);
          var encoder = GetEncoder(extension);

          if (encoder != null)
          {
            var thumbnailWidth = Convert.ToInt32(Environment.GetEnvironmentVariable("RESIZE_WIDTH"));
            var destinationContainerName = Environment.GetEnvironmentVariable("DESTINATION_CONTAINER_NAME");
            var blobServiceClient = new BlobServiceClient(BLOB_STORAGE_CONNECTION_STRING);
            var blobContainerClient = blobServiceClient.GetBlobContainerClient(destinationContainerName);
            var blobName = GetBlobNameFromUrl(createdEvent.Url);

            using (var output = new MemoryStream())
            using (var image = Image.Load(input))
            {
              var divisor = image.Width / thumbnailWidth;

              image.Mutate(x => x.AutoOrient());
              image.Mutate(x => x.Resize(thumbnailWidth, 0));
              image.Save(output, encoder);
              output.Position = 0;
              await blobContainerClient.UploadBlobAsync(blobName, output);
            }
          }
          else
          {
            log.LogInformation($"No encoder support for: {createdEvent.Url}");
          }
        }
      }
      catch (Exception ex)
      {
        log.LogError(ex.Message);
        throw;
      }
    }
  }
}
