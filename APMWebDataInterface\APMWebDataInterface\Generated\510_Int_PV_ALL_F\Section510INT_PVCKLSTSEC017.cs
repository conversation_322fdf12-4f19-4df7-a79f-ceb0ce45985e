//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Int_PV_ALL_F
{
  public class Section510INT_PVCKLSTSEC017 : DataModelItem {

    public override String DisplayName { 
      get {
        return "STEEL SUPPORTS";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC017Q001;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC017Q002;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC017Q003;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC017Q004;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510INT_PVCKLSTSEC017";

    public Section510INT_PVCKLSTSEC017(DataModelItem parent) : base(parent)
    {
            
        attribute510INT_PVCKLSTSEC017Q001 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Cracking", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Distortion", null)
        }, false, this, "Is there any evidence of corrosion, distortion, and or cracking of the steel supports:", databaseName: "510_INT-PV_CKLST_SEC017_Q001"); 
     
        attribute510INT_PVCKLSTSEC017Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Has the remaining thickness of corroded supporting elements been determined:", databaseName: "510_INT-PV_CKLST_SEC017_Q002"); 
     
        attribute510INT_PVCKLSTSEC017Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is there any evidence of cracking of the support to shell attachment weldment(s):", databaseName: "510_INT-PV_CKLST_SEC017_Q003"); 
     
        attribute510INT_PVCKLSTSEC017Q004 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Blistering", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Cracking", null),
          new PredefinedValueOption("Yes: Dimpling", null),
          new PredefinedValueOption("Yes: Distortion", null)
        }, false, this, "Is there any evidence of dimpling, distortion, cracking etc. of the shell in the heat effective zone of support attachment:", databaseName: "510_INT-PV_CKLST_SEC017_Q004"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute510INT_PVCKLSTSEC017Q001,
           attribute510INT_PVCKLSTSEC017Q002,
           attribute510INT_PVCKLSTSEC017Q003,
           attribute510INT_PVCKLSTSEC017Q004,
        };
    }
  }
}
