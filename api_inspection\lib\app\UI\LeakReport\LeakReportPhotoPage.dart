import 'package:api_inspection/app/DataModel/LeakReport/leakReportPhoto.dart';
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'package:api_inspection/generic/AppStyle.dart';
import 'package:flutter/material.dart';

class LeakReportPhotoPage extends StatefulWidget {
  final LeakReportPhoto photo;
  const LeakReportPhotoPage(this.photo, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return LeakReportPhotoPageState();
  }
}

class LeakReportPhotoPageState extends State<LeakReportPhotoPage> {
  @override
  Widget build(BuildContext context) {
    var leakReportPhoto = widget.photo;

    return Scaffold(
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        appBar: AppBar(
          title: Text(
            "Leak Report",
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        body: SingleChildScrollView(
            child: Column(children: [
          AttributePadding.WithStdPadding(
              leakReportPhoto.photos.buildWidget(showComments: false)),
          AttributePadding.WithStdPadding(
              leakReportPhoto.description.buildWidget(showPhotos: false)),
          AttributePadding.WithStdPadding(leakReportPhoto.comment
              .buildWidget(showPhotos: false, showComments: false)),
          AttributePadding.WithStdPadding(leakReportPhoto
              .areaOfInterestCoordinate
              .buildWidget(showPhotos: false)),
          AttributePadding.WithStdPadding(leakReportPhoto
              .upstreamTieInCoordinate
              .buildWidget(showPhotos: false)),
          AttributePadding.WithStdPadding(leakReportPhoto
              .downstreamTieInCoordinate
              .buildWidget(showPhotos: false)),
          AttributePadding.WithStdPadding(
              leakReportPhoto.utHighMeasurment.buildWidget(showPhotos: false)),
          AttributePadding.WithStdPadding(
              leakReportPhoto.utLowMeasurment.buildWidget(showPhotos: false)),
        ])));
  }
}
