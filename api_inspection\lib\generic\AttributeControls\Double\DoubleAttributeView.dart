import 'package:api_inspection/generic/AttributeControls/Double/DoubleAttributeViewEditable.dart';
import 'package:api_inspection/generic/AttributeControls/Double/DoubleAttributeViewNonEditable.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
import 'package:flutter/material.dart';

import '../AttributeStructure.dart';

class DoubleAttributeView extends StatefulWidget {
  final DoubleAttribute _attribute;
  final IsEditableController? editingController;
  final bool showPhotos;
  final bool showComments;

  const DoubleAttributeView(this._attribute,
      {Key? key,
      this.editingController,
      this.showPhotos = true,
      this.showComments = true})
      : super(key: key);

  @override
  _DoubleAttributeViewState createState() => _DoubleAttributeViewState();
}

class _DoubleAttributeViewState extends State<DoubleAttributeView> {
  @override
  Widget build(BuildContext context) {
    return AttributeStructure(widget._attribute,
        showComments: widget.showComments,
        showPhotos: widget.showPhotos,
        editingController: widget.editingController,
        editingBuilder: (context, updateListener) {
      return DoubleAttributeViewEditable(widget._attribute, updateListener);
    }, nonEditingBuilder: (context) {
      return DoubleAttributeViewNonEditable(widget._attribute);
    });
  }
}
