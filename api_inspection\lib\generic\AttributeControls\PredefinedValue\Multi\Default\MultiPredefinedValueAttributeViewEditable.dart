import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';

class MultiPredefinedValueAttributeViewEditable extends StatefulWidget {
  final MultiPredefinedValueAttribute _attribute;

  const MultiPredefinedValueAttributeViewEditable(this._attribute, {Key? key})
      : super(key: key);

  @override
  _MultiPredefinedValueAttributeViewEditableState createState() =>
      _MultiPredefinedValueAttributeViewEditableState();
}

class _MultiPredefinedValueAttributeViewEditableState
    extends State<MultiPredefinedValueAttributeViewEditable> {
  bool initialized = false;

  void initialize() {
    if (initialized) return;
    initialized = true;
    var attribute = widget._attribute;
    attribute.addListener(onAttributeChanged);
  }

  void onAttributeChanged() {
    setState(() {});
  }

  @override
  void dispose() {
    var attribute = widget._attribute;
    attribute.removeListener(onAttributeChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    initialize();

    double width = MediaQuery.of(context).size.width;

    Color selectedColor = const Color.fromARGB(255, 4, 188, 242);
    Color unselectedColor = const Color.fromARGB(255, 122, 122, 122);

    List<Widget> buttons = [];
    var attrValue = widget._attribute.getValue();
    for (var item in widget._attribute.getOptions()) {
      Color buttonColor;
      if (attrValue != null &&
          attrValue.any((element) => element == item.value)) {
        buttonColor = selectedColor;
      } else {
        buttonColor = unselectedColor;
      }

      buttons.add(SizedBox(
          width: (width - 55) / 2,
          child: TeamToggleButton.withText(
              item.value + " " + (widget._attribute.unit ?? ""),
              () => {
                    setState(() {
                      var currentValue = widget._attribute.getValue();
                      var batch = FirebaseFirestore.instance.batch();
                      if (currentValue != null &&
                          currentValue
                              .any((element) => element == item.value)) {
                        widget._attribute.removeValue(item.value);
                      } else {
                        widget._attribute.addValue(item.value);
                      }
                      widget._attribute.saveItem(batch);
                      batch.commit();
                    })
                  },
              borderColor: buttonColor)));
    }
    return Container(
        margin: const EdgeInsets.fromLTRB(0, 10, 0, 10),
        child: Wrap(children: buttons));
  }
}
