import 'package:api_inspection/generic/DataModelCommon/DataModel/user.dart';
import 'package:api_inspection/generic/AppStyle.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';

class CreateUserPage extends StatefulWidget {
  const CreateUserPage({Key? key}) : super(key: key);

  @override
  _CreateUserPageState createState() => _CreateUserPageState();
}

class _CreateUserPageState extends State<CreateUserPage> {
  final TextEditingController _emailController = TextEditingController();

  void showErrorMessage(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color.fromARGB(255, 41, 45, 52),
          title: const Text(
            'Error',
            style: TextStyle(color: Colors.white),
          ),
          content: Text(message, style: const TextStyle(color: Colors.white)),
          actions: [
            ElevatedButton(
              child: const Text('Ok', style: TextStyle(color: Colors.white)),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  Color selectedColor = const Color.fromARGB(255, 4, 188, 242);
  Color unselectedColor = const Color.fromARGB(255, 122, 122, 122);

  String role = "None";

  void createUserClicked() {
    var email = _emailController.text.trim();

    var numOfAtSymbol = email.characters.where((a) => a == "@").toList().length;
    if (email.isEmpty ||
        email.length < 5 ||
        numOfAtSymbol != 1 ||
        email.endsWith("@") ||
        email.startsWith("@")) {
      showErrorMessage(
          "An email must have atleast 5 characters and contain a @");
      return;
    }

    var user = UserProfile(email);
    user.role = role;
    var batch = FirebaseFirestore.instance.batch();
    user.saveItem(batch);

    batch.commit();

    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    var textFieldDecoration = const InputDecoration(
      enabledBorder: OutlineInputBorder(
        borderSide: BorderSide(color: Colors.white, width: 1.0),
      ),
      border: OutlineInputBorder(
        borderSide: BorderSide(color: Colors.white, width: 1.0),
      ),
    );

    var adminButton = TeamToggleButton(
        child: const Center(
            child: Text("Admin", style: TextStyle(color: Colors.white))),
        borderColor: role == "admin" ? selectedColor : unselectedColor,
        onPressed: () {
          setState(() {
            role = "admin";
          });
        });
    var technicianButton = TeamToggleButton(
        child: const Center(
            child: Text(
          "Technician",
          style: TextStyle(color: Colors.white),
        )),
        borderColor: role == "technician" ? selectedColor : unselectedColor,
        onPressed: () {
          setState(() {
            role = "technician";
          });
        });

    var roleRow = Container(
        margin: const EdgeInsets.fromLTRB(0, 0, 0, 10),
        child: Row(
          children: [
            SizedBox(width: 150, height: 75, child: adminButton),
            SizedBox(width: 150, height: 75, child: technicianButton),
          ],
        ));

    return Scaffold(
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        appBar: AppBar(
          title: Text(
            "New User",
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        body: SingleChildScrollView(
          child: Container(
            margin: const EdgeInsets.fromLTRB(20, 10, 20, 0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                const Text("Email",
                    style: TextStyle(color: Colors.white, fontSize: 22)),
                TextField(
                  controller: _emailController,
                  decoration: textFieldDecoration,
                  style: const TextStyle(color: Colors.white),
                ),
                Container(height: 20),
                const Text(
                  "Role",
                  style: TextStyle(color: Colors.white, fontSize: 22),
                ),
                roleRow,
                Container(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(
                        width: 100,
                        height: 40,
                        child: ElevatedButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            child: const Text("Cancel"))),
                    SizedBox(
                        width: 100,
                        height: 40,
                        child: ElevatedButton(
                            onPressed: createUserClicked,
                            child: const Text("Create"))),
                  ],
                )
              ],
            ),
          ),
        ));
  }
}
