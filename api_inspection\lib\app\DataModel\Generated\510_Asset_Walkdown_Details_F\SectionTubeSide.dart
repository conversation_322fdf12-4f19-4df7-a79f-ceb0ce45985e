//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class SectionTubeSide extends DataModelSection {
  @override
  String getDisplayName() => "Tube Side";
  SectionTubeSide(DataModelItem? parent)
      : super(parent: parent, sectionName: "Tube Side");

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeDesign_MAWP = DoubleAttribute(
    parent: this,
    displayName: "Design MAWP",
    databaseName: "510AW_Q331",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "psi",
  );

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeDesign_Temperature = DoubleAttribute(
    parent: this,
    displayName: "Design Temperature",
    databaseName: "510AW_Q332",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "F",
  );

  // ignore: non_constant_identifier_names
  late IntegerAttribute attributeOperating_Temperature = IntegerAttribute(
    parent: this,
    displayName: "Operating Temperature",
    databaseName: "510AW_Q333",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "F",
  );

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeOperating_Pressure = DoubleAttribute(
    parent: this,
    displayName: "Operating Pressure",
    databaseName: "510AW_Q334",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "psi",
  );

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributePRV_Set_Pressure = DoubleAttribute(
    parent: this,
    displayName: "PRV Set Pressure",
    databaseName: "510AW_Q335",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "psi",
  );

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeDesign_MAWP,
      attributeDesign_Temperature,
      attributeOperating_Temperature,
      attributeOperating_Pressure,
      attributePRV_Set_Pressure,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionTubeSide";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
