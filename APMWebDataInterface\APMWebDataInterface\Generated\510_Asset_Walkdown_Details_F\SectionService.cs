//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Asset_Walkdown_Details_F
{
  public class SectionService : DataModelItem {

    public override String DisplayName { 
      get {
        return "Service";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public StringAttribute attributeServiceProductContents;
    public DoubleAttribute attributeSpecific_Gravity;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionService";

    public SectionService(DataModelItem parent) : base(parent)
    {
            
        attributeServiceProductContents = new StringAttribute(this, displayName: "Service/Product/Contents", databaseName: "510AW_Q136"); 
     
        attributeSpecific_Gravity = new DoubleAttribute(this, displayName: "Specific Gravity", databaseName: "510AW_Q137", areCommentsRequired: false, displayUnit: null, allowNegatives: true); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeServiceProductContents,
           attributeSpecific_Gravity,
        };
    }
  }
}
