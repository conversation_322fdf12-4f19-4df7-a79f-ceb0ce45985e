//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionFlexible_Task_Data_F
{
  public class SectionTaskPhotos : DataModelCollectionItem {

    public override String DisplayName { 
      get {
        return "TaskPhotos";
      }
    }
    
    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PhotoAttribute attributePhotos;
    public StringAttribute attributeDescription;
    public LocationAttribute attributeGIS_Location;

    #endregion [-- Attributes --]

    public SectionTaskPhotos(String id, DataModelItem parent) : base(id, parent)
    {
            
        attributePhotos = new PhotoAttribute(this, displayName: "Photos", databaseName: "GT_Q212"); 
     
        attributeDescription = new StringAttribute(this, displayName: "Description", databaseName: "GT_Q210"); 
     
        attributeGIS_Location = new LocationAttribute(this, displayName: "GIS Location", databaseName: "GT_Q211"); 
    }   
    
    public override DataModelItem[] GetChildren() {
      var parentChildren = base.GetChildren();

      return parentChildren.Concat(new DataModelItem[] { 
        attributePhotos,
           attributeDescription,
           attributeGIS_Location,
      }).ToArray();
    }
  }
}
