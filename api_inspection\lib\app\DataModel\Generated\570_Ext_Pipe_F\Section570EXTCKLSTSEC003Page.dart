//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'Section570EXTCKLSTSEC003.dart';

// ignore: camel_case_types
class Section570EXTCKLSTSEC003Page extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final Section570EXTCKLSTSEC003 section570EXTCKLSTSEC003;

  const Section570EXTCKLSTSEC003Page(this.section570EXTCKLSTSEC003, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _Section570EXTCKLSTSEC003PageState();
  }
}

class _Section570EXTCKLSTSEC003PageState
    extends State<Section570EXTCKLSTSEC003Page> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.section570EXTCKLSTSEC003,
        title: "STEEL SUPPORTS",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC003
                      .attributeIs_the_piping_properly_supported
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC003.attribute570EXTCKLSTSEC003Q002
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC003.attribute570EXTCKLSTSEC003Q003
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC003
                      .attributeAre_pipe_rollers_shoes_or_slide_plates_restricted
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC003
                      .attributeIs_there_evidence_of_piping_distortion_due_to_pipe_movement
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC003.attribute570EXTCKLSTSEC003Q006
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC003.attribute570EXTCKLSTSEC003Q007
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC003.attribute570EXTCKLSTSEC003Q008
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC003.attribute570EXTCKLSTSEC003Q009
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC003.attribute570EXTCKLSTSEC003Q010
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC003
                      .attributeAre_foundation_anchor_bolts_loose_or_corroded
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC003.attribute570EXTCKLSTSEC003Q012
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC003
                      .attributeAre_dummy_legs_retaining_moisture_or_water
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC003.attribute570EXTCKLSTSEC003Q014
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC003.attribute570EXTCKLSTSEC003Q015
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC003
                      .attributeAre_supports_in_acceptable_condition_for_continued_service
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
