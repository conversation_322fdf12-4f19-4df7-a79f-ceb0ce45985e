//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'Section510INT_PVCKLSTSEC007.dart';

// ignore: camel_case_types
class Section510INT_PVCKLSTSEC007Page extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final Section510INT_PVCKLSTSEC007 section510INT_PVCKLSTSEC007;

  const Section510INT_PVCKLSTSEC007Page(this.section510INT_PVCKLSTSEC007,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _Section510INT_PVCKLSTSEC007PageState();
  }
}

class _Section510INT_PVCKLSTSEC007PageState
    extends State<Section510INT_PVCKLSTSEC007Page> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.section510INT_PVCKLSTSEC007,
        title: "BONNET - HEX ONLY",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC007
                      .attribute510INT_PVCKLSTSEC007Q001
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC007
                      .attribute510INT_PVCKLSTSEC007Q002
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC007
                      .attribute510INT_PVCKLSTSEC007Q003
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC007
                      .attribute510INT_PVCKLSTSEC007Q004
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC007
                      .attribute510INT_PVCKLSTSEC007Q005
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC007
                      .attribute510INT_PVCKLSTSEC007Q006
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC007
                      .attributeIs_the_bonnet_to_shell_attachment_achieved_via_welding
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC007
                      .attribute510INT_PVCKLSTSEC007Q008
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC007
                      .attribute510INT_PVCKLSTSEC007Q009
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC007
                      .attribute510INT_PVCKLSTSEC007Q010
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC007
                      .attribute510INT_PVCKLSTSEC007Q011
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
