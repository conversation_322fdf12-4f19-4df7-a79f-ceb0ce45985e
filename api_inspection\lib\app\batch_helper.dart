import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../generic/DataModelCommon/Attributes/AttributeBase.dart';
import '../generic/DataModelCommon/DataModelItem.dart';

class BatchHelper {
  static Future<void> saveAndCommit(DataModelItem item) async {
    var batch = FirebaseFirestore.instance.batch();
    item.saveItem(batch);
    batch.commit();
  }

  static Future<void> saveAndCommitStringAttribute(
      StringAttribute attr, String? value) async {
    var batch = FirebaseFirestore.instance.batch();
    attr.setValue(value, batch);
    attr.saveItem(batch);
    batch.commit();
  }

  static Future<void> setCommentAndCommit(AttributeBase attr, String comment,
      {bool notify = false}) async {
    var batch = FirebaseFirestore.instance.batch();
    attr.setComment(comment, notify: notify);
    attr.saveItem(batch);
    batch.commit();
  }
}
