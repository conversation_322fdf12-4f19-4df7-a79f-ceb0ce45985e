//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class Section510EXT_PVCKLSTSEC005 extends DataModelSection {
  @override
  String getDisplayName() => "STEEL SUPPORTS";
  Section510EXT_PVCKLSTSEC005(DataModelItem? parent)
      : super(parent: parent, sectionName: "STEEL SUPPORTS");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510EXT_PVCKLSTSEC005Q001 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Is there any evidence of corrosion, distortion, and or cracking of the steel supports:",
          databaseName: "510_EXT-PV_CKLST_SEC005_Q001",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Cracking", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Corrosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Distortion", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC005Q002 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Has the remaining thickness of corroded supporting elements been determined:  (Skirts, columns, and bracing)",
          databaseName: "510_EXT-PV_CKLST_SEC005_Q002",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510EXT_PVCKLSTSEC005Q003 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Is there any evidence of buckling or excessive deflection of the columns and or load-carrying beams:",
          databaseName: "510_EXT-PV_CKLST_SEC005_Q003",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Buckling", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Deflection", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC005Q004 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "HEX Only - Is there adequate area to allow free expansion of the exchanger:",
          databaseName: "510_EXT-PV_CKLST_SEC005_Q004",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC005Q005 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "HEX Only - Is the expansion allowance system employed in acceptable condition for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC005_Q005",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeDoes_the_asset_have_a_support_skirt =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Does the asset have a support skirt:",
          databaseName: "510_EXT-PV_CKLST_SEC005_Q006",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC005Q007 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the asset support skirt accessible for internal inspection:",
          databaseName: "510_EXT-PV_CKLST_SEC005_Q007",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC005Q008 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Does the asset skirt have a protective coating system applied to the external surfaces:",
          databaseName: "510_EXT-PV_CKLST_SEC005_Q008",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC005Q009 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the skirt protective coating system in acceptable condition for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC005_Q009",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510EXT_PVCKLSTSEC005Q010 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Were any corrosion cells, pitting, or thinning noted on the skirt surfaces:",
          databaseName: "510_EXT-PV_CKLST_SEC005_Q010",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Corrosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Pitting", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Thinning", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510EXT_PVCKLSTSEC005Q011 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Was any deformation, cracking, or mechanical damage noted on the skirt surfaces:",
          databaseName: "510_EXT-PV_CKLST_SEC005_Q011",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Cracking", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Deformation", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Mechanical Damage", null,
            isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC005Q012 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are support lugs and connection hardware in acceptable condition for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC005_Q012",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attributeIs_the_asset_skirt_insulated =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Is the asset skirt insulated:",
          databaseName: "510_EXT-PV_CKLST_SEC005_Q013",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC005Q014 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the skirt insulation in acceptable condition for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC005_Q014",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeIs_fireproofing_applied_to_asset_skirt_or_support_beams =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is fireproofing applied to asset skirt or support beams:",
          databaseName: "510_EXT-PV_CKLST_SEC005_Q015",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC005Q016 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the skirt fireproofing in acceptable condition for continued service:  (Any crack over 0.250” in width, and any crack which has displacement or bulging of the concrete fireproofing material should be investigated for corrosion under fireproofing)(CUF)",
          databaseName: "510_EXT-PV_CKLST_SEC005_Q016",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC005Q017 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the asset skirt in acceptable condition for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC005_Q017",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC005Q018 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is there evidence of piping attachment distortion due to pipe movement:  (I.e. supports and guides attached to asset)",
          databaseName: "510_EXT-PV_CKLST_SEC005_Q018",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeAre_supports_in_acceptable_condition_for_continued_service =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are supports in acceptable condition for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC005_Q019",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attribute510EXT_PVCKLSTSEC005Q001,
      attribute510EXT_PVCKLSTSEC005Q002,
      attribute510EXT_PVCKLSTSEC005Q003,
      attribute510EXT_PVCKLSTSEC005Q004,
      attribute510EXT_PVCKLSTSEC005Q005,
      attributeDoes_the_asset_have_a_support_skirt,
      attribute510EXT_PVCKLSTSEC005Q007,
      attribute510EXT_PVCKLSTSEC005Q008,
      attribute510EXT_PVCKLSTSEC005Q009,
      attribute510EXT_PVCKLSTSEC005Q010,
      attribute510EXT_PVCKLSTSEC005Q011,
      attribute510EXT_PVCKLSTSEC005Q012,
      attributeIs_the_asset_skirt_insulated,
      attribute510EXT_PVCKLSTSEC005Q014,
      attributeIs_fireproofing_applied_to_asset_skirt_or_support_beams,
      attribute510EXT_PVCKLSTSEC005Q016,
      attribute510EXT_PVCKLSTSEC005Q017,
      attribute510EXT_PVCKLSTSEC005Q018,
      attributeAre_supports_in_acceptable_condition_for_continued_service,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section510EXT_PVCKLSTSEC005";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
