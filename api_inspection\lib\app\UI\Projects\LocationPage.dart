import 'package:api_inspection/generic/AppStyle.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/location.dart';
import 'package:api_inspection/generic/UIControls/attributePadding.dart';

class LocationPage extends StatefulWidget {
  final Location location;

  const LocationPage(this.location, {Key? key}) : super(key: key);

  @override
  _LocationPageState createState() => _LocationPageState();
}

class _LocationPageState extends State<LocationPage> {
  @override
  Widget build(BuildContext context) {
    var location = widget.location;
    return Scaffold(
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        appBar: AppBar(
          title: Text(
            "Location",
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        body: Scrollbar(
            thumbVisibility: true,
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(location.name.buildWidget()),
                  AttributePadding.WithStdPadding(
                      location.description.buildWidget()),
                  AttributePadding.WithStdPadding(
                      location.street1.buildWidget()),
                  AttributePadding.WithStdPadding(
                      location.street2.buildWidget()),
                  AttributePadding.WithStdPadding(location.city.buildWidget()),
                  AttributePadding.WithStdPadding(
                      location.region.buildWidget()),
                  AttributePadding.WithStdPadding(
                      location.postalCode.buildWidget()),
                  AttributePadding.WithStdPadding(
                      location.country.buildWidget()),
                  AttributePadding.WithStdPadding(
                      location.comment.buildWidget()),
                ],
              ),
            )));
  }
}
