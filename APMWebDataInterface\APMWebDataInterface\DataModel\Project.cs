﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CommonDataInterface;
using CommonDataInterface.Attributes;
using APMWebDataInterface.DataModel;
using APMWebDataInterface.DataModel.Activity;
using String = System.String;

namespace APMWebDataInterface.ExampleDataModel
{
    
  public class Contact : DataModelCollectionItem {

    public StringAttribute name;
    public StringAttribute title;
    public PhoneNumberAttribute phoneNumber;
    public StringAttribute email;
    
    public override string DisplayName => "Project";

    public Contact(String id, DataModelItem parent) : base(id, parent)
    {
      
      name = new StringAttribute(parent: this, displayName: "Contact");
      title = new StringAttribute(parent: this, displayName: "Title");
      phoneNumber = new PhoneNumberAttribute(parent: this, displayName: "Phone Number");
      email = new StringAttribute(parent: this, displayName: "Email");

    }
      
    public override DataModelItem[] GetChildren(){
      var parentChildren = base.GetChildren();

      return parentChildren.Concat(new DataModelItem[] { name, title, phoneNumber, email }).ToArray();
    }

  }

  public class ProjectClientDetails : DataModelItem {
    
    public override string DisplayName => "Client Details";

    public ProjectClientDetails(DataModelItem? parent) : base(parent)
    {
      contacts = new DataModelCollection<Contact>("Contacts", (parent, entry) => { 
        return new Contact(entry.Key, parent); 
      }, (parent, id) => { 
        return new Contact(id, parent); 
      }, this);
    }

    public DataModelCollection<Contact> contacts;

    
    public override DataModelItem[] GetChildren(){
      var parentChildren = base.GetChildren();

      return parentChildren.Concat(new DataModelItem[] { contacts }).ToArray();
    }


    internal override String GetDBName() {
      return "ClientDetails";
    }

  }

  public class ProjectAccountingDetails : DataModelItem{
    public override string DisplayName => "Accounting Details";

    public ProjectAccountingDetails(DataModelItem? parent) : base(parent)
    {
      apmProjectNumber = new StringAttribute(parent: this, displayName: "APM Project Number", isQueryable: true);
      teamDistrictNumber = new StringAttribute(parent: this, displayName: "TEAM District Number");
      teamProjectNumber = new StringAttribute(parent: this, displayName: "TEAM Project Number");
      projectNumber = new StringAttribute(parent: this, displayName: "Project Number");
    }

    public StringAttribute apmProjectNumber;
    public StringAttribute teamDistrictNumber;
    public StringAttribute teamProjectNumber;
    public StringAttribute projectNumber;

    
    public override DataModelItem[] GetChildren(){
      var parentChildren = base.GetChildren();

      return parentChildren.Concat(new DataModelItem[] { 
        apmProjectNumber, 
        teamDistrictNumber,
        teamProjectNumber,
        projectNumber
      }).ToArray();
    }
    
    internal override String GetDBName() {
      return "AccountingDetails";
    }
  }

  public class Project : ConcretePhotoRoot
  {
    public override string DisplayName => "Project";

    public MultiStringAttribute assetIds;
    
    private String _locationId;

    public String locationId => _locationId;

    public String pendingLocationId => _pendingLocationId?.Value;
    private PendingChange<String> _pendingLocationId;

    public void SetLocationId(PendingChange<String> locationIdChange)
    {
      _pendingLocationId = locationIdChange;
    }

    
    public StringAttribute name;
    public StringAttribute businessUnitId;
    public StringAttribute description;
    public PredefinedValueAttribute status;
    public DateAttribute plannedStart;
    public DateAttribute plannedEnd;

    public ProjectAccountingDetails accountingDetails;
    public ProjectClientDetails clientDetails;



    public DataModelCollection<ProjectActivity> activities;


    /// <summary>
    /// Used to create a new project
    /// </summary>
    public Project(String locationId) : base(null)
    {
      this._locationId = locationId;
      InitializeAttribute();
    }


    internal Project(String id, String locationId) : base(id, null)
    {
      if (id == null || locationId == null) {
        throw new Exception("id and locationId cannot be null,  if created new project, please use empty constructor");
      }

      this._locationId = locationId;
      InitializeAttribute();
    }

    private void InitializeAttribute()
    {
      name = new StringAttribute(parent: this, displayName: "Name");
      businessUnitId = new StringAttribute(this, "BusinessUnitId", isQueryable: true);
      description = new StringAttribute(parent: this, displayName: "Project Description");
      status = new PredefinedValueAttribute(new List<PredefinedValueOption>
      {
          new PredefinedValueOption("Scheduled",null ), new PredefinedValueOption("In Progress", null),
          new PredefinedValueOption("Completed",null ), new PredefinedValueOption("Published",null )
      },false, this, "Status");
      plannedStart = new DateAttribute(this, "Planned Start");
      plannedEnd = new DateAttribute(this, "Planned End");

      
      accountingDetails = new ProjectAccountingDetails(this);
      clientDetails = new ProjectClientDetails(this);

      assetIds = new MultiStringAttribute(this, "AssetIds", null, false);


      activities = new DataModelCollection<ProjectActivity>("ProjectActivities", (parent, entry) => {
        return new ProjectActivity(parent, entry.Key);
      }, (parent, id) => {
        return new ProjectActivity(parent, id);
      }, this);
    }

    
    internal override String GetDBPath() => "projects." + GetDBName();


    
    public override DataModelItem[] GetChildren(){
      var parentChildren = base.GetChildren();

      return parentChildren.Concat(new DataModelItem[] { 
        name, description, status, plannedStart, plannedEnd, accountingDetails, clientDetails, assetIds, activities, businessUnitId
      }).ToArray();
    }

    
    internal override String GetDBName() {
      return id;
    }
    
    public override void UpdateFromMap(Dictionary<string, object> map)
    {
      if (map.ContainsKey("LocationId")) {
        _locationId = map["LocationId"].ToString();
      }
      base.UpdateFromMap(map);
    }

    public override bool GetHasDatabaseChangesPending()
    {
      if (_pendingLocationId != null) {
        return true;
      }

      return base.GetHasDatabaseChangesPending();
    }

    protected override async Task DoAddPendingChangesToDictionary(Dictionary<string, object> updates, string user)
    {
      if (_pendingLocationId != null) {
        updates["LocationId"] = _pendingLocationId.Value;
        _locationId = _pendingLocationId.Value;
        _pendingLocationId = null;
      }


      await base.DoAddPendingChangesToDictionary(updates, user);
    }
    
    public override void DoAddOneTimeChangesToDictionary(Dictionary<string, Object> updates)
    {
      updates["LocationId"] = locationId;
    }


    public override Task SavePendingChanges(string user)
    {
        APM_WebDataInterface.Global.AuthorizeWriteToRootObject(this.businessUnitId, user);
        return base.SavePendingChanges(user);
    }
  }

}
