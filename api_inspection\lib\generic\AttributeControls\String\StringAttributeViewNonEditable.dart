import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';

class StringAttributeViewNonEditable extends StatefulWidget {
  final StringAttribute _attribute;

  const StringAttributeViewNonEditable(this._attribute, {Key? key})
      : super(key: key);

  @override
  _StringAttributeViewNonEditableState createState() =>
      _StringAttributeViewNonEditableState();
}

class _StringAttributeViewNonEditableState
    extends State<StringAttributeViewNonEditable> {
  void onAttributeChanged() {
    setState(() {});
  }

  @override
  void dispose() {
    StringAttribute attribute = widget._attribute;
    attribute.removeListener(onAttributeChanged);
    super.dispose();
  }

  @override
  void initState() {
    StringAttribute attribute = widget._attribute;
    attribute.addListener(onAttributeChanged);

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: const EdgeInsets.fromLTRB(40, 10, 40, 10),
        child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              widget._attribute.getValue() ?? "",
              style: const TextStyle(color: Colors.white, fontSize: 22),
            )));
  }
}
