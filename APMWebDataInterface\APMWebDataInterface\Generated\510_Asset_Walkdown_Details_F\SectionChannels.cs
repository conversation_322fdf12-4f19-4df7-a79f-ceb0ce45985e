//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Asset_Walkdown_Details_F
{
  public class SectionChannels : DataModelCollectionItem {

    public override String DisplayName { 
      get {
        return "Channels";
      }
    }
    
    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public StringAttribute attributeNumber;
    public PhotoAttribute attributePhotos;
    public PredefinedValueAttribute attributeLocation;
    public StringAttribute attributeMaterial_Spec_and_Grade;
    public DoubleAttribute attributeAllowable_Stress_at_Temperature;
    public DoubleAttribute attributeNominal_Thickness;
    public DoubleAttribute attributeCorrosion_Allowance;
    public DoubleAttribute attributeLength_or_Height;
    public DoubleAttribute attributeJoint_Efficiency;

    #endregion [-- Attributes --]

    public SectionChannels(String id, DataModelItem parent) : base(id, parent)
    {
            
        attributeNumber = new StringAttribute(this, displayName: "Number", databaseName: "510AW_Q431"); 
     
        attributePhotos = new PhotoAttribute(this, displayName: "Photos", databaseName: "510AW_Q439"); 
     
        attributeLocation = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Top", null),
          new PredefinedValueOption("Bottom", null),
          new PredefinedValueOption("N", null),
          new PredefinedValueOption("S", null),
          new PredefinedValueOption("E", null),
          new PredefinedValueOption("W", null)
        }, false, this, "Location", databaseName: "510AW_Q432"); 
     
        attributeMaterial_Spec_and_Grade = new StringAttribute(this, displayName: "Material Spec and Grade", databaseName: "510AW_Q433"); 
     
        attributeAllowable_Stress_at_Temperature = new DoubleAttribute(this, displayName: "Allowable Stress at Temperature", databaseName: "510AW_Q434", areCommentsRequired: false, displayUnit: "psi", allowNegatives: true); 
     
        attributeNominal_Thickness = new DoubleAttribute(this, displayName: "Nominal Thickness", databaseName: "510AW_Q435", areCommentsRequired: false, displayUnit: "in", allowNegatives: true); 
     
        attributeCorrosion_Allowance = new DoubleAttribute(this, displayName: "Corrosion Allowance", databaseName: "510AW_Q436", areCommentsRequired: false, displayUnit: "in", allowNegatives: true); 
     
        attributeLength_or_Height = new DoubleAttribute(this, displayName: "Length or Height", databaseName: "510AW_Q437", areCommentsRequired: false, displayUnit: "ft", allowNegatives: true); 
     
        attributeJoint_Efficiency = new DoubleAttribute(this, displayName: "Joint Efficiency", databaseName: "510AW_Q438", areCommentsRequired: false, displayUnit: null, allowNegatives: true); 
    }   
    
    public override DataModelItem[] GetChildren() {
      var parentChildren = base.GetChildren();

      return parentChildren.Concat(new DataModelItem[] { 
        attributeNumber,
           attributePhotos,
           attributeLocation,
           attributeMaterial_Spec_and_Grade,
           attributeAllowable_Stress_at_Temperature,
           attributeNominal_Thickness,
           attributeCorrosion_Allowance,
           attributeLength_or_Height,
           attributeJoint_Efficiency,
      }).ToArray();
    }
  }
}
