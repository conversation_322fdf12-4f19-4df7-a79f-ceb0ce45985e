//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class Section510EXT_PVCKLSTSEC009 extends DataModelSection {
  @override
  String getDisplayName() => "NOZZLES";
  Section510EXT_PVCKLSTSEC009(DataModelItem? parent)
      : super(parent: parent, sectionName: "NOZZLES");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC009Q001 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Were indications of leaks or repair clamps noted at piping attachments: (Discoloration, wet surfaces, odors, etc.)",
          databaseName: "510_EXT-PV_CKLST_SEC009_Q001",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC009Q002 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Were flange fasteners fully engaged: (Any fastener failing to do so is considered acceptably engaged if the lack of complete engagement is not more than one thread)",
          databaseName: "510_EXT-PV_CKLST_SEC009_Q002",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC009Q003 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are the flange fasteners utilized in alignment with Client specification:",
          databaseName: "510_EXT-PV_CKLST_SEC009_Q003",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC009Q004 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are the flange gaskets utilized in alignment with Client specification:",
          databaseName: "510_EXT-PV_CKLST_SEC009_Q004",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC009Q005 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the Nozzle protective coating in acceptable condition for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC009_Q005",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC009Q006 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are nozzle re-pads and weep holes in acceptable condition for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC009_Q006",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC009Q007 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are nozzles and adjacent shell areas in acceptable condition for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC009_Q007",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeDo_the_gaskets_show_signs_of_damage_or_leaking =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Do the gaskets show signs of damage or leaking:",
          databaseName: "510_EXT-PV_CKLST_SEC009_Q008",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510EXT_PVCKLSTSEC009Q009 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Was any mechanical damage, distortion, or cracking noted on the nozzle surfaces:  (Possibly caused by settling)",
          databaseName: "510_EXT-PV_CKLST_SEC009_Q009",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Cracking", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Distortion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Mechanical Damage", null,
            isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510EXT_PVCKLSTSEC009Q010 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Were any corrosion cells or pitting noted on the nozzle surfaces or fasteners:",
          databaseName: "510_EXT-PV_CKLST_SEC009_Q010",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Corrosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Pitting", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeAre_nozzles_in_acceptable_condition_for_continued_service =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are nozzles in acceptable condition for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC009_Q011",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attribute510EXT_PVCKLSTSEC009Q001,
      attribute510EXT_PVCKLSTSEC009Q002,
      attribute510EXT_PVCKLSTSEC009Q003,
      attribute510EXT_PVCKLSTSEC009Q004,
      attribute510EXT_PVCKLSTSEC009Q005,
      attribute510EXT_PVCKLSTSEC009Q006,
      attribute510EXT_PVCKLSTSEC009Q007,
      attributeDo_the_gaskets_show_signs_of_damage_or_leaking,
      attribute510EXT_PVCKLSTSEC009Q009,
      attribute510EXT_PVCKLSTSEC009Q010,
      attributeAre_nozzles_in_acceptable_condition_for_continued_service,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section510EXT_PVCKLSTSEC009";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
