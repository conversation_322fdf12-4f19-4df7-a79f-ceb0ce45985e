//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';

// ignore: camel_case_types
class SectionLeakReportPhotos extends DataModelCollectionItem {
  @override
  String getDisplayName() => "Leak Report Photos";

  SectionLeakReportPhotos(String id, DataModelItem parent) : super(id, parent);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeDescription = StringAttribute(
      parent: this,
      displayName: "Description",
      databaseName: "LR_Q210",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeComment = StringAttribute(
      parent: this,
      displayName: "Comment",
      databaseName: "LR_Q211",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late LocationAttribute attributeArea_of_interest_GIS_position =
      LocationAttribute(
          parent: this,
          displayName: "Area of interest GIS position",
          databaseName: "LR_Q212",
          areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late LocationAttribute attributeUpstream_tie_in_GIS_location =
      LocationAttribute(
          parent: this,
          displayName: "Upstream tie-in GIS location",
          databaseName: "LR_Q213",
          areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late LocationAttribute attributeDownstream_tie_in_GIS_location =
      LocationAttribute(
          parent: this,
          displayName: "Downstream tie-in GIS location",
          databaseName: "LR_Q214",
          areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeUT_High_Measurement = DoubleAttribute(
    parent: this,
    displayName: "UT High Measurement",
    databaseName: "LR_Q215",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "in",
  );

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeUT_Low_measurement = DoubleAttribute(
    parent: this,
    displayName: "UT Low measurement",
    databaseName: "LR_Q216",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "in",
  );

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeDescription,
      attributeComment,
      attributeArea_of_interest_GIS_position,
      attributeUpstream_tie_in_GIS_location,
      attributeDownstream_tie_in_GIS_location,
      attributeUT_High_Measurement,
      attributeUT_Low_measurement,
    ]);
    return children;
  }
}
