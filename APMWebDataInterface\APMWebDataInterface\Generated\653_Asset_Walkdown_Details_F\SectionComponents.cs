//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section653_Asset_Walkdown_Details_F
{
  public class SectionComponents : DataModelItem {

    public override String DisplayName { 
      get {
        return "Components";
      }
    }

    #region [-- Sub Sections --]

    #region [-- SectionShellCourses --]
    private DataModelCollection<SectionShellCourses> _sectionShellCourses;
    public DataModelCollection<SectionShellCourses> sectionShellCourses {
        get {
            if (_sectionShellCourses == null) {
              _sectionShellCourses = new DataModelCollection<SectionShellCourses>("Shell Courses", (parent, entry) => {
                 return new SectionShellCourses(entry.Key, _sectionShellCourses);
              }, (parent, id) => {
                return new SectionShellCourses(id, _sectionShellCourses);
              }, this);
            }

            return _sectionShellCourses;
        }
    }
    #endregion [-- SectionShellCourses --]
    
    #region [-- SectionTankBottomFloor --]
    private SectionTankBottomFloor _sectionTankBottomFloor;
    public SectionTankBottomFloor sectionTankBottomFloor {
        get {
            if (_sectionTankBottomFloor == null) {
               _sectionTankBottomFloor = new SectionTankBottomFloor(this);
            }

            return _sectionTankBottomFloor;
        }
    }
    #endregion [-- SectionTankBottomFloor --]
    
    #region [-- SectionTankRoof --]
    private DataModelCollection<SectionTankRoof> _sectionTankRoof;
    public DataModelCollection<SectionTankRoof> sectionTankRoof {
        get {
            if (_sectionTankRoof == null) {
              _sectionTankRoof = new DataModelCollection<SectionTankRoof>("Tank Roof", (parent, entry) => {
                 return new SectionTankRoof(entry.Key, _sectionTankRoof);
              }, (parent, id) => {
                return new SectionTankRoof(id, _sectionTankRoof);
              }, this);
            }

            return _sectionTankRoof;
        }
    }
    #endregion [-- SectionTankRoof --]
    
    #region [-- SectionNozzles --]
    private DataModelCollection<SectionNozzles> _sectionNozzles;
    public DataModelCollection<SectionNozzles> sectionNozzles {
        get {
            if (_sectionNozzles == null) {
              _sectionNozzles = new DataModelCollection<SectionNozzles>("Nozzles", (parent, entry) => {
                 return new SectionNozzles(entry.Key, _sectionNozzles);
              }, (parent, id) => {
                return new SectionNozzles(id, _sectionNozzles);
              }, this);
            }

            return _sectionNozzles;
        }
    }
    #endregion [-- SectionNozzles --]
    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionComponents";

    public SectionComponents(DataModelItem parent) : base(parent)
    {
            
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           sectionShellCourses,
           sectionTankBottomFloor,
           sectionTankRoof,
           sectionNozzles,
        };
    }
  }
}
