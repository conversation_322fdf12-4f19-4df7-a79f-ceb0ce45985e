//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/UI/CollectionControl.dart';
import 'package:uuid/uuid.dart';
import 'SectionNozzles.dart';

// ignore: camel_case_types
// ignore: camel_case_types
class SectionNozzlesPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final DataModelCollection<SectionNozzles> sectionNozzles;
  const SectionNozzlesPage(this.sectionNozzles, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionNozzlesPageState();
  }
}

class _SectionNozzlesPageState extends State<SectionNozzlesPage> {
  Widget _cardBuilder(BuildContext context, int number, SectionNozzles item) {
    return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(number.toString(),
              style: const TextStyle(color: Colors.white, fontSize: 18)),
        ]);
  }

  Widget _editPageBuilder(BuildContext context, SectionNozzles item) {
    return SectionScaffold(
        section: item,
        title: "Nozzles",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(item.attributeNumber
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item.attributePhotos
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item.attributeType
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item
                      .attributeMaterial_Spec_and_Grade
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item.attributePipe_Size
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item.attributePipe_Schedule
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item.attributeFlange_Rating
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item
                      .attributeReinforcement_pad_type
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item
                      .attributeReinforcement_pad_dimensions
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item
                      .attributeReinforcement_pad_thickness
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }

  SectionNozzles _createNewItem() {
    String id = const Uuid().v4();
    var item = SectionNozzles(id, widget.sectionNozzles);

    return item;
  }

  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionNozzles,
        title: "Nozzles",
        childBuilder: (editableController) {
          return Center(
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: <Widget>[
                Expanded(
                    child: CollectionControl<SectionNozzles>(
                  cardTitle: "Nozzles",
                  collection: widget.sectionNozzles,
                  cardBuilder: _cardBuilder,
                  createNewItem: _createNewItem,
                  editPageBuilder: _editPageBuilder,
                ))
              ]));
        });
  }
}
