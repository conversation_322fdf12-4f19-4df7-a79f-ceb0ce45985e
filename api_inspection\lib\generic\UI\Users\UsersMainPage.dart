import 'dart:async';

import 'package:api_inspection/generic/DataModelCommon/DataModel/user.dart';
import 'package:api_inspection/generic/AppStyle.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/DatabaseInterfaces/FirebaseDatabaseHelper.dart';
import 'package:api_inspection/generic/UI/Users/<USER>';
import 'package:api_inspection/generic/UI/Users/<USER>';
import 'package:collection/collection.dart';

class UsersMainPage extends StatefulWidget {
  final String title = "Users";
  const UsersMainPage({Key? key}) : super(key: key);

  @override
  _UsersMainPageState createState() => _UsersMainPageState();
}

class _UsersMainPageState extends State<UsersMainPage> {
  final List<UserProfile> _users = [];
  // ignore: cancel_subscriptions
  StreamSubscription<dynamic>? _querySub;
  // ignore: cancel_subscriptions
  StreamSubscription<dynamic>? _nonVerifiedQuerySub;

  @override
  void deactivate() {
    super.deactivate();
  }

  @override
  void dispose() {
    var sub = _querySub;
    if (sub != null) {
      sub.cancel();
    }

    var nonVerifiedSub = _nonVerifiedQuerySub;
    if (nonVerifiedSub != null) {
      nonVerifiedSub.cancel();
    }

    var controller = searchTextController;
    if (controller != null) {
      controller.removeListener(onSearchTextChanged);
      controller.dispose();
    }
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _createListenerForUsers();
  }

  void _createListenerForUsers() {
    var databaseReference = FirebaseDatabaseHelper.global().databaseReference;

    _nonVerifiedQuerySub = databaseReference
        .collection('nonverifiedusers')
        .snapshots()
        .listen((snapshot) {
      setState(() {
        for (var entry in snapshot.docs) {
          var matchingTask = _users.firstWhereOrNull(
              (element) => element.email.replaceAll(".", "|") == entry.id);
          if (matchingTask == null) {
            var newProfile = UserProfile((entry.id).replaceAll("|", "."));
            _users.add(newProfile);
          }
        }
      });
    });

    _querySub =
        databaseReference.collection('users').snapshots().listen((snapshot) {
      setState(() {
        for (var entry in snapshot.docs) {
          var matchingTask = _users.firstWhereOrNull(
              (element) => element.email.replaceAll(".", "|") == entry.id);
          if (matchingTask != null) {
            matchingTask.updateFromMap(entry.data());
          } else {
            var newProfile = UserProfile((entry.id).replaceAll("|", "."));
            newProfile.updateFromMap(entry.data());
            _users.add(newProfile);
          }
        }
      });
    });
  }

  TextEditingController? searchTextController;
  void onSearchTextChanged() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    TextEditingController? controller = searchTextController;
    if (controller == null) {
      controller = TextEditingController();
      controller.addListener(onSearchTextChanged);
      searchTextController = controller;
    }

    List<Widget> userWidgets = [];

    var userList = _users.sortedBy((element) => element.email).toList();
    var searchText = controller.text;
    if (searchText != "") {
      userList = userList
          .where((element) =>
              element.email.toLowerCase().contains(searchText.toLowerCase()) ||
              (element.role != null &&
                  element.role!
                      .toLowerCase()
                      .contains(searchText.toLowerCase())) ||
              (element.name.getValue() != null &&
                      element.name
                          .getValue()!
                          .toLowerCase()
                          .contains(searchText.toLowerCase()) ||
                  (element.role == null &&
                      "no rules set".contains(searchText.toLowerCase()))))
          .toList();
    }

    for (var user in userList) {
      userWidgets.add(Card(
          margin: const EdgeInsets.all(10),
          color: const Color.fromARGB(255, 41, 45, 52),
          child: ElevatedButton(
              onPressed: () {
                Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => UserEditPage(user)))
                    .then((value) => setState(() {}));
              },
              style: ButtonStyle(
                  backgroundColor: MaterialStateProperty.resolveWith<Color?>(
                (Set<MaterialState> states) {
                  return const Color.fromARGB(
                      255, 41, 45, 52); // Use the component's default.
                },
              )),
              child: Container(
                  margin: const EdgeInsets.all(2),
                  child: Container(
                    margin: const EdgeInsets.fromLTRB(5, 5, 5, 5),
                    constraints: const BoxConstraints(minHeight: 45),
                    child: Column(children: [
                      Text(user.email,
                          style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.white)),
                      Container(height: 5),
                      Text(user.role ?? "No rules set",
                          style: const TextStyle(
                              fontSize: 14, color: Colors.white)),
                    ]),
                  )))));
    }

    return Scaffold(
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        appBar: AppBar(
          title: Text(
            "Users",
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Container(
                margin: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                child: const Text(
                  "Search",
                  style: TextStyle(color: Colors.white, fontSize: 20),
                ),
              ),
              Container(
                margin: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                height: 40,
                child: TextField(
                    autocorrect: false,
                    enableSuggestions: false,
                    keyboardType: TextInputType.text,
                    style: const TextStyle(color: Colors.white),
                    key: const ValueKey('UserSearchTextField'),
                    controller: controller,
                    decoration: const InputDecoration(
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.white, width: 1.0),
                      ),
                      border: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.white, width: 1.0),
                      ),
                    )),
              ),
              Expanded(
                  child: ListView(
                children: userWidgets,
              )),
              Align(
                  alignment: Alignment.centerRight,
                  child: Container(
                      margin: const EdgeInsets.all(10),
                      height: 50,
                      child: ElevatedButton(
                          onPressed: () async {
                            Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) =>
                                            const CreateUserPage()))
                                .then((value) => setState(() {}));
                          },
                          child: const Text("Create New User"))))
            ],
          ),
        ));
  }
}
