﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface.DatabaseHelpers;

namespace CommonDataInterface
{
  public abstract class DataModelCollectionItem : DataModelItem {
    internal String id;

    public String DatabaseId => id;
    
    public String CreatedBy {
      get {
        try {
          var parent = this.Parent as IDataModelCollection;
          var changeEntry = parent.changeLog.entries.FirstOrDefault(a => a.Value == this.id);
          if (changeEntry == null) {
            return "Unknown";
          }

          return changeEntry.UserName;
        }
        catch {
          return "Unknown";
        }
      }
    }
    
    public DateTime CreatedTime {
      get {
        try {
          var parent = this.Parent as IDataModelCollection;
          var changeEntry = parent.changeLog.entries.FirstOrDefault(a => a.Value == this.id);
          if (changeEntry == null)
            return default(DateTime);

          return changeEntry.TimeChanged;
        }
        catch {
          return default(DateTime);
        }
      }
    }

    public String GetId() { 
      return id;
    }

    internal override String GetDBName() {
      return id;
    }


    public DataModelCollectionItem(String id, DataModelItem parent) : base(parent)
    {
      this.id = id;
    }
    
    internal override String GetDBPath() {
      DataModelItem parent = Parent;
      if (parent == null) {
        return GetDBName();
      }

      return parent.GetDBPath() + ".Values." + GetDBName();
    }

    public override void DoAddOneTimeChangesToDictionary(Dictionary<string, object> updates)
    {
      base.DoAddOneTimeChangesToDictionary(updates);

      var documentPath = GetDBPath().Split('.').Skip(2).AggregateEXT((a, b) => a + "." + b);
      updates[documentPath + ".id"] = id;
    }

    public override bool UpdateDirectPropertiesFromMapEntry(KeyValuePair<string, object>? entry) {
      if (entry == null)
        return false;
      if (entry.Value.Key == "Id") {
        this.id = entry.Value.Value as String;
        return true;
      }

      return false;
    }

  }
}