import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/MediaControls/MediaEntry.dart';

class MediaCache {
  static final Map _mediaMap = {};

  static MediaEntry? findEntry(String mediaName, String extension) {
    if (_mediaMap.containsKey(mediaName)) {
      return _mediaMap[mediaName] as MediaEntry;
    }

    return null;
  }

  static MediaEntry getEntry(
      String mediaName, String extension, DataModelItem parent) {
    if (_mediaMap.containsKey(mediaName)) {
      return _mediaMap[mediaName] as MediaEntry;
    }
    MediaEntry newEntry = MediaEntry(mediaName, extension, parent);
    _mediaMap[mediaName] = newEntry;
    return newEntry;
  }

  static MediaEntry? fromMapEntry(
      String mediaName, Map map, DataModelItem parent) {
    if (!map.containsKey("E")) return null;
    var extension = map["E"] as String;
    MediaEntry? entry;
    if (_mediaMap.containsKey(mediaName)) {
      entry = _mediaMap[mediaName] as MediaEntry;
    }
    if (entry == null) {
      entry = MediaEntry(mediaName, extension, parent);
      _mediaMap[mediaName] = entry;
    }

    for (var item in map.entries) {
      entry.updateDirectPropertiesFromMapEntry(item);
    }

    return entry;
  }
}
