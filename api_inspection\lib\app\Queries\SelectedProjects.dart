import 'package:api_inspection/app/APMRoot.dart';
import 'package:api_inspection/app/DataModel/project.dart';
import 'package:api_inspection/generic/AppRoot.dart';
import 'package:api_inspection/generic/UIControls/listener.dart';
import 'package:darq/darq.dart';

class SelectedProjects {
  void dispose() {
    selectedProjectsListener.dispose();
  }

  final List<Project> _selectedProjects = [];
  bool isInitialized = false;
  void initailize() {
    if (isInitialized) {
      return;
    }
    isInitialized = true;

    var selectedProjects =
        AppRoot.global().sharedPreferences.getStringList("SelectedProjects");
    var allProjects = APMRoot.global.queries.projectQueries.projects;
    if (selectedProjects != null) {
      for (var proj in selectedProjects) {
        var match = allProjects.where((value) => value.id == proj);
        if (match.isNotEmpty) {
          if (!_selectedProjects.contains(match.first)) {
            _selectedProjects.add(match.first);
          }
        }
      }
      selectedProjectsListener.notifyListeners();
    }
  }

  ListenerWrapper selectedProjectsListener = ListenerWrapper();

  void addSelectedProject(Project project) {
    _selectedProjects.add(project);
    selectedProjectsListener.notifyListeners();

    AppRoot.global().sharedPreferences.setStringList("SelectedProjects",
        _selectedProjects.select((element, index) => element.id).toList());
  }

  void removeSelectedProject(Project project) {
    _selectedProjects.remove(project);
    selectedProjectsListener.notifyListeners();

    AppRoot.global().sharedPreferences.setStringList("SelectedProjects",
        _selectedProjects.select((element, index) => element.id).toList());
  }

  bool isProjectSelectedEmpty() {
    return _selectedProjects.isEmpty;
  }

  bool isProjectSelected(Project project) {
    return _selectedProjects.contains(project);
  }

  List<Project> getSelectedProjects() {
    return _selectedProjects;
  }
}
