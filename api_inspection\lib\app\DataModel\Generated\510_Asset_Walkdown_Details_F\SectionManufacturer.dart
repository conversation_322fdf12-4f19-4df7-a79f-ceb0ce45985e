//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class SectionManufacturer extends DataModelSection {
  @override
  String getDisplayName() => "Manufacturer";
  SectionManufacturer(DataModelItem? parent)
      : super(parent: parent, sectionName: "Manufacturer");

  // ignore: non_constant_identifier_names
  late StringAttribute attributeName = StringAttribute(
      parent: this,
      displayName: "Name",
      databaseName: "510AW_Q126",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late DateAttribute attributeDate = DateAttribute(
      parent: this,
      displayName: "Date",
      databaseName: "510AW_Q127",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeSerial_Number = StringAttribute(
      parent: this,
      displayName: "Serial Number",
      databaseName: "510AW_Q128",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeNational_Board_Number = StringAttribute(
      parent: this,
      displayName: "National Board Number",
      databaseName: "510AW_Q129",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeRT_Number = StringAttribute(
      parent: this,
      displayName: "RT Number",
      databaseName: "510AW_Q130",
      areCommentsRequired: false);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeName,
      attributeDate,
      attributeSerial_Number,
      attributeNational_Board_Number,
      attributeRT_Number,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionManufacturer";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
