//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionFlexible_Task_Data_F
{
  public class SectionAttachments : DataModelItem {

    public override String DisplayName { 
      get {
        return "Attachments";
      }
    }

    #region [-- Sub Sections --]

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionAttachments";

    public SectionAttachments(DataModelItem parent) : base(parent)
    {
            
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { };
    }
  }
}
