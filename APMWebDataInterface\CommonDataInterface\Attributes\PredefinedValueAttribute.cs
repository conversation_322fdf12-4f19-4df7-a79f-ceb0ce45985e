﻿using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;

namespace CommonDataInterface.Attributes
{
  public class PredefinedValueAttribute : StringAttribute {
    public override String AttributeType => "PredefinedValue";
    
    public bool HasOtherOption;
    List<PredefinedValueOption> _availableOptions;

    public String Unit { get; set; }

    public String SelectedOption {
      get {
        var currrentValue = this.CurrentValue;
        if (String.IsNullOrWhiteSpace(currrentValue))
          return null;
        if (_availableOptions.All(a => !String.Equals(a.Value, currrentValue, StringComparison.InvariantCultureIgnoreCase))) {
          return "Other";
        }

        return currrentValue;
      }
    }

    public List<PredefinedValueOption> Options { get => _availableOptions; }

    public PredefinedValueAttribute(List<PredefinedValueOption> availableOptions, bool hasOtherOption, DataModelItem parent, String displayName, String unit = null, String databaseName= null) : base(parent, displayName, databaseName: databaseName)
    {
      Unit = unit;
      _availableOptions = availableOptions;
      HasOtherOption = hasOtherOption;
    }

  }
}