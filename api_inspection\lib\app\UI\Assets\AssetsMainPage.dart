import 'dart:async';

import 'package:api_inspection/app/APMRoot.dart';
import 'package:api_inspection/app/UI/BusinessUnits/BusinessUnitSelector.dart';
import 'package:api_inspection/generic/AppRoot.dart';
import 'package:api_inspection/generic/AppStyle.dart';
import 'package:api_inspection/generic/DataModelCommon/DatabaseInterfaces/FirebaseDatabaseHelper.dart';
import 'package:api_inspection/generic/MediaControls/MediaDownloadIndicator.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/asset.dart';
import 'package:api_inspection/app/DataModel/assetCard.dart';
import 'package:api_inspection/app/DataModel/project.dart';
import 'package:api_inspection/app/UI/Assets/AssetEditPage.dart';
import 'package:api_inspection/app/UI/Assets/AssetLoadingDialog.dart';
import 'package:api_inspection/app/UI/Assets/SelectAssetPage.dart';
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'package:api_inspection/generic/UIControls/selector.dart';
import 'package:uuid/uuid.dart';
import 'package:darq/darq.dart';
import 'package:collection/collection.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import 'AssetsCardView.dart';

class AssetsMainPage extends StatefulWidget {
  final String title = "Assets";
  const AssetsMainPage({Key? key}) : super(key: key);

  @override
  _AssetsMainPageState createState() => _AssetsMainPageState();
}

class _AssetsMainPageState extends State<AssetsMainPage> {
  String filter = "All Assets";

  TextEditingController searchTextController = TextEditingController();

  List<AssetCard> filteredAssets = [];

  @override
  void deactivate() {
    super.deactivate();
  }

  StreamSubscription<ConnectivityResult>? conectivitySubscription;

  bool isConnected = false;

  bool searchPanelExpanded = false;

  @override
  void initState() {
    super.initState();

    conectivitySubscription = Connectivity()
        .onConnectivityChanged
        .listen((ConnectivityResult result) {
      setState(() {
        isConnected = result != ConnectivityResult.none;
      });
    });
    APMRoot.global.queries.businessUnitQueries.selectedBusinessUnitIdListener
        .addListener(onSelectedBusinessUnitIdChanged);
    APMRoot.global.queries.businessUnitQueries.businessUnitsChangedListener
        .addListener(onBusinessUnitsChanged);
    APMRoot.global.queries.userQuery.userListener.addListener(onUserChanged);
  }

  @override
  void dispose() {
    APMRoot.global.queries.assetCards.assetCardListener
        .removeListener(onAssetsChanged);
    APMRoot.global.queries.projectQueries.projectsListener
        .removeListener(onAssetsChanged);
    searchTextController.removeListener(onSearchTextChanged);
    APMRoot.global.queries.businessUnitQueries.selectedBusinessUnitIdListener
        .removeListener(onSelectedBusinessUnitIdChanged);
    APMRoot.global.queries.businessUnitQueries.businessUnitsChangedListener
        .removeListener(onBusinessUnitsChanged);
    APMRoot.global.queries.userQuery.userListener.removeListener(onUserChanged);

    conectivitySubscription?.cancel();

    super.dispose();
  }

  bool isInit = false;

  void ensureInit() {
    if (isInit) {
      return;
    }

    isInit = true;

    APMRoot.global.queries.assetCards.assetCardListener
        .addListener(onAssetsChanged);
    APMRoot.global.queries.projectQueries.projectsListener
        .addListener(onAssetsChanged);
    searchTextController.addListener(onSearchTextChanged);
    filterAssets();
  }

  void onAssetsChanged() {
    filterAssets();
    setState(() {});
  }

  void onSearchTextChanged() {
    if (simpleSearch) {
      filterAssets();
    }
    setState(() {});
  }

  void onSelectedBusinessUnitIdChanged() {
    assetIdsFromSearch = [];
    searchTerm = "";
    searchTextController.text = "";
    filterAssets();
    setState(() => {});
  }

  void onUserChanged() {
    setState(() {});
  }

  void onBusinessUnitsChanged() {
    setState(() {});
  }

  bool doesAssetMatchFilters(AssetCard asset, List<String> searchText) {
    if (filter == "My Assets") {
      if (!APMRoot.global.newQueries.isAssetUserFavorited(asset)) {
        return false;
      }
    }

    if (asset.businessUnitId == null ||
        asset.businessUnitId != APMRoot.global.selectedBusinessUnitId) {
      return false;
    }

    if (searchText.isEmpty) return true;

    List<String> filteredStrings = [];

    filteredStrings.add(asset.assetCategory);

    var assetName = asset.assetName ?? "No Asset Name";
    var assetId = asset.assetId ?? "No Asset Id";
    var assetType = asset.assetType ?? "Type not set";

    filteredStrings.add(assetName);
    filteredStrings.add(assetId);
    filteredStrings.add(assetType);

    Map<String, bool> searchTextMap = {};
    for (var item in filteredStrings) {
      for (var text in searchText) {
        if (item.toLowerCase().contains(text)) searchTextMap[text] = true;
      }
    }
    if (searchTextMap.length == searchText.length) return true;
    return false;
  }

  InputDecoration getSearchFieldDecoration() {
    return InputDecoration(
      hintText: simpleSearch ? "Local Search" : "Asset Id",
      hintStyle: TextStyle(color: Colors.grey[400], fontSize: 16),
      contentPadding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
      enabledBorder: const OutlineInputBorder(
        borderSide: BorderSide(color: Colors.white, width: 1.0),
      ),
      border: const OutlineInputBorder(
        borderSide: BorderSide(color: Colors.white, width: 1.0),
      ),
    );
  }

  void filterAssets() {
    List<AssetCard> assetList = [];

    var projects =
        APMRoot.global.queries.selectedProjects.getSelectedProjects();
    var locations =
        projects.select((element, index) => element.location).toList();

    var projectAssetIds = projects
        .selectMany((element, index) => element.assetIds.getValue() ?? []);

    var assetCards = locations
        .selectMany((element, index) => element.assetCards.collection)
        .where((element) => projectAssetIds.contains(element.assetDBId))
        .toList();

    var additionalAssets = APMRoot.global.newQueries.assets;
    for (var asset in additionalAssets) {
      var newCard = asset.buildCard();
      newCard.asset = asset;
      assetCards.add(newCard);
    }

    var searchText = searchTextController.text
        .toLowerCase()
        .split(' ')
        .where((element) => element.isNotEmpty)
        .toList();

    var groupedCards = assetCards.groupBy((element) => element.assetDBId);
    for (var group in groupedCards) {
      var card = group.firstOrNull;
      if (card == null) continue;
      if (!doesAssetMatchFilters(card, searchText)) continue;
      assetList.add(card);
    }

    filteredAssets = assetList;
  }

  Widget listviewBuilder(BuildContext context, int id) {
    if (id >= filteredAssets.length) return Container();
    var assetCard = filteredAssets[id];
    return AssetCardView(assetCard, () {
      var asset = assetCard.asset;
      FocusScope.of(context).unfocus();
      if (asset != null) {
        Navigator.push(context,
                MaterialPageRoute(builder: (context) => AssetEditPage(asset)))
            .then((value) => setState(() {}));
      } else {
        APMRoot.global.newQueries.addAssetIdToSession(assetCard.assetDBId);
        showLoadingPromptForAsset(assetCard);
      }
    });
  }

  void showLoadingPromptForAsset(AssetCard card) async {
    await showDialog(
        context: context,
        builder: (BuildContext context) {
          return AssetLoadingDialog(
              assetToLoad: card,
              onAssetLoaded: () {
                Navigator.of(context).pop();
                var asset = card.asset;
                if (asset != null) {
                  Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => AssetEditPage(asset)))
                      .then((value) => setState(() {}));
                }
              },
              onAssetFailedToLoad: () {
                Navigator.of(context).pop();
              });
        });
  }

  bool isSearching = false;

  List<AssetCard> assetIdsFromSearch = [];
  String searchTerm = "";
  void onSearchTextSubmitted(String str) async {
    if (simpleSearch) return;

    if (!isConnected) {
      showErrorPopup(context, "You are not connected to the internet",
          "You must be connected to the internet to use the online search");
      return;
    }

    if (searchTerm == str) {
      var currentAssets = APMRoot.global.newQueries.assets;
      for (var card in assetIdsFromSearch) {
        var matchingAsset = currentAssets
            .firstWhereOrNull((element) => element.id == card.assetId);
        card.asset = matchingAsset;
      }
      filteredAssets = assetIdsFromSearch;
      return;
    }
    assetIdsFromSearch = [];
    searchTerm = str;

    if (!isConnected) return;

    if (str.length <= 2) {
      setState(() {
        filterAssets();
      });
      return;
    }

    setState(() {
      isSearching = true;
    });

    try {
      var databaseReference = FirebaseDatabaseHelper.global().databaseReference;
      str = str.toLowerCase();
      String lower = str;
      String lastChar =
          String.fromCharCode(lower.codeUnitAt(lower.length - 1) + 1);
      String higher = lower.substring(0, lower.length - 1) + lastChar;

      var results510 = (await databaseReference
              .collection('assets')
              .where(
                  "Section510_Asset_Walkdown_Details_F.SectionIdentification.510AW_Q006.Value",
                  isGreaterThanOrEqualTo: str)
              .where(
                  "Section510_Asset_Walkdown_Details_F.SectionIdentification.510AW_Q006.Value",
                  isLessThan: higher)
              .where('BusinessUnitId.Value',
                  isEqualTo: APMRoot.global.selectedBusinessUnitId)
              .get())
          .docs;

      var results570 = (await databaseReference
              .collection('assets')
              .where(
                  "Section570_Asset_Walkdown_Details_F.SectionIdentification.570AW_Q006.Value",
                  isGreaterThanOrEqualTo: str)
              .where(
                  "Section570_Asset_Walkdown_Details_F.SectionIdentification.570AW_Q006.Value",
                  isLessThan: higher)
              .where('BusinessUnitId.Value',
                  isEqualTo: APMRoot.global.selectedBusinessUnitId)
              .get())
          .docs;

      var results653 = (await databaseReference
              .collection('assets')
              .where(
                  "Section653_Asset_Walkdown_Details_F.SectionIdentification.653AW_Q002.Value",
                  isGreaterThanOrEqualTo: str)
              .where(
                  "Section653_Asset_Walkdown_Details_F.SectionIdentification.653AW_Q002.Value",
                  isLessThan: higher)
              .where('BusinessUnitId.Value',
                  isEqualTo: APMRoot.global.selectedBusinessUnitId)
              .get())
          .docs;

      var allResults = results510.concat2(results570, results653);

      List<AssetCard> assetList = [];
      for (var doc in allResults) {
        var newAsset = Asset(
            id: doc.id,
            assetCategory: doc.data()["AssetCategory"] as String,
            locationId: doc.data()["LocationId"] as String);
        newAsset.updateFromMap(doc.data());
        var card = newAsset.buildCard();
        card.asset = null;
        assetList.add(card);
      }
      setState(() {
        filteredAssets = assetList;
      });
    } catch (e) {
      setState(() {
        filteredAssets = [];
      });
    }
    assetIdsFromSearch = filteredAssets;
    setState(() {
      isSearching = false;
    });
  }

  bool simpleSearch = true;

  @override
  Widget build(BuildContext context) {
    ensureInit();

    Widget allAssetsWidget;
    Widget myAssetsWidget;

    if (filter == "All Assets") {
      allAssetsWidget = Row(children: [
        SizedBox(
            width: AppStyle.global.pixels20,
            child: const Icon(
              Icons.arrow_forward,
              size: 16,
            )),
        Text("All Assets",
            style: TextStyle(color: Colors.blue[400], fontSize: 14))
      ]);
      myAssetsWidget = Row(children: [
        Container(width: AppStyle.global.pixels20),
        const Text("My Assets",
            style: TextStyle(color: Colors.white, fontSize: 14))
      ]);
    } else {
      allAssetsWidget = Row(children: [
        Container(width: AppStyle.global.pixels20),
        const Text("All Assets",
            style: TextStyle(color: Colors.white, fontSize: 14))
      ]);
      myAssetsWidget = Row(children: [
        SizedBox(
          width: AppStyle.global.pixels20,
          child: const Icon(
            Icons.arrow_forward,
            size: 16,
          ),
        ),
        Text("My Assets",
            style: TextStyle(color: Colors.blue[400], fontSize: 14))
      ]);
    }

    Widget simpleSearchWidget;
    Widget advancedSearchWidget;
    if (simpleSearch) {
      simpleSearchWidget = Row(children: [
        Text("Local", style: TextStyle(color: Colors.blue[400], fontSize: 14)),
        SizedBox(
            width: AppStyle.global.pixels20,
            child: const Icon(
              Icons.arrow_back,
              size: 16,
            )),
      ]);
      advancedSearchWidget = Row(children: [
        const Text("Online",
            style: TextStyle(color: Colors.white, fontSize: 14)),
        Container(width: AppStyle.global.pixels20),
      ]);
    } else {
      simpleSearchWidget = Row(children: [
        const Text("Local",
            style: TextStyle(color: Colors.white, fontSize: 14)),
        Container(width: AppStyle.global.pixels20),
      ]);
      advancedSearchWidget = Row(children: [
        Text("Online", style: TextStyle(color: Colors.blue[400], fontSize: 14)),
        SizedBox(
          width: AppStyle.global.pixels20,
          child: const Icon(
            Icons.arrow_back,
            size: 16,
          ),
        ),
      ]);
    }

    Widget title;
    if (simpleSearch) {
      title = Text(
        "Assets",
        style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
        overflow: TextOverflow.visible,
      );
    } else {
      Widget connectivityIcon;
      if (isConnected) {
        connectivityIcon = const Icon(Icons.wifi, color: Colors.white);
      } else {
        connectivityIcon = const Icon(Icons.wifi_off, color: Colors.white);
      }
      title = Row(children: [
        Text(
          "Assets",
          style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          overflow: TextOverflow.visible,
        ),
        Container(width: 10),
        connectivityIcon
      ]);
    }

    var user = AppRoot.global().currentUser!;
    var businessUnits = APMRoot.global.queries.businessUnitQueries.businessUnits
        .where((element) => user.effectiveBusinessUnitIds.contains(element.id));

    return Column(
      children: [
        AppBar(
          title: title,
          toolbarHeight: AppStyle.global.toolBarHeight,
          actions: [
            BusinessUnitSelector(businessUnits: businessUnits.toList()),
            const MediaDownloadIndicator(),
            AppRoot.global().buildMenuButtonWidget(context)
          ],
          titleSpacing: 0,
        ),
        Expanded(
            child: GestureDetector(
                onTap: () {
                  FocusScope.of(context).unfocus();
                },
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: <Widget>[
                      Container(
                        height: 10,
                      ),
                      Row(
                        children: [
                          TextButton(
                              onPressed: () {
                                setState(() {
                                  simpleSearch = !simpleSearch;
                                  searchTextController.text = "";
                                  filterAssets();
                                });
                              },
                              child: Container(
                                  margin:
                                      const EdgeInsets.fromLTRB(0, 0, 10, 0),
                                  child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.end,
                                      children: [
                                        simpleSearchWidget,
                                        Container(height: 5),
                                        advancedSearchWidget
                                      ]))),
                          Expanded(
                              child: SizedBox(
                                  height: AppStyle.global.pixels50,
                                  child: TextField(
                                      autocorrect: false,
                                      enableSuggestions: false,
                                      keyboardType: TextInputType.text,
                                      key: const ValueKey("AssetSearchField"),
                                      decoration: getSearchFieldDecoration(),
                                      controller: searchTextController,
                                      onSubmitted: onSearchTextSubmitted,
                                      style: const TextStyle(
                                          color: Colors.white, fontSize: 16)))),
                          TextButton(
                              onPressed: () {
                                setState(() {
                                  if (filter == "All Assets") {
                                    filter = "My Assets";
                                  } else {
                                    filter = "All Assets";
                                  }
                                  filterAssets();
                                });
                              },
                              child: Container(
                                  margin:
                                      const EdgeInsets.fromLTRB(0, 0, 10, 0),
                                  child: Column(children: [
                                    allAssetsWidget,
                                    Container(height: 5),
                                    myAssetsWidget
                                  ])))
                        ],
                      ),
                      Container(
                        height: 4,
                      ),
                      Divider(
                          color: Colors.grey[100], indent: 10, endIndent: 10),
                      isSearching
                          ? Expanded(
                              child: Container(
                                  alignment: Alignment.center,
                                  child: const Text("Searching..",
                                      style: TextStyle(
                                          color: Colors.white, fontSize: 22))))
                          : Expanded(
                              child: ListView.builder(
                                  itemBuilder: listviewBuilder,
                                  itemCount: filteredAssets.length)),
                      Divider(
                          color: Colors.grey[100], indent: 10, endIndent: 10),
                      AttributePadding.WithStdSidePadding(Container(
                        margin: const EdgeInsets.fromLTRB(0, 0, 0, 10),
                        child: ElevatedButton(
                            child: const Text(
                              'Add Asset To Project',
                              style: TextStyle(fontSize: 16),
                            ),
                            onPressed: () async {
                              var project = await selectProject();
                              if (project == null) return;
                              Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                          builder: (context) =>
                                              SelectAssetPage(project)))
                                  .then((value) => setState(() {}));
                            }),
                      )),
                    ],
                  ),
                )))
      ],
    );
  }

  Future<Asset?> createNewAsset() async {
    var selectedProject = await selectProject();
    if (selectedProject == null) return null;

    var assetCategory = await selectAssetCategory();
    var uuid = const Uuid().v4();
    if (assetCategory == null) return null;

    var asset = Asset(
        id: uuid,
        locationId: selectedProject.location.id,
        assetCategory: assetCategory);
    var batch = FirebaseFirestore.instance.batch();
    asset.saveItem(batch);
    await batch.commit();
    APMRoot.global.newQueries.addNewAsset(asset);
    return asset;
  }

  Color selectedColor = const Color.fromARGB(255, 4, 188, 242);
  Color unselectedColor = const Color.fromARGB(255, 122, 122, 122);

  Future<Project?> selectProject() async {
    var root = APMRoot.global;
    var selectedProjects = root.queries.selectedProjects.getSelectedProjects();
    if (selectedProjects.length == 1) return selectedProjects.first;

    Project? selectedProject;

    await showDialog(
        context: context,
        builder: (BuildContext context) {
          List<Widget> options = [
            const Text("Please select a project for new task",
                style: TextStyle(color: Colors.white)),
            SingleChildScrollView(
                child: Selector<Project>(selectedProjects, (project) {
              return project.name.getValue() ?? "Project with no name";
            }, (Project? selectedItem) {
              selectedProject = selectedItem;
            }, buttonColor: Colors.blueGrey[800]!))
          ];

          return AlertDialog(
            backgroundColor: const Color.fromARGB(255, 41, 45, 52),
            title: const Text('Select Project',
                style: TextStyle(color: Colors.white)),
            content: SizedBox(
                height: 300,
                child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: options)),
            actions: [
              ElevatedButton(
                child:
                    const Text('Cancel', style: TextStyle(color: Colors.white)),
                onPressed: () {
                  selectedProject = null;
                  Navigator.of(context).pop();
                },
              ),
              ElevatedButton(
                child:
                    const Text('Select', style: TextStyle(color: Colors.white)),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        });

    return selectedProject;
  }

  Future<String?> selectAssetCategory() async {
    String? selectedCategory;
    await showDialog(
        context: context,
        builder: (BuildContext context) {
          List<Widget> options = [
            const Text("Please select an asset type for new asset",
                style: TextStyle(color: Colors.white)),
            SingleChildScrollView(
                child: Selector<String>(const ["Vessel", "Piping", "Tank"],
                    (item) {
              return item;
            }, (String? selectedItem) {
              selectedCategory = selectedItem;
            }, buttonColor: Colors.blueGrey[800]!))
          ];

          return AlertDialog(
            backgroundColor: const Color.fromARGB(255, 41, 45, 52),
            title: const Text('Select Project',
                style: TextStyle(color: Colors.white)),
            content: SizedBox(
                height: 300,
                child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: options)),
            actions: [
              ElevatedButton(
                child:
                    const Text('Cancel', style: TextStyle(color: Colors.white)),
                onPressed: () {
                  selectedCategory = null;
                  Navigator.of(context).pop();
                },
              ),
              ElevatedButton(
                child:
                    const Text('Select', style: TextStyle(color: Colors.white)),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        });

    return selectedCategory;
  }
}
