//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'SectionIdentification.dart';
import 'SectionGeneralInformation.dart';
import 'SectionOperatingDesignConditions.dart';
import 'SectionComponents.dart';

// ignore: camel_case_types
class Section510_Asset_Walkdown_Details_F extends DataModelSection {
  @override
  String getDisplayName() => "510 Asset Walkdown-Details";
  Section510_Asset_Walkdown_Details_F(DataModelItem? parent)
      : super(parent: parent, sectionName: "510 Asset Walkdown-Details");

// ignore: non_constant_identifier_names
  late SectionIdentification sectionIdentification =
      SectionIdentification(this);
  // ignore: non_constant_identifier_names
  late SectionGeneralInformation sectionGeneralInformation =
      SectionGeneralInformation(this);
  // ignore: non_constant_identifier_names
  late SectionOperatingDesignConditions sectionOperatingDesignConditions =
      SectionOperatingDesignConditions(this);
  // ignore: non_constant_identifier_names
  late SectionComponents sectionComponents = SectionComponents(this);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      sectionIdentification,
      sectionGeneralInformation,
      sectionOperatingDesignConditions,
      sectionComponents,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section510_Asset_Walkdown_Details_F";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
