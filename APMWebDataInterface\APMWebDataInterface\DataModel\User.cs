﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.DataModel
{
 
  public class UserProfile : ConcretePhotoRoot
  {
    public override string DisplayName => "User";

    public String Email;

    private String dbRole;
    public PendingChange<String> PendingRole;

    /// <summary>
    /// technician, admin, or none
    /// </summary>
    public String Role {
      get {
        if (PendingRole != null) {
          return PendingRole.Value;
        }

        return dbRole;
      }
      set {
        if (value != "technician" && value != "admin" && value != "none") {
          throw new Exception("Role must be either 'technician', 'admin', or 'none'");
        }
        PendingRole = new PendingChange<string>{Value = value};
      }
    }



    public MultiStringAttribute BusinessUnitIds;

    public StringAttribute Name { get; private set; }

    internal UserProfile(String email, bool exists = true) : base(email.Replace(".", "|"), null, exists)
    {
      this.Email = email;
      Name = new StringAttribute(parent: this, displayName: "Name");

      InitializeAttributes();
    }
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="email"></param>
    /// <param name="role">technician, admin, or none</param>
    public UserProfile(String email, String role) : base(email.Replace(".", "|"), null)
    {
      Exists = false;
      this.Email = email;
      Name = new StringAttribute(parent: this, displayName: "Name");
      Role = role;

      InitializeAttributes();
    }

    private void InitializeAttributes()
    {
        BusinessUnitIds = new MultiStringAttribute(this, "BusinessUnitIds", null, false);
    }


    internal override String GetDBPath() => "users." + GetDBName();


    public override DataModelItem[] GetChildren()
    {
      return base.GetChildren().Concat(new DataModelItem[]{ Name, BusinessUnitIds }).ToArray();
    }


    internal override String GetDBName() {
      return Email.Replace(".", "|");
    }


    protected override async Task DoAddPendingChangesToDictionary(Dictionary<string, object> updates, string user)
    {
      if (PendingRole != null) {
        updates["role"] = PendingRole.Value;
      }

      await base.DoAddPendingChangesToDictionary(updates, user);
    }

    public override bool GetHasDatabaseChangesPending()
    {
      return (PendingRole != null) || base.GetHasDatabaseChangesPending();
        }


    public override bool UpdateDirectPropertiesFromMapEntry(KeyValuePair<string, object>? entry)
    {
      if (entry == null)
        return false;
      if (entry.Value.Key == "role") {
        dbRole = entry.Value.Value as String;
        return true;
      }

      return base.UpdateDirectPropertiesFromMapEntry(entry);
    }


  }
}
