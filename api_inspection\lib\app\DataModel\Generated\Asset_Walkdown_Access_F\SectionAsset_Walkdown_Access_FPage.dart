//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionAsset_Walkdown_Access_F.dart';
import 'SectionInspectionAccessConditionsPage.dart';
import 'SectionExternalSurfaceConditionsPage.dart';
import 'SectionAccessibilityPage.dart';
import 'SectionInternalAccessConditionsPage.dart';

// ignore: camel_case_types
class SectionAsset_Walkdown_Access_FPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionAsset_Walkdown_Access_F sectionAsset_Walkdown_Access_F;

  const SectionAsset_Walkdown_Access_FPage(this.sectionAsset_Walkdown_Access_F,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionAsset_Walkdown_Access_FPageState();
  }
}

class _SectionAsset_Walkdown_Access_FPageState
    extends State<SectionAsset_Walkdown_Access_FPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionAsset_Walkdown_Access_F,
        title: "Asset Walkdown-Access",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionAsset_Walkdown_Access_F
                            .sectionInspectionAccessConditions,
                        onPressed: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      SectionInspectionAccessConditionsPage(widget
                                          .sectionAsset_Walkdown_Access_F
                                          .sectionInspectionAccessConditions))).then(
                              (value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionAsset_Walkdown_Access_F
                            .sectionExternalSurfaceConditions,
                        onPressed: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      SectionExternalSurfaceConditionsPage(widget
                                          .sectionAsset_Walkdown_Access_F
                                          .sectionExternalSurfaceConditions))).then(
                              (value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionAsset_Walkdown_Access_F
                            .sectionAccessibility,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          SectionAccessibilityPage(widget
                                              .sectionAsset_Walkdown_Access_F
                                              .sectionAccessibility)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionAsset_Walkdown_Access_F
                            .sectionInternalAccessConditions,
                        onPressed: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      SectionInternalAccessConditionsPage(widget
                                          .sectionAsset_Walkdown_Access_F
                                          .sectionInternalAccessConditions))).then(
                              (value) => setState(() {}));
                        }),
                  ),
                ]),
          );
        });
  }
}
