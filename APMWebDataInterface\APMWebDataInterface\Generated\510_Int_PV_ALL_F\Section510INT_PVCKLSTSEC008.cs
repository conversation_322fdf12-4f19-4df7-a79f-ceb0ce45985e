//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Int_PV_ALL_F
{
  public class Section510INT_PVCKLSTSEC008 : DataModelItem {

    public override String DisplayName { 
      get {
        return "HEADS";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC008Q001;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC008Q002;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC008Q003;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC008Q004;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC008Q005;
    public PredefinedValueAttribute attributeIs_the_head_to_shell_attachment_achieved_via_welding;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC008Q007;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC008Q008;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC008Q009;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC008Q010;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC008Q011;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510INT_PVCKLSTSEC008";

    public Section510INT_PVCKLSTSEC008(DataModelItem parent) : base(parent)
    {
            
        attribute510INT_PVCKLSTSEC008Q001 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Erosion", null),
          new PredefinedValueOption("Yes: Pitting", null)
        }, false, this, "Were any corrosion, erosion, or pitting cells noted on the head surfaces:  (The dimensions and locations of any damage shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC008_Q001"); 
     
        attribute510INT_PVCKLSTSEC008Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Cracking", null),
          new PredefinedValueOption("Yes: Mechanical Damage", null)
        }, false, this, "Was any mechanical damage, or cracking noted on the head surfaces:  (The dimensions and location of any damage shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC008_Q002"); 
     
        attribute510INT_PVCKLSTSEC008Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Deformation", null),
          new PredefinedValueOption("Yes: Hot Spots", null)
        }, false, this, "Does the head have any deformations or hot spots: (Bulges, Blisters, Dimpling)", databaseName: "510_INT-PV_CKLST_SEC008_Q003"); 
     
        attribute510INT_PVCKLSTSEC008Q004 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Cracking", null)
        }, false, this, "Were there indications of weld corrosion or cracking in the welds or Heat Affected Zones of the pressure retaining welds:", databaseName: "510_INT-PV_CKLST_SEC008_Q004"); 
     
        attribute510INT_PVCKLSTSEC008Q005 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was any mechanical damage or impacts from objects noted on the heads:", databaseName: "510_INT-PV_CKLST_SEC008_Q005"); 
     
        attributeIs_the_head_to_shell_attachment_achieved_via_welding = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the head to shell attachment achieved via welding:", databaseName: "510_INT-PV_CKLST_SEC008_Q006"); 
     
        attribute510INT_PVCKLSTSEC008Q007 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the head to shell weld in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC008_Q007"); 
     
        attribute510INT_PVCKLSTSEC008Q008 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the head to shell attachment achieved via flanged connection(s):", databaseName: "510_INT-PV_CKLST_SEC008_Q008"); 
     
        attribute510INT_PVCKLSTSEC008Q009 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the head to shell flanged connection(s) in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC008_Q009"); 
     
        attribute510INT_PVCKLSTSEC008Q010 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are the heads of the vessel in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC008_Q010"); 
     
        attribute510INT_PVCKLSTSEC008Q011 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are head penetrations and adjacent areas in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC008_Q011"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute510INT_PVCKLSTSEC008Q001,
           attribute510INT_PVCKLSTSEC008Q002,
           attribute510INT_PVCKLSTSEC008Q003,
           attribute510INT_PVCKLSTSEC008Q004,
           attribute510INT_PVCKLSTSEC008Q005,
           attributeIs_the_head_to_shell_attachment_achieved_via_welding,
           attribute510INT_PVCKLSTSEC008Q007,
           attribute510INT_PVCKLSTSEC008Q008,
           attribute510INT_PVCKLSTSEC008Q009,
           attribute510INT_PVCKLSTSEC008Q010,
           attribute510INT_PVCKLSTSEC008Q011,
        };
    }
  }
}
