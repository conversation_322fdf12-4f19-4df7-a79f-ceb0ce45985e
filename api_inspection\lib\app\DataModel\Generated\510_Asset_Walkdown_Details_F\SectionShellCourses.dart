//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';

// ignore: camel_case_types
class SectionShellCourses extends DataModelCollectionItem {
  @override
  String getDisplayName() => "Shell Courses";

  SectionShellCourses(String id, DataModelItem parent) : super(id, parent);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeNumber = StringAttribute(
      parent: this,
      displayName: "Number",
      databaseName: "510AW_Q406",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late PhotoAttribute attributePhotos = PhotoAttribute(
      parent: this,
      displayName: "Photos",
      databaseName: "510AW_Q413",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeMaterial_Spec_and_Grade = StringAttribute(
      parent: this,
      displayName: "Material Spec and Grade",
      databaseName: "510AW_Q407",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeAllowable_Stress_at_Temperature =
      DoubleAttribute(
    parent: this,
    displayName: "Allowable Stress at Temperature",
    databaseName: "510AW_Q408",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "psi",
  );

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeNominal_Thickness = DoubleAttribute(
    parent: this,
    displayName: "Nominal Thickness",
    databaseName: "510AW_Q409",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "in",
  );

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeCorrosion_Allowance = DoubleAttribute(
    parent: this,
    displayName: "Corrosion Allowance",
    databaseName: "510AW_Q410",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "in",
  );

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeLength_or_Height = DoubleAttribute(
    parent: this,
    displayName: "Length or Height",
    databaseName: "510AW_Q411",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "ft",
  );

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeJoint_Efficiency = DoubleAttribute(
    parent: this,
    displayName: "Joint Efficiency",
    databaseName: "510AW_Q412",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "",
  );

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeNumber,
      attributePhotos,
      attributeMaterial_Spec_and_Grade,
      attributeAllowable_Stress_at_Temperature,
      attributeNominal_Thickness,
      attributeCorrosion_Allowance,
      attributeLength_or_Height,
      attributeJoint_Efficiency,
    ]);
    return children;
  }
}
