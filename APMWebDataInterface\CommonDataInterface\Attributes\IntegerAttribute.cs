﻿using System;
using System.Linq;

namespace CommonDataInterface.Attributes
{
  public class IntegerAttribute : SingleAttributeBase<int?> {
    public override String AttributeType => "Integer";


    public override bool IsValueEqualTo(String other)
    {
      var currentValue = GetValue();
      if (String.IsNullOrWhiteSpace(other)) {
        return currentValue == null;
      }

      if (int.TryParse(other, out var parsed)) {
        return currentValue == parsed;
      }

      throw new Exception("Could not parse as integer: " + other);
    }

    public override void SetGenericValueTo(String other)
    { 
      if (String.IsNullOrWhiteSpace(other)) {
        SetValue(null);
        return;
      }

      if (int.TryParse(other, out var parsed)) {
        SetValue(parsed);
        return;
      }

      throw new Exception("Could not parse as integer: " + other);
    }


    public int? GetValue() {
      if (this.GetValueChangeLog().entries.Count == 0)
        return null;
      return this.GetValueChangeLog().entries.Last().Value;
    }

    public void SetValue(int? value){
      if (GetValue() == value)
        return;

      this.GetValueChangeLog().PendingChange = new PendingChange<int?>{Value = value};

      NotifyListeners();
    }
    
    public bool allowNegatives { get; set; }
    public String unit { get; set; }

    public IntegerAttribute(DataModelItem parent, String displayName, bool allowNegatives, String databaseName = null, bool areCommentsRequired = false, String displayUnit = null)
      : base(parent, displayName, databaseName, areCommentsRequired)
    {
      this.unit = displayUnit;
      GetValueChangeLog().SetConversionMethod(convertDynamicToInt);
    }

    int? convertDynamicToInt(object obj)
    {
      if (int.TryParse(obj?.ToString(), out var result)) {
        return result;
      }

      return null;
    }

    

    
    public override String GetPreviewText(){
      var value = GetValue();
      return value == null ? "" : value.ToString();
    }
      
  }
}