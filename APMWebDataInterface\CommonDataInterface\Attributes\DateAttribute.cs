﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CommonDataInterface.Attributes
{
 
  public class DateAttribute : SingleAttributeBase<String>{
    
    public override String AttributeType => "Date";

    
    public override String CurrentValue
    {
      get {
        return getValue()?.ToString("G");
      }
    }
    
    public DateTime? CurrentValueDateTime
    {
      get {
        return getValue();
      }
    }
    public override String PendingValue
    {
      get {
        return getPendingValue()?.ToString("G");
      }
      set {
        if (DateTime.TryParse(value, out var parsed)) {
          SetValue(parsed);
        }
        else {
          SetValue(null);
        }
      }
    }

    
    public override bool IsValueEqualTo(String other)
    {
      var currentValue = getValue();
      if (String.IsNullOrWhiteSpace(other)) {
        return currentValue == null;
      }
      
      var parsed = parseDateTime(other);
      if (parsed != null) {
        return currentValue == parsed;
      }

      throw new Exception("Could not parse as date: " + other);
    }

    public override void SetGenericValueTo(String other)
    { 
      if (String.IsNullOrWhiteSpace(other)) {
        SetValue(null);
        return;
      }

      var parsed = parseDateTime(other);
      if (parsed != null) {
        SetValue(parsed);
        return;
      }

      throw new Exception("Could not parse as date: " + other);
    }


    public DateAttribute(DataModelItem parent, String displayName, bool areCommentsRequired=false, String databaseName = null)
      :base(parent, displayName, databaseName, areCommentsRequired)
    {

    }

    public DateTime? getValue() {
      if (this.GetValueChangeLog().entries.Count == 0)
        return null;
      var currentValue = GetValueChangeLog().entries.Last().Value;
      if (currentValue == null){
        return null;
      }
      
      return parseDateTime(currentValue);
    }
    

    public DateTime? getPendingValue() {
      if (GetValueChangeLog().PendingChange == null)
        return null;
      var currentValue = GetValueChangeLog().PendingChange?.Value;
      if (currentValue == null)
        return null;
      return parseDateTime(currentValue);
    }


    private DateTime? parseDateTime(string value)
    {
      try {
        if (value == null)
          return null;
        var split = value.Split("-");
        if (split.Length != 3) {
          return parseDateTimeStandard(value);
        }

        var year = int.Parse(split[0]);
        var month = int.Parse(split[1]);
        var day = int.Parse(split[2]);

        return new DateTime(year, month, day);
      }
      catch {

      }

      return parseDateTimeStandard(value);
    }

    private DateTime? parseDateTimeStandard(string value)
    {
      
      if (DateTime.TryParse(value, out var parsedValue))
        return parsedValue;
      return null;
    }

    private bool AreValuesEqual(String? value, DateTime? dateTime)
    {
      if (value == null && dateTime == null) {
        return true;
      }

      if (value == null || dateTime == null) {
        return false;
      }

      var parsedTime = parseDateTime(value);

      return parsedTime == dateTime;
    }

    public virtual void SetValue(DateTime? value)
    {
      var currentValue = getValue();
      if (value == currentValue)
        return;

      if (value == null) {
        this.GetValueChangeLog().PendingChange = new PendingChange<string>{Value = null};
      }
      else {
        this.GetValueChangeLog().PendingChange = new PendingChange<string>{Value = value.Value.ToString("yyyy-M-d")};
      }
          
      NotifyListeners();
    }


    public override string GetPreviewText()
    {
      return getValue()?.ToLongDateString();
    }
  }
}
