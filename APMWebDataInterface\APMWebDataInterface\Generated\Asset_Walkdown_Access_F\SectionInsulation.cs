//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionAsset_Walkdown_Access_F
{
  public class SectionInsulation : DataModelItem {

    public override String DisplayName { 
      get {
        return "Insulation";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributeDoes_the_asset_have_insulation;
    public PredefinedValueAttribute attributePossible_asbestos;
    public MultiPredefinedValueAttribute attributeJacketing_type;
    public MultiPredefinedValueAttribute attributeInsulation_type;
    public PredefinedValueAttribute attributeInsulation_removal_required;
    public MultiPredefinedValueAttribute attributeHeat_tracing;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionInsulation";

    public SectionInsulation(DataModelItem parent) : base(parent)
    {
            
        attributeDoes_the_asset_have_insulation = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Partial", null)
        }, false, this, "Does the asset have insulation?", databaseName: "AWA_Q006"); 
     
        attributePossible_asbestos = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Possible asbestos?", databaseName: "AWA_Q007"); 
     
        attributeJacketing_type = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Aluminum", null),
          new PredefinedValueOption("Stainless", null),
          new PredefinedValueOption("Ferro Sheeting", null),
          new PredefinedValueOption("PVC", null),
          new PredefinedValueOption("None", null)
        }, true, this, "Jacketing type", databaseName: "AWA_Q008"); 
     
        attributeInsulation_type = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Bags/Blankets", null),
          new PredefinedValueOption("Fiber", null),
          new PredefinedValueOption("CalSil", null),
          new PredefinedValueOption("Rock Wool", null)
        }, true, this, "Insulation type", databaseName: "AWA_Q009"); 
     
        attributeInsulation_removal_required = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Insulation removal required?", databaseName: "AWA_Q010"); 
     
        attributeHeat_tracing = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Live Electrical", null),
          new PredefinedValueOption("Inactive Electrical", null),
          new PredefinedValueOption("Live Steam Tubing", null),
          new PredefinedValueOption("Inactive Steam Tubing", null),
          new PredefinedValueOption("None", null)
        }, true, this, "Heat tracing", databaseName: "AWA_Q011"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeDoes_the_asset_have_insulation,
           attributePossible_asbestos,
           attributeJacketing_type,
           attributeInsulation_type,
           attributeInsulation_removal_required,
           attributeHeat_tracing,
        };
    }
  }
}
