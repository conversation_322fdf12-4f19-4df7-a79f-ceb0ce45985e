import 'package:api_inspection/generic/AppStyle.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/activity.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/UIControls/SideSlideoutControl.dart';
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'package:uuid/uuid.dart';

import 'TaskActivityItemPage.dart';

class TaskActivityTrackerPage extends StatefulWidget {
  final DataModelCollection<TaskActivity> activities;

  const TaskActivityTrackerPage(this.activities, {Key? key}) : super(key: key);

  @override
  _TaskActivityTrackerPageState createState() =>
      _TaskActivityTrackerPageState();
}

class _TaskActivityTrackerPageState extends State<TaskActivityTrackerPage> {
  void addNewActivity() {
    var activity = TaskActivity(widget.activities, const Uuid().v4());
    widget.activities.addItem(activity);
    var batch = FirebaseFirestore.instance.batch();
    widget.activities.saveItem(batch);
    batch.commit();
    Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => TaskActivityItemPage(activity)))
        .then((value) => {setState(() {})});
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> activityButtons = [];
    for (var item in widget.activities.getEntries()) {
      Widget interior = Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            margin: const EdgeInsets.all(20),
            child: Text(
              item.date.getPreviewText(),
              style: const TextStyle(color: Colors.white, fontSize: 18),
            ),
          ),
          Container(
            margin: const EdgeInsets.fromLTRB(0, 0, 20, 0),
            child: Text(item.getTotalHours().toString() + " hours",
                style: const TextStyle(color: Colors.white, fontSize: 18)),
          )
        ],
      );

      leftSliderWidgetBuilder(context) {
        return SizedBox(
            height: 110,
            width: 110,
            child: ElevatedButton(
                onPressed: () {
                  setState(() {
                    widget.activities.removeItem(item);
                  });
                },
                child: const Text(
                  "Remove",
                  style: TextStyle(fontSize: 18),
                ),
                style: ButtonStyle(
                  backgroundColor: MaterialStateProperty.resolveWith<Color>(
                      (Set<MaterialState> states) {
                    return Colors.red[900]!;
                  }),
                )));
      }

      activityButtons.add(SideSlideoutControl(
          leftSliderWidget: leftSliderWidgetBuilder,
          child: AttributePadding.WithStdPadding(
            TeamToggleButton(
                child: interior,
                onPressed: () {
                  Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => TaskActivityItemPage(item)))
                      .then((value) => {setState(() {})});
                }),
          )));
    }

    return Scaffold(
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        appBar: AppBar(
          title: Text(
            "Task Activity Tracker",
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Expanded(child: ListView(children: activityButtons)),
              AttributePadding.WithStdPadding(Container(
                margin: const EdgeInsets.fromLTRB(0, 0, 0, 10),
                child: ElevatedButton(
                    child: const Text(
                      'Add Day',
                      style: TextStyle(fontSize: 18),
                    ),
                    onPressed: addNewActivity),
              )),
            ],
          ),
        ));
  }
}
