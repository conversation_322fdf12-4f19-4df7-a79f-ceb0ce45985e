import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/ConcretePhotoRoot.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';

class AccountRelationship extends DataModelCollectionItem {
  @override
  String getDisplayName() => "Account Relationship";

  AccountRelationship(DataModelItem parent, String id) : super(id, parent);

  late StringAttribute topLevelAccountId =
      StringAttribute(parent: this, displayName: "Top Level Account Id");
  late StringAttribute bottomLevelAccountId =
      StringAttribute(parent: this, displayName: "Bottom Level Account Id");

  late PredefinedValueAttribute relationshipDescriptor =
      PredefinedValueAttribute(
    hasOtherOption: false,
    parent: this,
    displayName: "Relationship",
    availableOptions: [
      PredefinedValueOption("Owns", null),
      PredefinedValueOption("Manages", null),
      PredefinedValueOption("Does work for", null)
    ],
  );

  @override
  // ignore: must_call_super
  List<DataModelItem> getChildren() {
    // ignore: todo
    // TODO: implement getChildren
    throw UnimplementedError();
  }
}

class Account extends ConcretePhotoRoot {
  @override
  String getDisplayName() => "Account";

  @override
  String getDBPath() => "accounts." + getDBName();

  Account(String id) : super(id);

  late StringAttribute name =
      StringAttribute(parent: this, displayName: "Account Name");

  late PredefinedValueAttribute type = PredefinedValueAttribute(
      hasOtherOption: false,
      parent: this,
      displayName: "Account Type",
      availableOptions: [
        PredefinedValueOption("District", null),
        PredefinedValueOption("Facility", null),
        PredefinedValueOption("Organization", null)
      ]);

  late DataModelCollection subAccounts =
      DataModelCollection("Sub Accounts", (parent, entry) {
    return AccountRelationship(subAccounts, entry.key);
  }, this);

  late DataModelCollection parentAccounts =
      DataModelCollection("Sub Accounts", (parent, entry) {
    return AccountRelationship(parentAccounts, entry.key);
  }, this);

  @override
  List<DataModelItem> getChildren() {
    var parentChildren = super.getChildren().toList();
    parentChildren.addAll([name, type, subAccounts, parentAccounts]);
    return parentChildren;
  }
}
