﻿using CheckListGen;

namespace ExcelCodeGenerator.CodeGenerators.CSharp
{
    public class PhoneNumberAttributeGenerator : IAttributeGenerator
    {
        public string BuildInitialization(Question question)
        {
            return
                @"
        " + question.DartVariableName + @" = new PhoneNumberAttribute(this, displayName: """ + question.DisplayText +
                @""", databaseName: """ + question.DataName + @"""); ";
        }

        public string BuildDeclaration(Question question)
        {
            return
                @"public PhoneNumberAttribute " + question.DartVariableName + ";";
        }
    }
}