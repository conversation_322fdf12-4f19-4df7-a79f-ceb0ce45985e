import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/location.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiStringAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/ConcretePhotoRoot.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';

import 'activity.dart';

class Client extends ConcretePhotoRoot {
  @override
  String getDBPath() => "clients." + getDBName();

  @override
  String getDisplayName() => "Client";

  Client(String id) : super(id);

  late StringAttribute name = StringAttribute(
      parent: this,
      displayName: "Name",
      iconWidget: const Icon(Icons.create_outlined, color: Colors.white));

  late DataModelCollection<Contact> contacts =
      DataModelCollection<Contact>("Contacts", (parent, entry) {
    return Contact(entry.key, contacts);
  }, this);

  @override
  List<DataModelItem> getChildren() {
    var parentChildren = super.getChildren().toList();
    parentChildren.addAll([name, contacts]);
    return parentChildren;
  }
}

class Contact extends DataModelCollectionItem {
  @override
  String getDisplayName() => "Contact:" + name.getPreviewText();

  late StringAttribute name = StringAttribute(
      parent: this,
      displayName: "Contact",
      iconWidget: const Icon(Icons.create_outlined, color: Colors.white));
  late StringAttribute title = StringAttribute(
      parent: this,
      displayName: "Title",
      iconWidget: const Icon(Icons.assignment_ind, color: Colors.white));
  late PhoneNumberAttribute phoneNumber = PhoneNumberAttribute(
      parent: this,
      displayName: "Phone Number",
      iconWidget: const Icon(Icons.phone, color: Colors.white));
  late StringAttribute email = StringAttribute(
      parent: this,
      displayName: "Email",
      iconWidget: const Icon(Icons.assignment_ind, color: Colors.white));

  Contact(String id, DataModelItem parent) : super(id, parent);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([name, title, phoneNumber, email]);
    return children;
  }
}

class ProjectClientDetails extends DataModelItem {
  ProjectClientDetails(DataModelItem? parent) : super(parent);

  late DataModelCollection<Contact> contacts =
      DataModelCollection<Contact>("Contacts", (parent, entry) {
    return Contact(entry.key, contacts);
  }, this);

  @override
  List<DataModelItem> getChildren() {
    return [contacts];
  }

  @override
  String getDisplayName() => "ClientDetails";

  @override
  String getDBName() {
    return "ClientDetails";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}

class ProjectAccountingDetails extends DataModelItem {
  ProjectAccountingDetails(DataModelItem? parent) : super(parent);

  late StringAttribute apmProjectNumber = StringAttribute(
      parent: this, displayName: "APM Project Number", isQueryable: true);
  late StringAttribute teamDistrictNumber =
      StringAttribute(parent: this, displayName: "TEAM District Number");
  late StringAttribute teamProjectNumber =
      StringAttribute(parent: this, displayName: "TEAM Project Number");
  late StringAttribute purchaseOrderAFE =
      StringAttribute(parent: this, displayName: "Purchase Order/AFE");
  late StringAttribute workOrderNumber =
      StringAttribute(parent: this, displayName: "Work Order Number");
  late StringAttribute projectNumber =
      StringAttribute(parent: this, displayName: "Project Number");

  @override
  List<DataModelItem> getChildren() {
    return [
      apmProjectNumber,
      teamDistrictNumber,
      teamProjectNumber,
      purchaseOrderAFE,
      workOrderNumber,
      projectNumber,
    ];
  }

  @override
  String getDBName() {
    return "AccountingDetails";
  }

  @override
  String getDisplayName() => "AccountingDetails";

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}

class Project extends ConcretePhotoRoot {
  Project(String id, String locationId) : super(id) {
    location = LocationCache.findEntry(locationId);
  }

  late MultiStringAttribute assetIds = MultiStringAttribute(this, "AssetIds");

  late Location location;

  @override
  String getDisplayName() => "Project: " + name.getDisplayName();

  late StringAttribute name =
      StringAttribute(parent: this, displayName: "Name");
  late StringAttribute businessUnitId = StringAttribute(
      parent: this, displayName: "BusinessUnitId", isQueryable: true);
  late StringAttribute description =
      StringAttribute(parent: this, displayName: "Project Description");
  late ProjectAccountingDetails accountingDetails =
      ProjectAccountingDetails(this);
  late ProjectClientDetails clientDetails = ProjectClientDetails(this);

  late DataModelCollection<ProjectActivity> activities =
      DataModelCollection<ProjectActivity>("ProjectActivities",
          (parent, entry) {
    return ProjectActivity(activities, entry.key);
  }, this);

  @override
  String getDBPath() => "projects." + getDBName();

  @override
  List<DataModelItem> getChildren() {
    var parentChildren = super.getChildren().toList();
    parentChildren.addAll([
      name,
      businessUnitId,
      description,
      accountingDetails,
      clientDetails,
      assetIds,
      activities
    ]);
    return parentChildren;
  }
}
