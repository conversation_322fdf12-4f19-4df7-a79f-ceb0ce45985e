import 'package:api_inspection/app/APMRoot.dart';
import 'package:api_inspection/generic/AppStyle.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/asset.dart';
import 'package:api_inspection/app/DataModel/assetCard.dart';
import 'package:api_inspection/app/DataModel/project.dart';
import 'package:api_inspection/app/UI/Assets/AssetEditPage.dart';
import 'package:api_inspection/app/UI/Assets/AssetsCardView.dart';
import 'package:api_inspection/generic/UIControls/selector.dart';
import 'package:darq/darq.dart';
import 'package:uuid/uuid.dart';

class SelectAssetPage extends StatefulWidget {
  final Project project;
  const SelectAssetPage(this.project, {Key? key}) : super(key: key);

  @override
  _SelectAssetPageState createState() => _SelectAssetPageState();
}

class _SelectAssetPageState extends State<SelectAssetPage> {
  TextEditingController searchTextController = TextEditingController();

  void onSearchTextChanged() {
    setState(() {});
  }

  bool doesAssetMatchFilters(AssetCard card) {
    var searchText = searchTextController.text.toLowerCase();
    if (searchText.isEmpty) return true;

    List<String> filteredStrings = [];

    filteredStrings.add(card.assetCategory);

    var assetName = card.assetName ?? "No Asset Name";
    var assetId = card.assetId ?? "No Asset Id";
    var assetType = card.assetType ?? "Type not set";

    filteredStrings.add(assetName);
    filteredStrings.add(assetId);
    filteredStrings.add(assetType);

    for (var item in filteredStrings) {
      if (item.toLowerCase().contains(searchText)) return true;
    }

    return false;
  }

  void createNewAsset() async {
    var project = widget.project;

    var assetCategory = await selectAssetCategory();
    var uuid = const Uuid().v4();
    if (assetCategory == null) return null;

    var asset = Asset(
        id: uuid,
        locationId: project.location.id,
        assetCategory: assetCategory);
    var batch = FirebaseFirestore.instance.batch();
    asset.businessUnitId.setValue(APMRoot.global.selectedBusinessUnitId, batch);

    asset.saveItem(batch);
    asset.updateDBCard(batch);
    project.assetIds.addValue(asset.id);
    project.saveItem(batch);
    asset.location.saveItem(batch);
    await batch.commit();
    APMRoot.global.newQueries.addNewAsset(asset);

    APMRoot.global.newQueries.assetListener.notifyListeners();

    Navigator.of(context).pop();
    Navigator.push(
        context, MaterialPageRoute(builder: (context) => AssetEditPage(asset)));
  }

  void selectAssetClicked() {
    var asset = selectedAsset;
    if (asset == null) {
      showErrorPrompt("Please select an asset first");
      return;
    }

    var batch = FirebaseFirestore.instance.batch();
    widget.project.assetIds.addValue(asset.assetDBId);
    widget.project.saveItem(batch);
    batch.commit();

    APMRoot.global.newQueries.assetListener.notifyListeners();

    Navigator.of(context).pop();
  }

  Future<String?> selectAssetCategory() async {
    String? selectedCategory;
    await showDialog(
        context: context,
        builder: (BuildContext context) {
          List<Widget> options = [
            const Text("Please select an asset type for new asset",
                style: TextStyle(color: Colors.white)),
            SingleChildScrollView(
                child: Selector<String>(const ["Vessel", "Piping", "Tank"],
                    (item) {
              return item;
            }, (String? selectedItem) {
              selectedCategory = selectedItem;
            }, buttonColor: Colors.blueGrey[800]!))
          ];

          return AlertDialog(
            backgroundColor: const Color.fromARGB(255, 41, 45, 52),
            title: const Text('Select Project',
                style: TextStyle(color: Colors.white)),
            content: SizedBox(
                height: 300,
                child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: options)),
            actions: [
              ElevatedButton(
                child:
                    const Text('Cancel', style: TextStyle(color: Colors.white)),
                onPressed: () {
                  selectedCategory = null;
                  Navigator.of(context).pop();
                },
              ),
              ElevatedButton(
                child:
                    const Text('Select', style: TextStyle(color: Colors.white)),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        });

    return selectedCategory;
  }

  void showErrorPrompt(String message) {
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: const Color.fromARGB(255, 41, 45, 52),
            title: Row(
              children: [
                Container(
                    margin: const EdgeInsets.all(5),
                    child:
                        const Icon(Icons.error, color: Colors.red, size: 32)),
                const Text(
                  'Error',
                  style: TextStyle(color: Colors.white),
                )
              ],
            ),
            content: Text(message, style: const TextStyle(color: Colors.white)),
            actions: [
              ElevatedButton(
                child: const Text('Ok', style: TextStyle(color: Colors.white)),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        });
  }

  var textFieldDecoration = const InputDecoration(
    enabledBorder: OutlineInputBorder(
      borderSide: BorderSide(color: Colors.white, width: 1.0),
    ),
    border: OutlineInputBorder(
      borderSide: BorderSide(color: Colors.white, width: 1.0),
    ),
  );

  String? selectedCategory;
  AssetCard? selectedAsset;

  @override
  void dispose() {
    searchTextController.removeListener(onSearchTextChanged);
    super.dispose();
  }

  @override
  void initState() {
    searchTextController.addListener(onSearchTextChanged);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var projects =
        APMRoot.global.queries.selectedProjects.getSelectedProjects();
    var locations =
        projects.select((element, index) => element.location).toList();
    var assetCards =
        locations.selectMany((element, index) => element.assetCards.collection);

    var selectedProjectAssets = projects.selectMany((element, index) =>
        element.assetIds.getValue() ?? List<String>.empty());

    var filteredCards = assetCards
        .where((element) => !selectedProjectAssets.contains(element.assetDBId))
        .toList();

    List<Widget> availableAssetWidgets = [];

    for (var card in filteredCards) {
      if (!doesAssetMatchFilters(card)) continue;

      var color = card == selectedAsset
          ? Colors.blueGrey[800]!
          : const Color.fromARGB(255, 41, 45, 52);
      availableAssetWidgets.add(AssetCardView(card, () {
        setState(() {
          FocusScope.of(context).unfocus();
          if (selectedAsset == card) {
            selectedAsset = null;
          } else {
            selectedAsset = card;
          }
        });
      }, color: color, showGPS: false));
    }

    Color selectAssetColor =
        selectedAsset == null ? Colors.blueGrey[600]! : Colors.blue;

    return Scaffold(
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        appBar: AppBar(
          title: Text(
            'Asset Selection Page',
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        body: SizedBox(
          child: Center(
              child: GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                Container(
                  height: 10,
                ),
                Row(
                  children: [
                    const Icon(
                      Icons.search,
                      color: Colors.white,
                    ),
                    Expanded(
                        child: SizedBox(
                            height: 50,
                            child: TextField(
                                autocorrect: false,
                                enableSuggestions: false,
                                keyboardType: TextInputType.text,
                                key:
                                    const ValueKey("AssetSelectionSearchField"),
                                decoration: textFieldDecoration,
                                controller: searchTextController,
                                style: const TextStyle(color: Colors.white))))
                  ],
                ),
                Container(
                  height: 4,
                ),
                Divider(color: Colors.grey[100], indent: 10, endIndent: 10),
                Expanded(child: ListView(children: availableAssetWidgets)),
                Container(
                  margin: const EdgeInsets.all(10),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      ElevatedButton(
                          onPressed: selectAssetClicked,
                          child: const Text(
                            "Select Asset",
                            style: TextStyle(fontSize: 18),
                          ),
                          style: ButtonStyle(backgroundColor:
                              MaterialStateProperty.resolveWith<Color?>(
                            (Set<MaterialState> states) {
                              return selectAssetColor;
                            },
                          ))),
                      ElevatedButton(
                          onPressed: createNewAsset,
                          child: const Text("Create Asset",
                              style: TextStyle(fontSize: 18))),
                    ],
                  ),
                )
              ],
            ),
          )),
        ));
  }
}
