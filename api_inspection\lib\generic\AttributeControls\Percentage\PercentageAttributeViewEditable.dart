import 'package:api_inspection/app/batch_helper.dart';
import 'package:api_inspection/generic/Common/Types/Percentage.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/PercentageAttribute.dart';
import 'package:api_inspection/generic/UIControls/NumericTextField.dart';
import 'package:api_inspection/generic/UIControls/listener.dart';
import 'package:flutter/material.dart';

class PercentageAttributeViewEditable extends StatefulWidget {
  final PercentageAttribute _attribute;
  final ListenerWrapper updateListener;

  const PercentageAttributeViewEditable(this._attribute, this.updateListener,
      {Key? key})
      : super(key: key);

  @override
  _PercentageAttributeViewEditableState createState() =>
      _PercentageAttributeViewEditableState();
}

class _PercentageAttributeViewEditableState
    extends State<PercentageAttributeViewEditable> {
  void updateAttributeValue() {
    TextEditingController? controller = _controller;

    if (controller != null) {
      double? parsedValue = double.tryParse(controller.text);
      widget._attribute.setValue(
          parsedValue == null ? null : Percentage.from0To100(parsedValue));

      BatchHelper.saveAndCommit(widget._attribute);
    }
  }

  TextEditingController? _controller;

  bool initialized = false;
  void initialize() {
    if (initialized) return;
    initialized = true;
    PercentageAttribute attribute = widget._attribute;
    attribute.addListener(onAttributeChanged);
  }

  bool areValuesEqual(String? str, double? value) {
    if (str == null && value == null) return true;
    if (str != null && str.isEmpty && value == null) return true;
    if (str == null || value == null) return false;

    double? strAsValue = double.tryParse(str);
    return strAsValue == value;
  }

  String doubleToString(double? value) {
    if (value == null) {
      return "";
    }
    return value.toString();
  }

  void onAttributeChanged() {
    setState(() {
      TextEditingController? controller = _controller;
      double? attributeValue = widget._attribute.getValue()?.value0To100;
      if (controller != null &&
          !areValuesEqual(controller.text, attributeValue)) {
        controller.text =
            attributeValue == null ? "" : doubleToString(attributeValue);
      }
    });
  }

  @override
  void dispose() {
    widget.updateListener.removeListener(updateAttributeValue);
    PercentageAttribute attribute = widget._attribute;
    attribute.removeListener(onAttributeChanged);
    _controller?.dispose();
    super.dispose();
  }

  @override
  void initState() {
    widget.updateListener.addListener(updateAttributeValue);
    super.initState();
  }

  @override
  void didUpdateWidget(covariant PercentageAttributeViewEditable oldWidget) {
    oldWidget.updateListener.removeListener(updateAttributeValue);
    widget.updateListener.addListener(updateAttributeValue);
    super.didUpdateWidget(oldWidget);
  }

  Widget buildAttributeField() {
    var textField = NumericTextField(
        _controller!, widget._attribute.allowNegatives, true, (String? value) {
      updateAttributeValue();
    });
    return Row(children: [
      Expanded(child: textField),
      Container(
          margin: const EdgeInsets.fromLTRB(10, 0, 0, 0),
          child: const Text(
            "%",
            style: TextStyle(color: Colors.white, fontSize: 18),
            textAlign: TextAlign.start,
          ))
    ]);
  }

  @override
  Widget build(BuildContext context) {
    initialize();

    _controller ??= TextEditingController(
        text: doubleToString(widget._attribute.getValue()?.value0To100));

    return Container(
        margin: const EdgeInsets.fromLTRB(20, 10, 20, 10),
        child:
            Column(crossAxisAlignment: CrossAxisAlignment.stretch, children: [
          Text(
            widget._attribute.displayName,
            style: const TextStyle(color: Colors.white, fontSize: 16),
            textAlign: TextAlign.start,
          ),
          buildAttributeField()
        ]));
  }
}
