//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class SectionTankOutOfServiceRequirements extends DataModelSection {
  @override
  String getDisplayName() => "Tank Out-Of-Service Requirements";
  SectionTankOutOfServiceRequirements(DataModelItem? parent)
      : super(parent: parent, sectionName: "Tank Out-Of-Service Requirements");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute653AWQ331 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the tank Out-Of-Service In accordance with Jurisdictional requirements?",
          databaseName: "653AW_Q331",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("N/A", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attribute653AWQ331,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionTankOutOfServiceRequirements";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
