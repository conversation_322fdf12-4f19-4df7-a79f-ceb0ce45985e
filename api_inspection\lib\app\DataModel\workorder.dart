import 'package:api_inspection/generic/UIControls/listener.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:api_inspection/app/DataModel/task.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/ConcretePhotoRoot.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/DataModelCommon/DatabaseInterfaces/FirebaseDatabaseHelper.dart';

import 'asset.dart';

class WorkOrder extends ConcretePhotoRoot {
  @override
  String getDBPath() => "workorders." + getDBName();
  Asset asset;
  String projectId;

  @override
  String getDisplayName() => "WorkOrder";

  WorkOrder(String id, this.asset, this.projectId) : super(id);

  late LocationAttribute gisLocation =
      LocationAttribute(parent: this, displayName: "GIS Location");

  late StringAttribute facilityName =
      StringAttribute(parent: this, displayName: "Facility Name");

  late DateAttribute dueDate =
      DateAttribute(parent: this, displayName: "Due Date");

  late DateAttribute plannedStart =
      DateAttribute(parent: this, displayName: "Planned Start");

  late DateAttribute plannedEnd =
      DateAttribute(parent: this, displayName: "Planned End");

  late StringAttribute primaryContactName =
      StringAttribute(parent: this, displayName: "Primary Contact Name");

  late PhoneNumberAttribute primaryContactPhone =
      PhoneNumberAttribute(parent: this, displayName: "Primary Contact Phone");

  late StringAttribute businessUnitId = StringAttribute(
      parent: this, displayName: "BusinessUnitId", isQueryable: true);

  ListenerWrapper tasksListener = ListenerWrapper();

  late List<Task> _tasks = [];
  List<Task> get tasks {
    return _tasks;
  }

  set tasks(List<Task> newList) {
    _tasks = newList;
    tasksListener.notifyListeners();
  }
  // late DataModelCollection<Task> tasks = DataModelCollection<Task>("Tasks", (parent, entry){
  //   var taskType = (entry.value as Map)["TaskType"];
  //   if (taskType is String){
  //    return Task(this, tasks, entry.key, taskType);
  //   }
  //   return null;
  // }, this);

  @override
  List<DataModelItem> getChildren() {
    var parentChildren = super.getChildren().toList();
    parentChildren.addAll([
      //tasks,
      gisLocation, facilityName, dueDate, plannedStart, plannedEnd,
      primaryContactName, primaryContactPhone, businessUnitId
    ]);
    return parentChildren;
  }

  List<String> assignedUsers = [];

  @override
  void updateDirectPropertiesFromMap(Map map) {
    dynamic assignedUsersFromMap =
        map.containsKey("AssignedUsers") ? map["AssignedUsers"] : null;

    if (assignedUsersFromMap is List<dynamic>) {
      List<String> users = [];
      for (var item in assignedUsersFromMap) {
        users.add(item.toString());
      }

      assignedUsers = users;
    }
    if (assignedUsersFromMap == null) {
      assignedUsers = [];
    }

    notifyListeners();
  }

  @override
  void saveDirectItems(WriteBatch batch) async {
    FirebaseDatabaseHelper helper = FirebaseDatabaseHelper.global();
    helper.updateProperties(
        getDBPath(),
        {
          "ProjectId": projectId,
          "AssetId": asset.id,
          "AssignedUsers": assignedUsers
        },
        batch);

    if (!asset.workOrdersContainedIn.contains(id)) {
      helper.updateItem(asset.getDBPath() + ".WorkOrderIds",
          FieldValue.arrayUnion([id]), batch);
    }
    if (!asset.projectsContainedIn.contains(projectId)) {
      helper.updateItem(asset.getDBPath() + ".ProjectIds",
          FieldValue.arrayUnion([projectId]), batch);
    }
  }
}
