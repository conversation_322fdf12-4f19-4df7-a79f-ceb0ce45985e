//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionLeakReport.dart';

// ignore: camel_case_types
class SectionLeakReportPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionLeakReport sectionLeakReport;

  const SectionLeakReportPage(this.sectionLeakReport, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionLeakReportPageState();
  }
}

class _SectionLeakReportPageState extends State<SectionLeakReportPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionLeakReport,
        title: "Leak Report",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionLeakReport.attributeEquipment_ID
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionLeakReport.attributeEquipment_Description
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionLeakReport.attributeEquipment_ID_at_line_START
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionLeakReport.attributeEquipment_ID_at_line_END
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionLeakReport.attributePipe_Size
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionLeakReport.attributePipe_Schedule
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionLeakReport.attributeProcessService
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionLeakReport.attributePipe_Cover
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionLeakReport.attributeAffected_Length
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionLeakReport.attributeDistance_between_tie_in_points
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionLeakReport.attributeCorrosion_Type
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionLeakReport.attributeEstimated_Loss_Rate
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionLeakReport.attributeExisting_clamp_count_same_line
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionLeakReport.attributeFeatureFitting_count_same_line
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionLeakReport.attributeObservation_Summary
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
