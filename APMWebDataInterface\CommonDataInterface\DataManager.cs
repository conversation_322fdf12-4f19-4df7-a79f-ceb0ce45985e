﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Firebase.Auth;
using Google.Cloud.Firestore;
using Google.Cloud.Firestore.V1;
using Grpc.Core;

namespace CommonDataInterface
{

  internal interface IDatabaseContextManager
  {
    Task ContextForNewListener(Action<FirestoreDb> action);
    Task<T> ContextForNonListenerAction<T>(Func<FirestoreDb, Task<T>> action);
    Task<T> ContextForNonListenerAction<T>(Func<FirestoreDb, T> action);
  }

  internal class DataManager
  {
    internal static IDatabaseContextManager ContextManager;
  }
}
