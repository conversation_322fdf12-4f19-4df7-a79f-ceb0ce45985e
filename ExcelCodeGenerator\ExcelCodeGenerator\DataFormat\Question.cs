﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace CheckListGen
{
  public class Question : IChildItem
  {
    public String DisplayText;
    public String DataName;
    public String DartVariableName {
      get {
        if (DisplayText.Length > 60) {
          return "attribute" + Helpers.CleanupVariableName(DataName);
        }
        return "attribute" + Helpers.CleanupVariableName(new string(DisplayText.Take(60).ToArray()));
      }
    }

    public List<String> References = new List<string>();

    public String Unit;

    private String _dataType;
    public String DataType {
      get => _dataType;
      set => _dataType = value?.Trim();
    }

    public bool Required;
    public double Order { get; set; }
    public String EntryMethod;

    public enum AttributeTypes { PhoneNumber, String, PredefinedValue, PredefinedValueWithOther, MultiPredefinedValue, MultiPredefinedValueWithOther, Boolean, Date, Decimal, Integer, PhotoCollection, Coordinate };

    public AttributeTypes AttributeType {
      get {
        if (DataType.Equals("phonenumber", StringComparison.InvariantCultureIgnoreCase))
          return AttributeTypes.PhoneNumber;
        if (DataType.Equals("coordinate", StringComparison.InvariantCultureIgnoreCase)) 
          return AttributeTypes.Coordinate;
        if (DataType.Equals("photo", StringComparison.InvariantCultureIgnoreCase) || DataType.Equals("photocollection", StringComparison.InvariantCultureIgnoreCase))
          return AttributeTypes.PhotoCollection;
        if (DataType.Equals("singleselect", StringComparison.InvariantCultureIgnoreCase)) 
          return AttributeTypes.PredefinedValue;
        if (DataType.Equals("singleselectwithother", StringComparison.InvariantCultureIgnoreCase)) 
          return AttributeTypes.PredefinedValueWithOther;
        if (DataType.Equals("multiselect", StringComparison.InvariantCultureIgnoreCase))
          return AttributeTypes.MultiPredefinedValue;
        if (DataType.Equals("multiselectwithother", StringComparison.InvariantCultureIgnoreCase))
          return AttributeTypes.MultiPredefinedValueWithOther;

        if (DataType.Equals("drop", StringComparison.InvariantCultureIgnoreCase) || DataType.Equals("text", StringComparison.InvariantCultureIgnoreCase)) {
          if (Choices.Any()) {
            return AttributeTypes.PredefinedValue;
          }

          return AttributeTypes.String;
        }
        if (DataType.Equals("Boolean", StringComparison.InvariantCultureIgnoreCase) || DataType.Equals("Bool", StringComparison.InvariantCultureIgnoreCase)) {
          return AttributeTypes.Boolean;
        }

        if (DataType.Equals("date", StringComparison.InvariantCultureIgnoreCase))
          return AttributeTypes.Date;
        if (DataType.Equals("decimal", StringComparison.InvariantCultureIgnoreCase))
          return AttributeTypes.Decimal;
        if (DataType.Equals("integer", StringComparison.InvariantCultureIgnoreCase))
          return AttributeTypes.Integer;
        if (DataType.Equals("int", StringComparison.InvariantCultureIgnoreCase))
          return AttributeTypes.Integer;
        throw new NotSupportedException("Could not determine attribute type: " + DataName);
      }
    }

    public List<String> Decorators = new List<string>();

    public String DevComments;
    public bool ForceComment => DevComments != null && DevComments.Contains("[RC]");

    public List<Choice> Choices = new List<Choice>();

  }
}