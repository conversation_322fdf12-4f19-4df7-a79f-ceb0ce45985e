//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Int_PV_ALL_F
{
  public class Section510INT_PVCKLSTSEC015 : DataModelItem {

    public override String DisplayName { 
      get {
        return "NOZZLES";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC015Q001;
    public PredefinedValueAttribute attributeAre_nozzles_obstruction_free_to_allow_free_flow_of_product;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC015Q003;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510INT_PVCKLSTSEC015";

    public Section510INT_PVCKLSTSEC015(DataModelItem parent) : base(parent)
    {
            
        attribute510INT_PVCKLSTSEC015Q001 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are accessible nozzle flange faces in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC015_Q001"); 
     
        attributeAre_nozzles_obstruction_free_to_allow_free_flow_of_product = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are nozzles obstruction free to allow free flow of product:", databaseName: "510_INT-PV_CKLST_SEC015_Q002"); 
     
        attribute510INT_PVCKLSTSEC015Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Cracking", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Distortion", null)
        }, false, this, "Is there corrosion, cracking, or distortion noted internally on accessible nozzles necks:", databaseName: "510_INT-PV_CKLST_SEC015_Q003"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute510INT_PVCKLSTSEC015Q001,
           attributeAre_nozzles_obstruction_free_to_allow_free_flow_of_product,
           attribute510INT_PVCKLSTSEC015Q003,
        };
    }
  }
}
