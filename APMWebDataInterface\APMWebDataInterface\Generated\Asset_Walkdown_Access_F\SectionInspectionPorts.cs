//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionAsset_Walkdown_Access_F
{
  public class SectionInspectionPorts : DataModelItem {

    public override String DisplayName { 
      get {
        return "Inspection Ports";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributeExisting_inspection_ports;
    public PredefinedValueAttribute attributeInsulation_plugs_missing;
    public PredefinedValueAttribute attributeAdditional_ports_needed;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionInspectionPorts";

    public SectionInspectionPorts(DataModelItem parent) : base(parent)
    {
            
        attributeExisting_inspection_ports = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Existing inspection ports?", databaseName: "AWA_Q021"); 
     
        attributeInsulation_plugs_missing = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Insulation plugs missing?", databaseName: "AWA_Q022"); 
     
        attributeAdditional_ports_needed = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Additional ports needed?", databaseName: "AWA_Q023"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeExisting_inspection_ports,
           attributeInsulation_plugs_missing,
           attributeAdditional_ports_needed,
        };
    }
  }
}
