//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'SectionPermittingRequired.dart';
import 'SectionPersonnelAccessConditions.dart';

// ignore: camel_case_types
class SectionGeneralSiteConditions extends DataModelSection {
  @override
  String getDisplayName() => "General Site Conditions";
  SectionGeneralSiteConditions(DataModelItem? parent)
      : super(parent: parent, sectionName: "General Site Conditions");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeAre_there_any_on_site_leaks =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Are there any on-site leaks?",
          databaseName: "PPEAW_Q061",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: true),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeVehicle_Accessibility =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Vehicle Accessibility",
          databaseName: "PPEAW_Q065",
          availableOptions: [
        PredefinedValueOption("Readily accessible", null,
            isCommentRequired: false),
        PredefinedValueOption("Partially accessible", null,
            isCommentRequired: false),
        PredefinedValueOption("Difficult or No Access", null,
            isCommentRequired: false),
      ]);

// ignore: non_constant_identifier_names
  late SectionPermittingRequired sectionPermittingRequired =
      SectionPermittingRequired(this);
  // ignore: non_constant_identifier_names
  late SectionPersonnelAccessConditions sectionPersonnelAccessConditions =
      SectionPersonnelAccessConditions(this);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      sectionPermittingRequired,
      sectionPersonnelAccessConditions,
      attributeAre_there_any_on_site_leaks,
      attributeVehicle_Accessibility,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionGeneralSiteConditions";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
