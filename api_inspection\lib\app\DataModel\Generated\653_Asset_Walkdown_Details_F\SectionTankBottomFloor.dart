//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class SectionTankBottomFloor extends DataModelSection {
  @override
  String getDisplayName() => "Tank Bottom (Floor)";
  SectionTankBottomFloor(DataModelItem? parent)
      : super(parent: parent, sectionName: "Tank Bottom (Floor)");

  // ignore: non_constant_identifier_names
  late StringAttribute attributeType = StringAttribute(
      parent: this,
      displayName: "Type",
      databaseName: "653AW_Q421",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeMaterial_Spec_and_Grade = StringAttribute(
      parent: this,
      displayName: "Material Spec and Grade",
      databaseName: "653AW_Q422",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeNominal_thickness_annular_ring =
      DoubleAttribute(
    parent: this,
    displayName: "Nominal thickness (annular ring)",
    databaseName: "653AW_Q423",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "in",
  );

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeNominal_thickness_sketch_plates =
      DoubleAttribute(
    parent: this,
    displayName: "Nominal thickness (sketch plates)",
    databaseName: "653AW_Q424",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "in",
  );

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeNominal_thickness_inner_plates =
      DoubleAttribute(
    parent: this,
    displayName: "Nominal thickness (inner plates)",
    databaseName: "653AW_Q425",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "in",
  );

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeCorrosion_Allowance = DoubleAttribute(
    parent: this,
    displayName: "Corrosion Allowance",
    databaseName: "653AW_Q426",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "in",
  );

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeType,
      attributeMaterial_Spec_and_Grade,
      attributeNominal_thickness_annular_ring,
      attributeNominal_thickness_sketch_plates,
      attributeNominal_thickness_inner_plates,
      attributeCorrosion_Allowance,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionTankBottomFloor";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
