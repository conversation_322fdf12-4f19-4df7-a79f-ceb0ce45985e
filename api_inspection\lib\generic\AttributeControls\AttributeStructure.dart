import 'package:api_inspection/generic/IExpandable.dart';
import 'package:api_inspection/generic/UIControls/SectionPage.dart';
import 'package:api_inspection/generic/UIControls/listener.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/AttributeControls/AttributeCommentTextField.dart';
import 'package:api_inspection/generic/AttributeControls/CollectionHistoryControl.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/AttributeBase.dart';
import 'package:api_inspection/generic/MediaControls/SinglePhotoCollectionControl.dart';
import 'package:api_inspection/generic/MediaControls/SinglePhotoCollectionControlNonEditable.dart';
import 'package:api_inspection/generic/UIControls/TeamToggleButtonWithPreviewText.dart';

import '../AppRoot.dart';
import 'HistoryControl.dart';
import 'StaticAttributeHelper.dart';

class IsEditableController {
  IsEditableController({bool isEditable = true}) {
    _isEditable = isEditable;
  }

  bool _isEditable = true;

  void setIsEditable(bool newValue) {
    if (_isEditable == newValue) return;
    _isEditable = newValue;
    listeners.notifyListeners();
  }

  bool getIsEditable() {
    return _isEditable;
  }

  ListenerWrapper listeners = ListenerWrapper();
}

class AttributeStructure extends StatefulWidget {
  final AttributeBase _attribute;
  final Widget Function(
          BuildContext context, ListenerWrapper forceSaveAttributeListener)
      editingBuilder;
  final Widget Function(BuildContext context) nonEditingBuilder;
  final bool showPhotos;
  final bool showComments;
  final IsEditableController? editingController;

  const AttributeStructure(this._attribute,
      {Key? key,
      required this.editingBuilder,
      required this.nonEditingBuilder,
      this.showPhotos = true,
      this.showComments = true,
      this.editingController})
      : super(key: key);

  @override
  _AttributeStructureState createState() => _AttributeStructureState();
}

class _AttributeStructureState extends State<AttributeStructure>
    with TickerProviderStateMixin
    implements IExpandable {
  ListenerWrapper forceSaveAttributeListener = ListenerWrapper();
  Widget? expandedChild;

  bool getIsEditable() {
    return widget.editingController?._isEditable ?? true;
  }

  bool _isExpanded = false;

  late final AnimationController _controller;
  late final Animation<double> _animation;

  @override
  void didUpdateWidget(covariant AttributeStructure oldWidget) {
    var oldEditingController = oldWidget.editingController;
    if (oldEditingController != null) {
      oldEditingController.listeners.removeListener(onEditableChanged);
    }

    oldWidget._attribute.removeListener(onAttributeChanged);

    var editingController = widget.editingController;
    if (editingController != null) {
      editingController.listeners.addListener(onEditableChanged);
    }
    widget._attribute.addListener(onAttributeChanged);

    super.didUpdateWidget(oldWidget);
  }

  @override
  void initState() {
    var editingController = widget.editingController;
    if (editingController != null) {
      editingController.listeners.addListener(onEditableChanged);
    }

    _controller = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.fastOutSlowIn,
    );

    _controller.addStatusListener((status) {
      setState(() {});
    });

    widget._attribute.addListener(onAttributeChanged);

    super.initState();
  }

  void onEditableChanged() {
    setState(() {});
  }

  void onAttributeChanged() {
    setState(() {});
  }

  @override
  void deactivate() {
    forceSaveAttributeListener.notifyListeners();

    super.deactivate();
  }

  @override
  void dispose() {
    if (StaticAttribute.selectedAttribute == this) {
      StaticAttribute.selectedAttribute = null;
    }

    var editingController = widget.editingController;
    if (editingController != null) {
      editingController.listeners.removeListener(onEditableChanged);
    }

    widget._attribute.removeListener(onAttributeChanged);
    _controller.dispose();
    super.dispose();
  }

  String getCommentTextFieldName() {
    if (widget._attribute.getAreCommentsRequired()) return "*Comment";
    return "Comment";
  }

  void expanderPressed() {
    setState(() {
      FocusScopeNode currentFocus = FocusScope.of(context);

      if (!currentFocus.hasPrimaryFocus) {
        currentFocus.unfocus();
      }

      setExpanded(!_isExpanded);
      if (_isExpanded) {
        if (StaticAttribute.selectedAttribute != null &&
            StaticAttribute.selectedAttribute != this) {
          StaticAttribute.selectedAttribute!.setExpanded(false);
        }
        StaticAttribute.selectedAttribute = this;
      } else {
        if (StaticAttribute.selectedAttribute == this) {
          StaticAttribute.selectedAttribute = null;
        }
      }
    });
  }

  Widget getCommentControl(BuildContext context) {
    if (!widget.showComments) return Container();
    if (!getIsEditable()) {
      var comment = widget._attribute.getComment();
      if (comment == null) return Row();
      return Container(
        margin: const EdgeInsets.fromLTRB(40, 10, 20, 0),
        child:
            Column(crossAxisAlignment: CrossAxisAlignment.stretch, children: [
          const Text(
            "Comment",
            style: TextStyle(color: Colors.white70, fontSize: 16),
            textAlign: TextAlign.start,
          ),
          Text(
            comment,
            style: const TextStyle(color: Colors.white, fontSize: 20),
            textAlign: TextAlign.start,
          ),
        ]),
      );
    }

    return Container(
      margin: const EdgeInsets.fromLTRB(20, 10, 20, 0),
      child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: buildAttributeField()),
    );
  }

  List<Widget> buildAttributeField() {
    List<Widget> columnChildren = [];

    var provideCommentsText = const Text(
      "Please provide a comment",
      style: TextStyle(color: Colors.blue, fontSize: 18),
      textAlign: TextAlign.start,
    );

    var commentsText = Text(
      getCommentTextFieldName(),
      style: const TextStyle(color: Colors.white, fontSize: 16),
      textAlign: TextAlign.start,
    );

    var comment = widget._attribute.getComment();
    if (widget._attribute.getAreCommentsRequired() &&
        (comment == "" || comment == null)) {
      columnChildren.add(provideCommentsText);
    }

    columnChildren.add(commentsText);
    columnChildren.add(AttributeCommentTextField(widget._attribute));

    return columnChildren;
  }

  @override
  Widget build(BuildContext context) {
    Color borderColor;
    if (_isExpanded) {
      borderColor = const Color.fromARGB(255, 4, 188, 242);
    } else {
      if (getIsEditable()) {
        borderColor = const Color.fromARGB(255, 122, 122, 122);
      } else {
        borderColor = Colors.transparent;
      }
    }

    Widget? errorIcon;
    var previewText = widget._attribute.getPreviewText();
    if (widget._attribute.getIsInErrorState() ||
        (SectionUtils.shouldWarn &&
            (previewText.isEmpty ||
                previewText.toLowerCase() == "0 photos" ||
                previewText.toLowerCase() == "not set"))) {
      errorIcon = const Icon(Icons.error, color: Colors.red);
    }

    if (!_isExpanded && !_controller.isAnimating) {
      return TeamToggleButtonWithPreviewText(
        widget._attribute.displayName,
        expanderPressed,
        previewText,
        widget._attribute.iconWidget,
        borderColor: borderColor,
        errorIcon: errorIcon,
      );
    }

    Widget button = TeamToggleButtonWithPreviewText(
      widget._attribute.displayName,
      expanderPressed,
      previewText,
      widget._attribute.iconWidget,
      borderColor: borderColor,
      errorIcon: errorIcon,
    );

    Widget historyControl;
    var user = AppRoot.global().currentUser;
    if (user != null && user.role == "admin") {
      var attr = widget._attribute;
      if (attr is SingleAttributeBase) {
        historyControl = HistoryControl(attr.valueChangeLog);
      } else if (attr is MultiAttributeBase) {
        historyControl = CollectionHistoryControl(attr.valueChangeLog);
      } else {
        historyControl = Row();
      }
    } else {
      historyControl = Row();
    }

    Widget photoControl;
    if (!widget.showPhotos) {
      photoControl = Container();
    } else if (getIsEditable()) {
      photoControl = SinglePhotoCollectionControl(widget._attribute);
    } else {
      photoControl = SinglePhotoCollectionControlNonEditable(widget._attribute);
    }
    expandedChild = (getIsEditable()
        ? widget.editingBuilder(context, forceSaveAttributeListener)
        : widget.nonEditingBuilder(context));

    return Column(children: [
      button,
      Center(
          child: SizeTransition(
              sizeFactor: _animation,
              axis: Axis.vertical,
              child: Column(children: [
                expandedChild!,
                getCommentControl(context),
                photoControl,
                historyControl
              ])))
    ]);
  }

  @override
  void setExpanded(bool value) {
    setState(() {
      _isExpanded = value;
      forceSaveAttributeListener.notifyListeners();

      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
  }
}
