import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';
import 'package:api_inspection/generic/DataModelCommon/Attributes/AttributeBase.dart';
import 'package:api_inspection/generic/DataModelCommon/DatabaseInterfaces/DatabaseHelperManager.dart';
import 'package:api_inspection/generic/DataModelCommon/PhotoRoot.dart';
import 'package:api_inspection/generic/MediaControls/PhotoConfirmationPage.dart';
import 'package:api_inspection/generic/MediaControls/file_name_package.dart';
import 'package:api_inspection/generic/MediaControls/pending_upload_data.dart';
import 'package:api_inspection/generic/MediaControls/pending_uploads.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';

import 'package:api_inspection/generic/MediaControls/file_cache.dart';
import 'package:camera/camera.dart';
import 'package:api_inspection/generic/UIControls/listener.dart';
import 'package:collection/collection.dart';
import 'package:flutter/services.dart';
import 'package:api_inspection/generic/MediaControls/MediaCache.dart';
import 'package:api_inspection/generic/MediaControls/MediaEntry.dart';
import 'package:flutter_uploader/flutter_uploader.dart';
import 'package:synchronized/synchronized.dart';
import 'package:darq/darq.dart';
import 'package:crypto/crypto.dart' as crypto;
import 'dart:convert';

import '../AppRoot.dart';

String getNameFromPath(String path) {
  return path.substring(path.lastIndexOf('/') + 1);
}

abstract class IMediaSynchronizer {
  ListenerWrapper listener = ListenerWrapper();

  Future<int> getPendingUploads();

  static IMediaSynchronizer? _global;
  static IMediaSynchronizer getMediaSynchronizer() {
    IMediaSynchronizer? global = _global;
    if (global == null) {
      _global = MobileMediaSynchronizer();
    }
    return _global!;
  }

  Future<void> downloadFileForMedia(MediaEntry entry,
      {bool forceDownload = false});
  Future<void> removeFileForCache(MediaEntry entry);
  Future<void> uploadNewDataForEntry(
      MediaEntry entry, Uint8List newBytes, WriteBatch batch);
  Future<void> removePendingFile(String file) async {
    throw UnimplementedError();
  }

  Future<void> ensureInitialization();
  Future<void> uploadPendingMedia(WriteBatch batch);

  File? getFileFromCache(String fileName);
  Future<void> onPhotoTaken(MediaPackage photoPackage, AttributeBase attribute);
  Future<void> enqueuePhotoUpload(
      PlatformFile photo, AttributeBase attribute, WriteBatch batch);
}

class FileData {
  String name;
  Uint8List data;
  FileData(this.name, this.data);
}

class MobileMediaSynchronizer with IMediaSynchronizer {
  final String _pendingFilesDataFileName = "PendingFiles.data";

  final FileCache _mediaFileCache = FileCache("PendingMediaUploads");

  Timer? runningTimer;

  final _pendingUploadingsLock = Lock(reentrant: false);

  final Map<String, PendingUploadData> _taskFileNameMap = {};

  final Map<String, int> _enqueueCounts = {};

  final Map<String, int> _uploadTaskAcknowledgements = {};

  Future<void>? _initializeFuture;
  @override
  Future<void> ensureInitialization() {
    var initializeFuture = _initializeFuture;
    if (initializeFuture == null) {
      _initializeFuture = _initialize();
      initializeFuture = _initializeFuture!;
    }
    return initializeFuture;
  }

  Future<void> _cleanupFileCache() async {
    var pendingFiles = await getPendingFiles();
    var currentTime = DateTime.now();

    var map = <String, List<FileNamePackage>>{};
    var filesInCache = _mediaFileCache.getFilesFromCache().toList();
    for (var file in filesInCache) {
      try {
        var fileName = getNameFromPath(file.path);
        if (fileName == _pendingFilesDataFileName) continue;
        var package = FileNamePackage.fromName(fileName);

        if (!pendingFiles.contains(fileName)) {
          if (map.containsKey(package.fileName)) {
            var list = map[package.fileName];
            if (list != null) list.add(package);
          } else {
            List<FileNamePackage> list = [package];
            map[package.fileName] = list;
          }

          var lastAccessTime = await file.lastAccessed();
          var timeDifference = currentTime.difference(lastAccessTime);
          if (timeDifference.inDays > 7) {
            _mediaFileCache.removeFileFromCache(file);
          }
        }
      } catch (ex) {
        log('error while cleaning up cache on file: ' + file.path, error: ex);
      }
    }

    for (var entry in map.entries) {
      var values = entry.value;
      if (values.length < 2) continue;

      var packagesByVersionNumber =
          values.orderByDescending((element) => element.fileVersion).toList();
      var oldPackages = packagesByVersionNumber.skip(1);
      for (var package in oldPackages) {
        var fileFull = filesInCache.firstWhereOrNull((element) =>
            getNameFromPath(element.path) == package.buildFileName());
        if (fileFull != null) {
          _mediaFileCache.removeFileFromCache(fileFull);
        }
      }
    }
  }

  Future<void> _initialize() async {
    await _mediaFileCache.initialize();

    FlutterUploader()
        .result
        .where((response) =>
            response.status == UploadTaskStatus.failed ||
            response.status == UploadTaskStatus.undefined)
        .distinct((a, b) => a.taskId == b.taskId)
        .listen((event) {
      var data = _taskFileNameMap.entries.firstWhereOrNull(
          (pendingUploadData) =>
              pendingUploadData.value.taskId == event.taskId);
      if (data != null) {
        data.value.entry.isUploading = false;
        log('${data.key}: ${data.value}, status: ${event.status?.description}',
            name: 'failed/undefined status');
      } else {
        log('${event.taskId}, status: ${event.status?.description}',
            name: 'failed/undefined status');
      }
    });

    FlutterUploader()
        .result
        .where((response) => response.status == UploadTaskStatus.complete)
        .distinct((a, b) => a.taskId == b.taskId)
        .listen((event) async {
      var data = _taskFileNameMap.entries.firstWhereOrNull(
          (pendingUploadData) =>
              pendingUploadData.value.taskId == event.taskId);

      if (data != null) {
        var entry = data.value.entry;
        var package = data.value.package;
        entry.uploadedVersion = package.fileVersion;

        var acknowledgementsKey = '${package.fileName}-${event.taskId}';
        var uploadTaskAlreadyAcknowledged =
            _uploadTaskAcknowledgements[acknowledgementsKey] != null &&
                event.status == UploadTaskStatus.complete;

        if (uploadTaskAlreadyAcknowledged) return;

        _uploadTaskAcknowledgements[acknowledgementsKey] = 1;

        log('$acknowledgementsKey: ${_uploadTaskAcknowledgements[acknowledgementsKey]}',
            name: 'updateVersionCounts');

        entry.isUploading = false;

        var batch = FirebaseFirestore.instance.batch();
        DatabaseHelperManager.global().updateItem(
            package.databasePath + ".U", package.fileVersion, batch);

        await batch.commit();

        await removePendingFile(data.key);
        _taskFileNameMap.remove(data.key);

        entry.parent?.notifyListeners();
        listener.notifyListeners();

        removeFileForCache(entry);

        log('Finished notifying after updating version \n file: ${package.fileName} \n taskId: ${event.taskId} \n updateItem calls: ${_uploadTaskAcknowledgements[package.fileName]} \n taskFileNameMap.keys.length: ${_taskFileNameMap.keys.length}',
            name: 'updateVersionCounts');

        if (_taskFileNameMap.keys.isEmpty) {
          FlutterUploader().clearUploads();
        }
      }
    });

    // Make sure that PendingFiles.data file exists in our cache
    // await getPendingFileCacheFile();

    await _cleanupFileCache();

    var batch = FirebaseFirestore.instance.batch();

    uploadPendingMedia(batch);

    await batch.commit();
  }

  @override
  File? getFileFromCache(String fileName) {
    return _mediaFileCache.getFileFromCache(fileName);
  }

  Future<List<String>> getPendingFiles() async {
    return await PendingUploads.getPendingFiles();
  }

  Future<File> getPendingFileCacheFile() async {
    var pendingFilesFile =
        _mediaFileCache.getFileFromCache(_pendingFilesDataFileName);
    if (pendingFilesFile != null) return pendingFilesFile;
    return await _mediaFileCache.addFileToCache(
        _pendingFilesDataFileName, Uint8List(0));
  }

  Future<bool> addPendingFile(String file) async {
    return await PendingUploads.addPendingFile(file);
  }

  @override
  Future<bool> removePendingFile(String file) async {
    return await PendingUploads.removePendingFile(file);
  }

  @override
  Future<void> uploadNewDataForEntry(
      MediaEntry entry, Uint8List newBytes, WriteBatch batch) async {
    var version = entry.version;
    if (version == null) {
      throw 'Cannot upload null version';
    }

    entry.isUploading = true;

    FileNamePackage namePackage = FileNamePackage.fromEntry(entry);

    var fileNameFull = namePackage.buildFileName();

    await _mediaFileCache.addFileToCache(fileNameFull, newBytes);

    await addPendingFile(fileNameFull);

    if (entry.isLoading) entry.isLoading = false;
    entry.resolvedVersion = version;
    entry.saveItem(batch);

    uploadPendingMedia(batch);

    listener.notifyListeners();

    return;
  }

  @override
  Future<int> getPendingUploads() async {
    var files = await getPendingFiles();
    return files.length;
  }

  @override
  Future<void> uploadPendingMedia(WriteBatch batch) async {
    await _pendingUploadingsLock.synchronized(() async {
      var files = await getPendingFiles();

      if (files.isNotEmpty) {
        for (var fileName in files) {
          if (_taskFileNameMap.containsKey(fileName)) continue;

          try {
            FileNamePackage namePackage = FileNamePackage.fromName(fileName);
            var file = _mediaFileCache.getFileFromCache(fileName);
            if (file == null) {
              continue;
            }
            var currentEntry = MediaCache.findEntry(
                namePackage.fileName, namePackage.extension);

            if (currentEntry == null) continue;

            currentEntry.isUploading = true;

            var md5 = crypto.md5;
            var hash = await md5.bind(file.openRead()).first;
            var base64Hash = base64.encode(hash.bytes);

            var taskId = await FlutterUploader().enqueue(RawUpload(
                url: currentEntry.uploadUrl,
                allowCellular: true,
                headers: {
                  'x-ms-blob-type': 'BlockBlob',
                  'content-type': 'application/octet-stream',
                  'content-md5': base64Hash
                },
                method: UploadMethod.PUT,
                path: file.path,
                tag: currentEntry.fullFileName));
            _enqueueCounts[currentEntry.fullFileName] =
                _enqueueCounts[currentEntry.fullFileName] == null
                    ? 1
                    : _enqueueCounts[currentEntry.fullFileName]! + 1;
            _taskFileNameMap[fileName] =
                PendingUploadData(currentEntry, namePackage, taskId);
          } catch (e) {
            log(e.toString(), name: 'uploadPendingMedia', time: DateTime.now());
          }
        }
      }
    });

    var timer = runningTimer;
    if (timer != null) {
      var pendingItems = await getPendingFiles();
      if (pendingItems.isEmpty) {
        timer.cancel();
        runningTimer = null;
      } else {
        runningTimer = Timer.periodic(const Duration(minutes: 2), (timer) {
          uploadPendingMedia(batch);
        });
      }
    } else {
      runningTimer = Timer.periodic(const Duration(minutes: 2), (timer) {
        uploadPendingMedia(batch);
      });
    }
  }

  final filesWithNullBytes = <String, int>{};
  final fileDownloadCalls = <String, int>{};

  @override
  Future<void> downloadFileForMedia(MediaEntry entry,
      {bool forceDownload = false}) async {
    try {
      var defaultCacheManager = DefaultCacheManager();

      entry.isDownloading = true;
      forceDownload
          ? await defaultCacheManager.downloadFile(entry.downloadUrl)
          : await defaultCacheManager.getSingleFile(entry.downloadUrl);

      entry.resolvedVersion = entry.version;
    } catch (e) {
      log(e.toString(), name: 'downloadFileForMedia', time: DateTime.now());
    } finally {
      entry.isDownloading = false;
      AppRoot.global().photoDownloadProgressEvent.notifyListeners();
    }
  }

  @override
  Future<void> removeFileForCache(MediaEntry entry) async {
    if (entry.isDownloading) return;

    var version = entry.version;

    if (version == null) {
      throw "cannot remove file for unknown version of media entry";
    }

    var package = FileNamePackage.fromEntry(entry);

    var filesInCache = _mediaFileCache.getFilesFromCache().toList();

    var fileFull = filesInCache.firstWhereOrNull(
        (element) => getNameFromPath(element.path) == package.buildFileName());
    if (fileFull != null) {
      _mediaFileCache.removeFileFromCache(fileFull);
    }
  }

  @override
  Future<void> onPhotoTaken(
      MediaPackage photoPackage, AttributeBase attribute) async {
    var fileName = "APM_${DateTime.now().millisecondsSinceEpoch}";

    var photoRoot = attribute.findParentOfType<PhotoRoot>();
    if (photoRoot == null) throw "Could not find photo root";

    var batch = FirebaseFirestore.instance.batch();

    var newEntry = photoRoot.addPhoto(fileName, "png");
    newEntry.description.setValue(photoPackage.description, batch);
    var photo = photoPackage.mediaFile;
    var photoBytes = await photo.readAsBytes();

    var gallerySaver = AppRoot.global().gallerySaver;
    if (gallerySaver != null) {
      await gallerySaver.saveImageToGallery(
          photoData: photoBytes,
          quality: 95,
          name: attribute.displayName,
          path: photo.path,
          albumName: "APMImages");
    }

    newEntry.version = 1;
    await uploadNewDataForEntry(newEntry, photoBytes, batch);

    attribute.addPhoto(newEntry);

    photoRoot.saveItem(batch);
    attribute.saveItem(batch);

    await batch.commit();

    return;
  }

  Future<void> _enqueueNewFileForEntry(
      MediaEntry entry, XFile file, WriteBatch batch) async {
    var version = entry.version;
    if (version == null) {
      throw 'Cannot upload null version';
    }

    entry.isUploading = true;

    FileNamePackage namePackage = FileNamePackage.fromEntry(entry);

    var fileNameFull = namePackage.buildFileName();
    var photoBytes = await file.readAsBytes();
    await _mediaFileCache.addFileToCache(fileNameFull, photoBytes);

    await addPendingFile(fileNameFull);

    if (entry.isLoading) entry.isLoading = false;
    entry.resolvedVersion = version;
    entry.saveItem(batch);

    listener.notifyListeners();

    return;
  }

  @override
  Future<void> enqueuePhotoUpload(
      PlatformFile photo, AttributeBase attribute, WriteBatch batch) async {
    var fileName = "APM_${DateTime.now().millisecondsSinceEpoch}";

    var photoRoot = attribute.findParentOfType<PhotoRoot>();
    if (photoRoot == null) throw "Could not find photo root";

    var newEntry = photoRoot.addPhoto(fileName, "png");
    newEntry.version = 1;

    var file = XFile(photo.path!);

    await _enqueueNewFileForEntry(newEntry, file, batch);

    attribute.addPhoto(newEntry);

    return;
  }
}
