﻿using System;
using System.Collections.Generic;

namespace CommonDataInterface
{
  public class ListChangeLogEntry<DataType> : ChangeLogEntry<DataType> {

    public String Action { get; private set; }

    public ListChangeLogEntry(String key, DataType value, DateTime timeChanged, String userName, String action) : base(key, value, timeChanged, userName)
    {
      Action = action;
    }

    public static ListChangeLogEntry<DataType> CreateNew(DataType value, String action)
    {
      var key = Guid.NewGuid().ToString();
      var timeChanged = DateTime.Now;
      var userName = "Unknown";

      return new ListChangeLogEntry<DataType>(key, value, timeChanged, userName, action);
    }
    
    public override void AddUpdates(Dictionary<string, Object> updates, String path)
    {
      updates[path + ".T"] = new DateTimeOffset(TimeChanged).ToUnixTimeMilliseconds();
      updates[path + ".U"] = UserName;
      updates[path + ".V"] = Value;
      updates[path + ".A"] = Action;
    }

  }
}