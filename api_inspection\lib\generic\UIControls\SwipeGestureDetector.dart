import 'package:flutter/material.dart';

typedef SwipedEvent = void Function();

class SwipeGestureRecognizer extends StatefulWidget {
  final Widget child;

  final SwipedEvent? onSwipeLeft;
  final SwipedEvent? onSwipeRight;
  final SwipedEvent? onSwipeUp;
  final SwipedEvent? onSwipeDown;

  const SwipeGestureRecognizer(
      {Key? key,
      required this.child,
      this.onSwipeLeft,
      this.onSwipeRight,
      this.onSwipeUp,
      this.onSwipeDown})
      : super(key: key);

  @override
  _SwipeGestureRecognizerState createState() => _SwipeGestureRecognizerState();
}

class _SwipeGestureRecognizerState extends State<SwipeGestureRecognizer> {
  DateTime? timeOfLastDrag;

  int sensitivity = 8;

  @override
  Widget build(BuildContext context) {
    Function(DragUpdateDetails)? horizontalDragDetails;
    if (widget.onSwipeLeft != null || widget.onSwipeRight != null) {
      horizontalDragDetails = (details) {
        var currentTime = DateTime.now();
        var lastDragTime = timeOfLastDrag;
        if (lastDragTime != null) {
          var timeSinceDrag = currentTime.difference(lastDragTime);
          if (timeSinceDrag.inMilliseconds < 250) return;
        }

        if (details.delta.dx < -sensitivity) {
          widget.onSwipeLeft?.call();
          timeOfLastDrag = currentTime;
        } else if (details.delta.dx > sensitivity) {
          widget.onSwipeRight?.call();
          timeOfLastDrag = currentTime;
        }
      };
    }

    Function(DragUpdateDetails)? verticalDragDetails;
    if (widget.onSwipeUp != null || widget.onSwipeDown != null) {
      verticalDragDetails = (details) {
        var currentTime = DateTime.now();
        var lastDragTime = timeOfLastDrag;
        if (lastDragTime != null) {
          var timeSinceDrag = currentTime.difference(lastDragTime);
          if (timeSinceDrag.inMilliseconds < 250) return;
        }

        if (details.delta.dy < -sensitivity) {
          widget.onSwipeUp?.call();
          timeOfLastDrag = currentTime;
        } else if (details.delta.dy > sensitivity) {
          widget.onSwipeDown?.call();
          timeOfLastDrag = currentTime;
        }
      };
    }
    return GestureDetector(
        child: widget.child,
        onHorizontalDragUpdate: horizontalDragDetails,
        onVerticalDragUpdate: verticalDragDetails);
  }
}
