﻿using System;
using Google.Protobuf.WellKnownTypes;

namespace CommonDataInterface.Attributes
{
  public class PhoneNumberAttribute : StringAttribute {
    public override String AttributeType => "PhoneNumber";
    
    public override bool IsValueEqualTo(String other)
    {
      var formattedStr = FormatValue(other);

      return AreStringsEqual(GetValue(), formattedStr);
    }

    public PhoneNumberAttribute(DataModelItem parent, String displayName, String databaseName = null, bool areCommentsRequired = false) : base(parent, displayName,  databaseName: databaseName, areCommentsRequired: areCommentsRequired)
    {

    }

    private String FormatValue(String value)
    {
      var formattedStr = value;
      if (!String.IsNullOrWhiteSpace(formattedStr)){
        var withoutSpace = formattedStr.Replace(" ", "");
        if (withoutSpace.Length == 10 && int.TryParse(withoutSpace, out var result)) {
          var first = withoutSpace.Substring(0, 3);
          var second = withoutSpace.Substring(3, 6);
          var last = withoutSpace.Substring(6);

          formattedStr = '(' + first + ") " + second + "-" + last;
        }
      }

      return formattedStr;
    }
    
    public override void SetValue(String? value) {
      var formattedStr = FormatValue(value);

      if (AreStringsEqual(GetValue(), formattedStr))
        return;
        
      this.GetValueChangeLog().PendingChange = new PendingChange<string>{Value = formattedStr};

      NotifyListeners();
    }
    
  }
}