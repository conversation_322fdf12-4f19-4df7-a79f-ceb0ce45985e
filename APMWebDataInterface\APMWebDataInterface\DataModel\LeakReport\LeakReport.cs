﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using APMWebDataInterface.ExampleDataModel;
using CommonDataInterface;
using CommonDataInterface.Attributes;
using APMWebDataInterface.Generated.sectionLeak_Report_F;

namespace APMWebDataInterface.DataModel.LeakReport
{


  public class LeakReport : ConcretePhotoRoot {
    public override string DisplayName => "Leak Report";

    internal override String GetDBPath() => "leakreports." + GetDBName();
    internal override String GetDBName() => id;

    public String createdBy;

    public PendingChange<String> PendingStatus;
    public String status { get; internal set; }

    public SectionLeakReport report;
    public SectionWorkDetail workDetail;
    public StringAttribute apmNumber;
    public StringAttribute businessUnitId;

    public DataModelCollection<LeakReportPhoto> leakReportPhotos;

    public enum LeakReportStatuses { Active, Closed}
    public void SetStatus(LeakReportStatuses status)
    {
      String newStatus = null;
      switch (status) {
        case LeakReportStatuses.Active:
          newStatus = "active";
          break;
        case LeakReportStatuses.Closed:
          newStatus = "closed";
          break;
      } 

      PendingStatus = new PendingChange<string> {Value = newStatus};
    }

    public LeakReport(String createdBy, String status) : base(null)
    {
      this.createdBy = createdBy;
      this.status = status;
      InitializeAttributes();
    }



    internal LeakReport(String id, String createdBy, String status) : base(id, null)
    {
      this.createdBy = createdBy;
      this.status = status;
      InitializeAttributes();
    }

    private void InitializeAttributes()
    {
      report = new SectionLeakReport(this);
      workDetail = new SectionWorkDetail(this);
      apmNumber = new StringAttribute(parent:this, displayName: "APM Number");
      businessUnitId = new StringAttribute(this, "BusinessUnitId", isQueryable: true);


      leakReportPhotos = new DataModelCollection<LeakReportPhoto>("LeakReportPhotos", (parent, entry) => { 
        return new LeakReportPhoto(leakReportPhotos, entry.Key); 
      }, (parent, id) => { 
        return new LeakReportPhoto(leakReportPhotos, id); 
      },this);
    }

    public override bool GetHasDatabaseChangesPending()
    {
      return PendingStatus != null || base.GetHasDatabaseChangesPending();
    }


    protected override async Task DoAddPendingChangesToDictionary(Dictionary<string, object> updates, string user) 
    {
     if (PendingStatus != null) {
        updates["status"] = PendingStatus.Value;
     }

     updates["createdBy"] = createdBy;

      await base.DoAddPendingChangesToDictionary(updates, user);
    }

    public override Task SavePendingChanges(string user)
    {
        APM_WebDataInterface.Global.AuthorizeWriteToRootObject(this.businessUnitId, user);
        return base.SavePendingChanges(user);

    }


    public override DataModelItem[] GetChildren(){
      var parentList = base.GetChildren().ToList();
      parentList.AddRange(new DataModelItem[]{ report, leakReportPhotos, workDetail, apmNumber, businessUnitId });
      return parentList.ToArray();
    }
    }
}
