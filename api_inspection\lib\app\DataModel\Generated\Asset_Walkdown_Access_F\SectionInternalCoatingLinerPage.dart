//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionInternalCoatingLiner.dart';

// ignore: camel_case_types
class SectionInternalCoatingLinerPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionInternalCoatingLiner sectionInternalCoatingLiner;

  const SectionInternalCoatingLinerPage(this.sectionInternalCoatingLiner,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionInternalCoatingLinerPageState();
  }
}

class _SectionInternalCoatingLinerPageState
    extends State<SectionInternalCoatingLinerPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionInternalCoatingLiner,
        title: "Internal Coating Liner",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionInternalCoatingLiner.attributeCoatingLiner_Type
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionInternalCoatingLiner
                      .attributeCoatingLiner_Condition
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionInternalCoatingLiner
                      .attributeCoatingLiner_Conditions_Observed
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
