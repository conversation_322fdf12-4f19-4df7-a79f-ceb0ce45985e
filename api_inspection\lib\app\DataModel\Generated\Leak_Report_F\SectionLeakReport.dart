//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class SectionLeakReport extends DataModelSection {
  @override
  String getDisplayName() => "Leak Report";
  SectionLeakReport(DataModelItem? parent)
      : super(parent: parent, sectionName: "Leak Report");

  // ignore: non_constant_identifier_names
  late StringAttribute attributeEquipment_ID = StringAttribute(
      parent: this,
      displayName: "Equipment ID",
      databaseName: "LR_Q105",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeEquipment_Description =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Equipment Description",
          databaseName: "LR_Q106",
          availableOptions: [
        PredefinedValueOption("On-Plot (Facility)", null,
            isCommentRequired: false),
        PredefinedValueOption("Off-Plot (Field)", null,
            isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeEquipment_ID_at_line_START = StringAttribute(
      parent: this,
      displayName: "Equipment ID at line START",
      databaseName: "LR_Q107",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeEquipment_ID_at_line_END = StringAttribute(
      parent: this,
      displayName: "Equipment ID at line END",
      databaseName: "LR_Q108",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributePipe_Size = PredefinedValueAttribute(
      parent: this,
      hasOtherOption: false,
      displayName: "Pipe Size",
      databaseName: "LR_Q109",
      unit: "in",
      availableOptions: [
        PredefinedValueOption("0.5", null, isCommentRequired: false),
        PredefinedValueOption("0.75", null, isCommentRequired: false),
        PredefinedValueOption("1", null, isCommentRequired: false),
        PredefinedValueOption("1.25", null, isCommentRequired: false),
        PredefinedValueOption("1.5", null, isCommentRequired: false),
        PredefinedValueOption("2", null, isCommentRequired: false),
        PredefinedValueOption("2.5", null, isCommentRequired: false),
        PredefinedValueOption("3", null, isCommentRequired: false),
        PredefinedValueOption("3.5", null, isCommentRequired: false),
        PredefinedValueOption("4", null, isCommentRequired: false),
        PredefinedValueOption("4.5", null, isCommentRequired: false),
        PredefinedValueOption("5", null, isCommentRequired: false),
        PredefinedValueOption("6", null, isCommentRequired: false),
        PredefinedValueOption("8", null, isCommentRequired: false),
        PredefinedValueOption("10", null, isCommentRequired: false),
        PredefinedValueOption("12", null, isCommentRequired: false),
        PredefinedValueOption("14", null, isCommentRequired: false),
        PredefinedValueOption("16", null, isCommentRequired: false),
        PredefinedValueOption("18", null, isCommentRequired: false),
        PredefinedValueOption("20", null, isCommentRequired: false),
        PredefinedValueOption("24", null, isCommentRequired: false),
        PredefinedValueOption("30", null, isCommentRequired: false),
        PredefinedValueOption("36", null, isCommentRequired: false),
        PredefinedValueOption("42", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributePipe_Schedule =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Pipe Schedule",
          databaseName: "LR_Q110",
          availableOptions: [
        PredefinedValueOption("5", null, isCommentRequired: false),
        PredefinedValueOption("10", null, isCommentRequired: false),
        PredefinedValueOption("20", null, isCommentRequired: false),
        PredefinedValueOption("30", null, isCommentRequired: false),
        PredefinedValueOption("40", null, isCommentRequired: false),
        PredefinedValueOption("50", null, isCommentRequired: false),
        PredefinedValueOption("60", null, isCommentRequired: false),
        PredefinedValueOption("70", null, isCommentRequired: false),
        PredefinedValueOption("80", null, isCommentRequired: false),
        PredefinedValueOption("100", null, isCommentRequired: false),
        PredefinedValueOption("120", null, isCommentRequired: false),
        PredefinedValueOption("140", null, isCommentRequired: false),
        PredefinedValueOption("160", null, isCommentRequired: false),
        PredefinedValueOption("STD", null, isCommentRequired: false),
        PredefinedValueOption("EH", null, isCommentRequired: false),
        PredefinedValueOption("DBL.EH", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeProcessService =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: true,
          displayName: "Process/Service",
          databaseName: "LR_Q111",
          availableOptions: [
        PredefinedValueOption("Oil", null, isCommentRequired: false),
        PredefinedValueOption("Gas", null, isCommentRequired: false),
        PredefinedValueOption("Water", null, isCommentRequired: false),
        PredefinedValueOption("Steam", null, isCommentRequired: false),
        PredefinedValueOption("Air", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late StringAttribute attributePipe_Cover = StringAttribute(
      parent: this,
      displayName: "Pipe Cover",
      databaseName: "LR_Q112",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late IntegerAttribute attributeAffected_Length = IntegerAttribute(
    parent: this,
    displayName: "Affected Length",
    databaseName: "LR_Q113",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "ft",
  );

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeDistance_between_tie_in_points =
      DoubleAttribute(
    parent: this,
    displayName: "Distance between tie-in-points",
    databaseName: "LR_Q114",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "ft",
  );

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeCorrosion_Type =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: true,
          displayName: "Corrosion Type",
          databaseName: "LR_Q115",
          availableOptions: [
        PredefinedValueOption("External", null, isCommentRequired: false),
        PredefinedValueOption("Internal", null, isCommentRequired: false),
        PredefinedValueOption("None", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late IntegerAttribute attributeEstimated_Loss_Rate = IntegerAttribute(
    parent: this,
    displayName: "Estimated Loss Rate",
    databaseName: "LR_Q116",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "bpd",
  );

  // ignore: non_constant_identifier_names
  late IntegerAttribute attributeExisting_clamp_count_same_line =
      IntegerAttribute(
    parent: this,
    displayName: "Existing clamp count (same line)",
    databaseName: "LR_Q117",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "",
  );

  // ignore: non_constant_identifier_names
  late IntegerAttribute attributeFeatureFitting_count_same_line =
      IntegerAttribute(
    parent: this,
    displayName: "Feature/Fitting count (same line)",
    databaseName: "LR_Q118",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "",
  );

  // ignore: non_constant_identifier_names
  late StringAttribute attributeObservation_Summary = StringAttribute(
      parent: this,
      displayName: "Observation Summary",
      databaseName: "LR_Q119",
      areCommentsRequired: false);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeEquipment_ID,
      attributeEquipment_Description,
      attributeEquipment_ID_at_line_START,
      attributeEquipment_ID_at_line_END,
      attributePipe_Size,
      attributePipe_Schedule,
      attributeProcessService,
      attributePipe_Cover,
      attributeAffected_Length,
      attributeDistance_between_tie_in_points,
      attributeCorrosion_Type,
      attributeEstimated_Loss_Rate,
      attributeExisting_clamp_count_same_line,
      attributeFeatureFitting_count_same_line,
      attributeObservation_Summary,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionLeakReport";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
