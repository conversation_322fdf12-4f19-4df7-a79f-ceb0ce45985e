import 'package:api_inspection/app/batch_helper.dart';
import 'package:api_inspection/generic/AttributeControls/AttributeStructure.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:flutter/material.dart';

import '../DataModelCommon/Attributes/AttributeBase.dart';

class SectionPage extends StatefulWidget {
  final DataModelSection section;

  final IsEditableController isEditableController = IsEditableController();

  final Widget Function(IsEditableController) childBuilder;

  SectionPage({Key? key, required this.section, required this.childBuilder})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return SectionPageState();
  }
}

class SectionPageState extends State<SectionPage> {
  Widget buildLockedPage(BuildContext context) {
    widget.isEditableController.setIsEditable(false);
    var section = widget.section;

    var value = section.sectionState.getValue();
    if (value == "Completed" || value == "Skipped") {
      return Column(children: [
        Container(
          margin: const EdgeInsets.fromLTRB(10, 10, 10, 0),
          child: Text(
            "This section is marked " + value.toString(),
            style: const TextStyle(color: Colors.white, fontSize: 20),
          ),
        ),
        TextButton(
            onPressed: () {
              setState(() {
                BatchHelper.saveAndCommitStringAttribute(
                    widget.section.sectionState, "In Progress");
              });
            },
            child: const Text("Revert to In Progress",
                style: TextStyle(fontSize: 18))),
        Expanded(child: widget.childBuilder(widget.isEditableController))
      ]);
    }

    return Column(children: [
      Container(
        margin: const EdgeInsets.fromLTRB(10, 10, 10, 0),
        child: const Text(
          "This section is contained within a finished section",
          textAlign: TextAlign.center,
          style: TextStyle(color: Colors.white, fontSize: 20),
        ),
      ),
      Expanded(child: widget.childBuilder(widget.isEditableController))
    ]);
  }

  Widget buildNormalPage() {
    Widget button;
    if (widget.section.showState) {
      button = Container(
        margin: const EdgeInsets.all(20),
        height: 50,
        width: 200,
        child: ElevatedButton(
            onPressed: () {
              var unanswered =
                  SectionUtils.getUnansweredQuestions(widget.section);
              if (unanswered <= 0) {
                setState(() {
                  BatchHelper.saveAndCommitStringAttribute(
                      widget.section.sectionState, "Completed");
                });
              } else {
                showIncompleteSectionDialog(unanswered);
              }
            },
            child: const Text("Mark section as completed")),
      );
    } else {
      button = Container();
    }
    widget.isEditableController.setIsEditable(true);
    return Column(crossAxisAlignment: CrossAxisAlignment.stretch, children: [
      Expanded(
        child: widget.childBuilder(widget.isEditableController),
      ),
      button,
    ]);
  }

  @override
  Widget build(BuildContext context) {
    var section = widget.section;
    if (section.isLocked()) {
      return buildLockedPage(context);
    }
    return buildNormalPage();
  }

  void showIncompleteSectionDialog(int unansweredQuestions) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color.fromARGB(255, 41, 45, 52),
          title: const Text(
            'Incomplete section',
            style: TextStyle(color: Colors.white),
          ),
          content: unansweredQuestions == 1
              ? const Text("This section has one unanswered question.",
                  style: TextStyle(color: Colors.white))
              : Text(
                  "This section has " +
                      unansweredQuestions.toString() +
                      " unanswered questions.",
                  style: const TextStyle(color: Colors.white)),
          actions: [
            TextButton(
              child: const Text('Complete Anyway',
                  style: TextStyle(color: Colors.white)),
              onPressed: () {
                setState(() {
                  SectionUtils.shouldWarn = false;
                  BatchHelper.saveAndCommitStringAttribute(
                      widget.section.sectionState, "Completed");
                  Navigator.of(context).pop();
                });
              },
            ),
            ElevatedButton(
              child:
                  const Text('Cancel', style: TextStyle(color: Colors.white)),
              onPressed: () {
                SectionUtils.shouldWarn = true;
                setState(() {});
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }
}

class SectionUtils {
  static bool shouldWarn = false;

  static int getUnansweredQuestions(DataModelSection section) {
    var sectionChildren = section.getChildren().where((a) {
      return (a is DataModelSection || a is AttributeBase) &&
          a != section.sectionState;
    });

    int total = sectionChildren.length;
    int completed = 0;

    for (var child in sectionChildren) {
      if (child is DataModelSection) {
        var sectionState = child.sectionState.getValue();
        if ((sectionState == "Completed" || sectionState == "Skipped") &&
            !child.sectionState.getIsInErrorState()) {
          completed++;
        }
      } else if (child is AttributeBase) {
        if (child.hasData() && !child.getIsInErrorState()) completed++;
      }
    }

    return total - completed;
  }
}
