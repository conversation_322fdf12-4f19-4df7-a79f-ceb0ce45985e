//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionAsset_Walkdown_PPE_F
{
  public class SectionPersonnelAccessConditions : DataModelItem {

    public override String DisplayName { 
      get {
        return "Personnel Access Conditions";
      }
    }

    #region [-- Sub Sections --]

    #region [-- SectionStandingWater --]
    private SectionStandingWater _sectionStandingWater;
    public SectionStandingWater sectionStandingWater {
        get {
            if (_sectionStandingWater == null) {
               _sectionStandingWater = new SectionStandingWater(this);
            }

            return _sectionStandingWater;
        }
    }
    #endregion [-- SectionStandingWater --]
    
    #region [-- SectionOvergrownvegetation --]
    private SectionOvergrownvegetation _sectionOvergrownvegetation;
    public SectionOvergrownvegetation sectionOvergrownvegetation {
        get {
            if (_sectionOvergrownvegetation == null) {
               _sectionOvergrownvegetation = new SectionOvergrownvegetation(this);
            }

            return _sectionOvergrownvegetation;
        }
    }
    #endregion [-- SectionOvergrownvegetation --]
    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public MultiPredefinedValueAttribute attributeConditions_observed_on_site;
    public PredefinedValueAttribute attributePower_available;
    public PredefinedValueAttribute attributeWater_available;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionPersonnelAccessConditions";

    public SectionPersonnelAccessConditions(DataModelItem parent) : base(parent)
    {
            
        attributeConditions_observed_on_site = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Clean", null),
          new PredefinedValueOption("Dirty", null),
          new PredefinedValueOption("Debris", null),
          new PredefinedValueOption("Spills", null),
          new PredefinedValueOption("Dry", null),
          new PredefinedValueOption("Mud", null)
        }, true, this, "Conditions observed on site", databaseName: "PPEAW_Q100"); 
     
        attributePower_available = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Power available", databaseName: "PPEAW_Q113"); 
     
        attributeWater_available = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Water available?", databaseName: "PPEAW_Q114"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           sectionStandingWater,
           sectionOvergrownvegetation,
           attributeConditions_observed_on_site,
           attributePower_available,
           attributeWater_available,
        };
    }
  }
}
