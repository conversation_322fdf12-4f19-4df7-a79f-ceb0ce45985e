//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Int_PV_ALL_F
{
  public class Section510_Int_PV_ALL_F : DataModelItem {

    public override String DisplayName { 
      get {
        return "510-Int-PV-ALL-F";
      }
    }

    #region [-- Sub Sections --]

    #region [-- Section510INT_PVCKLSTSEC001 --]
    private Section510INT_PVCKLSTSEC001 _section510INT_PVCKLSTSEC001;
    public Section510INT_PVCKLSTSEC001 section510INT_PVCKLSTSEC001 {
        get {
            if (_section510INT_PVCKLSTSEC001 == null) {
               _section510INT_PVCKLSTSEC001 = new Section510INT_PVCKLSTSEC001(this);
            }

            return _section510INT_PVCKLSTSEC001;
        }
    }
    #endregion [-- Section510INT_PVCKLSTSEC001 --]
    
    #region [-- Section510INT_PVCKLSTSEC002 --]
    private Section510INT_PVCKLSTSEC002 _section510INT_PVCKLSTSEC002;
    public Section510INT_PVCKLSTSEC002 section510INT_PVCKLSTSEC002 {
        get {
            if (_section510INT_PVCKLSTSEC002 == null) {
               _section510INT_PVCKLSTSEC002 = new Section510INT_PVCKLSTSEC002(this);
            }

            return _section510INT_PVCKLSTSEC002;
        }
    }
    #endregion [-- Section510INT_PVCKLSTSEC002 --]
    
    #region [-- Section510INT_PVCKLSTSEC003 --]
    private Section510INT_PVCKLSTSEC003 _section510INT_PVCKLSTSEC003;
    public Section510INT_PVCKLSTSEC003 section510INT_PVCKLSTSEC003 {
        get {
            if (_section510INT_PVCKLSTSEC003 == null) {
               _section510INT_PVCKLSTSEC003 = new Section510INT_PVCKLSTSEC003(this);
            }

            return _section510INT_PVCKLSTSEC003;
        }
    }
    #endregion [-- Section510INT_PVCKLSTSEC003 --]
    
    #region [-- Section510INT_PVCKLSTSEC004 --]
    private Section510INT_PVCKLSTSEC004 _section510INT_PVCKLSTSEC004;
    public Section510INT_PVCKLSTSEC004 section510INT_PVCKLSTSEC004 {
        get {
            if (_section510INT_PVCKLSTSEC004 == null) {
               _section510INT_PVCKLSTSEC004 = new Section510INT_PVCKLSTSEC004(this);
            }

            return _section510INT_PVCKLSTSEC004;
        }
    }
    #endregion [-- Section510INT_PVCKLSTSEC004 --]
    
    #region [-- Section510INT_PVCKLSTSEC005 --]
    private Section510INT_PVCKLSTSEC005 _section510INT_PVCKLSTSEC005;
    public Section510INT_PVCKLSTSEC005 section510INT_PVCKLSTSEC005 {
        get {
            if (_section510INT_PVCKLSTSEC005 == null) {
               _section510INT_PVCKLSTSEC005 = new Section510INT_PVCKLSTSEC005(this);
            }

            return _section510INT_PVCKLSTSEC005;
        }
    }
    #endregion [-- Section510INT_PVCKLSTSEC005 --]
    
    #region [-- Section510INT_PVCKLSTSEC006 --]
    private Section510INT_PVCKLSTSEC006 _section510INT_PVCKLSTSEC006;
    public Section510INT_PVCKLSTSEC006 section510INT_PVCKLSTSEC006 {
        get {
            if (_section510INT_PVCKLSTSEC006 == null) {
               _section510INT_PVCKLSTSEC006 = new Section510INT_PVCKLSTSEC006(this);
            }

            return _section510INT_PVCKLSTSEC006;
        }
    }
    #endregion [-- Section510INT_PVCKLSTSEC006 --]
    
    #region [-- Section510INT_PVCKLSTSEC007 --]
    private Section510INT_PVCKLSTSEC007 _section510INT_PVCKLSTSEC007;
    public Section510INT_PVCKLSTSEC007 section510INT_PVCKLSTSEC007 {
        get {
            if (_section510INT_PVCKLSTSEC007 == null) {
               _section510INT_PVCKLSTSEC007 = new Section510INT_PVCKLSTSEC007(this);
            }

            return _section510INT_PVCKLSTSEC007;
        }
    }
    #endregion [-- Section510INT_PVCKLSTSEC007 --]
    
    #region [-- Section510INT_PVCKLSTSEC008 --]
    private Section510INT_PVCKLSTSEC008 _section510INT_PVCKLSTSEC008;
    public Section510INT_PVCKLSTSEC008 section510INT_PVCKLSTSEC008 {
        get {
            if (_section510INT_PVCKLSTSEC008 == null) {
               _section510INT_PVCKLSTSEC008 = new Section510INT_PVCKLSTSEC008(this);
            }

            return _section510INT_PVCKLSTSEC008;
        }
    }
    #endregion [-- Section510INT_PVCKLSTSEC008 --]
    
    #region [-- Section510INT_PVCKLSTSEC009 --]
    private Section510INT_PVCKLSTSEC009 _section510INT_PVCKLSTSEC009;
    public Section510INT_PVCKLSTSEC009 section510INT_PVCKLSTSEC009 {
        get {
            if (_section510INT_PVCKLSTSEC009 == null) {
               _section510INT_PVCKLSTSEC009 = new Section510INT_PVCKLSTSEC009(this);
            }

            return _section510INT_PVCKLSTSEC009;
        }
    }
    #endregion [-- Section510INT_PVCKLSTSEC009 --]
    
    #region [-- Section510INT_PVCKLSTSEC010 --]
    private Section510INT_PVCKLSTSEC010 _section510INT_PVCKLSTSEC010;
    public Section510INT_PVCKLSTSEC010 section510INT_PVCKLSTSEC010 {
        get {
            if (_section510INT_PVCKLSTSEC010 == null) {
               _section510INT_PVCKLSTSEC010 = new Section510INT_PVCKLSTSEC010(this);
            }

            return _section510INT_PVCKLSTSEC010;
        }
    }
    #endregion [-- Section510INT_PVCKLSTSEC010 --]
    
    #region [-- Section510INT_PVCKLSTSEC011 --]
    private Section510INT_PVCKLSTSEC011 _section510INT_PVCKLSTSEC011;
    public Section510INT_PVCKLSTSEC011 section510INT_PVCKLSTSEC011 {
        get {
            if (_section510INT_PVCKLSTSEC011 == null) {
               _section510INT_PVCKLSTSEC011 = new Section510INT_PVCKLSTSEC011(this);
            }

            return _section510INT_PVCKLSTSEC011;
        }
    }
    #endregion [-- Section510INT_PVCKLSTSEC011 --]
    
    #region [-- Section510INT_PVCKLSTSEC012 --]
    private Section510INT_PVCKLSTSEC012 _section510INT_PVCKLSTSEC012;
    public Section510INT_PVCKLSTSEC012 section510INT_PVCKLSTSEC012 {
        get {
            if (_section510INT_PVCKLSTSEC012 == null) {
               _section510INT_PVCKLSTSEC012 = new Section510INT_PVCKLSTSEC012(this);
            }

            return _section510INT_PVCKLSTSEC012;
        }
    }
    #endregion [-- Section510INT_PVCKLSTSEC012 --]
    
    #region [-- Section510INT_PVCKLSTSEC013 --]
    private Section510INT_PVCKLSTSEC013 _section510INT_PVCKLSTSEC013;
    public Section510INT_PVCKLSTSEC013 section510INT_PVCKLSTSEC013 {
        get {
            if (_section510INT_PVCKLSTSEC013 == null) {
               _section510INT_PVCKLSTSEC013 = new Section510INT_PVCKLSTSEC013(this);
            }

            return _section510INT_PVCKLSTSEC013;
        }
    }
    #endregion [-- Section510INT_PVCKLSTSEC013 --]
    
    #region [-- Section510INT_PVCKLSTSEC014 --]
    private Section510INT_PVCKLSTSEC014 _section510INT_PVCKLSTSEC014;
    public Section510INT_PVCKLSTSEC014 section510INT_PVCKLSTSEC014 {
        get {
            if (_section510INT_PVCKLSTSEC014 == null) {
               _section510INT_PVCKLSTSEC014 = new Section510INT_PVCKLSTSEC014(this);
            }

            return _section510INT_PVCKLSTSEC014;
        }
    }
    #endregion [-- Section510INT_PVCKLSTSEC014 --]
    
    #region [-- Section510INT_PVCKLSTSEC015 --]
    private Section510INT_PVCKLSTSEC015 _section510INT_PVCKLSTSEC015;
    public Section510INT_PVCKLSTSEC015 section510INT_PVCKLSTSEC015 {
        get {
            if (_section510INT_PVCKLSTSEC015 == null) {
               _section510INT_PVCKLSTSEC015 = new Section510INT_PVCKLSTSEC015(this);
            }

            return _section510INT_PVCKLSTSEC015;
        }
    }
    #endregion [-- Section510INT_PVCKLSTSEC015 --]
    
    #region [-- Section510INT_PVCKLSTSEC016 --]
    private Section510INT_PVCKLSTSEC016 _section510INT_PVCKLSTSEC016;
    public Section510INT_PVCKLSTSEC016 section510INT_PVCKLSTSEC016 {
        get {
            if (_section510INT_PVCKLSTSEC016 == null) {
               _section510INT_PVCKLSTSEC016 = new Section510INT_PVCKLSTSEC016(this);
            }

            return _section510INT_PVCKLSTSEC016;
        }
    }
    #endregion [-- Section510INT_PVCKLSTSEC016 --]
    
    #region [-- Section510INT_PVCKLSTSEC017 --]
    private Section510INT_PVCKLSTSEC017 _section510INT_PVCKLSTSEC017;
    public Section510INT_PVCKLSTSEC017 section510INT_PVCKLSTSEC017 {
        get {
            if (_section510INT_PVCKLSTSEC017 == null) {
               _section510INT_PVCKLSTSEC017 = new Section510INT_PVCKLSTSEC017(this);
            }

            return _section510INT_PVCKLSTSEC017;
        }
    }
    #endregion [-- Section510INT_PVCKLSTSEC017 --]
    
    #region [-- Section510INT_PVCKLSTSEC018 --]
    private Section510INT_PVCKLSTSEC018 _section510INT_PVCKLSTSEC018;
    public Section510INT_PVCKLSTSEC018 section510INT_PVCKLSTSEC018 {
        get {
            if (_section510INT_PVCKLSTSEC018 == null) {
               _section510INT_PVCKLSTSEC018 = new Section510INT_PVCKLSTSEC018(this);
            }

            return _section510INT_PVCKLSTSEC018;
        }
    }
    #endregion [-- Section510INT_PVCKLSTSEC018 --]
    
    #region [-- Section510INT_PVCKLSTSEC019 --]
    private Section510INT_PVCKLSTSEC019 _section510INT_PVCKLSTSEC019;
    public Section510INT_PVCKLSTSEC019 section510INT_PVCKLSTSEC019 {
        get {
            if (_section510INT_PVCKLSTSEC019 == null) {
               _section510INT_PVCKLSTSEC019 = new Section510INT_PVCKLSTSEC019(this);
            }

            return _section510INT_PVCKLSTSEC019;
        }
    }
    #endregion [-- Section510INT_PVCKLSTSEC019 --]
    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510_Int_PV_ALL_F";

    public Section510_Int_PV_ALL_F(DataModelItem parent) : base(parent)
    {
            
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           section510INT_PVCKLSTSEC001,
           section510INT_PVCKLSTSEC002,
           section510INT_PVCKLSTSEC003,
           section510INT_PVCKLSTSEC004,
           section510INT_PVCKLSTSEC005,
           section510INT_PVCKLSTSEC006,
           section510INT_PVCKLSTSEC007,
           section510INT_PVCKLSTSEC008,
           section510INT_PVCKLSTSEC009,
           section510INT_PVCKLSTSEC010,
           section510INT_PVCKLSTSEC011,
           section510INT_PVCKLSTSEC012,
           section510INT_PVCKLSTSEC013,
           section510INT_PVCKLSTSEC014,
           section510INT_PVCKLSTSEC015,
           section510INT_PVCKLSTSEC016,
           section510INT_PVCKLSTSEC017,
           section510INT_PVCKLSTSEC018,
           section510INT_PVCKLSTSEC019,
        };
    }
  }
}
