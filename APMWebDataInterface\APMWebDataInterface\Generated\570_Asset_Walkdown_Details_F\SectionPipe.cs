//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section570_Asset_Walkdown_Details_F
{
  public class SectionPipe : DataModelCollectionItem {

    public override String DisplayName { 
      get {
        return "Pipe";
      }
    }
    
    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public StringAttribute attributeLine_No_;
    public PhotoAttribute attributePhotos;
    public StringAttribute attributeMaterial_Spec_and_Grade;
    public DoubleAttribute attributeAllowable_Stress_at_Temperature;
    public DoubleAttribute attributeNominal_Thickness_schedule;
    public DoubleAttribute attributeCorrosion_Allowance;
    public DoubleAttribute attributeJoint_Efficiency;
    public StringAttribute attributePipe_Spec_Number;

    #endregion [-- Attributes --]

    public SectionPipe(String id, DataModelItem parent) : base(id, parent)
    {
            
        attributeLine_No_ = new StringAttribute(this, displayName: "Line No.", databaseName: "570AW_Q306"); 
     
        attributePhotos = new PhotoAttribute(this, displayName: "Photos", databaseName: "570AW_Q313"); 
     
        attributeMaterial_Spec_and_Grade = new StringAttribute(this, displayName: "Material Spec and Grade", databaseName: "570AW_Q307"); 
     
        attributeAllowable_Stress_at_Temperature = new DoubleAttribute(this, displayName: "Allowable Stress at Temperature", databaseName: "570AW_Q308", areCommentsRequired: false, displayUnit: "psi", allowNegatives: true); 
     
        attributeNominal_Thickness_schedule = new DoubleAttribute(this, displayName: "Nominal Thickness (schedule)", databaseName: "570AW_Q309", areCommentsRequired: false, displayUnit: "in", allowNegatives: true); 
     
        attributeCorrosion_Allowance = new DoubleAttribute(this, displayName: "Corrosion Allowance", databaseName: "570AW_Q310", areCommentsRequired: false, displayUnit: "in", allowNegatives: true); 
     
        attributeJoint_Efficiency = new DoubleAttribute(this, displayName: "Joint Efficiency", databaseName: "570AW_Q311", areCommentsRequired: false, displayUnit: null, allowNegatives: true); 
     
        attributePipe_Spec_Number = new StringAttribute(this, displayName: "Pipe Spec Number", databaseName: "570AW_Q312"); 
    }   
    
    public override DataModelItem[] GetChildren() {
      var parentChildren = base.GetChildren();

      return parentChildren.Concat(new DataModelItem[] { 
        attributeLine_No_,
           attributePhotos,
           attributeMaterial_Spec_and_Grade,
           attributeAllowable_Stress_at_Temperature,
           attributeNominal_Thickness_schedule,
           attributeCorrosion_Allowance,
           attributeJoint_Efficiency,
           attributePipe_Spec_Number,
      }).ToArray();
    }
  }
}
