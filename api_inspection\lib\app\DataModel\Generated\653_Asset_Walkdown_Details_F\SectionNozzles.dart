//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';

// ignore: camel_case_types
class SectionNozzles extends DataModelCollectionItem {
  @override
  String getDisplayName() => "Nozzles";

  SectionNozzles(String id, DataModelItem parent) : super(id, parent);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeNumber = StringAttribute(
      parent: this,
      displayName: "Number",
      databaseName: "653AW_Q451",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late PhotoAttribute attributePhotos = PhotoAttribute(
      parent: this,
      displayName: "Photos",
      databaseName: "653AW_Q460",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeType = StringAttribute(
      parent: this,
      displayName: "Type",
      databaseName: "653AW_Q452",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeMaterial_Spec_and_Grade = StringAttribute(
      parent: this,
      displayName: "Material Spec and Grade",
      databaseName: "653AW_Q453",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributePipe_Size =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Pipe Size",
          databaseName: "653AW_Q454",
          availableOptions: [
        PredefinedValueOption("0.5", null, isCommentRequired: false),
        PredefinedValueOption("0.75", null, isCommentRequired: false),
        PredefinedValueOption("1", null, isCommentRequired: false),
        PredefinedValueOption("1.25", null, isCommentRequired: false),
        PredefinedValueOption("1.5", null, isCommentRequired: false),
        PredefinedValueOption("2", null, isCommentRequired: false),
        PredefinedValueOption("2.5", null, isCommentRequired: false),
        PredefinedValueOption("3", null, isCommentRequired: false),
        PredefinedValueOption("3.5", null, isCommentRequired: false),
        PredefinedValueOption("4", null, isCommentRequired: false),
        PredefinedValueOption("4.5", null, isCommentRequired: false),
        PredefinedValueOption("5", null, isCommentRequired: false),
        PredefinedValueOption("6", null, isCommentRequired: false),
        PredefinedValueOption("8", null, isCommentRequired: false),
        PredefinedValueOption("10", null, isCommentRequired: false),
        PredefinedValueOption("12", null, isCommentRequired: false),
        PredefinedValueOption("14", null, isCommentRequired: false),
        PredefinedValueOption("16", null, isCommentRequired: false),
        PredefinedValueOption("18", null, isCommentRequired: false),
        PredefinedValueOption("20", null, isCommentRequired: false),
        PredefinedValueOption("24", null, isCommentRequired: false),
        PredefinedValueOption("30", null, isCommentRequired: false),
        PredefinedValueOption("36", null, isCommentRequired: false),
        PredefinedValueOption("42", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributePipe_Schedule =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Pipe Schedule",
          databaseName: "653AW_Q455",
          availableOptions: [
        PredefinedValueOption("5", null, isCommentRequired: false),
        PredefinedValueOption("10", null, isCommentRequired: false),
        PredefinedValueOption("20", null, isCommentRequired: false),
        PredefinedValueOption("30", null, isCommentRequired: false),
        PredefinedValueOption("40", null, isCommentRequired: false),
        PredefinedValueOption("50", null, isCommentRequired: false),
        PredefinedValueOption("60", null, isCommentRequired: false),
        PredefinedValueOption("70", null, isCommentRequired: false),
        PredefinedValueOption("80", null, isCommentRequired: false),
        PredefinedValueOption("100", null, isCommentRequired: false),
        PredefinedValueOption("120", null, isCommentRequired: false),
        PredefinedValueOption("140", null, isCommentRequired: false),
        PredefinedValueOption("160", null, isCommentRequired: false),
        PredefinedValueOption("STD", null, isCommentRequired: false),
        PredefinedValueOption("EH", null, isCommentRequired: false),
        PredefinedValueOption("DBL.EH", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeFlange_Rating =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Flange Rating",
          databaseName: "653AW_Q456",
          availableOptions: [
        PredefinedValueOption("150", null, isCommentRequired: false),
        PredefinedValueOption("300", null, isCommentRequired: false),
        PredefinedValueOption("400", null, isCommentRequired: false),
        PredefinedValueOption("600", null, isCommentRequired: false),
        PredefinedValueOption("900", null, isCommentRequired: false),
        PredefinedValueOption("1500", null, isCommentRequired: false),
        PredefinedValueOption("2500", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeReinforcement_pad_type = StringAttribute(
      parent: this,
      displayName: "Reinforcement pad type",
      databaseName: "653AW_Q457",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeReinforcement_pad_dimensions = StringAttribute(
      parent: this,
      displayName: "Reinforcement pad dimensions",
      databaseName: "653AW_Q458",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeReinforcement_pad_thickness = DoubleAttribute(
    parent: this,
    displayName: "Reinforcement pad thickness",
    databaseName: "653AW_Q459",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "in",
  );

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeNumber,
      attributePhotos,
      attributeType,
      attributeMaterial_Spec_and_Grade,
      attributePipe_Size,
      attributePipe_Schedule,
      attributeFlange_Rating,
      attributeReinforcement_pad_type,
      attributeReinforcement_pad_dimensions,
      attributeReinforcement_pad_thickness,
    ]);
    return children;
  }
}
