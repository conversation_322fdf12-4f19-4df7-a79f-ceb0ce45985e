import 'package:api_inspection/app/APMRoot.dart';
import 'package:api_inspection/generic/AppStyle.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/project.dart';
import 'package:api_inspection/app/UI/Projects/ProjectEditPage.dart';
import 'package:api_inspection/generic/UIControls/SideSlideoutControl.dart';

class ProjectCardView extends StatefulWidget {
  final Project project;
  const ProjectCardView(this.project, {Key? key}) : super(key: key);

  @override
  _ProjectCardViewState createState() => _ProjectCardViewState();
}

class _ProjectCardViewState extends State<ProjectCardView> {
  final SlideOutController _controller = SlideOutController();

  void projectCardPressed(BuildContext context) {
    var root = APMRoot.global;

    var project = widget.project;
    setState(() {
      if (root.queries.selectedProjects.isProjectSelected(project)) {
        root.queries.selectedProjects.removeSelectedProject(project);
      } else {
        root.queries.selectedProjects.addSelectedProject(project);
      }
    });
  }

  void projectEditPressed() {
    var project = widget.project;

    Navigator.push(context,
            MaterialPageRoute(builder: (context) => ProjectEditPage(project)))
        .then((value) => setState(() {}));
    _controller.closeSlideOuts();
  }

  Widget buildEditSliderButton(BuildContext context) {
    return SizedBox(
        height: 110,
        width: 110,
        child: ElevatedButton(
            onPressed: projectEditPressed,
            child: const Text(
              "Edit",
              style: TextStyle(fontSize: 18),
            ),
            style: ButtonStyle(
              backgroundColor: MaterialStateProperty.resolveWith<Color>(
                  (Set<MaterialState> states) {
                return Colors.green[800]!;
              }),
            )));
  }

  Widget buildCardInterior(BuildContext context, Color cardColor) {
    var project = widget.project;

    String? apmNumber = project.accountingDetails.apmProjectNumber.getValue();
    var apmNumberDisplay = "APM Number: " + (apmNumber ?? "Not Set");

    String? description = project.description.getValue();
    var descriptionDisplay = description ?? "No Description";

    String? name = project.name.getValue();
    var titleDisplay = "Chevron: " + (name ?? "Project Name Not Set");

    String? clientWorkOrder =
        project.accountingDetails.workOrderNumber.getValue();
    String clientWorkOrderDisplay =
        "Client Work Order: " + (clientWorkOrder ?? "Not Set");

    String locationDisplay =
        project.location.name.getValue() ?? "Location name is not set";

    var interior = Container(
      key: const ValueKey('ContainerCard'),
      margin: const EdgeInsets.fromLTRB(5, 10, 5, 10),
      child: Column(children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
                child: Container(
              margin: const EdgeInsets.fromLTRB(5, 0, 5, 0),
              height: AppStyle.global.pixels20,
              child: FittedBox(
                fit: BoxFit.contain,
                child: Text(titleDisplay,
                    style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white),
                    key: const ValueKey("titleDisplay")),
              ),
            )),
          ],
        ),
        Container(height: 5),
        Text(descriptionDisplay,
            style: const TextStyle(fontSize: 14, color: Colors.white),
            key: const ValueKey('descriptionDisplay')),
        Text(locationDisplay,
            style: const TextStyle(fontSize: 14, color: Colors.white),
            key: const ValueKey('locationDisplay')),
        Text(apmNumberDisplay,
            style: const TextStyle(fontSize: 14, color: Colors.white),
            key: const ValueKey('apmNumberDisplay')),
        Text(clientWorkOrderDisplay,
            style: const TextStyle(fontSize: 14, color: Colors.white),
            key: const ValueKey('clientWorkOrderDisplay')),
      ]),
    );

    double cardWidth = MediaQuery.of(context).size.width - 40;
    return Column(children: [
      ElevatedButton(
          style: ButtonStyle(
              backgroundColor: MaterialStateProperty.resolveWith<Color?>(
            (Set<MaterialState> states) {
              return cardColor;
            },
          )),
          onPressed: () {
            projectCardPressed(context);
          },
          child: SizedBox(width: cardWidth, child: interior))
    ]);
  }

  @override
  Widget build(BuildContext context) {
    var project = widget.project;

    var root = APMRoot.global;
    bool isSelected = root.queries.selectedProjects.isProjectSelected(project);
    Color cardColor = isSelected
        ? Colors.blueGrey[700]!
        : const Color.fromARGB(255, 41, 45, 52);

    Widget cardInterior = buildCardInterior(context, cardColor);

    Widget cardContainer;
    if (isSelected) {
      cardContainer = Container(
          decoration: const BoxDecoration(
            border: Border(
              top: BorderSide(width: 2, color: Colors.white),
              left: BorderSide(width: 2, color: Colors.white),
              right: BorderSide(width: 2, color: Colors.white),
              bottom: BorderSide(width: 2, color: Colors.white),
            ),
          ),
          child: cardInterior);
    } else {
      cardContainer =
          Container(margin: const EdgeInsets.all(2), child: cardInterior);
    }

    return SideSlideoutControl(
        controller: _controller,
        rightSliderWidget: buildEditSliderButton,
        child: Card(
            margin: const EdgeInsets.all(10),
            color: cardColor,
            child: cardContainer));
  }
}
