import 'package:api_inspection/app/batch_helper.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
import 'package:api_inspection/generic/UIControls/NumericTextField.dart';

class DoubleAttributeTextField extends StatefulWidget {
  final DoubleAttribute _attribute;

  final InputDecoration? decoration;
  final TextStyle? textStyle;

  const DoubleAttributeTextField(this._attribute,
      {Key? key, this.decoration, this.textStyle})
      : super(key: key);

  @override
  _DoubleAttributeTextFieldState createState() =>
      _DoubleAttributeTextFieldState();
}

class _DoubleAttributeTextFieldState extends State<DoubleAttributeTextField> {
  void updateAttributeValue() {
    TextEditingController? controller = _controller;

    if (controller != null) {
      double? parsedValue = double.tryParse(controller.text);
      widget._attribute.setValue(parsedValue);
      BatchHelper.saveAndCommit(widget._attribute);
    }
  }

  TextEditingController? _controller;

  bool initialized = false;
  void initialize() {
    if (initialized) return;
    initialized = true;
    DoubleAttribute attribute = widget._attribute;
    attribute.addListener(onAttributeChanged);
  }

  bool areValuesEqual(String? str, double? value) {
    if (str == null && value == null) return true;
    if (str != null && str.isEmpty && value == null) return true;
    if (str == null || value == null) return false;

    double? strAsValue = double.tryParse(str);
    return strAsValue == value;
  }

  String doubleToString(double? value) {
    if (value == null) {
      return "";
    }
    return value.toString();
  }

  void onAttributeChanged() {
    setState(() {
      TextEditingController? controller = _controller;
      double? attributeValue = widget._attribute.getValue();
      if (controller != null &&
          !areValuesEqual(controller.text, attributeValue)) {
        controller.text =
            attributeValue == null ? "" : doubleToString(attributeValue);
      }
    });
  }

  @override
  void dispose() {
    DoubleAttribute attribute = widget._attribute;
    attribute.removeListener(onAttributeChanged);
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    initialize();

    _controller ??= TextEditingController(
        text: doubleToString(widget._attribute.getValue()));

    return NumericTextField(
        _controller!, widget._attribute.allowNegatives, true, (String? value) {
      updateAttributeValue();
    }, textStyle: widget.textStyle, decoration: widget.decoration);
  }
}
