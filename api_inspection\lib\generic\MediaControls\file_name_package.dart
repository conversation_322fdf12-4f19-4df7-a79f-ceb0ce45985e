import 'package:api_inspection/generic/MediaControls/MediaEntry.dart';

class FileNamePackage {
  late String fileName;
  late String extension;
  late String databasePath;
  late int fileVersion;

  String get fullName => fileName + '.' + extension;

  FileNamePackage(
      this.fileName, this.extension, this.databasePath, this.fileVersion);

  FileNamePackage.fromEntry(MediaEntry entry) {
    fileName = entry.mediaName;
    extension = entry.extension;
    databasePath = entry.getDBPath();
    fileVersion = entry.version ?? 0;
  }

  FileNamePackage.fromName(String name) {
    var parts = name.split('^');
    databasePath = parts[0].split(':')[1];
    fileVersion = int.parse(parts[1].split(':')[1]);

    var fullFileName = parts[2];
    var dotIndex = fullFileName.lastIndexOf('.');
    fileName = fullFileName.substring(0, dotIndex);
    extension = fullFileName.substring(dotIndex + 1);
  }

  String buildFileName() {
    var name = "DBPath:" +
        databasePath +
        "^V:" +
        fileVersion.toString() +
        '^' +
        fileName +
        '.' +
        extension;
    return name;
  }
}
