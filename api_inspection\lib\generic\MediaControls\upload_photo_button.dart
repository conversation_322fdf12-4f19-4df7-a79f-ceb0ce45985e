import 'package:api_inspection/app/APMRoot.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/AttributeBase.dart';
import 'package:api_inspection/generic/MediaControls/MediaSynchronizer.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';

import '../DataModelCommon/PhotoRoot.dart';

class UploadPhotoButtonWidget extends StatefulWidget {
  final AttributeBase attribute;

  const UploadPhotoButtonWidget(this.attribute, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _UploadPhotoButtonWidgetState();
}

class _UploadPhotoButtonWidgetState extends State<UploadPhotoButtonWidget> {
  @override
  Widget build(BuildContext context) {
    return TextButton(
        onPressed: onUploadPhotoClicked,
        child: Container(
          width: 65,
          height: 65,
          padding: const EdgeInsets.all(20),
          decoration: const BoxDecoration(
            color: Color.fromARGB(255, 41, 44, 52),
            borderRadius: BorderRadius.all(Radius.circular(8)),
          ),
          child: const FittedBox(
              fit: BoxFit.fill,
              child: Icon(
                Icons.folder_open,
                color: Colors.grey,
              )),
        ));
  }

  void onUploadPhotoClicked() async {
    var result = await FilePicker.platform
        .pickFiles(type: FileType.image, allowMultiple: true);
    if (result == null) return;

    await APMRoot.global.newQueries.pause();
    var batch = FirebaseFirestore.instance.batch();
    for (var file in result.files) {
      var path = file.path;
      if (path != null) {
        await IMediaSynchronizer.getMediaSynchronizer()
            .enqueuePhotoUpload(file, widget.attribute, batch);
      }
    }

    var photoRoot = widget.attribute.findParentOfType<PhotoRoot>();
    if (photoRoot == null) throw "Could not find photo root";
    photoRoot.saveItem(batch);
    widget.attribute.saveItem(batch);

    IMediaSynchronizer.getMediaSynchronizer().uploadPendingMedia(batch);

    await batch.commit();

    await APMRoot.global.newQueries.resume();
  }
}
