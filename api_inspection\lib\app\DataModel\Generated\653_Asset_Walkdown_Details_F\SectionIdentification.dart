//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class SectionIdentification extends DataModelSection {
  @override
  String getDisplayName() => "Identification";
  SectionIdentification(DataModelItem? parent)
      : super(parent: parent, sectionName: "Identification");

  // ignore: non_constant_identifier_names
  late StringAttribute attributeName = StringAttribute(
      parent: this,
      displayName: "Name",
      databaseName: "653AW_Q001",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeNumber_or_ID = StringAttribute(
      parent: this,
      displayName: "Number or ID",
      databaseName: "653AW_Q002",
      areCommentsRequired: false,
      isQueryable: true);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeAsset_Type = StringAttribute(
      parent: this,
      displayName: "Asset Type",
      databaseName: "653AW_Q003",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeEquipment_Description = StringAttribute(
      parent: this,
      displayName: "Equipment Description",
      databaseName: "653AW_Q004",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late DateAttribute attributeLast_known_inspection_date = DateAttribute(
      parent: this,
      displayName: "Last known inspection date",
      databaseName: "653AW_Q005",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeLocation = PredefinedValueAttribute(
      parent: this,
      hasOtherOption: false,
      displayName: "Location",
      databaseName: "653AW_Q006",
      availableOptions: [
        PredefinedValueOption("On-Plot (Facility)", null,
            isCommentRequired: false),
        PredefinedValueOption("Off-Plot (Field)", null,
            isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late LocationAttribute attributeGIS_Location = LocationAttribute(
      parent: this,
      displayName: "GIS Location",
      databaseName: "653AW_Q007",
      areCommentsRequired: false);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeName,
      attributeNumber_or_ID,
      attributeAsset_Type,
      attributeEquipment_Description,
      attributeLast_known_inspection_date,
      attributeLocation,
      attributeGIS_Location,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionIdentification";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
