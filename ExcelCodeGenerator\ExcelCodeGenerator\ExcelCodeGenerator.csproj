﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{270DE45A-F50A-4FE6-AB4C-C73329029BA7}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>ExcelCodeGenerator</RootNamespace>
    <AssemblyName>ExcelCodeGenerator</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{60dc8134-eba5-43b8-bcc9-bb4bc16c2548};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <WarningLevel>4</WarningLevel>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ExcelDataReader, Version=3.6.0.0, Culture=neutral, PublicKeyToken=93517dbe6a4012fa, processorArchitecture=MSIL">
      <HintPath>..\packages\ExcelDataReader.3.6.0\lib\net45\ExcelDataReader.dll</HintPath>
    </Reference>
    <Reference Include="ExcelDataReader.DataSet, Version=3.6.0.0, Culture=neutral, PublicKeyToken=93517dbe6a4012fa, processorArchitecture=MSIL">
      <HintPath>..\packages\ExcelDataReader.DataSet.3.6.0\lib\net35\ExcelDataReader.DataSet.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAPICodePack, Version=1.1.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.WindowsAPICodePack-Core.1.1.0.2\lib\Microsoft.WindowsAPICodePack.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAPICodePack.Shell, Version=1.1.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.WindowsAPICodePack-Shell.1.1.0.0\lib\Microsoft.WindowsAPICodePack.Shell.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAPICodePack.ShellExtensions, Version=1.1.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.WindowsAPICodePack-Shell.1.1.0.0\lib\Microsoft.WindowsAPICodePack.ShellExtensions.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.Xml" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xaml">
      <RequiredTargetFramework>4.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
  </ItemGroup>
  <ItemGroup>
    <ApplicationDefinition Include="App.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </ApplicationDefinition>
    <Compile Include="CodeGenerators\CSharp\AttributeDataGenerators\AttributeGeneratorFactory.cs" />
    <Compile Include="CodeGenerators\CSharp\AttributeDataGenerators\IndividualTypes\PhoneNumberAttributeGenerator.cs" />
    <Compile Include="CodeGenerators\CSharp\AttributeDataGenerators\IndividualTypes\DecimalAttributeGenerator.cs" />
    <Compile Include="CodeGenerators\CSharp\AttributeDataGenerators\IndividualTypes\IntegerAttributeGenerator.cs" />
    <Compile Include="CodeGenerators\CSharp\AttributeDataGenerators\IndividualTypes\BooleanAttributeGenerator.cs" />
    <Compile Include="CodeGenerators\CSharp\AttributeDataGenerators\IndividualTypes\DateAttributeGenerator.cs" />
    <Compile Include="CodeGenerators\CSharp\AttributeDataGenerators\IndividualTypes\MultiPredefinedValueAttributeGenerator.cs" />
    <Compile Include="CodeGenerators\CSharp\AttributeDataGenerators\IndividualTypes\PhotoAttributeGenerator.cs" />
    <Compile Include="CodeGenerators\CSharp\AttributeDataGenerators\IndividualTypes\CoordinateAttributeGenerator.cs" />
    <Compile Include="CodeGenerators\CSharp\CSharpCodeGenerator.cs" />
    <Compile Include="CodeGenerators\CSharp\AttributeDataGenerators\IAttributeGenerator.cs" />
    <Compile Include="CodeGenerators\CSharp\AttributeDataGenerators\IndividualTypes\PredefinedValueAttributeGenerator.cs" />
    <Compile Include="CodeGenerators\CSharp\AttributeDataGenerators\IndividualTypes\StringAttributeGenerator.cs" />
    <Compile Include="CodeGenerators\Dart\DartDataFileGenerator.cs" />
    <Compile Include="CodeGenerators\Dart\DartUIGenerator.cs" />
    <Compile Include="Config.cs" />
    <Compile Include="DataFormat\Choice.cs" />
    <Compile Include="Helpers.cs" />
    <Compile Include="DataFormat\IChildItem.cs" />
    <Compile Include="DataFormat\Question.cs" />
    <Compile Include="DataFormat\Section.cs" />
    <Page Include="UI\MainWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Compile Include="App.xaml.cs">
      <DependentUpon>App.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="CodeGeneratorMain.cs" />
    <Compile Include="CodeGenerators\Dart\DartCodeGenerator.cs" />
    <Compile Include="ExcelParser\ExcelForm.cs" />
    <Compile Include="ExcelParser\ExcelParser.cs" />
    <Compile Include="ExcelParser\ExcelQuestion.cs" />
    <Compile Include="ExcelParser\ExcelSection.cs" />
    <Compile Include="ExcelParser\IExcelParser.cs" />
    <Compile Include="ExcelParser\Version1Parser.cs" />
    <Compile Include="ExcelParser\Version2Parser.cs" />
    <Compile Include="UI\MainWindow.xaml.cs">
      <DependentUpon>MainWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>