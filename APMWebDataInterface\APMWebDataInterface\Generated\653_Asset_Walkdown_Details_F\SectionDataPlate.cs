//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section653_Asset_Walkdown_Details_F
{
  public class SectionDataPlate : DataModelItem {

    public override String DisplayName { 
      get {
        return "Data Plate";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributeAttached;
    public PredefinedValueAttribute attributeLegible;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionDataPlate";

    public SectionDataPlate(DataModelItem parent) : base(parent)
    {
            
        attributeAttached = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Attached", databaseName: "653AW_Q161"); 
     
        attributeLegible = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Legible", databaseName: "653AW_Q162"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeAttached,
           attributeLegible,
        };
    }
  }
}
