//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section653_Asset_Walkdown_Details_F
{
  public class SectionTankBottomFloor : DataModelItem {

    public override String DisplayName { 
      get {
        return "Tank Bottom (Floor)";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public StringAttribute attributeType;
    public StringAttribute attributeMaterial_Spec_and_Grade;
    public DoubleAttribute attributeNominal_thickness_annular_ring;
    public DoubleAttribute attributeNominal_thickness_sketch_plates;
    public DoubleAttribute attributeNominal_thickness_inner_plates;
    public DoubleAttribute attributeCorrosion_Allowance;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionTankBottomFloor";

    public SectionTankBottomFloor(DataModelItem parent) : base(parent)
    {
            
        attributeType = new StringAttribute(this, displayName: "Type", databaseName: "653AW_Q421"); 
     
        attributeMaterial_Spec_and_Grade = new StringAttribute(this, displayName: "Material Spec and Grade", databaseName: "653AW_Q422"); 
     
        attributeNominal_thickness_annular_ring = new DoubleAttribute(this, displayName: "Nominal thickness (annular ring)", databaseName: "653AW_Q423", areCommentsRequired: false, displayUnit: "in", allowNegatives: true); 
     
        attributeNominal_thickness_sketch_plates = new DoubleAttribute(this, displayName: "Nominal thickness (sketch plates)", databaseName: "653AW_Q424", areCommentsRequired: false, displayUnit: "in", allowNegatives: true); 
     
        attributeNominal_thickness_inner_plates = new DoubleAttribute(this, displayName: "Nominal thickness (inner plates)", databaseName: "653AW_Q425", areCommentsRequired: false, displayUnit: "in", allowNegatives: true); 
     
        attributeCorrosion_Allowance = new DoubleAttribute(this, displayName: "Corrosion Allowance", databaseName: "653AW_Q426", areCommentsRequired: false, displayUnit: "in", allowNegatives: true); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeType,
           attributeMaterial_Spec_and_Grade,
           attributeNominal_thickness_annular_ring,
           attributeNominal_thickness_sketch_plates,
           attributeNominal_thickness_inner_plates,
           attributeCorrosion_Allowance,
        };
    }
  }
}
