﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.DataModel
{
    public class BusinessUnit : ConcretePhotoRoot
    {
        public override string DisplayName => "Business Unit: " + Name?.CurrentValue ?? "Unknown";
        

        public StringAttribute Name { get; set; }
        public String ClientId { get; set; }


        public BusinessUnit() : base(null)
        {
            InitializeAttributes();
        }

        internal BusinessUnit(string id) : base(id, null)
        {
            InitializeAttributes();
        }

        private void InitializeAttributes()
        {
            Name = new StringAttribute(this, "Name");
        }


        internal override string GetDBName()
        {
            return id;
        }

        internal override string GetDBPath()
        {
            return "businessUnits." + GetDBName();
        }

        public override DataModelItem[] GetChildren()
        {
            var parentChildren = base.GetChildren();
            return parentChildren.Concat(new DataModelItem[]
            {
                Name
            }).ToArray();
        }

        public override bool UpdateDirectPropertiesFromMapEntry(KeyValuePair<string, object>? entry)
        {
            if (entry == null)
                return false;

            if (entry.Value.Key == "ClientId")
            {
                ClientId = entry.Value.Value as String;
                return true;
            }

            return false;
        }

        public override void DoAddOneTimeChangesToDictionary(Dictionary<string, Object> updates)
        {
            updates["ClientId"] = ClientId;
        }
    }
}
