import 'package:api_inspection/generic/AttributeControls/TimeSpan/TimeSpanAttributeViewEditable.dart';
import 'package:api_inspection/generic/AttributeControls/TimeSpan/TimeSpanAttributeViewNonEditable.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/TimeSpanAttribute.dart';
import 'package:flutter/material.dart';

import '../AttributeStructure.dart';

class TimeSpanAttributeView extends StatefulWidget {
  final TimeSpanAttribute _attribute;
  final IsEditableController? editingController;
  final bool showPhotos;
  final bool showComments;

  const TimeSpanAttributeView(this._attribute,
      {Key? key,
      this.editingController,
      this.showPhotos = true,
      this.showComments = true})
      : super(key: key);

  @override
  _TimeSpanAttributeViewState createState() => _TimeSpanAttributeViewState();
}

class _TimeSpanAttributeViewState extends State<TimeSpanAttributeView> {
  @override
  Widget build(BuildContext context) {
    return AttributeStructure(widget._attribute,
        showPhotos: widget.showPhotos,
        showComments: widget.showComments,
        editingController: widget.editingController,
        editingBuilder: (context, updateListener) {
      return TimeSpanAttributeViewEditable(widget._attribute, updateListener);
    }, nonEditingBuilder: (context) {
      return TimeSpanAttributeViewNonEditable(widget._attribute);
    });
  }
}
