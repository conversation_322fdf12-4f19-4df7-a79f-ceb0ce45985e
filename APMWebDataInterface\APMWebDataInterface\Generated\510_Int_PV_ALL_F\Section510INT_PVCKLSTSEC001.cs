//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Int_PV_ALL_F
{
  public class Section510INT_PVCKLSTSEC001 : DataModelItem {

    public override String DisplayName { 
      get {
        return "GENERAL INFORMATION";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributeIs_the_asset_appropriately_prepared_for_internal_inspection;
    public BooleanAttribute attribute510INT_PVCKLSTSEC001Q002;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC001Q003;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510INT_PVCKLSTSEC001";

    public Section510INT_PVCKLSTSEC001(DataModelItem parent) : base(parent)
    {
            
        attributeIs_the_asset_appropriately_prepared_for_internal_inspection = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the asset appropriately prepared for internal inspection:", databaseName: "510_INT-PV_CKLST_SEC001_Q001"); 
     
        attribute510INT_PVCKLSTSEC001Q002 = new BooleanAttribute(this, displayName: "Are the internal areas of the asset free of debris and product residue:", databaseName: "510_INT-PV_CKLST_SEC001_Q002"); 
     
        attribute510INT_PVCKLSTSEC001Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was design data, previous inspection / examination reporting and or relevant  MOC / information pertaining to abnormal operating conditions made available prior to inspection:", databaseName: "510_INT-PV_CKLST_SEC001_Q003"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeIs_the_asset_appropriately_prepared_for_internal_inspection,
           attribute510INT_PVCKLSTSEC001Q002,
           attribute510INT_PVCKLSTSEC001Q003,
        };
    }
  }
}
