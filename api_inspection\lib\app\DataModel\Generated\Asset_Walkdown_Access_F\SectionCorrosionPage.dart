//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionCorrosion.dart';

// ignore: camel_case_types
class SectionCorrosionPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionCorrosion sectionCorrosion;

  const SectionCorrosionPage(this.sectionCorrosion, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionCorrosionPageState();
  }
}

class _SectionCorrosionPageState extends State<SectionCorrosionPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionCorrosion,
        title: "Corrosion",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionCorrosion.attributeCorrosion_identified
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget.sectionCorrosion
                      .attributeCorrosion_removal_recommendation
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
