import 'package:api_inspection/generic/AppStyle.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/project.dart';
import 'package:api_inspection/generic/UIControls/attributePadding.dart';

class ContactEditPage extends StatefulWidget {
  final Contact contact;
  const ContactEditPage(this.contact, {Key? key}) : super(key: key);

  @override
  _ContactEditPageState createState() => _ContactEditPageState();
}

class _ContactEditPageState extends State<ContactEditPage> {
  @override
  Widget build(BuildContext context) {
    var contact = widget.contact;

    return Scaffold(
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        appBar: AppBar(
          title: Text(
            'Contact',
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        body: SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
              AttributePadding.WithStdPadding(contact.name.buildWidget()),
              AttributePadding.WithStdPadding(contact.title.buildWidget()),
              AttributePadding.WithStdPadding(
                  contact.phoneNumber.buildWidget()),
              AttributePadding.WithStdPadding(contact.email.buildWidget())
            ])));
  }
}
