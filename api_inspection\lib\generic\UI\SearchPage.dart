import 'package:api_inspection/generic/AttributeControls/AttributeStructure.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/AttributeBase.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/MediaControls/SinglePhotoCollectionControl.dart';
import 'package:api_inspection/generic/MediaControls/SinglePhotoCollectionControlNonEditable.dart';
import 'package:flutter/material.dart';

import '../AppStyle.dart';

class SearchPage extends StatefulWidget {
  final String title;

  final DataModelItem rootToSearch;

  const SearchPage({Key? key, required this.rootToSearch, required this.title})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SearchPageState();
  }
}

typedef filterAttributeMethod = bool Function(AttributeBase);

class _SearchPageState extends State<SearchPage> {
  TextEditingController searchController = TextEditingController();

  List<AttributeBase> filteredAttributes = [];

  List<AttributeBase> mediaEntries = [];

  void filterAttributes(filterAttributeMethod filter) {
    var allChildren = widget.rootToSearch.getChildrenRecursive();
    var attributes = allChildren.whereType<AttributeBase>();

    List<AttributeBase> attributesToShow = [];
    for (var attr in attributes) {
      if (!attr.isUserFacing) continue;
      if (filter(attr)) {
        attributesToShow.add(attr);
      }
    }
    setState(() {
      filteredAttributes = attributesToShow;
    });
  }

  bool doesAttributeMatchFilters(
      AttributeBase attribute, List<String> searchText) {
    if (!attribute.isUserFacing) return false;

    if (searchText.isEmpty) return false;

    List<String> filteredStrings = attribute.displayName.split(' ');

    Map<String, bool> searchTextMap = <String, bool>{};
    for (var item in filteredStrings) {
      for (var text in searchText) {
        if (item.toLowerCase().contains(text)) searchTextMap[text] = true;
      }
    }
    if (searchTextMap.length == searchText.length) return true;
    return false;
  }

  void searchClicked() {
    mediaEntries = [];
    var searchText = searchController.text
        .toLowerCase()
        .split(' ')
        .where((element) => element.isNotEmpty)
        .toList();

    filterAttributes((attr) => doesAttributeMatchFilters(attr, searchText));
  }

  void searchForEmptyClicked() {
    mediaEntries = [];
    filterAttributes((attr) => !attr.hasData());
  }

  void searchForPhotosClicked() {
    mediaEntries = [];
    filterAttributes((attr) => attr.getPhotos().isNotEmpty);
  }

  void searchForJustPhotosClicked() {
    var allChildren = widget.rootToSearch.getChildrenRecursive();
    var attributes = allChildren.whereType<AttributeBase>();

    List<AttributeBase> newMediaEntries = [];
    for (var attr in attributes) {
      if (attr.getPhotos().isNotEmpty) {
        newMediaEntries.add(attr);
      }
    }
    setState(() {
      filteredAttributes = [];
      mediaEntries = newMediaEntries;
    });
  }

  void addAttributeWidgets(List<Widget> widgets) {
    for (var attr in filteredAttributes) {
      var parent = attr.parent;
      IsEditableController controller = IsEditableController();
      if (parent is DataModelSection) {
        if (parent.isLocked()) {
          controller.setIsEditable(false);
          widgets.add(Container(
              margin: const EdgeInsets.fromLTRB(10, 0, 10, 0),
              alignment: Alignment.centerLeft,
              child: const Text("This attribute is in a locked section",
                  style: TextStyle(color: Colors.white, fontSize: 16))));
        }
      }

      widgets.add(Container(
          margin: const EdgeInsets.fromLTRB(10, 0, 10, 0),
          alignment: Alignment.centerLeft,
          child: Text(
              attr.parent?.getDisplayPath(rootToStop: widget.rootToSearch) ??
                  "Unknown path",
              style: const TextStyle(color: Colors.white, fontSize: 16))));
      widgets.add(attr.buildWidget(editingController: controller));
      widgets.add(Container(height: 20));
    }
  }

  void addPhotoWidgets(List<Widget> widgets) {
    for (var attr in mediaEntries) {
      Widget title = Container(
          margin: const EdgeInsets.fromLTRB(10, 0, 10, 0),
          alignment: Alignment.centerLeft,
          child: Text(
              attr.parent?.getDisplayPath(rootToStop: widget.rootToSearch) ??
                  "Unknown path",
              style: const TextStyle(color: Colors.white, fontSize: 16)));
      var parent = attr.parent;
      IsEditableController controller = IsEditableController();
      if (parent is DataModelSection && parent.isLocked()) {
        controller.setIsEditable(false);
        widgets.add(Container(
            margin: const EdgeInsets.fromLTRB(10, 0, 10, 0),
            alignment: Alignment.centerLeft,
            child: const Text("These photos are in a locked section",
                style: TextStyle(color: Colors.white, fontSize: 16))));
        widgets.add(title);
        widgets.add(SinglePhotoCollectionControlNonEditable(attr));
      } else {
        widgets.add(title);
        widgets.add(SinglePhotoCollectionControl(attr));
      }

      widgets.add(Container(height: 20));
    }
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> childAttributes = [];

    addAttributeWidgets(childAttributes);
    addPhotoWidgets(childAttributes);

    return Scaffold(
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        appBar: AppBar(
          title: Text(
            "Search: " + widget.title.toLowerCase(),
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize - 2),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        body: Column(children: [
          Row(
            children: [
              Expanded(
                child: Container(
                  margin: const EdgeInsets.all(10),
                  child: TextField(
                      decoration: AppStyle.global.textFieldDecoration,
                      style: const TextStyle(color: Colors.white),
                      controller: searchController),
                ),
              ),
              IconButton(
                  onPressed: searchClicked,
                  icon: const Icon(
                    Icons.search,
                    color: Colors.white,
                    size: 32,
                  )),
            ],
          ),
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            Container(
              margin: const EdgeInsets.fromLTRB(10, 0, 5, 0),
              child: ElevatedButton(
                  onPressed: searchForPhotosClicked,
                  child: const Text("Items with photos",
                      style: TextStyle(color: Colors.white))),
            ),
            Container(
              margin: const EdgeInsets.fromLTRB(5, 0, 5, 0),
              child: ElevatedButton(
                  onPressed: searchForJustPhotosClicked,
                  child: const Text("Photos",
                      style: TextStyle(color: Colors.white))),
            ),
            Container(
              margin: const EdgeInsets.fromLTRB(5, 0, 10, 0),
              child: ElevatedButton(
                  onPressed: searchForEmptyClicked,
                  child: const Text("Empty",
                      style: TextStyle(color: Colors.white))),
            ),
          ]),
          Container(height: 10),
          Expanded(
              child: SingleChildScrollView(
                  child: Column(children: childAttributes)))
        ]));
  }
}
