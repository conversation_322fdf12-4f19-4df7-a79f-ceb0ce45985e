//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/UI/CollectionControl.dart';
import 'package:uuid/uuid.dart';
import 'SectionPipe.dart';

// ignore: camel_case_types
// ignore: camel_case_types
class SectionPipePage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final DataModelCollection<SectionPipe> sectionPipe;
  const SectionPipePage(this.sectionPipe, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionPipePageState();
  }
}

class _SectionPipePageState extends State<SectionPipePage> {
  Widget _cardBuilder(BuildContext context, int number, SectionPipe item) {
    return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
              "Line No.: " +
                  (item.attributeLine_No_.getPreviewText() == ""
                      ? "Not set"
                      : item.attributeLine_No_.getPreviewText()),
              style: const TextStyle(color: Colors.white, fontSize: 18)),
        ]);
  }

  Widget _editPageBuilder(BuildContext context, SectionPipe item) {
    return SectionScaffold(
        section: item,
        title: "Pipe",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(item.attributeLine_No_
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item.attributePhotos
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item
                      .attributeMaterial_Spec_and_Grade
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item
                      .attributeAllowable_Stress_at_Temperature
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item
                      .attributeNominal_Thickness_schedule
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item
                      .attributeCorrosion_Allowance
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item.attributeJoint_Efficiency
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item.attributePipe_Spec_Number
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }

  SectionPipe _createNewItem() {
    String id = const Uuid().v4();
    var item = SectionPipe(id, widget.sectionPipe);

    return item;
  }

  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionPipe,
        title: "Pipe",
        childBuilder: (editableController) {
          return Center(
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: <Widget>[
                Expanded(
                    child: CollectionControl<SectionPipe>(
                  cardTitle: "Pipe",
                  collection: widget.sectionPipe,
                  cardBuilder: _cardBuilder,
                  createNewItem: _createNewItem,
                  editPageBuilder: _editPageBuilder,
                ))
              ]));
        });
  }
}
