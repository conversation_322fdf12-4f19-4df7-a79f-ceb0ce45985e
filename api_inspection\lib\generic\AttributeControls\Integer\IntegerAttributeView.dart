import 'package:api_inspection/generic/AttributeControls/Integer/IntegerAttributeViewEditable.dart';
import 'package:api_inspection/generic/AttributeControls/Integer/IntegerAttributeViewNonEditable.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
import 'package:flutter/material.dart';

import '../AttributeStructure.dart';

class IntegerAttributeView extends StatefulWidget {
  final IntegerAttribute _attribute;
  final IsEditableController? editingController;
  final bool showPhotos;
  final bool showComments;

  const IntegerAttributeView(this._attribute,
      {Key? key,
      this.editingController,
      this.showPhotos = true,
      this.showComments = true})
      : super(key: key);

  @override
  _IntegerAttributeViewState createState() => _IntegerAttributeViewState();
}

class _IntegerAttributeViewState extends State<IntegerAttributeView> {
  @override
  Widget build(BuildContext context) {
    return AttributeStructure(widget._attribute,
        showComments: widget.showComments,
        showPhotos: widget.showPhotos,
        editingController: widget.editingController,
        editingBuilder: (context, updateListener) {
      return IntegerAttributeViewEditable(widget._attribute, updateListener);
    }, nonEditingBuilder: (context) {
      return IntegerAttributeViewNonEditable(widget._attribute);
    });
  }
}
