//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'SectionShellCourses.dart';
import 'SectionTankBottomFloor.dart';
import 'SectionTankRoof.dart';
import 'SectionNozzles.dart';

// ignore: camel_case_types
class SectionComponents extends DataModelSection {
  @override
  String getDisplayName() => "Components";
  SectionComponents(DataModelItem? parent)
      : super(parent: parent, sectionName: "Components");

// ignore: non_constant_identifier_names
  late SectionTankBottomFloor sectionTankBottomFloor =
      SectionTankBottomFloor(this);

// ignore: non_constant_identifier_names
  late DataModelCollection<SectionShellCourses> sectionShellCourses =
      DataModelCollection<SectionShellCourses>("Shell Courses",
          (parent, entry) {
    return SectionShellCourses(entry.key, sectionShellCourses);
  }, this);
  // ignore: non_constant_identifier_names
  late DataModelCollection<SectionTankRoof> sectionTankRoof =
      DataModelCollection<SectionTankRoof>("Tank Roof", (parent, entry) {
    return SectionTankRoof(entry.key, sectionTankRoof);
  }, this);
  // ignore: non_constant_identifier_names
  late DataModelCollection<SectionNozzles> sectionNozzles =
      DataModelCollection<SectionNozzles>("Nozzles", (parent, entry) {
    return SectionNozzles(entry.key, sectionNozzles);
  }, this);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      sectionShellCourses,
      sectionTankBottomFloor,
      sectionTankRoof,
      sectionNozzles,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionComponents";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
