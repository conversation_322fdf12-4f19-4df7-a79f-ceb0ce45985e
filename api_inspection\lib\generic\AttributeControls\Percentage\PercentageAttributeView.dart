import 'package:api_inspection/generic/AttributeControls/Percentage/PercentageAttributeViewEditable.dart';
import 'package:api_inspection/generic/AttributeControls/Percentage/PercentageAttributeViewNonEditable.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/PercentageAttribute.dart';
import 'package:flutter/material.dart';

import '../AttributeStructure.dart';

class PercentageAttributeView extends StatefulWidget {
  final PercentageAttribute _attribute;
  final IsEditableController? editingController;
  final bool showPhotos;
  final bool showComments;

  const PercentageAttributeView(this._attribute,
      {Key? key,
      this.editingController,
      this.showPhotos = true,
      this.showComments = true})
      : super(key: key);

  @override
  _PercentageAttributeViewState createState() =>
      _PercentageAttributeViewState();
}

class _PercentageAttributeViewState extends State<PercentageAttributeView> {
  @override
  Widget build(BuildContext context) {
    return AttributeStructure(widget._attribute,
        showComments: widget.showComments,
        showPhotos: widget.showPhotos,
        editingController: widget.editingController,
        editingBuilder: (context, updateListener) {
      return PercentageAttributeViewEditable(widget._attribute, updateListener);
    }, nonEditingBuilder: (context) {
      return PercentageAttributeViewNonEditable(widget._attribute);
    });
  }
}
