//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionOperatingDesignConditions.dart';

// ignore: camel_case_types
class SectionOperatingDesignConditionsPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionOperatingDesignConditions sectionOperatingDesignConditions;

  const SectionOperatingDesignConditionsPage(
      this.sectionOperatingDesignConditions,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionOperatingDesignConditionsPageState();
  }
}

class _SectionOperatingDesignConditionsPageState
    extends State<SectionOperatingDesignConditionsPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionOperatingDesignConditions,
        title: "Operating/Design Conditions",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionOperatingDesignConditions
                      .attributeOperating_Temperature
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionOperatingDesignConditions.attributeDesign_MAWP
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionOperatingDesignConditions
                      .attributeDesign_Temperature
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionOperatingDesignConditions
                      .attributeOperating_Pressure
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionOperatingDesignConditions
                      .attributePRV_Set_Pressure
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionOperatingDesignConditions
                      .attributeOperation_Status
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
