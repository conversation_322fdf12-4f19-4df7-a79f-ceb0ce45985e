//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionAccessibility.dart';
import 'SectionAerialLiftRequirementsPage.dart';

// ignore: camel_case_types
class SectionAccessibilityPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionAccessibility sectionAccessibility;

  const SectionAccessibilityPage(this.sectionAccessibility, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionAccessibilityPageState();
  }
}

class _SectionAccessibilityPageState extends State<SectionAccessibilityPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionAccessibility,
        title: "Accessibility",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget.sectionAccessibility
                      .attributeFixed_Equipment_LaddersStairwaysPlatforms_Installed
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget.sectionAccessibility
                      .attributeAll_components_under_4_ft_in_height
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionAccessibility.attributeLadder_Requirements
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget
                            .sectionAccessibility.sectionAerialLiftRequirements,
                        onPressed: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      SectionAerialLiftRequirementsPage(widget
                                          .sectionAccessibility
                                          .sectionAerialLiftRequirements))).then(
                              (value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(widget
                      .sectionAccessibility.attributeScaffolding_required
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionAccessibility.attributeRope_access_required
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
