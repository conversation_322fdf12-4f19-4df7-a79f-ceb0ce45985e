//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionLeak_Report_F
{
  public class SectionLeak_Report_F : DataModelItem {

    public override String DisplayName { 
      get {
        return "Leak Report-F";
      }
    }

    #region [-- Sub Sections --]

    #region [-- SectionWorkDetail --]
    private SectionWorkDetail _sectionWorkDetail;
    public SectionWorkDetail sectionWorkDetail {
        get {
            if (_sectionWorkDetail == null) {
               _sectionWorkDetail = new SectionWorkDetail(this);
            }

            return _sectionWorkDetail;
        }
    }
    #endregion [-- SectionWorkDetail --]
    
    #region [-- SectionLeakReport --]
    private SectionLeakReport _sectionLeakReport;
    public SectionLeakReport sectionLeakReport {
        get {
            if (_sectionLeakReport == null) {
               _sectionLeakReport = new SectionLeakReport(this);
            }

            return _sectionLeakReport;
        }
    }
    #endregion [-- SectionLeakReport --]
    
    #region [-- SectionPhotos --]
    private SectionPhotos _sectionPhotos;
    public SectionPhotos sectionPhotos {
        get {
            if (_sectionPhotos == null) {
               _sectionPhotos = new SectionPhotos(this);
            }

            return _sectionPhotos;
        }
    }
    #endregion [-- SectionPhotos --]
    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionLeak_Report_F";

    public SectionLeak_Report_F(DataModelItem parent) : base(parent)
    {
            
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           sectionWorkDetail,
           sectionLeakReport,
           sectionPhotos,
        };
    }
  }
}
