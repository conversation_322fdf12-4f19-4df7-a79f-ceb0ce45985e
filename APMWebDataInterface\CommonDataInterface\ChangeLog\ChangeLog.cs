﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;

namespace CommonDataInterface
{
  public class ChangeLog<ValueType> : ChangeLogBase<ValueType> {
    public PendingChange<ValueType> PendingChange { get; set; }
    public PendingChange_FromOther<ValueType>[] PendingChangeCopy { get; set; }

    public ChangeLog(String name, DataModelItem parent, List<ChangeLogEntry<ValueType>> entries) : base(name, parent, entries)
    {
    }
    
    
    internal override bool updateFromOther(DataModelItem other) {
      if (!(other is ChangeLog<ValueType>)){
        throw new Exception("Unexpected type encountered in updateFromOther");
      }

      bool updated =false;

      var pendingChanges = new List<PendingChange_FromOther<ValueType>>();

      foreach (var entry in (other as ChangeLog<ValueType>).entries){
        if (!entries.Any((element) => element.Key == entry.Key)){
          pendingChanges.Add(new PendingChange_FromOther<ValueType> {
            key = entry.Key,
            timeChanged = entry.TimeChanged,
            Value = entry.Value,
            userName = entry.UserName,
          });
          updated = true;
        }
      }

      if (pendingChanges.Any()) {
        PendingChangeCopy = pendingChanges.ToArray();
      }

      return updated || base.updateFromOther(other);
    }

    protected override async Task DoAddPendingChangesToDictionary(Dictionary<string, Object> updates, String user)
    {
      if (PendingChange == null && (PendingChangeCopy == null || PendingChangeCopy.Length == 0)) {
        return;
      }

      var currentPath = GetDBPath().Split('.');
      var remainingPath = currentPath.Skip(2);

      if (PendingChangeCopy != null) {
        foreach (var change in PendingChangeCopy) {
          var entry = new ChangeLogEntry<ValueType>(change.key, change.Value, change.timeChanged, change.userName);
          var path1 = remainingPath.AggregateEXT((a, b) => a + '.' + b) + "." + change.key;
          entry.AddUpdates(updates, path1);
          entries.Add(entry);
        }

        PendingChangeCopy = null;
      }

      if (PendingChange != null) {
        var newId = Guid.NewGuid().ToString();
        var newEntry = new ChangeLogEntry<ValueType>(newId, PendingChange.Value, DateTime.Now, user);

        var path = remainingPath.AggregateEXT((a, b) => a + '.' + b) + "." + newId;
        newEntry.AddUpdates(updates, path);
        PendingChange = null;
        entries.Add(newEntry);
      }

    }


    public override bool GetHasDatabaseChangesPending()
    {
      if (PendingChange == null && PendingChangeCopy == null) {
        return base.GetHasDatabaseChangesPending();
      }

      return true;
    }
    
  }
}
