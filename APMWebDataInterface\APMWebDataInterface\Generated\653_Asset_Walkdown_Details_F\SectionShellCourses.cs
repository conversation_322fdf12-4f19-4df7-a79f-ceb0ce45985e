//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section653_Asset_Walkdown_Details_F
{
  public class SectionShellCourses : DataModelCollectionItem {

    public override String DisplayName { 
      get {
        return "Shell Courses";
      }
    }
    
    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public StringAttribute attributeNumber;
    public PhotoAttribute attributePhotos;
    public StringAttribute attributeMaterial_Spec_and_Grade;
    public DoubleAttribute attributeAllowable_Stress_at_Temperature;
    public DoubleAttribute attributeNominal_Thickness;
    public DoubleAttribute attributeCorrosion_Allowance;
    public DoubleAttribute attributeLength_or_Height;
    public DoubleAttribute attributeJoint_Efficiency;

    #endregion [-- Attributes --]

    public SectionShellCourses(String id, DataModelItem parent) : base(id, parent)
    {
            
        attributeNumber = new StringAttribute(this, displayName: "Number", databaseName: "653AW_Q406"); 
     
        attributePhotos = new PhotoAttribute(this, displayName: "Photos", databaseName: "653AW_Q413"); 
     
        attributeMaterial_Spec_and_Grade = new StringAttribute(this, displayName: "Material Spec and Grade", databaseName: "653AW_Q407"); 
     
        attributeAllowable_Stress_at_Temperature = new DoubleAttribute(this, displayName: "Allowable Stress at Temperature", databaseName: "653AW_Q408", areCommentsRequired: false, displayUnit: "psi", allowNegatives: true); 
     
        attributeNominal_Thickness = new DoubleAttribute(this, displayName: "Nominal Thickness", databaseName: "653AW_Q409", areCommentsRequired: false, displayUnit: "in", allowNegatives: true); 
     
        attributeCorrosion_Allowance = new DoubleAttribute(this, displayName: "Corrosion Allowance", databaseName: "653AW_Q410", areCommentsRequired: false, displayUnit: "in", allowNegatives: true); 
     
        attributeLength_or_Height = new DoubleAttribute(this, displayName: "Length or Height", databaseName: "653AW_Q411", areCommentsRequired: false, displayUnit: "ft", allowNegatives: true); 
     
        attributeJoint_Efficiency = new DoubleAttribute(this, displayName: "Joint Efficiency", databaseName: "653AW_Q412", areCommentsRequired: false, displayUnit: null, allowNegatives: true); 
    }   
    
    public override DataModelItem[] GetChildren() {
      var parentChildren = base.GetChildren();

      return parentChildren.Concat(new DataModelItem[] { 
        attributeNumber,
           attributePhotos,
           attributeMaterial_Spec_and_Grade,
           attributeAllowable_Stress_at_Temperature,
           attributeNominal_Thickness,
           attributeCorrosion_Allowance,
           attributeLength_or_Height,
           attributeJoint_Efficiency,
      }).ToArray();
    }
  }
}
