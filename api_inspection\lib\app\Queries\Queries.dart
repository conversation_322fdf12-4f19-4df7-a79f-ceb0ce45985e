import 'package:api_inspection/app/APMRoot.dart';
import 'package:api_inspection/app/Queries/AssetCards.dart';
import 'package:api_inspection/app/Queries/BusinessUnitQueries.dart';
import 'package:api_inspection/app/Queries/ClientQueries.dart';
import 'package:api_inspection/app/Queries/LeakReportQueries.dart';
import 'package:api_inspection/app/Queries/LocationQueries.dart';
import 'package:api_inspection/app/Queries/ProjectQueries.dart';
import 'package:api_inspection/app/Queries/SelectedProjects.dart';
import 'package:api_inspection/app/Queries/UserQuery.dart';

class Queries {
  SelectedProjects selectedProjects = SelectedProjects();
  LocationQueries locationQueries = LocationQueries();
  ProjectQueries projectQueries = ProjectQueries();
  AssetCards assetCards = AssetCards();
  LeakReportQueries leakReports = LeakReportQueries();
  ClientQueries clientQueries = ClientQueries();
  BusinessUnitQueries businessUnitQueries = BusinessUnitQueries();
  UserQuery userQuery = UserQuery();

  Future dispose() async {
    selectedProjects.dispose();
    await locationQueries.dispose();
    await projectQueries.dispose();
    assetCards.dispose();
    await leakReports.dispose();
    await clientQueries.dispose();
    await businessUnitQueries.dispose();
    await userQuery.dispose();
  }

  Queries();

  initialize() {
    assetCards.initialize(this, APMRoot.global.newQueries);
    locationQueries.initialize(this);
    projectQueries.initialize(this);
    leakReports.initialize(this);
    clientQueries.initialize(this);
    businessUnitQueries.initialize(this);
    userQuery.initialize(this);
  }
}
