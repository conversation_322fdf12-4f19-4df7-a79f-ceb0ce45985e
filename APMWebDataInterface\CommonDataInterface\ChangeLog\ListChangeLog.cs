﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CommonDataInterface
{
  public class ListChangeLog<ValueType> : ChangeLogBase<ValueType>{
    
    internal override bool updateFromOther(DataModelItem other) {
      if (!(other is ListChangeLog<ValueType>)){
        throw new Exception("Unexpected type encountered in updateFromOther");
      }
      bool updated =false;
      foreach (var entry in (other as ListChangeLog<ValueType>).entries){
        if (!(entry is ListChangeLogEntry<ValueType>))
          throw new Exception("Unexpected type encountered in updateFromOther");
        var castedEntry = entry as ListChangeLogEntry<ValueType>;
        if (!entries.Any((element) => element.Key == castedEntry.Key)){
          PendingChanges.Add(new PendingListChange_FromOther<ValueType> {
            timeChanged = castedEntry.TimeChanged,
            key = castedEntry.Key,
            Value = castedEntry.Value,
            userName = castedEntry.UserName,
            Action = castedEntry.Action == "Added" ? ChangeAction.Added : ChangeAction.Removed
          });
          updated = true;
        }
      }
      return updated || base.updateFromOther(other);
    }



    public List<ValueType> GetCurrentEntries() { 
      var sorted = entries.OrderBy((element) => element.TimeChanged).ToList();
      var allEntries = sorted.Select((a) => a.Value).Distinct().ToList();
      var allCopy = allEntries.ToList();
      foreach (var entry in allCopy){
        var lastMatch = sorted.LastOrDefault((element) => Equals(element.Value, entry));
        
        if (lastMatch is ListChangeLogEntry<ValueType> changeEntry){
          if (changeEntry.Action == "Removed"){
            allEntries.Remove(entry);
          }
        }
      }

      return allEntries.Where(a => a != null)
        .ToList();
    }


    public List<ValueType> GetPendingEntries() { 
      var sorted = entries.OrderBy((element) => element.TimeChanged).ToList();
      var allEntries = sorted.Select((a) => a.Value).Distinct().ToList();
      var allCopy = allEntries.ToList();
      foreach (var entry in allCopy){
        var lastMatch = sorted.LastOrDefault((element) => Equals(element.Value, entry));
        
        if (lastMatch is ListChangeLogEntry<ValueType> changeEntry){
          if (changeEntry.Action == "Removed"){
            allEntries.Remove(entry);
          }
        }
      }

      foreach (var entry in PendingChanges){
        if (entry.Action == ChangeAction.Removed) {
          allEntries.Remove(entry.Value);
        }
        else if (entry.Action == ChangeAction.Added) {
          allEntries.Add(entry.Value);
        }
      }

      return allEntries.Where(a => a != null)
        .ToList();
    }


    public ListChangeLog(String name, DataModelItem parent, List<ChangeLogEntry<ValueType>> entries) : base(name, parent, entries)
    {

    }
    
    public override void UpdateFromMap(Dictionary<string, object> map){
      entries = new List<ChangeLogEntry<ValueType>>();

      if (map == null)
        return;

      foreach (var entry in map){
        var subMap = entry.Value as Dictionary<string, object>;
        if (subMap == null)
          continue;

        String userName = null;
        long? timeChanged = null;
        ValueType value = default(ValueType);
        String action = null;

        foreach (var subEntry in subMap){
          if (subEntry.Key == "U"){
            userName = subEntry.Value as String;
          }
          if (subEntry.Key == "T"){
            timeChanged = long.Parse(subEntry.Value.ToString());
          }
          if (subEntry.Key == "V"){
            
            if (_conversion != null){
              value = _conversion.Invoke(subEntry.Value);
            }
            else{
              value = (ValueType)subEntry.Value;
            }
            
          }
          if (subEntry.Key == "A"){
            action = subEntry.Value as String;
          }
        }

        if (userName == null || timeChanged == null || action == null)
          continue;
        String key = entry.Key;
        
        var dateTime = DateTimeOffset.FromUnixTimeMilliseconds(timeChanged.Value).DateTime;

        ListChangeLogEntry<ValueType> entry1 = new ListChangeLogEntry<ValueType>(key, value, dateTime, userName, action);
        entries.Add(entry1);
      }

      
      entries.Sort((entryA, entryB) => entryA.TimeChanged.CompareTo(entryB.TimeChanged));

    }

    private List<PendingListChange<ValueType>> PendingChanges = new List<PendingListChange<ValueType>>();



    
    protected override async Task DoAddPendingChangesToDictionary(Dictionary<string, Object> updates, String user)
    {
      if (!PendingChanges.Any()) {
        return;
      }

      foreach (var item in PendingChanges) {
        var currentPath = GetDBPath().Split('.');
        var remainingPath = currentPath.Skip(2);

        ListChangeLogEntry<ValueType> newEntry;
        if (item is PendingListChange_FromOther<ValueType> fromOther) {
          newEntry = new ListChangeLogEntry<ValueType>(fromOther.key, fromOther.Value, fromOther.timeChanged, fromOther.userName , fromOther.Action == ChangeAction.Added ? "Added" : "Removed");
        }
        else {
          var newId = Guid.NewGuid().ToString();
          newEntry = new ListChangeLogEntry<ValueType>(newId, item.Value, DateTime.UtcNow, user, item.Action == ChangeAction.Added ? "Added" : "Removed");
        }

        var path = remainingPath.AggregateEXT((a, b) => a + '.' + b) + "." + newEntry.Key;
        newEntry.AddUpdates(updates, path);
        entries.Add(newEntry);
      }
      PendingChanges.Clear();
    }


    public override bool GetHasDatabaseChangesPending()
    {
      if (!PendingChanges.Any()) {
        return base.GetHasDatabaseChangesPending();
      }

      return true;
    }




    public void AddPendingChange(ValueType value, ChangeAction action)
    {
      var matchingChanges = PendingChanges.Where(a => Equals(a.Value, value)).ToArray();
      foreach (var change in matchingChanges) {
        PendingChanges.Remove(change);
      }
      PendingChanges.Add(new PendingListChange<ValueType>{Action = action, Value = value});
    }


  }
}