﻿using System;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using APMWebDataInterface.ExampleDataModel;
using Newtonsoft.Json;

namespace APMWebDataInterface.Tests
{
  public class TestPlayground
  {



    /// <summary>
    /// Ignore this test,  it is just here to informally test things
    /// </summary>
    static async Task DotestingPlayground()
    {

      Stopwatch stopwatch = new Stopwatch();

      var driver = APM_WebDataInterface.Global;
      driver.Initialize();


      stopwatch.Start();

      // //var location = new Location();
      // //location.name.SetValue("New Location From Web");
      // //await location.SavePendingChanges();

      var locations = await driver.GetLocations("<EMAIL>");

      // //var newAsset = new Asset("Vessel", locations[0].id);
      // //newAsset.SavePendingChanges("<EMAIL>");

      // //var newProject = new Project(locations[0]);
      // //newProject.name.SetValue("New Project From Web 2");
      // //await newProject.SavePendingChanges();



      var location = new[] {locations[0].id};

      var projects = await driver.GetProjectsAtLocation(location, "<EMAIL>");

      var project = projects.FirstOrDefault(a => a.name.GetValue().Equals("Demo_ProjectTest", StringComparison.InvariantCultureIgnoreCase));

      var assets = driver.GetAssetsForProjects(new[] {project.id}, "<EMAIL>");

      //var assets = await driver.GetAssetsForProjects(new[] {project.id});
      //var assets = await driver.GetAssets(new[] { "a0320381-c3ee-445e-a897-08b4bed226e1" });
      var asset = assets.Result.FirstOrDefault(a => a.unit.GetValue() == "100A");

      var workOrders = await driver.GetWorkOrders(new[] {project.id}, "<EMAIL>");
      var workOrder = workOrders.FirstOrDefault(a => a.asset.id == asset.id);

      var walkdown = workOrder.tasks.FirstOrDefault(a => a.taskType == "Asset Walkdown");
      var externalChecklist = workOrder.tasks.FirstOrDefault(a => a.taskType == "External Visual");
      var internalChecklist = workOrder.tasks.FirstOrDefault(a => a.taskType == "Internal Visual");


      var asset2 = assets.Result.FirstOrDefault(a => a.unit.GetValue() == "100pipe");

      var workOrder2 = workOrders.FirstOrDefault(a => a.asset.id == asset2.id);

      var walkdownPipe = workOrder2.tasks.FirstOrDefault(a => a.taskType == "Asset Walkdown");
      var externalChecklistPipe = workOrder2.tasks.FirstOrDefault(a => a.taskType == "External Visual");


      var asset3 = assets.Result.FirstOrDefault(a => a.unit.GetValue() == "100tank");

      var workOrder3 = workOrders.FirstOrDefault(a => a.asset.id == asset3.id);

      var walkdownTank = workOrder3.tasks.FirstOrDefault(a => a.taskType == "Asset Walkdown");

      await workOrder.ResolvePhotos();

      //var reportVesselWalkdown = new ReportPackage {
      //  Asset = asset,
      //  Project = project,
      //  Task = walkdown

      //};
      //string jsonVesselWalkdown = JsonConvert.SerializeObject(reportVesselWalkdown, Formatting.Indented);



      //var reportVesselEV = new ReportPackage {
      //  Asset = asset,
      //  Project = project,
      //  Task = externalChecklist

      //};
      //string jsonVesselExternalChecklist = JsonConvert.SerializeObject(reportVesselEV, Formatting.Indented);


      //var reportVesselIV = new ReportPackage {
      //  Asset = asset,
      //  Project = project,
      //  Task = internalChecklist

      //};
      //string jsonVesselInternalChecklist = JsonConvert.SerializeObject(reportVesselIV, Formatting.Indented);



      //var reportPipeWalkdown = new ReportPackage {
      //  Asset = asset2,
      //  Project = project,
      //  Task = walkdownPipe

      //};
      //string jsonPipewalkdown = JsonConvert.SerializeObject(reportPipeWalkdown, Formatting.Indented);

      //var reportPipeExt = new ReportPackage {
      //  Asset = asset2,
      //  Project = project,
      //  Task = externalChecklistPipe

      //};
      //string jsonPipeExt = JsonConvert.SerializeObject(reportPipeExt, Formatting.Indented);


      //var reportTankWalkdown = new ReportPackage {
      //  Asset = asset3,
      //  Project = project,
      //  Task = walkdownTank

      //};
      //string jsonTankwalkdown = JsonConvert.SerializeObject(reportTankWalkdown, Formatting.Indented);

      //Console.Write(jsonVesselWalkdown);


      //var asset1 = new Asset("Piping", "100");
      //var workOrder1 = new WorkOrder(asset1, "Project1");
      //var task1 = workOrder1.AddNewTask(WorkOrder.TaskTypes.ExternalVisual);



      //await asset.ResolvePhotos();
      //var photoData = asset.assetPPE.sectionGeneralSiteConditions.attributeAre_there_any_on_site_leaks.Photos[0].ImageData;




      //var photoPath = "C:\\Temp\\photo.png";
      //var photoBytes = System.IO.File.ReadAllBytes(photoPath);
      //asset.assetPPE.sectionGeneralSiteConditions.attributeAre_there_any_on_site_leaks.AddPhoto(new PendingMediaEntry {
      //  MediaName = Guid.NewGuid().ToString(),
      //  Extension = "png", 
      //  ImageData =photoBytes
      //});


      //await project.ResolvePhotos();
      //var photoData = project.accountingDetails.workOrderNumber.Photos[0].ImageData;
      //await asset.SavePendingChanges("<EMAIL>");




      //var workOrder = workOrders.FirstOrDefault(a => a.id == "ea776315-6350-4b4b-a853-1463d128ac69");

      //workOrder.tasks.CurrentEntries[0].ChangeStatus(APMTask.Statuses.Completed);

      //var newWalkdown = workOrder.AddNewTask(WorkOrder.TaskTypes.AssetWalkdown);




      //await workOrder.SavePendingChanges("<EMAIL>");

      //Console.Write(workOrders.Length);


      // var assets = await driver.GetAssetsAtLocation(location);

      // //var newWO = new WorkOrder(assets[0], projects[0].id);
      // //newWO.SavePendingChanges("<EMAIL>");


      // var workOrders = await driver.GetWorkOrders(projects.Select( a=> a.id).ToArray());

      // var workOrder = workOrders[0];
      // var task = workOrder.tasks.GetEntries().FirstOrDefault();

      // var casted = task as APMTaskGeneric<Section510_Asset_Walkdown_Details_F>;
      //// var head = (casted.assetDetails as Section510_Asset_Walkdown_Details_F).sectionComponents.sectionShell_Courses.GetEntries()[0];
      // casted.assignedUsers = new[] {"<EMAIL>"};
      // workOrder.SavePendingChanges("<EMAIL>");

      // var newWalkDown = workOrder.AddNewTask(WorkOrder.TaskTypes.AssetWalkdown);
      // workOrder.SavePendingChanges("<EMAIL>");
      // var tasks = workOrder.tasks.GetEntries();
      //  tasks[0].assignedUsers = new [] { "<EMAIL>"};
      //

      //var activity = new ProjectActivity(project.activities, Guid.NewGuid().ToString());
      //var activity = project.activities.AddNewItem();

      // set activity values..
      //activity.date.SetValue("12/5/2012");

      // await project.SavePendingChanges("TEST");

      //var users = await driver.GetUsers();

      //users[0].Role = "admin";
      //users[0].SavePendingChanges("<EMAIL>");


      //var newUser = new UserProfile("<EMAIL>", "technician");
      //newUser.SavePendingChanges("<EMAIL>");



      //string json = JsonConvert.SerializeObject(projects[0], Formatting.Indented);
      //var workOrder = workOrders[0];

      //var tasks = workOrder.tasks.GetEntries();
      //tasks[0].assignedUsers = new [] { "<EMAIL>"};

      //await workOrder.SavePendingChanges("SomeEmail.Test");

      //  workOrder.AddTask(new  APMTaskGeneric<Section510_Asset_Walkdown>(workOrder, workOrder.tasks, Guid.NewGuid().ToString(), (parent) => new Section510_Asset_Walkdown(parent)));



      //var activeAssets = await driver.GetAssetsForProjects(projects.Select( a=> a.id).ToArray());

      //var workOrders = await driver.GetWorkOrders(projects, assets);

      //
      //foreach (var asset in assets) {
      //  Console.Write(asset.id);
      //}


      stopwatch.Stop();




      // Console.Write("Projects: " +  assets.Length + " time " + stopwatch.ElapsedMilliseconds / 1000.0);

      stopwatch.Restart();



      //Project[] projectsArray = await driver.GetProjects();

      // if (projectsArray.Length > projects.Length) {
      ////    Console.Write("New Project added");
      // }
      // var project1 = projectsArray[0];


      // project1.accountingDetails.apmProjectNumber.SetValue("APM Project Number from web " + new String(Guid.NewGuid().ToString().Take(4).ToArray()));
      //project1.SavePendingChanges();



      stopwatch.Stop();

      //Console.Write("Projects: " +  assets.Length + " time " + stopwatch.ElapsedMilliseconds / 1000.0);

      Thread.Sleep(500000);
    }

    /// <summary>
    /// Ignore this test,  it is just here to informally test things
    /// </summary>
    public void testingPlayground()
    {
      var task = new Task(async () => { await DotestingPlayground(); });
      task.Start();
      Thread.Sleep(500000);
      task.Wait();
    }
  }
}