//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionManufacturerFabricator.dart';

// ignore: camel_case_types
class SectionManufacturerFabricatorPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionManufacturerFabricator sectionManufacturerFabricator;

  const SectionManufacturerFabricatorPage(this.sectionManufacturerFabricator,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionManufacturerFabricatorPageState();
  }
}

class _SectionManufacturerFabricatorPageState
    extends State<SectionManufacturerFabricatorPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionManufacturerFabricator,
        title: "Manufacturer (Fabricator)",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionManufacturerFabricator.attributeMFG_Name
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionManufacturerFabricator.attributeMFG_Date
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
