//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class SectionInternalAccessRequirements extends DataModelSection {
  @override
  String getDisplayName() => "Internal Access Requirements";
  SectionInternalAccessRequirements(DataModelItem? parent)
      : super(parent: parent, sectionName: "Internal Access Requirements");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeAre_there_inspection_openings =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Are there inspection openings?",
          databaseName: "AWA_Q311",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeInspection_opening_Types =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: true,
          displayName: "Inspection opening Types",
          databaseName: "AWA_Q312",
          availableOptions: [
        PredefinedValueOption("Manway", null, isCommentRequired: false),
        PredefinedValueOption("Handhole", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeSize_of_all_accessible_openings =
      StringAttribute(
          parent: this,
          displayName: "Size of all accessible openings",
          databaseName: "AWA_Q313",
          areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeVentilation_requirements =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: true,
          displayName: "Ventilation requirements",
          databaseName: "AWA_Q314",
          availableOptions: [
        PredefinedValueOption("Open to Atmosphere", null,
            isCommentRequired: false),
        PredefinedValueOption("Air Mover", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeAre_there_inspection_openings,
      attributeInspection_opening_Types,
      attributeSize_of_all_accessible_openings,
      attributeVentilation_requirements,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionInternalAccessRequirements";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
