import 'package:api_inspection/app/batch_helper.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/project.dart';
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'ClientPage.dart';
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
import 'package:api_inspection/generic/AppStyle.dart';
import 'package:uuid/uuid.dart';

class ClientsPage extends StatefulWidget {
  final List<Client> clients;

  final String title = "Asset Management";

  const ClientsPage(this.clients, {Key? key}) : super(key: key);

  @override
  _ClientsPageState createState() => _ClientsPageState();
}

class _ClientsPageState extends State<ClientsPage> {
  @override
  void deactivate() {
    super.deactivate();
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> clientList = [];
    for (var client in widget.clients) {
      clientList.add(AttributePadding.WithStdPadding(TeamToggleButton.withText(
          client.name.getValue() ?? "",
          () => {
                Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => ClientEditPage(client)))
                    .then((value) => setState(() {}))
              })));
    }

    return Column(
      children: [
        AppBar(
          title: Text(
            "Clients",
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        Expanded(
            child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Expanded(
                  child: ListView(
                children: clientList,
              )),
              Align(
                  alignment: Alignment.centerRight,
                  child: Container(
                      margin: const EdgeInsets.all(10),
                      height: 50,
                      child: ElevatedButton(
                          onPressed: () {
                            var uuid = const Uuid().v4();
                            var client = Client(uuid);
                            BatchHelper.saveAndCommitStringAttribute(
                                client.name, "New Client");
                            Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) =>
                                            ClientEditPage(client)))
                                .then((value) => setState(() {}));
                          },
                          child: const Text("Create New Client"))))
            ],
          ),
        ))
      ],
    );
  }
}
