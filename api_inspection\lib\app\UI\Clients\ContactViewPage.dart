import 'package:api_inspection/generic/AppStyle.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/project.dart';
import 'package:api_inspection/generic/UIControls/attributePadding.dart';

class ContactViewPage extends StatefulWidget {
  final Contact contact;
  const ContactViewPage(this.contact, {Key? key}) : super(key: key);

  @override
  _ContactViewPageState createState() => _ContactViewPageState();
}

class _ContactViewPageState extends State<ContactViewPage> {
  @override
  Widget build(BuildContext context) {
    var contact = widget.contact;

    return Scaffold(
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        appBar: AppBar(
          title: Text(
            'Contact',
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        body: SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
              AttributePadding.WithStdPadding(
                  contact.name.buildWidgetNonEditable()),
              AttributePadding.WithStdPadding(
                  contact.title.buildWidgetNonEditable()),
              AttributePadding.WithStdPadding(
                  contact.phoneNumber.buildWidgetNonEditable()),
              AttributePadding.WithStdPadding(
                  contact.email.buildWidgetNonEditable())
            ])));
  }
}
