//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionRepairRecord.dart';
import 'SectionRepairsPage.dart';

// ignore: camel_case_types
class SectionRepairRecordPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionRepairRecord sectionRepairRecord;

  const SectionRepairRecordPage(this.sectionRepairRecord, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionRepairRecordPageState();
  }
}

class _SectionRepairRecordPageState extends State<SectionRepairRecordPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionRepairRecord,
        title: "Repair Record",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget.sectionRepairRecord
                      .attributeDoes_the_asset_have_a_repair_or_alteration_plate
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget.sectionRepairRecord
                      .attributeRepairAlteration_Plates_Legible
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionRepairRecord.sectionRepairs,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => SectionRepairsPage(
                                          widget.sectionRepairRecord
                                              .sectionRepairs)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                ]),
          );
        });
  }
}
