﻿<Window x:Class="CheckListGen.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:CheckListGen"
        mc:Ignorable="d" x:Name="Control_ROOT"
        Title="MainWindow" Height="450" Width="800">
    <Grid>
      <Grid.RowDefinitions>
        <RowDefinition Height="Auto"/>
        <RowDefinition Height="Auto"/>
        <RowDefinition Height="Auto"/>
        <RowDefinition Height="Auto"/>
        <RowDefinition Height="Auto"/>
        <RowDefinition Height="Auto"/>
        <RowDefinition Height="Auto"/>
        <RowDefinition Height="*"/>
      </Grid.RowDefinitions>
      <TextBlock Text="Data file path" FontSize="16" Margin="10, 10, 10, 5"/>
      <Grid Grid.Row="1">
        <Grid.ColumnDefinitions>
          <ColumnDefinition Width="*"/>
          <ColumnDefinition Width="Auto"/>
          <ColumnDefinition Width="Auto"/>
        </Grid.ColumnDefinitions>
        <TextBox Text="{Binding ExcelFilePath, ElementName=Control_ROOT}" FontSize="16" Margin="10, 5, 0, 5" Background="WhiteSmoke" Height="40" VerticalContentAlignment="Center" HorizontalAlignment="Stretch"/>
        <Button Content="Find Path" Click="FindExcelFileClicked" Grid.Column="1" FontSize="18" Width="100"  Height="40" Background="DimGray" Foreground="white" Margin="5,0"  BorderThickness="0" VerticalContentAlignment="Center"/>
        <Button Content="Generate" Grid.Column="2" FontSize="18" Width="150" Margin="0,0,5,0" Height="40" Background="DeepSkyBlue" Foreground="white" BorderThickness="0" VerticalContentAlignment="Center" Click="GenerateClicked"/>
      </Grid>

      <TextBlock Grid.Row="2" Text="Dart Output path" FontSize="16" Margin="10, 10, 10, 5"/>
      <Grid Grid.Row="3">
        <Grid.ColumnDefinitions>
          <ColumnDefinition Width="*"/>
          <ColumnDefinition Width="Auto"/>
          <ColumnDefinition Width="Auto"/>
        </Grid.ColumnDefinitions>
        <TextBox Text="{Binding DartOutputPath, ElementName=Control_ROOT}" FontSize="16" Margin="10, 5, 0, 5" Background="WhiteSmoke" Height="40" VerticalContentAlignment="Center" HorizontalAlignment="Stretch"/>
        <Button Content="Find Path" Click="FindDartOutputPathClicked" Grid.Column="1" FontSize="18" Width="100"  Height="40" Background="DimGray" Foreground="white" Margin="5,0"  BorderThickness="0" VerticalContentAlignment="Center"/>
      </Grid>

      <TextBlock Grid.Row="4" Text="C# Output path" FontSize="16" Margin="10, 10, 10, 5"/>
      <Grid Grid.Row="5">
        <Grid.ColumnDefinitions>
          <ColumnDefinition Width="*"/>
          <ColumnDefinition Width="Auto"/>
          <ColumnDefinition Width="Auto"/>
        </Grid.ColumnDefinitions>
        <TextBox Text="{Binding CSharpOutputPath, ElementName=Control_ROOT}" FontSize="16" Margin="10, 5, 0, 5" Background="WhiteSmoke" Height="40" VerticalContentAlignment="Center" HorizontalAlignment="Stretch"/>
        <Button Content="Find Path" Click="FindCSharpOutputPathClicked" Grid.Column="1" FontSize="18" Width="100"  Height="40" Background="DimGray" Foreground="white" Margin="5,0"  BorderThickness="0" VerticalContentAlignment="Center"/>
      </Grid>
      <TextBlock Text="Output" Grid.Row="6" FontSize="16" Margin="10, 5, 10, 5"/>
      <TabControl Grid.Row="7" HorizontalAlignment="Stretch" VerticalAlignment="Stretch"  Margin="10, 5, 10, 10">
        <TabItem Header="Main Output">
          <TextBox x:Name="MainOutput" AcceptsReturn="True" AcceptsTab="true" VerticalAlignment="Stretch" HorizontalAlignment="Stretch" FontSize="12" Margin="5" Background="WhiteSmoke"/>
        </TabItem>

      </TabControl>
    </Grid>
</Window>
