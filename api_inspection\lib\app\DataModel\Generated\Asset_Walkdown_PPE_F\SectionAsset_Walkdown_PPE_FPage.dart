//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionAsset_Walkdown_PPE_F.dart';
import 'SectionPPERequirementsPage.dart';
import 'SectionGeneralSiteConditionsPage.dart';

// ignore: camel_case_types
class SectionAsset_Walkdown_PPE_FPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionAsset_Walkdown_PPE_F sectionAsset_Walkdown_PPE_F;

  const SectionAsset_Walkdown_PPE_FPage(this.sectionAsset_Walkdown_PPE_F,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionAsset_Walkdown_PPE_FPageState();
  }
}

class _SectionAsset_Walkdown_PPE_FPageState
    extends State<SectionAsset_Walkdown_PPE_FPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionAsset_Walkdown_PPE_F,
        title: "Asset Walkdown-PPE",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget
                            .sectionAsset_Walkdown_PPE_F.sectionPPERequirements,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          SectionPPERequirementsPage(widget
                                              .sectionAsset_Walkdown_PPE_F
                                              .sectionPPERequirements)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionAsset_Walkdown_PPE_F
                            .sectionGeneralSiteConditions,
                        onPressed: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      SectionGeneralSiteConditionsPage(widget
                                          .sectionAsset_Walkdown_PPE_F
                                          .sectionGeneralSiteConditions))).then(
                              (value) => setState(() {}));
                        }),
                  ),
                ]),
          );
        });
  }
}
