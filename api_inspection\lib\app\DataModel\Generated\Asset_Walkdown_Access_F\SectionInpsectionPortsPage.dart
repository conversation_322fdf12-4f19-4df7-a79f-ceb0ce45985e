//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionInpsectionPorts.dart';

// ignore: camel_case_types
class SectionInpsectionPortsPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionInpsectionPorts sectionInpsectionPorts;

  const SectionInpsectionPortsPage(this.sectionInpsectionPorts, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionInpsectionPortsPageState();
  }
}

class _SectionInpsectionPortsPageState
    extends State<SectionInpsectionPortsPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionInpsectionPorts,
        title: "Inpsection Ports",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionInpsectionPorts.attributeExisting_inspection_ports
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionInpsectionPorts.attributeInsulation_plugs_missing
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionInpsectionPorts.attributeAdditional_ports_needed
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
