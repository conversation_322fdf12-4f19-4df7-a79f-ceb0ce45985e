import 'package:api_inspection/app/APMRoot.dart';
import 'package:api_inspection/generic/AppStyle.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/Generated/510_Asset_Walkdown_Details_F/Section510_Asset_Walkdown_Details_F.dart';
import 'package:api_inspection/app/DataModel/Generated/570_Asset_Walkdown_Details_F/Section570_Asset_Walkdown_Details_F.dart';
import 'package:api_inspection/app/DataModel/Generated/653_Asset_Walkdown_Details_F/Section653_Asset_Walkdown_Details_F.dart';
import 'package:api_inspection/app/DataModel/task.dart';
import 'package:api_inspection/generic/UIControls/SideSlideoutControl.dart';
import 'package:collection/collection.dart';

import 'TaskCompletionBarWrapper.dart';
import 'TaskEditPage.dart';

class TaskCardView extends StatefulWidget {
  final Task task;
  const TaskCardView(this.task, {Key? key}) : super(key: key);

  @override
  _TaskCardViewState createState() => _TaskCardViewState();
}

class _TaskCardViewState extends State<TaskCardView> {
  Widget buildInnerCard(BuildContext context) {
    var project = APMRoot.global.queries.projectQueries.projects
        .firstWhereOrNull(
            (element) => element.id == widget.task.workOrder.projectId);
    if (project == null) {
      return const Text("Error could not find project associated with task");
    }

    String assetId;

    var walkDown = widget.task.asset.walkDown;
    if (walkDown is Section510_Asset_Walkdown_Details_F) {
      assetId =
          walkDown.sectionIdentification.attributeNumber_or_ID.getValue() ??
              "No Asset Id";
    } else if (walkDown is Section570_Asset_Walkdown_Details_F) {
      assetId = walkDown.sectionIdentification.attributeNumber_or_Circuit_ID
              .getValue() ??
          "No Asset Id";
    } else if (walkDown is Section653_Asset_Walkdown_Details_F) {
      assetId =
          walkDown.sectionIdentification.attributeNumber_or_ID.getValue() ??
              "No Asset Id";
    } else {
      assetId = "Unimplemented asset category";
    }
    Widget descriptionWidget;
    var description = widget.task.clientWorkOrderDescription.getValue();
    if (description != null) {
      descriptionWidget = Text("Description: " + description,
          style: const TextStyle(fontSize: 15, color: Colors.white));
    } else {
      descriptionWidget = Container(height: AppStyle.global.pixels20);
    }

    return Container(
        margin: const EdgeInsets.all(10),
        child: Stack(
          children: [
            Column(children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SizedBox(
                      width: AppStyle.global.pixels80,
                      height: AppStyle.global.pixels20,
                      child: FittedBox(
                          alignment: Alignment.centerLeft,
                          fit: BoxFit.contain,
                          child: Text(widget.task.asset.assetCategory,
                              style: const TextStyle(
                                  fontSize: 15, color: Colors.white)))),
                  Container(
                      alignment: Alignment.center,
                      child: Column(
                        children: [
                          Text(widget.task.status.getPreviewText(),
                              style: const TextStyle(
                                  fontSize: 15, color: Colors.white)),
                        ],
                      )),
                  Container(
                    margin: const EdgeInsets.fromLTRB(5, 0, 5, 0),
                    height: AppStyle.global.pixels20,
                    child: Text(widget.task.taskType,
                        style:
                            const TextStyle(fontSize: 15, color: Colors.white)),
                  )
                ],
              ),
              Container(height: 10),
              Container(
                alignment: Alignment.centerLeft,
                child: Text("Asset ID: " + assetId,
                    style: const TextStyle(
                        fontSize: 17,
                        fontWeight: FontWeight.bold,
                        color: Colors.white)),
              ),
              Container(height: 5),
              descriptionWidget,
              Container(height: 10),
            ]),
          ],
        ));
  }

  void onCardTapped() {
    FocusScope.of(context).unfocus();
    var task = widget.task;
    Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => TaskCompletionBarWrapper(
                    task: widget.task,
                    child: TaskEditPage(Navigator.of(context), task))))
        .then((value) => setState(() {}));
  }

  Widget buildTopOfCard(BuildContext context) {
    return ElevatedButton(
        onPressed: onCardTapped,
        style: ButtonStyle(
            backgroundColor: MaterialStateProperty.resolveWith<Color?>(
          (Set<MaterialState> states) {
            return const Color.fromARGB(255, 41, 45, 52);
          },
        )),
        child: buildInnerCard(context));
  }

  Widget buildBottomOfCard(BuildContext context) {
    return ElevatedButton(
        onPressed: onCardTapped,
        style: ButtonStyle(
            backgroundColor: MaterialStateProperty.resolveWith<Color?>(
          (Set<MaterialState> states) {
            return Colors.blueGrey[800];
          },
        )),
        child: Container(
          margin: const EdgeInsets.fromLTRB(20, 14, 20, 14),
          child: Row(
            children: [
              const Text("Assigned To: ",
                  style: TextStyle(fontSize: 16, color: Colors.white)),
              Container(width: 10),
              Expanded(
                child: Text(widget.task.getAssignedToPreviewText(),
                    style: const TextStyle(fontSize: 15, color: Colors.white)),
              )
            ],
          ),
        ));
  }

  @override
  Widget build(BuildContext context) {
    var card = Card(
        margin: const EdgeInsets.all(10),
        color: const Color.fromARGB(255, 41, 45, 52),
        child: Column(
            children: [buildTopOfCard(context), buildBottomOfCard(context)]));

    return SideSlideoutControl(child: card);
  }
}
