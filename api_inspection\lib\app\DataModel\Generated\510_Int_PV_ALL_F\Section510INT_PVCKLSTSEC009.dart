//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class Section510INT_PVCKLSTSEC009 extends DataModelSection {
  @override
  String getDisplayName() => "INTERNAL TRAYS AND COMPONENTS";
  Section510INT_PVCKLSTSEC009(DataModelItem? parent)
      : super(parent: parent, sectionName: "INTERNAL TRAYS AND COMPONENTS");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC009Q001 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Does the vessel have internal components: (I.e. trays, downcomers, weirs etc.)(Note: the number and type of trays (Sieve trays, cascade trays, valve trays, etc.) shall be recorded. Tray numbering sequence will be from top of vessel down.",
          databaseName: "510_INT-PV_CKLST_SEC009_Q001",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC009Q002 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are tray decks in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC009_Q002",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC009Q003 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are internal manways and clips in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC009_Q003",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC009Q004 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are tray perforations in acceptable condition for continued service: (Note: the type of tray perforations (Floating valves, fixed valves, bubble caps, extruded valves, etc. shall be recorded) (Perforation numbering sequence will correlate with tray numbering)",
          databaseName: "510_INT-PV_CKLST_SEC009_Q004",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC009Q005 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are downcomers and / or weirs in acceptable condition for continued service:  (Downcomers and/or weir numbering sequence will correlate with tray numbering)",
          databaseName: "510_INT-PV_CKLST_SEC009_Q005",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC009Q006 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are miscellaneous hardware associated with trays, perforations, weirs, downcomers, etc. in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC009_Q006",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC009Q007 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are perforations contained within the internal piping / distribution system obstruction free and appropriately shaped: (I.e. round or square cut)",
          databaseName: "510_INT-PV_CKLST_SEC009_Q007",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC009Q008 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are accessible tray support ring attachment welds in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC009_Q008",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC009Q009 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the support plate clamping system in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC009_Q009",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attribute510INT_PVCKLSTSEC009Q001,
      attribute510INT_PVCKLSTSEC009Q002,
      attribute510INT_PVCKLSTSEC009Q003,
      attribute510INT_PVCKLSTSEC009Q004,
      attribute510INT_PVCKLSTSEC009Q005,
      attribute510INT_PVCKLSTSEC009Q006,
      attribute510INT_PVCKLSTSEC009Q007,
      attribute510INT_PVCKLSTSEC009Q008,
      attribute510INT_PVCKLSTSEC009Q009,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section510INT_PVCKLSTSEC009";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
