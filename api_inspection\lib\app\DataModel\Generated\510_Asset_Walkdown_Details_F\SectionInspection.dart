//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class SectionInspection extends DataModelSection {
  @override
  String getDisplayName() => "Inspection";
  SectionInspection(DataModelItem? parent)
      : super(parent: parent, sectionName: "Inspection");

  // ignore: non_constant_identifier_names
  late StringAttribute attributeInspection_Code = StringAttribute(
      parent: this,
      displayName: "Inspection Code",
      databaseName: "510AW_Q116",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeYear = StringAttribute(
      parent: this,
      displayName: "Year",
      databaseName: "510AW_Q117",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeAddendum = StringAttribute(
      parent: this,
      displayName: "Addendum",
      databaseName: "510AW_Q118",
      areCommentsRequired: false);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeInspection_Code,
      attributeYear,
      attributeAddendum,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionInspection";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
