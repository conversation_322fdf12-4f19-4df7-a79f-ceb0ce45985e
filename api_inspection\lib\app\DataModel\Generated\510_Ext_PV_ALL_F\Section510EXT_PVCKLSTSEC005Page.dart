//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'Section510EXT_PVCKLSTSEC005.dart';

// ignore: camel_case_types
class Section510EXT_PVCKLSTSEC005Page extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final Section510EXT_PVCKLSTSEC005 section510EXT_PVCKLSTSEC005;

  const Section510EXT_PVCKLSTSEC005Page(this.section510EXT_PVCKLSTSEC005,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _Section510EXT_PVCKLSTSEC005PageState();
  }
}

class _Section510EXT_PVCKLSTSEC005PageState
    extends State<Section510EXT_PVCKLSTSEC005Page> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.section510EXT_PVCKLSTSEC005,
        title: "STEEL SUPPORTS",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC005
                      .attribute510EXT_PVCKLSTSEC005Q001
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC005
                      .attribute510EXT_PVCKLSTSEC005Q002
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC005
                      .attribute510EXT_PVCKLSTSEC005Q003
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC005
                      .attribute510EXT_PVCKLSTSEC005Q004
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC005
                      .attribute510EXT_PVCKLSTSEC005Q005
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC005
                      .attributeDoes_the_asset_have_a_support_skirt
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC005
                      .attribute510EXT_PVCKLSTSEC005Q007
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC005
                      .attribute510EXT_PVCKLSTSEC005Q008
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC005
                      .attribute510EXT_PVCKLSTSEC005Q009
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC005
                      .attribute510EXT_PVCKLSTSEC005Q010
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC005
                      .attribute510EXT_PVCKLSTSEC005Q011
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC005
                      .attribute510EXT_PVCKLSTSEC005Q012
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC005
                      .attributeIs_the_asset_skirt_insulated
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC005
                      .attribute510EXT_PVCKLSTSEC005Q014
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC005
                      .attributeIs_fireproofing_applied_to_asset_skirt_or_support_beams
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC005
                      .attribute510EXT_PVCKLSTSEC005Q016
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC005
                      .attribute510EXT_PVCKLSTSEC005Q017
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC005
                      .attribute510EXT_PVCKLSTSEC005Q018
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC005
                      .attributeAre_supports_in_acceptable_condition_for_continued_service
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
