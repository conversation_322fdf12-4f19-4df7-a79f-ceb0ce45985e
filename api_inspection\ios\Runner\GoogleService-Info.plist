<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>375236612863-9288t80t32q2tvb8302nj9sijbmje91n.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.375236612863-9288t80t32q2tvb8302nj9sijbmje91n</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>375236612863-h3dovqjvpitbe9cn8iut0770m9f7ed2p.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyBch_DWAHEFB0K659KzgR32qx5d-RTkmSM</string>
	<key>GCM_SENDER_ID</key>
	<string>375236612863</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>teamdigital.flutter.firebase.poc</string>
	<key>PROJECT_ID</key>
	<string>asset-performance-management</string>
	<key>STORAGE_BUCKET</key>
	<string>asset-performance-management.appspot.com</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<true></true>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:375236612863:ios:033adf795f94b147bc72ca</string>
	<key>DATABASE_URL</key>
	<string>https://asset-performance-management-default-rtdb.firebaseio.com</string>
</dict>
</plist>