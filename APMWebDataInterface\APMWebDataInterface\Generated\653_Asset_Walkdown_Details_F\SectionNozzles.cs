//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section653_Asset_Walkdown_Details_F
{
  public class SectionNozzles : DataModelCollectionItem {

    public override String DisplayName { 
      get {
        return "Nozzles";
      }
    }
    
    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public StringAttribute attributeNumber;
    public PhotoAttribute attributePhotos;
    public StringAttribute attributeType;
    public StringAttribute attributeMaterial_Spec_and_Grade;
    public MultiPredefinedValueAttribute attributePipe_Size;
    public MultiPredefinedValueAttribute attributePipe_Schedule;
    public MultiPredefinedValueAttribute attributeFlange_Rating;
    public StringAttribute attributeReinforcement_pad_type;
    public StringAttribute attributeReinforcement_pad_dimensions;
    public DoubleAttribute attributeReinforcement_pad_thickness;

    #endregion [-- Attributes --]

    public SectionNozzles(String id, DataModelItem parent) : base(id, parent)
    {
            
        attributeNumber = new StringAttribute(this, displayName: "Number", databaseName: "653AW_Q451"); 
     
        attributePhotos = new PhotoAttribute(this, displayName: "Photos", databaseName: "653AW_Q460"); 
     
        attributeType = new StringAttribute(this, displayName: "Type", databaseName: "653AW_Q452"); 
     
        attributeMaterial_Spec_and_Grade = new StringAttribute(this, displayName: "Material Spec and Grade", databaseName: "653AW_Q453"); 
     
        attributePipe_Size = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("0.5", null),
          new PredefinedValueOption("0.75", null),
          new PredefinedValueOption("1", null),
          new PredefinedValueOption("1.25", null),
          new PredefinedValueOption("1.5", null),
          new PredefinedValueOption("2", null),
          new PredefinedValueOption("2.5", null),
          new PredefinedValueOption("3", null),
          new PredefinedValueOption("3.5", null),
          new PredefinedValueOption("4", null),
          new PredefinedValueOption("4.5", null),
          new PredefinedValueOption("5", null),
          new PredefinedValueOption("6", null),
          new PredefinedValueOption("8", null),
          new PredefinedValueOption("10", null),
          new PredefinedValueOption("12", null),
          new PredefinedValueOption("14", null),
          new PredefinedValueOption("16", null),
          new PredefinedValueOption("18", null),
          new PredefinedValueOption("20", null),
          new PredefinedValueOption("24", null),
          new PredefinedValueOption("30", null),
          new PredefinedValueOption("36", null),
          new PredefinedValueOption("42", null)
        }, false, this, "Pipe Size", databaseName: "653AW_Q454"); 
     
        attributePipe_Schedule = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("5", null),
          new PredefinedValueOption("10", null),
          new PredefinedValueOption("20", null),
          new PredefinedValueOption("30", null),
          new PredefinedValueOption("40", null),
          new PredefinedValueOption("50", null),
          new PredefinedValueOption("60", null),
          new PredefinedValueOption("70", null),
          new PredefinedValueOption("80", null),
          new PredefinedValueOption("100", null),
          new PredefinedValueOption("120", null),
          new PredefinedValueOption("140", null),
          new PredefinedValueOption("160", null),
          new PredefinedValueOption("STD", null),
          new PredefinedValueOption("EH", null),
          new PredefinedValueOption("DBL.EH", null)
        }, false, this, "Pipe Schedule", databaseName: "653AW_Q455"); 
     
        attributeFlange_Rating = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("150", null),
          new PredefinedValueOption("300", null),
          new PredefinedValueOption("400", null),
          new PredefinedValueOption("600", null),
          new PredefinedValueOption("900", null),
          new PredefinedValueOption("1500", null),
          new PredefinedValueOption("2500", null)
        }, false, this, "Flange Rating", databaseName: "653AW_Q456"); 
     
        attributeReinforcement_pad_type = new StringAttribute(this, displayName: "Reinforcement pad type", databaseName: "653AW_Q457"); 
     
        attributeReinforcement_pad_dimensions = new StringAttribute(this, displayName: "Reinforcement pad dimensions", databaseName: "653AW_Q458"); 
     
        attributeReinforcement_pad_thickness = new DoubleAttribute(this, displayName: "Reinforcement pad thickness", databaseName: "653AW_Q459", areCommentsRequired: false, displayUnit: "in", allowNegatives: true); 
    }   
    
    public override DataModelItem[] GetChildren() {
      var parentChildren = base.GetChildren();

      return parentChildren.Concat(new DataModelItem[] { 
        attributeNumber,
           attributePhotos,
           attributeType,
           attributeMaterial_Spec_and_Grade,
           attributePipe_Size,
           attributePipe_Schedule,
           attributeFlange_Rating,
           attributeReinforcement_pad_type,
           attributeReinforcement_pad_dimensions,
           attributeReinforcement_pad_thickness,
      }).ToArray();
    }
  }
}
