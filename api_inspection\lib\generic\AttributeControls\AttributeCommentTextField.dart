import 'package:api_inspection/app/batch_helper.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/AttributeBase.dart';

class AttributeCommentTextField extends StatefulWidget {
  final AttributeBase _attribute;

  final TextStyle? textStyle;

  final InputDecoration? decoration;

  const AttributeCommentTextField(this._attribute,
      {Key? key, this.decoration, this.textStyle})
      : super(key: key);

  @override
  _AttributeCommentTextFieldState createState() =>
      _AttributeCommentTextFieldState();
}

class _AttributeCommentTextFieldState extends State<AttributeCommentTextField> {
  TextEditingController? _controller;

  @override
  void initState() {
    var attribute = widget._attribute;
    attribute.addListener(onAttributeChanged_FromCommentTextField);
    super.initState();
  }

  @override
  void didUpdateWidget(covariant AttributeCommentTextField oldWidget) {
    super.didUpdateWidget(oldWidget);

    var attribute = oldWidget._attribute;

    attribute.removeListener(onAttributeChanged_FromCommentTextField);
    var controller = _controller;
    if (controller != null) {
      BatchHelper.setCommentAndCommit(attribute, controller.text,
          notify: false);

      String? attributeValue = widget._attribute.getComment();
      controller.text = attributeValue ?? "";
    }

    widget._attribute.addListener(onAttributeChanged_FromCommentTextField);
  }

  @override
  void dispose() {
    var attribute = widget._attribute;
    updateAttributeValue();

    attribute.removeListener(onAttributeChanged_FromCommentTextField);
    _controller?.dispose();
    super.dispose();
  }

  // ignore: non_constant_identifier_names
  void onAttributeChanged_FromCommentTextField() {
    setState(() {
      TextEditingController? controller = _controller;
      var attribute = widget._attribute;
      if (controller != null && controller.text != attribute.getComment()) {
        String? attributeValue = attribute.getComment();
        controller.text = attributeValue ?? "";
      }
    });
  }

  void updateAttributeValue() {
    TextEditingController? controller = _controller;
    if (controller != null) {
      var attribute = widget._attribute;
      BatchHelper.setCommentAndCommit(attribute, controller.text);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_controller == null) {
      var attribute = widget._attribute;
      _controller = TextEditingController(text: attribute.getComment());
    }

    return Focus(
        onFocusChange: (hasFocus) {
          if (!hasFocus) {
            updateAttributeValue();
          }
        },
        child: TextField(
          key: const ValueKey('CommentTextboxField'),
          controller: _controller,
          decoration: widget.decoration ??
              const InputDecoration(
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.white, width: 1.0),
                ),
                border: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.white, width: 1.0),
                ),
              ),
          onSubmitted: (String value) {
            updateAttributeValue();
          },
          style: widget.textStyle ??
              const TextStyle(color: Colors.white, fontSize: 16),
        ));
  }
}
