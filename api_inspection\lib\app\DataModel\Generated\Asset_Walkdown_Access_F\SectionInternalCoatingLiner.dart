//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class SectionInternalCoatingLiner extends DataModelSection {
  @override
  String getDisplayName() => "Internal Coating Liner";
  SectionInternalCoatingLiner(DataModelItem? parent)
      : super(parent: parent, sectionName: "Internal Coating Liner");

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeCoatingLiner_Type =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: true,
          displayName: "Coating/Liner Type",
          databaseName: "AWA_Q341",
          availableOptions: [
        PredefinedValueOption("Concrete", null, isCommentRequired: false),
        PredefinedValueOption("Tile", null, isCommentRequired: false),
        PredefinedValueOption("Epoxy", null, isCommentRequired: false),
        PredefinedValueOption("Resin", null, isCommentRequired: false),
        PredefinedValueOption("None", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeCoatingLiner_Condition =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: true,
          displayName: "Coating/Liner Condition",
          databaseName: "AWA_Q342",
          availableOptions: [
        PredefinedValueOption("Acceptable", null, isCommentRequired: false),
        PredefinedValueOption("Concern", null, isCommentRequired: false),
        PredefinedValueOption("N/A", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeCoatingLiner_Conditions_Observed =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: true,
          displayName: "Coating/Liner Conditions Observed",
          databaseName: "AWA_Q343",
          availableOptions: [
        PredefinedValueOption("Smooth", null, isCommentRequired: false),
        PredefinedValueOption("Peeling", null, isCommentRequired: false),
        PredefinedValueOption("Blistering", null, isCommentRequired: false),
        PredefinedValueOption("Holiday", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeCoatingLiner_Type,
      attributeCoatingLiner_Condition,
      attributeCoatingLiner_Conditions_Observed,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionInternalCoatingLiner";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
