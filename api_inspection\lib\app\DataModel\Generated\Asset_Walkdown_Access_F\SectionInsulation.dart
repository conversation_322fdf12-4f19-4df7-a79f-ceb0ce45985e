//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class SectionInsulation extends DataModelSection {
  @override
  String getDisplayName() => "Insulation";
  SectionInsulation(DataModelItem? parent)
      : super(parent: parent, sectionName: "Insulation");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeDoes_the_asset_have_insulation =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Does the asset have insulation?",
          databaseName: "AWA_Q006",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Partial", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributePossible_asbestos =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Possible asbestos?",
          databaseName: "AWA_Q007",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: true),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeJacketing_type =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: true,
          displayName: "Jacketing type",
          databaseName: "AWA_Q008",
          availableOptions: [
        PredefinedValueOption("Aluminum", null, isCommentRequired: false),
        PredefinedValueOption("Stainless", null, isCommentRequired: false),
        PredefinedValueOption("Ferro Sheeting", null, isCommentRequired: false),
        PredefinedValueOption("PVC", null, isCommentRequired: false),
        PredefinedValueOption("None", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeInsulation_type =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: true,
          displayName: "Insulation type",
          databaseName: "AWA_Q009",
          availableOptions: [
        PredefinedValueOption("Bags/Blankets", null, isCommentRequired: false),
        PredefinedValueOption("Fiber", null, isCommentRequired: false),
        PredefinedValueOption("CalSil", null, isCommentRequired: false),
        PredefinedValueOption("Rock Wool", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeInsulation_removal_required =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Insulation removal required?",
          databaseName: "AWA_Q010",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: true),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeHeat_tracing =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: true,
          displayName: "Heat tracing",
          databaseName: "AWA_Q011",
          availableOptions: [
        PredefinedValueOption("Live Electrical", null,
            isCommentRequired: false),
        PredefinedValueOption("Inactive Electrical", null,
            isCommentRequired: false),
        PredefinedValueOption("Live Steam Tubing", null,
            isCommentRequired: false),
        PredefinedValueOption("Inactive Steam Tubing", null,
            isCommentRequired: false),
        PredefinedValueOption("None", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeDoes_the_asset_have_insulation,
      attributePossible_asbestos,
      attributeJacketing_type,
      attributeInsulation_type,
      attributeInsulation_removal_required,
      attributeHeat_tracing,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionInsulation";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
