import 'dart:developer';

import 'package:api_inspection/generic/MediaControls/PhotoConfirmationPage.dart';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';

typedef OnPhototakenMethod = void Function(MediaPackage photo);

class PhotoPreviewControl extends StatefulWidget {
  final OnPhototakenMethod _onPhotoTaken;
  final bool showConfirmation;

  const PhotoPreviewControl(this._onPhotoTaken,
      {Key? key, this.showConfirmation = true})
      : super(key: key);

  @override
  _PhotoPreviewControlState createState() => _PhotoPreviewControlState();
}

class _PhotoPreviewControlState extends State<PhotoPreviewControl>
    with TickerProviderStateMixin {
  String? errorMessage;

  CameraController? _controller;
  double _minAvailableZoom = 1.0;
  double _maxAvailableZoom = 1.0;
  double _currentScale = 1.0;
  double _baseScale = 1.0;

  double _minAvailableExposureOffset = 0.0;
  double _maxAvailableExposureOffset = 0.0;
  double _currentExposureOffset = 0.0;

  bool _exposureVisible = false;

  bool _flashOn = false;
  bool hasFlash = false;
  // Counting pointers (number of user fingers on screen)
  int _pointers = 0;

  double flashOpacity = 0;

  @override
  void initState() {
    init();
    super.initState();
  }

  @override
  void dispose() {
    var controller = _controller;
    if (controller != null) {
      controller.removeListener(onCameraControllerChanged);
      controller.dispose();
    }

    super.dispose();
  }

  void toggleFlash() async {
    var controller = _controller;
    if (controller == null) return;
    try {
      if (_flashOn) {
        await controller.setFlashMode(FlashMode.off);
      } else {
        await controller.setFlashMode(FlashMode.torch);
      }
    } catch (ex) {
      log(ex.toString(),
          name: '_PhotoPreviewControlState:toggleFlash', time: DateTime.now());
    }
    setState(() {
      _flashOn = !_flashOn;
    });
  }

  void onCameraControllerChanged() {
    CameraController? controller1 = _controller;
    if (controller1 == null) return;

    if (mounted) {
      setState(() {});
    }

    if (controller1.value.hasError) {
      log('Camera error ${controller1.value.errorDescription}',
          name: 'onCameraControllerChanged');
    }
  }

  void init() async {
    var cameras = await availableCameras();
    if (cameras.isEmpty) {
      setState(() {
        errorMessage = "Could not find any cameras to connect to";
      });
      return;
    }
    CameraDescription camera = cameras.first;
    _initCameraController(camera);
  }

  Future _initCameraController(CameraDescription cameraDescription) async {
    if (_controller != null) {
      _controller!.removeListener(onCameraControllerChanged);
      await _controller!.dispose();
    }

    final CameraController cameraController =
        CameraController(cameraDescription, ResolutionPreset.veryHigh);
    _controller = cameraController;
    cameraController.addListener(onCameraControllerChanged);

    try {
      await cameraController.initialize();
      cameraController
          .getMaxZoomLevel()
          .then((value) => _maxAvailableZoom = value);
      cameraController
          .getMinZoomLevel()
          .then((value) => _minAvailableZoom = value);
      cameraController
          .getMinExposureOffset()
          .then((value) => _minAvailableExposureOffset = value);
      cameraController
          .getMaxExposureOffset()
          .then((value) => _maxAvailableExposureOffset = value);

      try {
        await cameraController.setFlashMode(FlashMode.off);
      } catch (e) {
        log(e.toString(),
            name: '_PhotoPreviewControlState:_initCameraController',
            time: DateTime.now());
      }
    } on CameraException catch (e) {
      setState(() {
        errorMessage = e.description.toString();
      });
    }

    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    var controller = _controller;
    if (controller == null || !controller.value.isInitialized) {
      if (errorMessage != null) {
        return Scaffold(
            backgroundColor: Colors.black,
            body: Container(
                alignment: Alignment.center,
                child: Text(
                  'There was an issue setting up the camera\r\n\r\n' +
                      errorMessage!,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.w900,
                  ),
                )));
      }
      return Scaffold(
          backgroundColor: Colors.black,
          body: Container(
              alignment: Alignment.center,
              child: const Text(
                'Loading',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.w900,
                ),
              )));
    }

    Color? buttonColors = Colors.blueGrey[600];
    Color? buttonHightlightColor = Colors.blueGrey[400];
    Icon flashIcon = _flashOn
        ? const Icon(Icons.flash_on, color: Colors.yellow)
        : const Icon(Icons.flash_off);

    Widget fastPhotoButton = widget.showConfirmation
        ? Container()
        : Container(
            width: 50,
            height: 50,
            margin: const EdgeInsets.fromLTRB(10, 10, 10, 10),
            alignment: Alignment.bottomCenter,
            child: FloatingActionButton(
                heroTag: "FastPhoto",
                backgroundColor: buttonColors,
                onPressed: () async {
                  await takePictureWithoutCloseClicked(context);
                },
                child: Stack(
                  children: [
                    const SizedBox(
                        width: 50, height: 50, child: Icon(Icons.camera)),
                    Container(
                        width: 50,
                        height: 50,
                        alignment: Alignment.centerRight,
                        child: const Icon(Icons.add, size: 16))
                  ],
                )),
          );

    var stack = Stack(fit: StackFit.expand, children: [
      AspectRatio(
        aspectRatio: controller.value.aspectRatio,
        child: Listener(
          onPointerDown: (_) => _pointers++,
          onPointerUp: (_) => _pointers--,
          child: CameraPreview(
            controller,
            child: LayoutBuilder(
                builder: (BuildContext context, BoxConstraints constraints) {
              return GestureDetector(
                behavior: HitTestBehavior.opaque,
                onScaleStart: _handleScaleStart,
                onScaleUpdate: _handleScaleUpdate,
                onTapDown: (details) => onViewFinderTap(details, constraints),
              );
            }),
          ),
        ),
      ),
      OrientationBuilder(builder: (context, orientation) {
        if (orientation == Orientation.landscape) {
          return Stack(fit: StackFit.expand, children: [
            Container(
                margin: const EdgeInsets.all(20),
                alignment: Alignment.centerRight,
                child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          fastPhotoButton,
                          Container(
                            width: 50,
                            height: 50,
                            margin: const EdgeInsets.all(10),
                            alignment: Alignment.bottomCenter,
                            child: FloatingActionButton(
                              heroTag: "PhotoButton",
                              backgroundColor: buttonColors,
                              onPressed: () async {
                                await takePictureClicked(context);
                              },
                              child: const Icon(Icons.camera),
                            ),
                          ),
                        ],
                      ),
                      Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Container(
                              width: 50,
                              height: 50,
                              margin: const EdgeInsets.all(10),
                              alignment: Alignment.bottomCenter,
                              child: FloatingActionButton(
                                heroTag: "FlashButton",
                                backgroundColor: buttonColors,
                                onPressed: toggleFlash,
                                child: flashIcon,
                              ),
                            ),
                            Container(
                              width: 50,
                              height: 50,
                              margin: const EdgeInsets.all(10),
                              alignment: Alignment.bottomCenter,
                              child: FloatingActionButton(
                                heroTag: "SwitchCameraButton",
                                backgroundColor: buttonColors,
                                onPressed: cameraSwitchClicked,
                                child: const Icon(Icons.cameraswitch),
                              ),
                            ),
                            Container(
                              width: 50,
                              height: 50,
                              margin: const EdgeInsets.fromLTRB(10, 10, 10, 10),
                              alignment: Alignment.bottomCenter,
                              child: FloatingActionButton(
                                heroTag: "ExposureButton",
                                backgroundColor: _exposureVisible
                                    ? buttonHightlightColor
                                    : buttonColors,
                                onPressed: onExposureModeButtonPressed,
                                child: const Icon(Icons.exposure),
                              ),
                            ),
                          ])
                    ])),
            _exposureModeControlRowWidget(const EdgeInsets.fromLTRB(0, 0, 0, 0))
          ]);
        }
        return Container(
            margin: const EdgeInsets.all(10),
            child: Stack(
              fit: StackFit.expand,
              children: [
                Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      //Container( height: 200, width: 200, child: _exposureModeControlRowWidget()),
                      Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Container(
                              width: 50,
                              height: 50,
                              margin: const EdgeInsets.fromLTRB(10, 10, 10, 10),
                              alignment: Alignment.bottomCenter,
                              child: FloatingActionButton(
                                heroTag: "ExposureButton2",
                                backgroundColor: _exposureVisible
                                    ? buttonHightlightColor
                                    : buttonColors,
                                onPressed: onExposureModeButtonPressed,
                                child: const Icon(Icons.exposure),
                              ),
                            ),
                            Container(
                              width: 50,
                              height: 50,
                              margin: const EdgeInsets.all(10),
                              alignment: Alignment.bottomCenter,
                              child: FloatingActionButton(
                                heroTag: "CameraSwitchButton2",
                                backgroundColor: buttonColors,
                                onPressed: cameraSwitchClicked,
                                child: const Icon(Icons.cameraswitch),
                              ),
                            ),
                            Container(
                              width: 50,
                              height: 50,
                              margin: const EdgeInsets.all(10),
                              alignment: Alignment.bottomCenter,
                              child: FloatingActionButton(
                                heroTag: "FlashButton2",
                                backgroundColor: buttonColors,
                                onPressed: toggleFlash,
                                child: flashIcon,
                              ),
                            ),
                          ]),
                      Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            fastPhotoButton,
                            Container(
                              width: 50,
                              height: 50,
                              margin: const EdgeInsets.fromLTRB(10, 10, 10, 10),
                              alignment: Alignment.bottomCenter,
                              child: FloatingActionButton(
                                heroTag: "TakePhotoButton2",
                                key: const ValueKey("PhotoCaptureBtn"),
                                backgroundColor: buttonColors,
                                onPressed: () async {
                                  await takePictureClicked(context);
                                },
                                child: const Icon(Icons.camera),
                              ),
                            ),
                          ])
                    ]),
                _exposureModeControlRowWidget(
                    const EdgeInsets.fromLTRB(0, 0, 0, 60)),
              ],
            ));
      }),
      IgnorePointer(
        child: AnimatedOpacity(
          opacity: flashOpacity,
          duration: const Duration(milliseconds: 150),
          curve: Curves.linear,
          child: Container(color: Colors.white54),
          onEnd: () {
            if (flashOpacity == 1) {
              setState(() {
                flashOpacity = 0;
              });
            }
          },
        ),
      )
    ]);

    return Scaffold(body: stack);
  }

  Widget _exposureModeControlRowWidget(EdgeInsets margin) {
    return Align(
        alignment: Alignment.bottomCenter,
        child: AnimatedOpacity(
          duration: const Duration(milliseconds: 100),
          opacity: _exposureVisible ? 1 : 0,
          child: Container(
              width: double.infinity,
              height: 65,
              margin: margin,
              child: Column(
                children: [
                  // Text(_currentExposureOffset.toString()),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      buildOutlinedText(_minAvailableExposureOffset.toString()),
                      Slider(
                        value: _currentExposureOffset,
                        min: _minAvailableExposureOffset,
                        max: _maxAvailableExposureOffset,
                        divisions: 32,
                        label: _currentExposureOffset.toString(),
                        onChanged: _minAvailableExposureOffset ==
                                _maxAvailableExposureOffset
                            ? null
                            : setExposureOffset,
                      ),
                      buildOutlinedText(_maxAvailableExposureOffset.toString()),
                    ],
                  ),
                ],
              )),
        ));
  }

  Widget buildOutlinedText(String textValue) {
    return Stack(
      children: <Widget>[
        Text(
          textValue,
          style: TextStyle(
            fontSize: 12,
            foreground: Paint()
              ..style = PaintingStyle.stroke
              ..strokeWidth = 2
              ..color = Colors.black,
          ),
        ),
        Text(
          textValue,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.white,
          ),
        ),
      ],
    );
  }

  void onSetExposureModeButtonPressed(ExposureMode mode) {
    setExposureMode(mode).then((_) {
      if (mounted) setState(() {});
      //showInSnackBar('Exposure mode set to ${mode.toString().split('.').last}');
    });
  }

  Future<void> setExposureOffset(double offset) async {
    var controller = _controller;
    if (controller == null) {
      return;
    }

    setState(() {
      _currentExposureOffset = offset;
    });
    try {
      offset = await controller.setExposureOffset(offset);
    } on CameraException {
      //_showCameraException(e);
      rethrow;
    }
  }

  Future<void> setExposureMode(ExposureMode mode) async {
    var controller = _controller;
    if (controller == null) {
      return;
    }

    try {
      await controller.setExposureMode(mode);
    } on CameraException {
      //  _showCameraException(e);
      rethrow;
    }
  }

  void onExposureModeButtonPressed() {
    setState(() {
      _exposureVisible = !_exposureVisible;
    });
  }

  void onViewFinderTap(
      TapDownDetails details, BoxConstraints constraints) async {
    var controller = _controller;
    if (controller == null || _switchingCameras) {
      return;
    }
    try {
      await controller.setFocusMode(FocusMode.locked);

      final offset = Offset(
        details.localPosition.dx / constraints.maxWidth,
        details.localPosition.dy / constraints.maxHeight,
      );
      await controller.setExposurePoint(offset);
      await controller.setFocusPoint(offset);
    } catch (e) {
      log(e.toString(), name: 'CameraPreviewPage');
    }
  }

  void _handleScaleStart(ScaleStartDetails details) {
    _baseScale = _currentScale;
  }

  Future<void> _handleScaleUpdate(ScaleUpdateDetails details) async {
    var controller = _controller;
    // When there are not exactly two fingers on screen don't scale
    if (controller == null || _pointers != 2 || _switchingCameras) {
      return;
    }

    _currentScale = (_baseScale * details.scale)
        .clamp(_minAvailableZoom, _maxAvailableZoom);

    await controller.setZoomLevel(_currentScale);
  }

  Future<void> takePictureWithoutCloseClicked(BuildContext context) async {
    if (_switchingCameras) return;
    var controller = _controller;
    if (controller == null || !controller.value.isInitialized) return;

    if (controller.value.isTakingPicture) return;

    setState(() {
      flashOpacity = 1;
    });
    var photo = await controller.takePicture();
    var photoPackage = MediaPackage(XFile(photo.path), null);
    widget._onPhotoTaken(photoPackage);
  }

  Future<void> takePictureClicked(BuildContext context) async {
    if (_switchingCameras) return;
    var controller = _controller;
    if (controller == null || !controller.value.isInitialized) return;

    if (controller.value.isTakingPicture) return;

    var photo = await controller.takePicture();
    if (widget.showConfirmation) {
      Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) =>
                  PhotoConfirmationPage(photo, (MediaPackage? photoPackage) {
                    if (photoPackage != null) {
                      widget._onPhotoTaken(photoPackage);
                    }
                    Navigator.pop(context);
                  })));
    } else {
      var photoPackage = MediaPackage(XFile(photo.path), null);
      widget._onPhotoTaken(photoPackage);
      Navigator.pop(context);
    }
  }

  bool _switchingCameras = false;

  void cameraSwitchClicked() async {
    var controller = _controller;
    if (controller == null) return;
    if (_switchingCameras) return;
    _switchingCameras = true;

    final cameras = await availableCameras();

    if (cameras.length < 2) return;

    int currentIndex = cameras.indexOf(controller.description);

    for (int i = 1; i <= cameras.length; i++) {
      int newIndex = (currentIndex + i) % cameras.length;
      try {
        var camera = cameras[newIndex];
        await _initCameraController(camera);
        break;
      } catch (ex) {
        log(ex.toString(), name: 'CameraPreviewPage');
      }
    }

    _switchingCameras = false;
  }
}
