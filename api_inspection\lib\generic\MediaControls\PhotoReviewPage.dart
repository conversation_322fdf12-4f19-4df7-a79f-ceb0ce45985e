import 'package:api_inspection/generic/AppStyle.dart';
import 'package:api_inspection/generic/MediaControls/photo_widgets_for_collection.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/AttributeControls/String/StringAttributeTextField.dart';
import 'package:api_inspection/generic/MediaControls/MediaEntry.dart';
import 'package:api_inspection/generic/UIControls/SwipeGestureDetector.dart';

class PhotoReviewPage extends StatefulWidget {
  final MediaEntry _media;
  late final List<MediaEntry> _otherMedia;

  PhotoReviewPage(this._media, List<MediaEntry> otherMedia, {Key? key})
      : super(key: key) {
    _otherMedia = otherMedia.toList();
  }

  @override
  _PhotoReviewPageState createState() => _PhotoReviewPageState();
}

class _PhotoReviewPageState extends State<PhotoReviewPage> {
  late MediaEntry media;

  @override
  void initState() {
    media = widget._media;
    media.addListener(onMediaUpdated);
    super.initState();
  }

  @override
  void dispose() {
    media.removeListener(onMediaUpdated);
    super.dispose();
  }

  void onMediaUpdated() {
    setState(() {});
  }

  void rotatePhotoLeft() {
    media.rotate(-90);
  }

  void rotatePhotoRight() {
    media.rotate(90);
  }

  void uploadPhoto() {
    media.upload();
  }

  @override
  Widget build(BuildContext context) {
    Widget imgControl = Expanded(child: MediaEntryImage(mediaEntry: media));

    var rotateImgControls = Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Container(
            margin: const EdgeInsets.fromLTRB(15, 5, 15, 5),
            child: Visibility(
                visible: false,
                child: Ink(
                    decoration: const ShapeDecoration(
                      color: Colors.blueGrey,
                      shape: CircleBorder(),
                    ),
                    child: IconButton(
                        splashRadius: 25,
                        iconSize: 40,
                        onPressed: rotatePhotoLeft,
                        icon: const Icon(
                          Icons.rotate_left,
                          color: Colors.white,
                        ))))),
        Stack(alignment: Alignment.center, children: <Widget>[
          Visibility(
              maintainSize: true,
              maintainAnimation: true,
              maintainState: true,
              visible: !media.isUploading,
              child: Container(
                  alignment: Alignment.center,
                  margin: const EdgeInsets.fromLTRB(15, 5, 15, 5),
                  child: Material(
                      color: Colors.transparent,
                      child: Ink(
                          decoration: const ShapeDecoration(
                            color: Colors.blueGrey,
                            shape: CircleBorder(),
                          ),
                          child: IconButton(
                              splashRadius: 25,
                              iconSize: 40,
                              onPressed: uploadPhoto,
                              icon: const Icon(
                                Icons.cloud_upload,
                                color: Colors.white,
                              )))))),
          Visibility(
              maintainSize: true,
              maintainAnimation: true,
              maintainState: true,
              visible: media.isUploading,
              child: Container(
                  alignment: Alignment.center,
                  child: const Text(
                    "Uploading...",
                    textAlign: TextAlign.start,
                    style: TextStyle(color: Colors.white, fontSize: 22),
                  ))),
        ]),
        Container(
            margin: const EdgeInsets.fromLTRB(15, 5, 15, 5),
            child: Visibility(
                visible: false,
                child: Ink(
                    decoration: const ShapeDecoration(
                      color: Colors.blueGrey,
                      shape: CircleBorder(),
                    ),
                    child: IconButton(
                        splashRadius: 25,
                        iconSize: 40,
                        onPressed: rotatePhotoRight,
                        icon: const Icon(Icons.rotate_right,
                            color: Colors.white))))),
      ],
    );

    var descriptionLabel = Container(
        margin: const EdgeInsets.fromLTRB(25, 5, 25, 0),
        alignment: Alignment.centerLeft,
        child: const Text(
          "Description",
          textAlign: TextAlign.start,
          style: TextStyle(color: Colors.white, fontSize: 22),
        ));

    var descriptionTextField = Container(
        margin: const EdgeInsets.fromLTRB(25, 5, 25, 25),
        child: StringAttributeTextField(media.description));

    if (media.isLoading) {
      return Scaffold(
          backgroundColor: const Color.fromARGB(255, 24, 28, 32),
          appBar: AppBar(
            title: Text(
              'Photo Review',
              style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
            ),
            toolbarHeight: AppStyle.global.toolBarHeight,
          ),
          body: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [descriptionLabel, descriptionTextField]));
    } else {
      return Scaffold(
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        appBar: AppBar(
          title: Text(
            'Photo Review',
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        body: SwipeGestureRecognizer(
          onSwipeRight: () {
            var others = widget._otherMedia;
            var index = others.indexOf(media);
            if (index > 0) {
              setState(() {
                media.removeListener(onMediaUpdated);
                media = others[index - 1];
                media.addListener(onMediaUpdated);
              });
            }
          },
          onSwipeLeft: () {
            var others = widget._otherMedia;
            var index = others.indexOf(media);
            if (index < others.length - 1) {
              setState(() {
                media.removeListener(onMediaUpdated);
                media = others[index + 1];
                media.addListener(onMediaUpdated);
              });
            }
          },
          child: Column(children: [
            imgControl,
            rotateImgControls,
            descriptionLabel,
            descriptionTextField
          ]),
        ),
      );
    }
  }
}
