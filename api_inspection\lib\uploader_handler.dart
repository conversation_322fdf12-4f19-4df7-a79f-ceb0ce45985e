import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_uploader/flutter_uploader.dart';

void backgroundUploadHandler() {
  // Needed so that plugin communication works.
  WidgetsFlutterBinding.ensureInitialized();

  // This uploader instance works within the isolate only.
  FlutterUploader uploader = FlutterUploader();

  // You have now access to:
  uploader.progress.listen((progress) {}, onError: (ex, stacktrace) {
    log(ex.toString(), name: 'backgroundUploadHandler');
  });
  uploader.result.listen((result) {}, onError: (ex, stacktrace) {
    log(ex.toString(), name: 'backgroundUploadHandler');
  });
}
