import 'dart:async';
import 'package:api_inspection/app/DataModel/client.dart';
import 'package:api_inspection/app/Queries/Queries.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:api_inspection/generic/DataModelCommon/DatabaseInterfaces/FirebaseDatabaseHelper.dart';
import 'package:api_inspection/generic/UIControls/listener.dart';

import '../../generic/AppRoot.dart';

class ClientQueries {
  late Queries _parent;

  ListenerWrapper clientsChangedListener = ListenerWrapper();

  List<Client> clients = [];
  List<StreamSubscription<QuerySnapshot<Map<String, dynamic>>>>
      clientSubscription = [];

  Future dispose() async {
    for (var element in clientSubscription) {
      await element.cancel();
    }
    clientsChangedListener.dispose();
  }

  void initialize(Queries parent) {
    _parent = parent;
    startQuery();
  }

  void startQuery() {
    var databaseReference = FirebaseDatabaseHelper.global().databaseReference;
    final buIdBatches = AppRoot.global().businessIdsBatches;
    if (buIdBatches != null) {
      for (var buIdBatch in buIdBatches) {
        clientSubscription.add(
          databaseReference
              .collection('clients')
              .where(FieldPath.documentId, whereIn: buIdBatch.toList())
              .snapshots()
              .listen(onClientsUpdated),
        );
      }
    }
  }

  void onClientsUpdated(QuerySnapshot<Map<String, dynamic>> snapshot) {
    List<Client> newClients = [];
    for (var item in snapshot.docs) {
      var client = ClientCache.findEntry(item.id);
      client.updateFromMap(item.data());
      client.setShouldDownloadPhotos(false);

      newClients.add(client);
    }

    clients = newClients;

    clientsChangedListener.notifyListeners();
  }
}
