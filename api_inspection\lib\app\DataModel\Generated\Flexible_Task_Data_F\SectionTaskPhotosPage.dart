//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/UI/CollectionControl.dart';
import 'package:uuid/uuid.dart';
import 'SectionTaskPhotos.dart';

// ignore: camel_case_types
// ignore: camel_case_types
class SectionTaskPhotosPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final DataModelCollection<SectionTaskPhotos> sectionTaskPhotos;
  const SectionTaskPhotosPage(this.sectionTaskPhotos, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionTaskPhotosPageState();
  }
}

class _SectionTaskPhotosPageState extends State<SectionTaskPhotosPage> {
  Widget _cardBuilder(
      BuildContext context, int number, SectionTaskPhotos item) {
    return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(number.toString(),
              style: const TextStyle(color: Colors.white, fontSize: 18)),
        ]);
  }

  Widget _editPageBuilder(BuildContext context, SectionTaskPhotos item) {
    return SectionScaffold(
        section: item,
        title: "TaskPhotos",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(item.attributePhotos
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item.attributeDescription
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item.attributeGIS_Location
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }

  SectionTaskPhotos _createNewItem() {
    String id = const Uuid().v4();
    var item = SectionTaskPhotos(id, widget.sectionTaskPhotos);

    return item;
  }

  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionTaskPhotos,
        title: "TaskPhotos",
        childBuilder: (editableController) {
          return Center(
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: <Widget>[
                Expanded(
                    child: CollectionControl<SectionTaskPhotos>(
                  cardTitle: "TaskPhotos",
                  collection: widget.sectionTaskPhotos,
                  cardBuilder: _cardBuilder,
                  createNewItem: _createNewItem,
                  editPageBuilder: _editPageBuilder,
                ))
              ]));
        });
  }
}
