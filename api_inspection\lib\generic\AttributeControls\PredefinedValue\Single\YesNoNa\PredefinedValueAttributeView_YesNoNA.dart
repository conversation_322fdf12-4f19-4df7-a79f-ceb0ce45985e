import 'package:api_inspection/generic/AttributeControls/AttributeStructure.dart';
import 'package:api_inspection/generic/AttributeControls/String/StringAttributeViewNonEditable.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
import 'package:flutter/material.dart';

import 'PredefinedValueAttributeView_YesNoNAEditable.dart';

class PredefinedValueAttributeView_YesNoNA extends StatefulWidget {
  final PredefinedValueAttribute _attribute;
  final IsEditableController? editingController;
  final bool showPhotos;
  final bool showComments;

  const PredefinedValueAttributeView_YesNoNA(this._attribute,
      {Key? key,
      this.editingController,
      this.showPhotos = true,
      this.showComments = true})
      : super(key: key);

  @override
  _PredefinedValueAttributeView_YesNoNAState createState() =>
      _PredefinedValueAttributeView_YesNoNAState();
}

class _PredefinedValueAttributeView_YesNoNAState
    extends State<PredefinedValueAttributeView_YesNoNA> {
  @override
  Widget build(BuildContext context) {
    return AttributeStructure(widget._attribute,
        showComments: widget.showComments,
        showPhotos: widget.showPhotos,
        editingController: widget.editingController,
        editingBuilder: (context, updateListener) {
      return PredefinedValueAttributeView_YesNoNAEditable(widget._attribute);
    }, nonEditingBuilder: (context) {
      return StringAttributeViewNonEditable(widget._attribute);
    });
  }
}
