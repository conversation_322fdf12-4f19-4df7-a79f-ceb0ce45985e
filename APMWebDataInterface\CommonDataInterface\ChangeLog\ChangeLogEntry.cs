﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json.Linq;

namespace CommonDataInterface
{
  public class ChangeLogEntry<DataType>
  {

    public String Key { get; private set; }

    public DataType Value { get; private set; }

    public DateTime TimeChanged { get; private set; }

    public String UserName { get; private set; }

    public ChangeLogEntry(String key, DataType value, DateTime timeChanged, String userName)
    {
      Key = key;
      Value = value;
      TimeChanged = timeChanged;
      UserName = userName;
    }


    public static ChangeLogEntry<DataType> CreateNew(DataType value)
    {
      var key = Guid.NewGuid().ToString();
      var timeChanged = DateTime.Now;
      var userName = "Unknown";

      return new ChangeLogEntry<DataType>(key, value, timeChanged, userName);
    }


    public virtual void AddUpdates(Dictionary<string, Object> updates, String path)
    {
      var localTime = new DateTime(TimeChanged.Ticks, DateTimeKind.Local);
      var asUTC = localTime.ToUniversalTime();
      updates[path + ".T"] = new DateTimeOffset(asUTC).ToUnixTimeMilliseconds();
      updates[path + ".U"] = UserName;
      updates[path + ".V"] = Value;
    }
  }
}