import 'dart:async';

import 'package:api_inspection/app/APMRoot.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/asset.dart';
import 'package:api_inspection/app/DataModel/assetCard.dart';
import 'package:api_inspection/generic/DataModelCommon/DatabaseInterfaces/FirebaseDatabaseHelper.dart';

class AssetLoadingDialog extends StatefulWidget {
  late final AssetCard _assetToLoad;

  final Function() onAssetLoaded;
  final Function() onAssetFailedToLoad;

  AssetLoadingDialog(
      {Key? key,
      required AssetCard assetToLoad,
      required this.onAssetLoaded,
      required this.onAssetFailedToLoad})
      : super(key: key) {
    _assetToLoad = assetToLoad;
  }

  @override
  State<StatefulWidget> createState() {
    return _AssetLoadingDialogState();
  }
}

class _AssetLoadingDialogState extends State<AssetLoadingDialog> {
  String? error;

  @override
  void initState() {
    startQuery();
    super.initState();
  }

  void startQuery() async {
    var databaseReference = FirebaseDatabaseHelper.global().databaseReference;

    try {
      var result = await databaseReference
          .collection('assets')
          .doc(widget._assetToLoad.assetDBId)
          .get()
          .timeout(const Duration(seconds: 5), onTimeout: () {
        throw TimeoutException('Timed out');
      });

      if (!result.exists) {
        setState(() {
          error =
              "Could not download asset.\r\n\r\nThe asset will be automatically downloaded when you are in an area with internet connectivity.";
        });
        return;
      }
      var entry = result;
      var newAsset = Asset(
          id: entry.id,
          assetCategory: entry.data()!["AssetCategory"] as String,
          locationId: entry.data()!["LocationId"] as String);
      newAsset.updateFromMap(entry.data());
      newAsset.setShouldDownloadPhotos(false);
      widget._assetToLoad.asset = newAsset;
      APMRoot.global.newQueries.addAssetIdToSession(newAsset.id);

      widget.onAssetLoaded();
    } catch (e) {
      setState(() {
        error =
            "Could not download asset.\r\n\r\nThe asset will be automatically downloaded when you are in an area with internet connectivity.";
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (error != null) {
      return AlertDialog(
        backgroundColor: const Color.fromARGB(255, 41, 45, 52),
        title: const Text('Error with asset download',
            style: TextStyle(color: Colors.white)),
        content: SizedBox(
            height: 300,
            child: Text(error!, style: const TextStyle(color: Colors.white))),
        actions: [
          ElevatedButton(
            child: const Text('Ok', style: TextStyle(color: Colors.white)),
            onPressed: () {
              widget.onAssetFailedToLoad();
            },
          ),
        ],
      );
    }
    return const AlertDialog(
      backgroundColor: Color.fromARGB(255, 41, 45, 52),
      title: Text('Downloading Asset', style: TextStyle(color: Colors.white)),
      content: SizedBox(
          height: 300,
          child: Text(
              "Asset has not been loaded into memory.  Downloading now..",
              style: TextStyle(color: Colors.white))),
      actions: [],
    );
  }
}
