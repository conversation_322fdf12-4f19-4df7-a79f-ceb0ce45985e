import 'dart:convert';

import 'package:api_inspection/app/DataModel/assetCard.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/ConcretePhotoRoot.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/DataModelCommon/DatabaseInterfaces/DatabaseHelperManager.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class LocationCache {
  static final Map<String, Location> _map = <String, Location>{};

  static bool containsKey(String id) {
    return _map.containsKey(id);
  }

  static Location findEntry(String id) {
    if (_map.containsKey(id)) {
      return _map[id] as Location;
    }
    var newLocation = Location(id);
    _map[id] = newLocation;
    return newLocation;
  }

  static void setEntry(String key, Location location) {
    _map[key] = location;
  }

  static List<Location> list() {
    return _map.values.toList();
  }
}

class Location extends ConcretePhotoRoot {
  Location(String id) : super(id);

  @override
  String getDBPath() => "locations." + getDBName();

  @override
  String getDisplayName() => "Location";

  AssetCard getCardFromBinary(String key, List<int> binary) {
    var decoded = utf8.decode(binary);
    var split = decoded.split("|");
    if (split.length != 5 && split.length != 4) {
      return AssetCard(
          assetDBId: key,
          assetName: "Unknown - missing cache information",
          assetId: "Unknown - missing cache information",
          assetCategory: "Unknown - missing cache information",
          assetType: "Unknown - missing cache information",
          businessUnitId: "Unknown - missing cache information");
    }

    var name = split[0];
    var id = split[1];
    var category = split[2];
    var type = split[3];
    var businessUnitId = split.length == 5 ? split[4] : null;
    return AssetCard(
        assetDBId: key,
        assetName: name == " " ? null : name,
        assetId: id == " " ? null : id,
        assetCategory: category,
        assetType: type == " " ? null : type,
        businessUnitId: businessUnitId == " " ? null : businessUnitId);
  }

  MapEntry convertCardToBinaryEntry(AssetCard card) {
    var str = (card.assetName?.replaceAll("|", " ") ?? " ") +
        "|" +
        (card.assetId?.replaceAll("|", " ") ?? " ") +
        "|" +
        card.assetCategory +
        "|" +
        (card.assetType?.replaceAll("|", " ") ?? " ") +
        "|" +
        (card.businessUnitId?.replaceAll("|", " ") ?? " ");
    var encoded = utf8.encode(str);

    return MapEntry(card.assetDBId, encoded);
  }

  late BinaryCollectionWithoutHistory<AssetCard> assetCards =
      BinaryCollectionWithoutHistory<AssetCard>(
          name: "AssetCards",
          convertFromBinary: getCardFromBinary,
          convertToBinary: convertCardToBinaryEntry,
          parent: this);

  late StringAttribute name =
      StringAttribute(parent: this, displayName: "Name");
  late StringAttribute businessUnitId = StringAttribute(
      parent: this, displayName: "BusinessUnitId", isQueryable: true);
  late StringAttribute description =
      StringAttribute(parent: this, displayName: "Description");
  late StringAttribute street1 =
      StringAttribute(parent: this, displayName: "Address Line 1");
  late StringAttribute street2 =
      StringAttribute(parent: this, displayName: "Address Line 2");
  late StringAttribute city =
      StringAttribute(parent: this, displayName: "City");
  late StringAttribute region =
      StringAttribute(parent: this, displayName: "State / Region");
  late StringAttribute postalCode =
      StringAttribute(parent: this, displayName: "Zip code");
  late StringAttribute country =
      StringAttribute(parent: this, displayName: "Country");
  late DoubleAttribute latitude = DoubleAttribute(
      parent: this, displayName: "Latitude", allowNegatives: true);
  late DoubleAttribute longitude = DoubleAttribute(
      parent: this, displayName: "Longitude", allowNegatives: true);
  late DoubleAttribute elevation = DoubleAttribute(
      parent: this, displayName: "Elevation", allowNegatives: true);

  late StringAttribute comment =
      StringAttribute(parent: this, displayName: "Comment");

  @override
  List<DataModelItem> getChildren() {
    var parentChildren = super.getChildren().toList();
    parentChildren.addAll([
      name,
      businessUnitId,
      description,
      street1,
      street2,
      city,
      region,
      postalCode,
      country,
      latitude,
      longitude,
      elevation,
      comment,
      assetCards
    ]);

    return parentChildren;
  }

  @override
  void saveDirectItems(WriteBatch batch) async {
    var helper = DatabaseHelperManager.global();
    var path = getDBPath();
    helper.updateItem(path + ".Id", id, batch);
  }

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
