﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace CommonDataInterface.Attributes
{
  public abstract class SingleAttributeBase<ValueType> : AttributeBase
  {
    public virtual ValueType CurrentValue
    {
      get {
        if (_valueChangeLog == null || _valueChangeLog.entries.Count == 0)
          return default(ValueType);
        return this._valueChangeLog.entries.Last().Value;
      }
    }
    
    public virtual ValueType PendingValue
    {
      get {
        if (_valueChangeLog?.PendingChange == null)
          return default(ValueType);
        return GetValueChangeLog().PendingChange.Value;
      }
      set {
        GetValueChangeLog().PendingChange = new PendingChange<ValueType>{Value = value};
      }
    }
    
    public virtual ValueType CurrentPendingOrValue {
      get {
        if (_valueChangeLog?.PendingChange != null) {
          return PendingValue;
        }

        return CurrentValue;
      }
    }
    
    public ChangeLog<ValueType> GetValueChangeLog()
    {
      if (_valueChangeLog == null) {
        _valueChangeLog = new ChangeLog<ValueType>("ValueChangeLog", this, new List<ChangeLogEntry<ValueType>>());
      }
      return _valueChangeLog;
    }

    private ChangeLog<ValueType> _valueChangeLog;
    //[JsonProperty("H")]
    public ChangeLog<ValueType> ValueChangeLog {
      get {
        var root = FindParentOfType<ConcretePhotoRoot>();
        if (root != null && !root.includeHistoryInJson)
          return null;
        return GetValueChangeLog();
      }
    }


    public SingleAttributeBase(DataModelItem parent, String displayName, String databaseName, bool areCommentsRequired, bool isQueryable = false)
      : base(parent, displayName, databaseName, areCommentsRequired, isQueryable)
    {

    }

    public override DataModelItem[] GetChildren()
    {
      return new DataModelItem[]{ GetValueChangeLog(), GetPhotoChangeLog(), GetCommentChangeLog() };
    }

  }
}
