//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section570_Asset_Walkdown_Details_F
{
  public class SectionComponents : DataModelItem {

    public override String DisplayName { 
      get {
        return "Components";
      }
    }

    #region [-- Sub Sections --]

    #region [-- SectionPipe --]
    private DataModelCollection<SectionPipe> _sectionPipe;
    public DataModelCollection<SectionPipe> sectionPipe {
        get {
            if (_sectionPipe == null) {
              _sectionPipe = new DataModelCollection<SectionPipe>("Pipe", (parent, entry) => {
                 return new SectionPipe(entry.Key, _sectionPipe);
              }, (parent, id) => {
                return new SectionPipe(id, _sectionPipe);
              }, this);
            }

            return _sectionPipe;
        }
    }
    #endregion [-- SectionPipe --]
    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionComponents";

    public SectionComponents(DataModelItem parent) : base(parent)
    {
            
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           sectionPipe,
        };
    }
  }
}
