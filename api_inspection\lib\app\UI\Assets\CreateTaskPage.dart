import 'package:api_inspection/app/APMRoot.dart';
import 'package:api_inspection/generic/AppStyle.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/asset.dart';
import 'package:api_inspection/app/DataModel/task.dart';
import 'package:api_inspection/app/DataModel/workorder.dart';
import 'package:api_inspection/generic/UIControls/selector.dart';
import 'package:uuid/uuid.dart';
import 'package:collection/collection.dart';

class CreateTaskPage extends StatefulWidget {
  final Asset asset;
  const CreateTaskPage(this.asset, {Key? key}) : super(key: key);

  @override
  _CreateTaskPageState createState() => _CreateTaskPageState();
}

class _CreateTaskPageState extends State<CreateTaskPage> {
  String? selectedTaskType;

  void showErrorPrompt(String message) {
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: const Color.fromARGB(255, 41, 45, 52),
            title: Row(
              children: [
                Container(
                    margin: const EdgeInsets.all(5),
                    child:
                        const Icon(Icons.error, color: Colors.red, size: 32)),
                const Text(
                  'Error',
                  style: TextStyle(color: Colors.white),
                )
              ],
            ),
            content: Text(message, style: const TextStyle(color: Colors.white)),
            actions: [
              ElevatedButton(
                child: const Text('Ok', style: TextStyle(color: Colors.white)),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        });
  }

  void createTaskClicked() {
    var matchingWorkOrders = APMRoot.global.newQueries.workOrders
        .where((workOrder) => workOrder.asset == widget.asset)
        .toList();

    String taskType;
    // should downcast, but isn't for some reason
    var localTaskType = selectedTaskType;
    if (localTaskType == null) {
      showErrorPrompt("Must select a task type");
      return;
    } else {
      taskType = localTaskType;
    }

    if (taskType == "Internal Visual") taskType = "Full";

    WorkOrder workOrder;
    if (matchingWorkOrders.isNotEmpty) {
      workOrder = matchingWorkOrders.first;
    } else {
      var newId = const Uuid().v4();
      var project = APMRoot.global.queries.selectedProjects
          .getSelectedProjects()
          .firstWhereOrNull(
              (element) => element.location.id == widget.asset.location.id);
      if (project == null) {
        throw Exception("Failed to finding matching project for asset");
      }
      workOrder = WorkOrder(newId, widget.asset, project.id);

      var batch = FirebaseFirestore.instance.batch();
      workOrder.businessUnitId
          .setValue(APMRoot.global.selectedBusinessUnitId, batch);
      workOrder.saveItem(batch);
      // TODO: We weren't awaiting this update, should we be?
      batch.commit();
    }

    var inprogressTasks =
        workOrder.tasks.where((element) => element.isInProgress());
    var taskOfTypeInProgress = inprogressTasks.firstWhereOrNull(
        (a) => a.taskType.toLowerCase() == taskType.toLowerCase());
    if (taskOfTypeInProgress != null) {
      showErrorPrompt(
          "There is already a task of " + taskType + " in progress");
      return;
    }

    var newTaskId = const Uuid().v4();
    var task = Task.createNew(workOrder, newTaskId, taskType);

    var batch = FirebaseFirestore.instance.batch();
    task.businessUnitId.setValue(APMRoot.global.selectedBusinessUnitId, batch);
    task.status.setValue("Not Started", batch);
    workOrder.tasks.add(task);
    task.saveItem(batch);
    // TODO: We weren't awaiting this update, should be be?
    batch.commit();

    APMRoot.global.newQueries.workOrderListener.notifyListeners();
    Navigator.of(context).pop();
  }

  var textFieldDecoration = const InputDecoration(
    enabledBorder: OutlineInputBorder(
      borderSide: BorderSide(color: Colors.white, width: 1.0),
    ),
    border: OutlineInputBorder(
      borderSide: BorderSide(color: Colors.white, width: 1.0),
    ),
  );

  bool isValid() {
    var taskType = selectedTaskType;
    if (taskType == null) {
      return false;
    }

    return true;
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> options = [];

    options.add(Container(height: 20));

    List<String> taskTypeOptions;
    if (widget.asset.assetCategory == "Vessel") {
      taskTypeOptions = [
        "Asset Walkdown",
        "External Visual",
        "Internal Visual"
      ];
    } else if (widget.asset.assetCategory == "Piping") {
      taskTypeOptions = ["Asset Walkdown", "External Visual"];
    } else if (widget.asset.assetCategory == "Tank") {
      taskTypeOptions = ["Asset Walkdown"];
    } else {
      throw Exception(
          "Unexpected asset category: " + widget.asset.assetCategory);
    }

    options.add(const Text("Please select task type",
        style: TextStyle(fontSize: 20, color: Colors.white)));
    options.add(SingleChildScrollView(
        child: Selector<String>(taskTypeOptions, (item) {
      return item;
    }, (String? selectedItem) {
      setState(() {
        selectedTaskType = selectedItem;
      });
    }, buttonColor: Colors.blueGrey[800]!)));

    Color buttonColor = isValid() ? Colors.blue : Colors.blueGrey;
    return Scaffold(
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        appBar: AppBar(
          title: Text(
            "Create Task",
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        body: Container(
            margin: const EdgeInsets.all(10),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  Expanded(
                      child: ListView(
                    children: options,
                  )),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                          margin: const EdgeInsets.all(10),
                          height: 50,
                          child: ElevatedButton(
                              onPressed: () async {
                                Navigator.of(context).pop();
                              },
                              child: const Text("Cancel",
                                  style: TextStyle(fontSize: 18)))),
                      Container(
                          margin: const EdgeInsets.all(10),
                          height: 50,
                          child: ElevatedButton(
                              onPressed: createTaskClicked,
                              child: const Text("Create New Task",
                                  style: TextStyle(fontSize: 18)),
                              style: ButtonStyle(
                                backgroundColor:
                                    MaterialStateProperty.resolveWith<Color?>(
                                  (Set<MaterialState> states) {
                                    return buttonColor; // Use the component's default.
                                  },
                                ),
                              )))
                    ],
                  )
                ],
              ),
            )));
  }
}
