import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/MediaControls/MediaCache.dart';
import 'package:api_inspection/generic/MediaControls/MediaEntry.dart';
import 'package:api_inspection/generic/MediaControls/MediaSynchronizer.dart';

class ListOfMediaItems extends DataModelCollection<MediaEntry> {
  bool _downloadPhotos = false;

  bool getShouldDownloadPhotos() {
    return _downloadPhotos;
  }

  Future<void> setShouldDownloadPhotos(bool newValue) async {
    if (_downloadPhotos == newValue) return;

    _downloadPhotos = newValue;
    List<Future<void>> futures = [];

    var entries = getEntries();
    for (var media in entries) {
      if (newValue) {
        futures.add(IMediaSynchronizer.getMediaSynchronizer()
            .downloadFileForMedia(media));
      }
    }

    for (var future in futures) {
      await future;
    }
  }

  ListOfMediaItems(String name, DataModelItem parent)
      : super(name, (DataModelItem parent, MapEntry map) {
          return MediaCache.fromMapEntry(map.key, map.value, parent);
        }, parent);
}
