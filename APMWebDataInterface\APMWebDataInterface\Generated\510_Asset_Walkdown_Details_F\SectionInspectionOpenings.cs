//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Asset_Walkdown_Details_F
{
  public class SectionInspectionOpenings : DataModelCollectionItem {

    public override String DisplayName { 
      get {
        return "Inspection Openings";
      }
    }
    
    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public StringAttribute attributeOpening_Type;
    public IntegerAttribute attributeOpening_Number;
    public StringAttribute attributeOpening_Size;

    #endregion [-- Attributes --]

    public SectionInspectionOpenings(String id, DataModelItem parent) : base(id, parent)
    {
            
        attributeOpening_Type = new StringAttribute(this, displayName: "Opening Type", databaseName: "653AW_Q171"); 
     
        attributeOpening_Number = new IntegerAttribute(this, displayName: "Opening Number", databaseName: "653AW_Q172", areCommentsRequired: false, displayUnit: null, allowNegatives: true); 
     
        attributeOpening_Size = new StringAttribute(this, displayName: "Opening Size", databaseName: "653AW_Q173"); 
    }   
    
    public override DataModelItem[] GetChildren() {
      var parentChildren = base.GetChildren();

      return parentChildren.Concat(new DataModelItem[] { 
        attributeOpening_Type,
           attributeOpening_Number,
           attributeOpening_Size,
      }).ToArray();
    }
  }
}
