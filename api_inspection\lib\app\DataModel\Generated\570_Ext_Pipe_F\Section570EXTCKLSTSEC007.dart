//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class Section570EXTCKLSTSEC007 extends DataModelSection {
  @override
  String getDisplayName() => "SOIL TO AIR INTERFACE";
  Section570EXTCKLSTSEC007(DataModelItem? parent)
      : super(parent: parent, sectionName: "SOIL TO AIR INTERFACE");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC007Q001 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are any sections of the piping susceptible to soil to air interface damage mechanisms:",
          databaseName: "570_EXT_CKLST_SEC007_Q001",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC007Q002 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Were any of the following indicators for leakage noted, change in the surface contour of the ground, discoloration of the soil, softening of paving asphalt, pool formation, bubbling water puddles, or noticeable odor:",
          databaseName: "570_EXT_CKLST_SEC007_Q002",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC007Q003 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is piping protected by an impressed current cathodic protection system:  (if yes, record rectifier reading)",
          databaseName: "570_EXT_CKLST_SEC007_Q003",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC007Q004 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "For impressed systems are the exposed lead wires in acceptable condition for continued service:",
          databaseName: "570_EXT_CKLST_SEC007_Q004",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC007Q005 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is piping protected by a galvanic anode cathodic protection system:",
          databaseName: "570_EXT_CKLST_SEC007_Q005",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC007Q006 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "For galvanic anode systems, are the exposed anodes in acceptable condition for continued service.",
          databaseName: "570_EXT_CKLST_SEC007_Q006",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC007Q007 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "For galvanic anode systems is the bonding method acceptable for continued service:",
          databaseName: "570_EXT_CKLST_SEC007_Q007",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC007Q008 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Was a Soil Resistivity Test conducted: (if yes record the Soil Resistivity (ohm cm) value obtained)).",
          databaseName: "570_EXT_CKLST_SEC007_Q008",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC007Q009 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are the concrete to air / asphalt to air interface acceptable sealed to prevent water ingress:",
          databaseName: "570_EXT_CKLST_SEC007_Q009",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute570EXTCKLSTSEC007Q010 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Were any corrosion, erosion, or pitting cells noted on the upper limit of the soil to air interface: (~6” above interface).",
          databaseName: "570_EXT_CKLST_SEC007_Q010",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Corrosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Erosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Pitting", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute570EXTCKLSTSEC007Q011 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Were any corrosion, erosion, or pitting cells noted on the lower limit of the soil to air interface: (~12” below interface).",
          databaseName: "570_EXT_CKLST_SEC007_Q011",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Corrosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Erosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Pitting", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC007Q012 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is bituminous coating and or wrapping in acceptable condition for continued service:",
          databaseName: "570_EXT_CKLST_SEC007_Q012",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute570EXTCKLSTSEC007Q013 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Was evidence of past or present leakage noted to be originating from the piping or welded connections:",
          databaseName: "570_EXT_CKLST_SEC007_Q013",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Active Leakage", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Evidence of Prior Leakage", null,
            isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC007Q014 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are the areas encompassed by the soil to air interface in acceptable condition for continued service:",
          databaseName: "570_EXT_CKLST_SEC007_Q014",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attribute570EXTCKLSTSEC007Q001,
      attribute570EXTCKLSTSEC007Q002,
      attribute570EXTCKLSTSEC007Q003,
      attribute570EXTCKLSTSEC007Q004,
      attribute570EXTCKLSTSEC007Q005,
      attribute570EXTCKLSTSEC007Q006,
      attribute570EXTCKLSTSEC007Q007,
      attribute570EXTCKLSTSEC007Q008,
      attribute570EXTCKLSTSEC007Q009,
      attribute570EXTCKLSTSEC007Q010,
      attribute570EXTCKLSTSEC007Q011,
      attribute570EXTCKLSTSEC007Q012,
      attribute570EXTCKLSTSEC007Q013,
      attribute570EXTCKLSTSEC007Q014,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section570EXTCKLSTSEC007";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
