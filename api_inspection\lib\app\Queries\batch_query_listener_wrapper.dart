import 'dart:async';
import 'dart:developer';

import 'package:api_inspection/app/Queries/query_listeners_manager.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class BatchQueryListenerWrapper {
  final Set<String> _ids = <String>{};
  late String _collectionName;
  late FirebaseFirestore _db;
  late QueryListenersManager _manager;

  late StreamSubscription<QuerySnapshot<Map<String, dynamic>>> _subscription;

  int get length {
    return _ids.length;
  }

  List<String> get ids {
    return List<String>.from(_ids);
  }

  BatchQueryListenerWrapper(String collectionName, FirebaseFirestore db,
      QueryListenersManager manager) {
    _collectionName = collectionName;
    _db = db;
    _manager = manager;
  }

  Future<void> cancel() async {
    return await _subscription.cancel();
  }

  bool addId(String id) {
    if (_ids.length >= 10 && !_ids.contains(id)) {
      throw UnsupportedError('Cannot add more than 10 ids');
    }
    return _ids.add(id);
  }

  bool removeId(String id) {
    return _ids.remove(id);
  }

  bool has(String id) {
    return _ids.contains(id);
  }

  buildListener() {
    _subscription = _db
        .collection(_collectionName)
        .where(_collectionName == 'assets' ? FieldPath.documentId : 'AssetId',
            whereIn: _ids.toList())
        .snapshots()
        .listen((event) {
      switch (_collectionName) {
        case 'workorders':
          _manager.updateWorkOrders(event);
          break;
        case 'tasks':
          _manager.updateTasks(event);
          break;
        case 'assets':
          _manager.updateAssets(event);
          break;
        default:
          throw UnsupportedError(
              'Unrecognized collectionName: $_collectionName');
      }
    }, onError: (error) {
      log(error.toString(), name: 'batch_query_listener_wrapper');
    });
  }
}
