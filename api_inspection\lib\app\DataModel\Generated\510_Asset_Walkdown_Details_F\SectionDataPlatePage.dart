//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionDataPlate.dart';

// ignore: camel_case_types
class SectionDataPlatePage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionDataPlate sectionDataPlate;

  const SectionDataPlatePage(this.sectionDataPlate, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionDataPlatePageState();
  }
}

class _SectionDataPlatePageState extends State<SectionDataPlatePage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionDataPlate,
        title: "Data Plate",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionDataPlate.attributeAttached
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionDataPlate.attributeLegible
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
