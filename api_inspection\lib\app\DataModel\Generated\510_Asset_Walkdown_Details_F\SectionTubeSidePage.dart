//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionTubeSide.dart';

// ignore: camel_case_types
class SectionTubeSidePage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionTubeSide sectionTubeSide;

  const SectionTubeSidePage(this.sectionTubeSide, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionTubeSidePageState();
  }
}

class _SectionTubeSidePageState extends State<SectionTubeSidePage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionTubeSide,
        title: "Tube Side",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionTubeSide.attributeDesign_MAWP
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionTubeSide.attributeDesign_Temperature
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionTubeSide.attributeOperating_Temperature
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionTubeSide.attributeOperating_Pressure
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionTubeSide.attributePRV_Set_Pressure
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
