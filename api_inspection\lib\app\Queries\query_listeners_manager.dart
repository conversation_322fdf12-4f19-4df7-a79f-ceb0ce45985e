import 'dart:developer';

import 'package:api_inspection/app/APMRoot.dart';
import 'package:api_inspection/app/DataModel/asset.dart';
import 'package:api_inspection/app/DataModel/task.dart';
import 'package:api_inspection/app/DataModel/workorder.dart';
import 'package:api_inspection/app/Queries/batch_query_listener_wrapper.dart';
import 'package:api_inspection/generic/AppRoot.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModel/user.dart';
import 'package:api_inspection/generic/UIControls/listener.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:darq/darq.dart';

class QueryListenersManager {
  final List<BatchQueryListenerWrapper> _workOrderQueries = [];
  final List<BatchQueryListenerWrapper> _taskQueries = [];
  final List<BatchQueryListenerWrapper> _assetQueries = [];

  /// Reference to the current user's nonverified user profile
  final UnverifiedUserProfile unverifiedUserProfile =
      AppRoot.global().currentUserNonVerified!;

  late FirebaseFirestore _db;

  Set<String> listenedAssetIds = {};
  Set<String> sessionAssetIds = {};

  final Map<String, WorkOrder> workOrders = {};
  final Map<String, Asset> assets = {};

  final Map<String, Map<String, dynamic>?> orphanedWorkOrders = {};
  final Map<String, Map<String, dynamic>?> orphanedTasks = {};

  final String userEmail = AppRoot.global().currentUser!.email;

  ListenerWrapper workOrderListener = ListenerWrapper();
  ListenerWrapper assetListener = ListenerWrapper();

  QueryListenersManager(FirebaseFirestore db) {
    _db = db;
  }

  Future<void> stop() async {
    workOrderListener.dispose();
    assetListener.dispose();
    await _cancelAll();
  }

  Future<void> _cancelAll() async {
    for (var query in _workOrderQueries) {
      await query.cancel();
    }
    for (var query in _taskQueries) {
      await query.cancel();
    }
    for (var query in _assetQueries) {
      await query.cancel();
    }
    _workOrderQueries.clear();
    _taskQueries.clear();
    _assetQueries.clear();
  }

  setIds(String collectionName, Iterable<String> ids) {
    List<BatchQueryListenerWrapper> wrappers;
    switch (collectionName) {
      case 'workorders':
        wrappers = _workOrderQueries;
        break;
      case 'tasks':
        wrappers = _taskQueries;
        break;
      case 'assets':
        wrappers = _assetQueries;
        break;
      default:
        throw UnsupportedError('Unsupported collection name for this class');
    }

    var wrappersToBuild = <BatchQueryListenerWrapper>{};

    // Remove any asset ids we don't care about any more.
    var allIds = wrappers.expand((wrapper) => wrapper.ids);
    var idsToRemove = allIds.except(ids);
    for (var id in idsToRemove) {
      var wrapper = wrappers.firstWhereOrDefault((w) => w.has(id));
      if (wrapper != null) {
        wrapper.removeId(id);
        wrappersToBuild.add(wrapper);
      }
    }

    // Find a wrapper to take on each of these ids we want to listen to.
    for (var id in ids) {
      if (wrappers.every((w) => !w.has(id))) {
        // Find the first wrapper with room for another id
        var wrapper = wrappers.firstWhereOrDefault((w) => w.length < 10);

        // If we didn't find one, create one
        if (wrapper == null) {
          wrapper = _createBatchQueryWrapper(collectionName);
          wrappers.add(wrapper);
        }

        // Add asset id and mark for (re)build
        wrapper.addId(id);
        wrappersToBuild.add(wrapper);
      }
    }

    wrappers.removeWhere((wrapper) => wrapper.length == 0);
    wrappersToBuild.removeWhere((wrapper) => wrapper.length == 0);

    for (var wrapper in wrappersToBuild) {
      wrapper.buildListener();
    }

    assetListener.notifyListeners();
  }

  void updateWorkOrders(QuerySnapshot<Map<String, dynamic>> event) {
    log('===== UPDATE WORK ORDERS =====');
    for (var doc in event.docs) {
      var assetId = doc.get('AssetId');
      if (workOrders.containsKey(doc.id)) {
        var workOrder = workOrders[doc.id]!;

        var docWasChanged =
            event.docChanges.any((element) => element.doc.id == doc.id);
        if (docWasChanged) {
          workOrder.updateFromMap(doc.data());
        }
        workOrder.setShouldDownloadPhotos(false);
      } else {
        // If we have the asset in cache and the asset is favorited, instantiate work order
        if (assets.containsKey(assetId) &&
            assets[assetId]!.listeningUsers.contains(userEmail)) {
          var workOrder =
              WorkOrder(doc.id, assets[assetId]!, doc.get('ProjectId'));
          workOrder.updateFromMap(doc.data());

          workOrder.setShouldDownloadPhotos(false);

          workOrders[doc.id] = workOrder;
        } else {
          // If we don't have an associated asset yet, orphan the work order doc data
          orphanedWorkOrders[doc.id] = doc.data();
        }
      }
      log('===== WORK ORDERS COUNT: ${workOrders.length} =====');
    }

    _checkOrphanedData(assets.values);

    workOrderListener.notifyListeners();
  }

  void updateTasks(QuerySnapshot<Map<String, dynamic>> event) {
    log('===== UPDATE TASKS =====');
    var favoritedAssets = unverifiedUserProfile.favoritedAssets;
    var currentProjectIds = APMRoot.global.queries.selectedProjects
        .getSelectedProjects()
        .select((element, index) => element.id)
        .toList();
    for (var doc in event.docs) {
      // Look for work order in our cache
      var workOrderId = doc.get('WorkOrderId');
      var workOrder = workOrders[workOrderId];
      if (workOrder != null) {
        // If work order found in cache, update/create task on work order
        var task = workOrder.tasks.firstWhereOrDefault((t) => t.id == doc.id);
        task ??= Task(workOrder, doc.id, doc.get('TaskType'));

        var docWasChanged =
            event.docChanges.any((element) => element.doc.id == doc.id);
        if (docWasChanged) {
          task.updateFromMap(doc.data());
        }

        var isCurrentUserAndClosed = task.assignedUsers.contains(userEmail) &&
            (task.status.getValue() == "Completed" ||
                task.status.getValue() == "Published") &&
            !favoritedAssets.contains(task.asset.id) &&
            !currentProjectIds.contains(task.projectId);
        // task.setShouldDownloadPhotos(!isCurrentUserAndClosed);
        task.setShouldDownloadPhotos(false);

        if (!workOrder.tasks.map((e) => e.id).contains(task.id)) {
          workOrder.tasks.add(task);
        }

        workOrder.tasksListener.notifyListeners();
      } else {
        // If work order NOT found, add task data to orphaned tasks
        orphanedTasks[doc.id] = doc.data();
      }
    }

    _checkOrphanedData(assets.values);

    log('===== TASKS COUNT: ${workOrders.values.map((e) => e.tasks.length).sum()} =====');
  }

  void updateAssets(QuerySnapshot<Map<String, dynamic>> event) {
    log('===== UPDATE ASSETS =====');
    for (var doc in event.docs) {
      if (assets.containsKey(doc.id)) {
        var docWasChanged =
            event.docChanges.any((element) => element.doc.id == doc.id);
        if (docWasChanged) {
          var asset = assets[doc.id];
          var data = doc.data();
          asset!.updateFromMap(data);
          asset.setShouldDownloadPhotos(false);
          assets[doc.id] = asset;
        }
      } else {
        var asset = Asset(
            assetCategory: doc.get('AssetCategory'),
            id: doc.id,
            locationId: doc.get('LocationId'));
        asset.updateFromMap(doc.data());
        asset.setShouldDownloadPhotos(false);
        assets[doc.id] = asset;
      }
    }

    _checkOrphanedData(assets.values);

    assetListener.notifyListeners();

    log('===== ASSET COUNT: ${assets.length} =====');
  }

  _checkOrphanedData(Iterable<Asset> cachedAssets) {
    log('===== CHECK ORPHANED DATA =====');
    for (var asset in cachedAssets) {
      // Check if we can un-orphan work orders
      var fixableOrphanedWorkOrders = orphanedWorkOrders.entries.where(
          (workOrderEntry) =>
              (workOrderEntry.value != null &&
                  workOrderEntry.value!['AssetId'] == asset.id) &&
              // asset is favorited
              (asset.listeningUsers.contains(userEmail) ||
                  sessionAssetIds.contains(asset.id) ||
                  // work order has a task we are assigned to
                  orphanedTasks.entries
                      .where((taskEntry) => taskEntry.value != null)
                      .where((taskEntry) =>
                          taskEntry.value!['WorkOrderId'] == workOrderEntry.key)
                      .where((taskEntry) =>
                          taskEntry.value!['AssignedUsers'] != null)
                      .map((taskEntry) =>
                          taskEntry.value!['AssignedUsers'] as List<dynamic>)
                      .any((assignedUsers) =>
                          assignedUsers.contains(userEmail))));
      List<String> recoveredWorkOrderIds = [];
      for (var entry in fixableOrphanedWorkOrders) {
        if (entry.value != null) {
          var workOrder =
              WorkOrder(entry.key, asset, entry.value!['ProjectId']);
          workOrder.updateFromMap(entry.value);

          workOrder.setShouldDownloadPhotos(false);

          workOrders[entry.key] = workOrder;
          recoveredWorkOrderIds.add(entry.key);
        }
      }

      // remove any recovered work orders from the orphaned list of work orders
      orphanedWorkOrders
          .removeWhere((key, value) => recoveredWorkOrderIds.contains(key));

      // Check if we can un-orphan tasks
      var fixableOrphanedTasks = orphanedTasks.entries.where((entry) =>
          entry.value != null && entry.value!['AssetId'] == asset.id);
      List<String> recoveredTaskIds = [];

      for (var entry in fixableOrphanedTasks) {
        if (entry.value != null) {
          var workOrder = workOrders[entry.value!['WorkOrderId']];
          if (workOrder != null) {
            var task = Task(workOrder, entry.key, entry.value!['TaskType']);
            task.updateFromMap(entry.value);
            workOrder.tasksListener.notifyListeners();

            task.setShouldDownloadPhotos(false);

            workOrder.tasks.add(task);
            recoveredTaskIds.add(entry.key);
          }
        }
      }

      // remove any recovered tasks from the orphaned list of tasks
      orphanedTasks.removeWhere((key, value) => recoveredTaskIds.contains(key));
    }
  }

  BatchQueryListenerWrapper _createBatchQueryWrapper(String collectionName) {
    var wrapper = BatchQueryListenerWrapper(collectionName, _db, this);
    return wrapper;
  }

  void removeUnwantedData() {
    assets.removeWhere((key, value) =>
        !listenedAssetIds.contains(key) && !sessionAssetIds.contains(key));
    workOrders.removeWhere((key, value) =>
        !listenedAssetIds.contains(value.asset.id) &&
        !sessionAssetIds.contains(value.asset.id));
  }
}
