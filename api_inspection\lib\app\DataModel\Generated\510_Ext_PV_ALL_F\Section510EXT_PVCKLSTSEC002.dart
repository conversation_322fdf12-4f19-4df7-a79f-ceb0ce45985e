//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class Section510EXT_PVCKLSTSEC002 extends DataModelSection {
  @override
  String getDisplayName() => "LADDERS, STAIRWAYS, PLATFORMS, AND WALKWAYS";
  Section510EXT_PVCKLSTSEC002(DataModelItem? parent)
      : super(
            parent: parent,
            sectionName: "LADDERS, STAIRWAYS, PLATFORMS, AND WALKWAYS");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC002Q001 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are ladders, stairways, platforms or walkways associated with this asset:",
          databaseName: "510_EXT-PV_CKLST_SEC002_Q001",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510EXT_PVCKLSTSEC002Q002 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Is attachment hardware or bolting cracked, corroded, broken, missing or loose:",
          databaseName: "510_EXT-PV_CKLST_SEC002_Q002",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Broken", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Cracking", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Corroded", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Loose", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Missing", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC002Q003 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the protective coating / galvanizing material in acceptable condition for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC002_Q003",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC002Q004 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are ladder rungs or stairway treads in acceptable condition for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC002_Q004",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC002Q005 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are handrails, mid-rails & toe rails in acceptable condition for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC002_Q005",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC002Q006 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are the flooring surfaces of platforms & walkways in acceptable condition for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC002_Q006",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC002Q007 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are safety devices in acceptable condition for continued service:  (I.e. ladder cage, safety gate, etc.)",
          databaseName: "510_EXT-PV_CKLST_SEC002_Q007",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attribute510EXT_PVCKLSTSEC002Q001,
      attribute510EXT_PVCKLSTSEC002Q002,
      attribute510EXT_PVCKLSTSEC002Q003,
      attribute510EXT_PVCKLSTSEC002Q004,
      attribute510EXT_PVCKLSTSEC002Q005,
      attribute510EXT_PVCKLSTSEC002Q006,
      attribute510EXT_PVCKLSTSEC002Q007,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section510EXT_PVCKLSTSEC002";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
