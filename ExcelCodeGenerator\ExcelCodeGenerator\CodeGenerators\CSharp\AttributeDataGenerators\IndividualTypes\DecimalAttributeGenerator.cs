﻿using CheckListGen;

namespace ExcelCodeGenerator.CodeGenerators.CSharp
{
    public class DecimalAttributeGenerator : IAttributeGenerator
    {
        public string BuildInitialization(Question question)
        {
            var unit = string.IsNullOrWhiteSpace(question.Unit) ? "null" : "\"" + question.Unit + "\"";
            return
                @"
        " + question.DartVariableName + @" = new DoubleAttribute(this, displayName: """ + question.DisplayText +
                @""", databaseName: """ + question.DataName + @""", areCommentsRequired: " +
                (question.ForceComment ? "true" : "false") + @", displayUnit: " + unit + @", allowNegatives: true); ";
        }

        public string BuildDeclaration(Question question)
        {
            return
                @"public DoubleAttribute " + question.DartVariableName + ";";
        }
    }
}