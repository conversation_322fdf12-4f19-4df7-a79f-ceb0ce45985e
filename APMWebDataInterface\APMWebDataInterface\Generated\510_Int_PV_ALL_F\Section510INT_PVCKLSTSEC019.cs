//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Int_PV_ALL_F
{
  public class Section510INT_PVCKLSTSEC019 : DataModelItem {

    public override String DisplayName { 
      get {
        return "EXTERNAL JACKETING";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC019Q001;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510INT_PVCKLSTSEC019";

    public Section510INT_PVCKLSTSEC019(DataModelItem parent) : base(parent)
    {
            
        attribute510INT_PVCKLSTSEC019Q001 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the exterior surfaces of the asset jacketed for heating or cooling purposes:  (If jacketed ultrasonic thickness readings should be obtained during the internal inspection from the interior of the vessel)", databaseName: "510_INT-PV_CKLST_SEC019_Q001"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute510INT_PVCKLSTSEC019Q001,
        };
    }
  }
}
