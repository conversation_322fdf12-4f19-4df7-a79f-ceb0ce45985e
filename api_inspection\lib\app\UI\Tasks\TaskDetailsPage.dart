import 'package:api_inspection/app/APMRoot.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/task.dart';
import 'package:api_inspection/app/UI/Tasks/TaskAssignmentPage.dart';
import 'package:api_inspection/generic/UIControls/TeamToggleButtonWithPreviewText.dart';
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'package:api_inspection/generic/AppStyle.dart';

class TaskDetailsPage extends StatefulWidget {
  final Task task;
  const TaskDetailsPage(this.task, {Key? key}) : super(key: key);

  @override
  _TaskDetailsPageState createState() => _TaskDetailsPageState();
}

class _TaskDetailsPageState extends State<TaskDetailsPage> {
  String getTitle() {
    return "Task Edit Page";
  }

  void showTaskAssignment() {
    Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => TaskAssignmentPage(widget.task)))
        .then((value) => {setState(() {})});
  }

  @override
  void dispose() {
    widget.task.removeListener(onTaskChanged);
    super.dispose();
  }

  @override
  void initState() {
    widget.task.addListener(onTaskChanged);
    super.initState();
  }

  void onTaskChanged() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    var task = widget.task;

    String assignedToText;
    if (task.assignedUsers.length > 3) {
      assignedToText = "Crew";
    } else if (task.assignedUsers.isEmpty) {
      assignedToText = "None";
    } else {
      assignedToText = task.assignedUsers.join(", ");
    }
    var project = APMRoot.global.queries.projectQueries.projects
        .firstWhere((element) => element.id == task.workOrder.projectId);

    return Scaffold(
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        appBar: AppBar(
          title: Text(
            getTitle(),
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        body: Align(
          alignment: Alignment.topCenter,
          child: SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                Container(
                    margin: const EdgeInsets.fromLTRB(20, 10, 20, 0),
                    child: const Text(
                      "Task Details",
                      style: TextStyle(color: Colors.white, fontSize: 20),
                    )),
                AttributePadding.WithStdPadding(
                  TeamToggleButtonWithPreviewText(
                      "Assignee", showTaskAssignment, assignedToText, null),
                ),
                AttributePadding.WithStdPadding(
                  task.taskDetails.supervisor.buildWidget(),
                ),
                AttributePadding.WithStdPadding(task.leadTech.buildWidget()),
                AttributePadding.WithStdPadding(
                    task.clientWorkOrderNumber.buildWidget()),
                AttributePadding.WithStdPadding(
                    task.clientWorkOrderDescription.buildWidget()),
                AttributePadding.WithStdPadding(task.dueDate.buildWidget()),
                AttributePadding.WithStdPadding(
                    task.plannedStart.buildWidget()),
                AttributePadding.WithStdPadding(task.plannedEnd.buildWidget()),
                AttributePadding.WithStdPadding(
                    task.clientCostCode.buildWidget()),
                const Divider(color: Colors.white),
                Container(
                    margin: const EdgeInsets.fromLTRB(20, 10, 20, 0),
                    child: const Text(
                      "Project Details",
                      style: TextStyle(color: Colors.white, fontSize: 20),
                    )),
                AttributePadding.WithStdPadding(project
                    .accountingDetails.apmProjectNumber
                    .buildWidgetNonEditable()),
                AttributePadding.WithStdPadding(project
                    .accountingDetails.teamProjectNumber
                    .buildWidgetNonEditable()),
                const Divider(color: Colors.white),
                Container(
                    margin: const EdgeInsets.fromLTRB(20, 10, 20, 0),
                    child: const Text(
                      "APM Work Order Details",
                      style: TextStyle(color: Colors.white, fontSize: 20),
                    )),
                AttributePadding.WithStdPadding(
                    task.workOrder.gisLocation.buildWidget()),
                AttributePadding.WithStdPadding(
                    task.workOrder.facilityName.buildWidget()),
                AttributePadding.WithStdPadding(
                    task.workOrder.dueDate.buildWidget()),
                AttributePadding.WithStdPadding(
                    task.workOrder.plannedStart.buildWidget()),
                AttributePadding.WithStdPadding(
                    task.workOrder.plannedEnd.buildWidget()),
                AttributePadding.WithStdPadding(
                    task.workOrder.primaryContactName.buildWidget()),
                AttributePadding.WithStdPadding(
                    task.workOrder.primaryContactPhone.buildWidget()),
              ],
            ),
          ),
        ));
  }
}
