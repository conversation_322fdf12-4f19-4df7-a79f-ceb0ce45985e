//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section570_Asset_Walkdown_Details_F
{
  public class SectionDesign : DataModelItem {

    public override String DisplayName { 
      get {
        return "Design";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public StringAttribute attributeDesign_Code;
    public StringAttribute attributeCode_Year;
    public StringAttribute attributeAddendum;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionDesign";

    public SectionDesign(DataModelItem parent) : base(parent)
    {
            
        attributeDesign_Code = new StringAttribute(this, displayName: "Design Code", databaseName: "570AW_Q111"); 
     
        attributeCode_Year = new StringAttribute(this, displayName: "Code Year", databaseName: "570AW_Q112"); 
     
        attributeAddendum = new StringAttribute(this, displayName: "Addendum", databaseName: "570AW_Q113"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeDesign_Code,
           attributeCode_Year,
           attributeAddendum,
        };
    }
  }
}
