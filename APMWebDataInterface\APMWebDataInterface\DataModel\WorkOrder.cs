﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using CommonDataInterface;
using CommonDataInterface.Attributes;
using APMWebDataInterface.DataModel;
using Google.Cloud.Firestore;

namespace APMWebDataInterface.ExampleDataModel
{
    internal interface IWorkOrder
    {
    }


    public class WorkOrderSnapshot
    {
        public DocumentSnapshot docSnapshot { get; set; }

        public String workOrderId => docSnapshot.Id;
    }

    public class AssetSnapshot
    {
        public DocumentSnapshot docSnapshot { get; set; }

        public String assetId => docSnapshot?.Id;
    }

    public class PartialWorkOrder : IWorkOrder
    {
        public DocumentSnapshot docSnapshot { get; set; }

        public String assetId { get; set; }
    }


    public class WorkOrder : ConcretePhotoRoot, IWorkOrder
    {
        public override string DisplayName => "Work order";

        internal override String GetDBPath() => "workorders." + GetDBName();
        internal override String GetDBName() => id;

        internal bool saveAssetAswell = false;

        public Asset asset;
        public String projectId;

        List<String> _assignedUsers = new List<string>();
        
        public WorkOrder(Asset asset, String projectId) : base(null)
        {
            this.asset = asset;
            this.projectId = projectId;

            InitializeAttribute();
        }

        internal WorkOrder(String id, Asset asset, String projectId) : base(id, null)
        {
            this.asset = asset;
            this.projectId = projectId;

            InitializeAttribute();
        }

        private void InitializeAttribute()
        {
            gisLocation = new LocationAttribute(parent: this, displayName: "GIS Location");
            facilityName = new StringAttribute(parent: this, displayName: "Facility Name");

            dueDate = new DateAttribute(parent: this, displayName: "Due Date");
            plannedStart = new DateAttribute(parent: this, displayName: "Planned Start");
            plannedEnd = new DateAttribute(parent: this, displayName: "Planned End");
            fieldWorkCompleted = new DateAttribute(parent: this, displayName: "Field Work Completed");
            apmWorkOrderNumber =
                new StringAttribute(parent: this, displayName: "APM Work Order Number", isQueryable: true);
            primaryContactName = new StringAttribute(parent: this, displayName: "Primary Contact Name");
            primaryContactPhone = new PhoneNumberAttribute(parent: this, displayName: "Primary Contact Phone");

            jobScope = new StringAttribute(parent: this, displayName: "Job Scope");
            inspectionSummary = new StringAttribute(parent: this, displayName: "Inspection Summary");
            applicableDamage = new StringAttribute(parent: this, displayName: "Applicable Damage Mechanism(s)",
                databaseName: "Applicable Damage Mechanisms");
            releventIndications = new StringAttribute(parent: this, displayName: "Relevant Indications / Findings(s)",
                databaseName: "Relevent Indications Findings");
            recommendations = new StringAttribute(parent: this, displayName: "Recommendations(s)",
                databaseName: "Recommendations");
            businessUnitId = new StringAttribute(this, "BusinessUnitId", isQueryable: true);

            status = new PredefinedValueAttribute(hasOtherOption: false,
                parent: this,
                displayName: "Status",
                availableOptions: new List<PredefinedValueOption>
                {
                    new PredefinedValueOption("Scheduled", null),
                    new PredefinedValueOption("In Progress", null),
                    new PredefinedValueOption("Completed", null),
                    new PredefinedValueOption("Canceled", null),
                    new PredefinedValueOption("On Hold", null),
                    new PredefinedValueOption("Published", null),
                }
            );


            //tasks = new DataModelCollection<APMTask>("Tasks", (parent, entry) =>
            //{
            //  var dictionary = (entry.Value as Dictionary<string, object>);
            //  if (!dictionary.ContainsKey("TaskType"))
            //    return null;

            //  return APMTask.Create(this, tasks, entry.Key, (entry.Value as Dictionary<string, object>)["TaskType"] as String);
            //}, (item, s) => throw new NotSupportedException("Please use WorkOrder.AddTask method,  this method is not supported because APM task is abstract"), this);
        }


        public void UnMarkThisAndTasksAsPublished()
        {
            if (status.GetValue() == "Published")
            {
                status.SetValue("Completed");
            }

            foreach (var task in tasks)
            {
                if (task.status.GetValue() == "Published")
                {
                    task.status.SetValue("Completed");
                }
            }
        }

        public void MarkThisAndTasksAsPublished()
        {
            status.SetValue("Published");
            foreach (var task in tasks)
                task.ChangeStatus(APMTask.Statuses.Published);
        }

        public DateTime? PublishedTime
        {
            get
            {
                if (status.GetValue() == "Published")
                    return status.GetValueChangeLog().GetLastChangedTime();
                return null;
            }
        }

        public LocationAttribute gisLocation;
        public StringAttribute facilityName;
        public DateAttribute dueDate;
        public DateAttribute plannedStart;
        public DateAttribute plannedEnd;
        public DateAttribute fieldWorkCompleted;
        public StringAttribute primaryContactName;
        public PredefinedValueAttribute status;
        public PhoneNumberAttribute primaryContactPhone;

        public StringAttribute apmWorkOrderNumber;

        public StringAttribute jobScope;
        public StringAttribute inspectionSummary;
        public StringAttribute applicableDamage;
        public StringAttribute releventIndications;
        public StringAttribute recommendations;
        public StringAttribute businessUnitId;

        private bool didAssignedUsersChange()
        {
            var tasksAssignedTo = tasks.SelectMany(a => a.assignedUsers).Distinct().ToArray();
            var assignedUsers = _assignedUsers;

            var orderedA = tasksAssignedTo.OrderBy(a => a).ToArray();
            var orderedB = assignedUsers.OrderBy(a => a).ToArray();

            if (orderedA.Length != orderedB.Length)
                return true;
            for (int i = 0; i < orderedA.Length; i++)
            {
                var match = orderedB[i];
                if (!orderedA[i].Equals(orderedB[i]))
                    return true;
            }

            return false;
        }

        public override bool UpdateDirectPropertiesFromMapEntry(KeyValuePair<string, object>? entry)
        {
            if (entry == null)
                return false;

            if (entry.Value.Key == "AssignedUsers")
            {
                if (entry.Value.Value is IEnumerable assignedUsers)
                {
                    var newUsers = new List<string>();
                    foreach (var item in assignedUsers)
                    {
                        newUsers.Add(item.ToString());
                    }

                    _assignedUsers = newUsers;
                }

                return true;
            }

            return false;
        }

        protected override async Task DoAddPendingChangesToDictionary(Dictionary<string, object> updates, string user)
        {
            if (didAssignedUsersChange())
            {
                var usersList = tasks.Where(a => a.assignedUsers != null).SelectMany(a => a.assignedUsers).Distinct()
                    .ToArray();
                updates["AssignedUsers"] = usersList.Any() ? usersList : new String[0];
            }


            await base.DoAddPendingChangesToDictionary(updates, user);
        }

        public List<APMTask> tasks = new List<APMTask>();

        public enum TaskTypes
        {
            AssetWalkdown,
            ExternalVisual,
            InternalVisual,
            Full
        };

        public APMTask AddNewTask(TaskTypes taskType)
        {
            String taskTypeStr = "";
            switch (taskType)
            {
                case TaskTypes.AssetWalkdown:
                    taskTypeStr = "Asset Walkdown";
                    break;
                case TaskTypes.ExternalVisual:
                    taskTypeStr = "External Visual";
                    break;
                case TaskTypes.InternalVisual:
                    taskTypeStr = "Internal Visual";
                    break;
                case TaskTypes.Full:
                    taskTypeStr = "Full";
                    break;
                default:
                    throw new Exception("Unexpected task type encountered: " + taskType.ToString());
            }

            var newTask = APMTask.Create(this, Guid.NewGuid().ToString(), taskTypeStr);
            newTask.ChangeStatus(APMTask.Statuses.NotStarted);

            newTask.Exists = false;

            tasks.Add(newTask);
            return newTask;
        }


        public override DataModelItem[] GetChildren()
        {
            var parentChildren = base.GetChildren();

            return parentChildren.Concat(new DataModelItem[]
            {
                gisLocation, facilityName, dueDate, primaryContactName, primaryContactPhone,
                plannedStart, plannedEnd, fieldWorkCompleted, status, apmWorkOrderNumber,
                jobScope, inspectionSummary, applicableDamage, releventIndications, recommendations, businessUnitId
            }).ToArray();
        }

        protected override async Task DoStartSavingChange(string user)
        {
            await base.DoStartSavingChange(user);

            if (!Exists)
            {
                var path = asset.GetDBPath().Split('.');
                try
                {
                    var result = await APM_WebDataInterface.Global.DatabaseContextManager.ContextForNonListenerAction(
                        db => db.Collection(path[0]).Document(path[1]).UpdateAsync(new Dictionary<string, object>
                        {
                            {"WorkOrderIds", FieldValue.ArrayUnion(new[] {this.id})},
                            {"ProjectIds", FieldValue.ArrayUnion(new[] {this.projectId})}
                        }));


                    Console.Write(result.UpdateTime);
                }
                catch (Exception ex)
                {
                    Console.Write(ex);
                }
            }
        }

        public override void DoAddOneTimeChangesToDictionary(Dictionary<string, Object> updates)
        {
            updates["AssetId"] = asset.id;
            updates["ProjectId"] = projectId;
        }

        public override bool GetHasDatabaseChangesPending()
        {
            return didAssignedUsersChange() ||
                   base.GetHasDatabaseChangesPending();
        }


        public override async Task SavePendingChanges(String user)
        {
            
            APM_WebDataInterface.Global.AuthorizeWriteToRootObject(this.businessUnitId, user);


            foreach (var task in tasks)
            {
                if (task.GetHasDatabaseChangesPending())
                {
                    await task.SavePendingChanges(user);
                }
            }

            await base.SavePendingChanges(user);

            if (saveAssetAswell)
            {
                await asset.SavePendingChanges(user);
                saveAssetAswell = false;
            }
        }
    }


    public class WorkOrderListViewModel
    {
        public static WorkOrderListViewModel Build(WorkOrder workOrder, string email)
        {
            var project = APM_WebDataInterface.Global.GetProjects(new[] {workOrder.projectId}, email)[0];
            return new WorkOrderListViewModel
            {
                WorkOrderId = workOrder.id,
                Project = project.name.GetValue(),
                AssetId = workOrder.asset.GetIDFromWalkdown(),
                APMWorkOrderNumber = workOrder.apmWorkOrderNumber.GetValue(),
                Status = workOrder.status.GetValue(),
                Facility = workOrder.facilityName.GetValue(),
                AssetCategory = workOrder.asset.assetCategory,
                PlannedStartStr = workOrder.plannedStart.CurrentValue,
                PlannedEndStr = workOrder.plannedEnd.CurrentValue,
                DueDateStr = workOrder.dueDate.CurrentValue,
                FieldWorkCompletedStr = workOrder.fieldWorkCompleted.CurrentValue,
                PlannedStart = workOrder.plannedStart.getValue(),
                PlannedEnd = workOrder.plannedEnd.getValue(),
                DueDate = workOrder.dueDate.getValue(),
                FieldWorkCompleted = workOrder.fieldWorkCompleted.getValue()
            };
        }

        public String WorkOrderId { get; set; }
        public String Project { get; set; }
        public String AssetId { get; set; }
        public String APMWorkOrderNumber { get; set; }
        public String Status { get; set; }
        public String Facility { get; set; }
        public String AssetCategory { get; set; }
        public String PlannedStartStr { get; set; }
        public String PlannedEndStr { get; set; }
        public String FieldWorkCompletedStr { get; set; }
        public String DueDateStr { get; set; }

        public DateTime? PlannedStart { get; set; }
        public DateTime? PlannedEnd { get; set; }
        public DateTime? DueDate { get; set; }
        public DateTime? FieldWorkCompleted { get; set; }
    }
}