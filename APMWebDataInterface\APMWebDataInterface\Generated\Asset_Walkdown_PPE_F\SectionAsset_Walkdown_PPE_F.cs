//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionAsset_Walkdown_PPE_F
{
  public class SectionAsset_Walkdown_PPE_F : DataModelItem {

    public override String DisplayName { 
      get {
        return "Asset Walkdown-PPE-F";
      }
    }

    #region [-- Sub Sections --]

    #region [-- SectionPPERequirements --]
    private SectionPPERequirements _sectionPPERequirements;
    public SectionPPERequirements sectionPPERequirements {
        get {
            if (_sectionPPERequirements == null) {
               _sectionPPERequirements = new SectionPPERequirements(this);
            }

            return _sectionPPERequirements;
        }
    }
    #endregion [-- SectionPPERequirements --]
    
    #region [-- SectionGeneralSiteConditions --]
    private SectionGeneralSiteConditions _sectionGeneralSiteConditions;
    public SectionGeneralSiteConditions sectionGeneralSiteConditions {
        get {
            if (_sectionGeneralSiteConditions == null) {
               _sectionGeneralSiteConditions = new SectionGeneralSiteConditions(this);
            }

            return _sectionGeneralSiteConditions;
        }
    }
    #endregion [-- SectionGeneralSiteConditions --]
    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionAsset_Walkdown_PPE_F";

    public SectionAsset_Walkdown_PPE_F(DataModelItem parent) : base(parent)
    {
            
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           sectionPPERequirements,
           sectionGeneralSiteConditions,
        };
    }
  }
}
