//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionTankOutOfServiceRequirements.dart';

// ignore: camel_case_types
class SectionTankOutOfServiceRequirementsPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionTankOutOfServiceRequirements sectionTankOutOfServiceRequirements;

  const SectionTankOutOfServiceRequirementsPage(
      this.sectionTankOutOfServiceRequirements,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionTankOutOfServiceRequirementsPageState();
  }
}

class _SectionTankOutOfServiceRequirementsPageState
    extends State<SectionTankOutOfServiceRequirementsPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionTankOutOfServiceRequirements,
        title: "Tank Out-Of-Service Requirements",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionTankOutOfServiceRequirements.attribute653AWQ331
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
