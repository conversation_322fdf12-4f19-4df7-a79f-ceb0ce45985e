//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionDesign.dart';
import 'SectionInspectionOpeningsPage.dart';
import 'SectionInspectionPage.dart';
import 'SectionDataPlatePage.dart';
import 'SectionManufacturerFabricatorPage.dart';

// ignore: camel_case_types
class SectionDesignPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionDesign sectionDesign;

  const SectionDesignPage(this.sectionDesign, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionDesignPageState();
  }
}

class _SectionDesignPageState extends State<SectionDesignPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionDesign,
        title: "Design",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionDesign.attributeDesign_Code
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionDesign.attributeCode_Year
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionDesign.attributeAddendum
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionDesign.attributeMaximum_Fill_Height
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionDesign.attributeDiameter
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionDesign.attributeHeight
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionDesign.attributeTank_Volume_in_BBL
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionDesign.attributeConstruction_Method
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionDesign.attributeOrientation
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionDesign.attributeRT
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionDesign.attributeInstallation_Date
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionDesign.attributeIn_service_Date
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionDesign.attributePID_Number
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionDesign.attributeConstructionDesign_Drawing_Number
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionDesign.attributeLowest_Flange_Rating
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionDesign.sectionInspectionOpenings,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          SectionInspectionOpeningsPage(widget
                                              .sectionDesign
                                              .sectionInspectionOpenings)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionDesign.sectionInspection,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          SectionInspectionPage(
                                              widget.sectionDesign
                                                  .sectionInspection)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionDesign.sectionDataPlate,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          SectionDataPlatePage(
                                              widget.sectionDesign
                                                  .sectionDataPlate)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section:
                            widget.sectionDesign.sectionManufacturerFabricator,
                        onPressed: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      SectionManufacturerFabricatorPage(widget
                                          .sectionDesign
                                          .sectionManufacturerFabricator))).then(
                              (value) => setState(() {}));
                        }),
                  ),
                ]),
          );
        });
  }
}
