//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/UI/CollectionControl.dart';
import 'package:uuid/uuid.dart';
import 'SectionShellCourses.dart';

// ignore: camel_case_types
// ignore: camel_case_types
class SectionShellCoursesPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final DataModelCollection<SectionShellCourses> sectionShellCourses;
  const SectionShellCoursesPage(this.sectionShellCourses, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionShellCoursesPageState();
  }
}

class _SectionShellCoursesPageState extends State<SectionShellCoursesPage> {
  Widget _cardBuilder(
      BuildContext context, int number, SectionShellCourses item) {
    return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(number.toString(),
              style: const TextStyle(color: Colors.white, fontSize: 18)),
        ]);
  }

  Widget _editPageBuilder(BuildContext context, SectionShellCourses item) {
    return SectionScaffold(
        section: item,
        title: "Shell Courses",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(item.attributeNumber
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item.attributePhotos
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item
                      .attributeMaterial_Spec_and_Grade
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item
                      .attributeAllowable_Stress_at_Temperature
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item
                      .attributeNominal_Thickness
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item
                      .attributeCorrosion_Allowance
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item.attributeLength_or_Height
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item.attributeJoint_Efficiency
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }

  SectionShellCourses _createNewItem() {
    String id = const Uuid().v4();
    var item = SectionShellCourses(id, widget.sectionShellCourses);

    return item;
  }

  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionShellCourses,
        title: "Shell Courses",
        childBuilder: (editableController) {
          return Center(
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: <Widget>[
                Expanded(
                    child: CollectionControl<SectionShellCourses>(
                  cardTitle: "Shell Courses",
                  collection: widget.sectionShellCourses,
                  cardBuilder: _cardBuilder,
                  createNewItem: _createNewItem,
                  editPageBuilder: _editPageBuilder,
                ))
              ]));
        });
  }
}
