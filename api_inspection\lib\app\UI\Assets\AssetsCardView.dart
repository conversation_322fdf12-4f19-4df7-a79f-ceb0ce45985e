import 'dart:developer';

import 'package:api_inspection/app/APMRoot.dart';
import 'package:api_inspection/generic/AppStyle.dart';
import 'package:flutter/material.dart';
import 'package:map_launcher/map_launcher.dart';
import 'package:api_inspection/app/DataModel/Generated/510_Asset_Walkdown_Details_F/Section510_Asset_Walkdown_Details_F.dart';
import 'package:api_inspection/app/DataModel/Generated/570_Asset_Walkdown_Details_F/Section570_Asset_Walkdown_Details_F.dart';
import 'package:api_inspection/app/DataModel/Generated/653_Asset_Walkdown_Details_F/Section653_Asset_Walkdown_Details_F.dart';
import 'package:api_inspection/app/DataModel/assetCard.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
import 'package:api_inspection/generic/UIControls/SideSlideoutControl.dart';
import 'package:darq/darq.dart';
import 'package:collection/collection.dart';

class AssetCardView extends StatefulWidget {
  final AssetCard assetCard;
  final Function()? onPressed;
  final Color color;
  final bool showGPS;
  const AssetCardView(this.assetCard, this.onPressed,
      {Key? key,
      this.color = const Color.fromARGB(255, 41, 45, 52),
      this.showGPS = true})
      : super(key: key);

  @override
  _AssetCardViewState createState() => _AssetCardViewState();
}

class _AssetCardViewState extends State<AssetCardView> {
  @override
  void dispose() {
    APMRoot.global.newQueries.workOrderListener
        .removeListener(onWorkOrdersChanged);
    super.dispose();
  }

  @override
  void initState() {
    APMRoot.global.newQueries.workOrderListener
        .addListener(onWorkOrdersChanged);
    super.initState();
  }

  void onWorkOrdersChanged() {
    setState(() {});
  }

  void showErrorMessage(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color.fromARGB(255, 41, 45, 52),
          title: const Text(
            'Error',
            style: TextStyle(color: Colors.white),
          ),
          content: Text(message, style: const TextStyle(color: Colors.white)),
          actions: [
            ElevatedButton(
              child: const Text('Ok', style: TextStyle(color: Colors.white)),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  void navigateToGPSCoordinate(LocationAttribute attr) async {
    FocusScope.of(context).unfocus();
    try {
      var lat = attr.getLatValue();
      var long = attr.getLongValue();
      if (lat == null || long == null) {
        showErrorMessage("Lat or long value on coordinate was not set");
        return;
      }
      if (!attr.areCurrentCoordinatesValid()) {
        showErrorMessage("Coordinates are invalid");
        return;
      }

      Coords latlng = Coords(lat, long);
      final availableMaps = await MapLauncher.installedMaps;
      if (availableMaps.isEmpty) {
        showErrorMessage(
            "This device does not have a maps application installed that can be launched.");
        return;
      }

      String assetName = widget.assetCard.assetName ?? "No name set";

      await availableMaps.first.showMarker(
        coords: latlng,
        title: assetName,
      );
    } catch (e) {
      log(e.toString(), name: 'navigateToGPSCoordinate');
    }
  }

  Widget buildGPSSection(BuildContext context) {
    var asset = widget.assetCard.asset;

    String textValue;
    Function()? onPressed;

    Widget icon;
    if (asset == null) {
      icon = Container();
      textValue = "";
      onPressed = () {
        showErrorMessage("Asset must be loaded in order to view GPS data");
      };
    } else {
      icon = Icon(Icons.location_pin,
          color: Colors.white, size: AppStyle.global.iconSizeMediumSmall);

      List<LocationAttribute> locationAttributes = [];
      var walkDown = asset.walkDown;

      if (walkDown is Section510_Asset_Walkdown_Details_F) {
        locationAttributes
            .add(walkDown.sectionIdentification.attributeGIS_Location);
      } else if (walkDown is Section570_Asset_Walkdown_Details_F) {
        locationAttributes
            .add(walkDown.sectionIdentification.attributeStart_GIS_Location);
        locationAttributes
            .add(walkDown.sectionIdentification.attributeEnd_GIS_Location);
      } else if (walkDown is Section653_Asset_Walkdown_Details_F) {
        locationAttributes
            .add(walkDown.sectionIdentification.attributeGIS_Location);
      }

      var location = locationAttributes.firstWhereOrNull((element) =>
          element.getLatValue() != null && element.getLongValue() != null);

      if (location == null) {
        textValue = "No location set";
        onPressed = () {};
      } else {
        textValue = "GPS (" + location.getPreviewText() + ")";
        onPressed = () {
          navigateToGPSCoordinate(location);
        };
      }
    }

    return Container(
        height: AppStyle.global.pixels50,
        color: Colors.blueGrey[800],
        child: ElevatedButton(
            style: ButtonStyle(
                backgroundColor: MaterialStateProperty.resolveWith<Color?>(
              (Set<MaterialState> states) {
                return Colors.blueGrey[800];
              },
            )),
            onPressed: onPressed,
            child: Row(children: [
              icon,
              Container(width: 10),
              Text(
                textValue,
                style: const TextStyle(color: Colors.white, fontSize: 14),
              )
            ])));
  }

  Widget buildStatusIndicator(
      BuildContext context, double width, double height) {
    if (widget.assetCard.asset == null) {
      return SizedBox(width: width, height: height);
    }

    var workOrders = APMRoot.global.newQueries.workOrders
        .where((workOrder) => workOrder.asset.id == widget.assetCard.assetDBId);
    var tasks = workOrders.selectMany((element, index) => element.tasks);

    int notStartedTasks = 0;
    int unAssignedTasks = 0;
    int inProgressTasks = 0;
    int completedTasks = 0;

    for (var task in tasks) {
      var status = task.status.getValue();
      if (status == "Completed") {
        completedTasks++;
      } else if (task.assignedUsers.isEmpty) {
        unAssignedTasks++;
      } else if (status == "In Progress") {
        inProgressTasks++;
      } else {
        notStartedTasks++;
      }
    }

    int taskCount = tasks.length;

    double sizePer = width / taskCount;

    if (tasks.isEmpty) {
      return SizedBox(width: width, height: height);
    }

    List<Widget> children = [];
    if (unAssignedTasks > 0) {
      children.add(buildWidgetForCount(
          unAssignedTasks, sizePer, Colors.grey[200], height));
    }
    if (notStartedTasks > 0) {
      children.add(
          buildWidgetForCount(notStartedTasks, sizePer, Colors.yellow, height));
    }
    if (inProgressTasks > 0) {
      children.add(buildWidgetForCount(
          inProgressTasks, sizePer, Colors.orange[600], height));
    }
    if (completedTasks > 0) {
      children.add(
          buildWidgetForCount(completedTasks, sizePer, Colors.green, height));
    }

    return Row(children: children);
  }

  Widget buildWidgetForCount(
      int count, double sizePer, Color? color, double height) {
    return Container(
      width: count * sizePer,
      color: color,
      height: height,
      child: FittedBox(
          fit: BoxFit.contain,
          child: Text(
            count.toString(),
            textAlign: TextAlign.center,
            style: const TextStyle(color: Colors.black),
          )),
    );
  }

  void favoriteAsset() {
    setState(() {
      APMRoot.global.newQueries.addAssetToUserFavorited(widget.assetCard);
    });
  }

  void unfavoriteAsset() {
    setState(() {
      APMRoot.global.newQueries.removeAssetFromUserFavorited(widget.assetCard);
    });
  }

  Widget buildInnerCard(BuildContext context) {
    var card = widget.assetCard;
    String assetName = card.assetName ?? "No Asset Name";
    String assetId = card.assetId ?? "No Asset Id";
    String assetType = card.assetType ?? "Type not set";
    String assetCategory = card.assetCategory;

    Widget iconWidget;

    bool isAssetFavorited =
        APMRoot.global.newQueries.isAssetUserFavorited(card);

    if (card.asset != null) {
      if (isAssetFavorited) {
        iconWidget = IconButton(
            onPressed: unfavoriteAsset,
            key: const ValueKey("unFavouriteAssetIcon"),
            icon: Icon(Icons.star_outlined,
                color: Colors.blue, size: AppStyle.global.pixels35));
      } else {
        iconWidget = IconButton(
            onPressed: favoriteAsset,
            key: const ValueKey("favouriteAssetIcon"),
            icon: Icon(Icons.cloud_done_outlined,
                color: Colors.blue, size: AppStyle.global.pixels35));
      }
    } else {
      if (isAssetFavorited) {
        iconWidget = IconButton(
            onPressed: unfavoriteAsset,
            key: const ValueKey("unFavouriteAssetIcon"),
            icon: Icon(Icons.star_outline,
                color: Colors.white, size: AppStyle.global.pixels35));
      } else {
        var cardIsContainedInExplicitlyListenedForAssets = APMRoot
            .global.newQueries
            .getAssetIdsExplicitlyListeningFor()
            .contains(card.assetDBId);
        if (cardIsContainedInExplicitlyListenedForAssets) {
          iconWidget = IconButton(
              onPressed: favoriteAsset,
              key: const ValueKey("favouriteAssetIcon"),
              icon: Icon(Icons.cloud_download_outlined,
                  color: Colors.orange, size: AppStyle.global.pixels35));
        } else {
          iconWidget = IconButton(
              onPressed: favoriteAsset,
              key: const ValueKey("favouriteAssetIcon"),
              icon: Icon(Icons.cloud_off,
                  color: Colors.white, size: AppStyle.global.pixels35));
        }
      }
    }

    return Container(
      margin: const EdgeInsets.fromLTRB(0, 10, 0, 0),
      child: Column(children: [
        Row(
          children: [
            Expanded(
              child: Container(
                margin: const EdgeInsets.fromLTRB(0, 0, 5, 55),
                alignment: Alignment.centerLeft,
                child: Text(assetName,
                    style: const TextStyle(
                        fontSize: 17,
                        fontWeight: FontWeight.bold,
                        color: Colors.white)),
              ),
            ),
            iconWidget,
          ],
        ),
        Container(
            alignment: Alignment.centerLeft,
            child: FittedBox(
              fit: BoxFit.contain,
              child: Text("ID: " + assetId,
                  style: const TextStyle(fontSize: 16, color: Colors.white)),
            )),
        Container(height: 5),
        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          Expanded(
              child: Container(
                  alignment: Alignment.centerLeft,
                  child: FittedBox(
                    fit: BoxFit.contain,
                    child: Text(assetCategory + ": " + assetType,
                        style:
                            const TextStyle(fontSize: 16, color: Colors.white)),
                  ))),
          buildStatusIndicator(context, AppStyle.global.pixels65 * 2, 15)
        ]),
        Container(height: 10),
      ]),
    );
  }

  Widget buildTopOfCard(BuildContext context) {
    return ElevatedButton(
        onPressed: widget.onPressed,
        style: ButtonStyle(
            backgroundColor: MaterialStateProperty.resolveWith<Color?>(
          (Set<MaterialState> states) {
            return widget.color;
          },
        )),
        child: buildInnerCard(context));
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> children = [buildTopOfCard(context)];
    if (widget.showGPS) {
      children.add(buildGPSSection(context));
    }

    var card = Card(
        margin: const EdgeInsets.all(10),
        color: widget.color,
        child: Column(children: children));

    return SideSlideoutControl(child: card);
  }
}
