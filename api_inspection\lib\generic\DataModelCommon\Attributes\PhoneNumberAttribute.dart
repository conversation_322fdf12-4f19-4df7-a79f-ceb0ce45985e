import 'package:api_inspection/generic/AttributeControls/AttributeStructure.dart';
import 'package:api_inspection/generic/AttributeControls/PhoneNumber/PhoneNumberAttributeView.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';

import 'ChangeLog/ChangeLogEntry.dart';

class PhoneNumberAttribute extends StringAttribute {
  PhoneNumberAttribute(
      {required DataModelItem parent,
      required String displayName,
      String? databaseName,
      Widget? iconWidget,
      bool areCommentsRequired = false})
      : super(
            parent: parent,
            displayName: displayName,
            iconWidget: iconWidget,
            areCommentsRequired: areCommentsRequired,
            databaseName: databaseName);

  @override
  Widget buildWidget(
      {IsEditableController? editingController,
      bool showPhotos = true,
      bool showComments = true}) {
    return PhoneNumberAttributeView(
      this,
      editingController: editingController,
      showPhotos: showPhotos,
      showComments: showComments,
    );
  }

  @override
  Widget buildWidgetNonEditable() {
    return buildWidget(
        editingController: IsEditableController(isEditable: false));
  }

  @override
  void setValue(String? value, WriteBatch batch) {
    var formattedStr = value;
    if (formattedStr != null && formattedStr.isNotEmpty) {
      var withoutSpace = formattedStr.replaceAll(" ", "");
      if (withoutSpace.length == 10 && int.tryParse(withoutSpace) != null) {
        var first = withoutSpace.substring(0, 3);
        var second = withoutSpace.substring(3, 6);
        var last = withoutSpace.substring(6);

        formattedStr = '(' + first + ") " + second + "-" + last;
      }
    }

    if (areStringsEqual(getValue(), formattedStr)) return;

    var entry = ChangeLogEntry<String>.newlyCreated(formattedStr ?? "");
    valueChangeLog.addNewItem(entry);

    changedBySelfListeners.notifyListeners();
    notifyListeners();
  }
}
