//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'Section510EXT_PVCKLSTSEC001.dart';
import 'Section510EXT_PVCKLSTSEC002.dart';
import 'Section510EXT_PVCKLSTSEC003.dart';
import 'Section510EXT_PVCKLSTSEC004.dart';
import 'Section510EXT_PVCKLSTSEC005.dart';
import 'Section510EXT_PVCKLSTSEC006.dart';
import 'Section510EXT_PVCKLSTSEC007.dart';
import 'Section510EXT_PVCKLSTSEC008.dart';
import 'Section510EXT_PVCKLSTSEC009.dart';
import 'Section510EXT_PVCKLSTSEC010.dart';
import 'Section510EXT_PVCKLSTSEC011.dart';
import 'Section510EXT_PVCKLSTSEC012.dart';
import 'Section510EXT_PVCKLSTSEC013.dart';
import 'Section510EXT_PVCKLSTSEC014.dart';

// ignore: camel_case_types
class Section510_Ext_PV_ALL_F extends DataModelSection {
  @override
  String getDisplayName() => "510-Ext-PV-ALL";
  Section510_Ext_PV_ALL_F(DataModelItem? parent)
      : super(parent: parent, sectionName: "510-Ext-PV-ALL");

// ignore: non_constant_identifier_names
  late Section510EXT_PVCKLSTSEC001 section510EXT_PVCKLSTSEC001 =
      Section510EXT_PVCKLSTSEC001(this);
  // ignore: non_constant_identifier_names
  late Section510EXT_PVCKLSTSEC002 section510EXT_PVCKLSTSEC002 =
      Section510EXT_PVCKLSTSEC002(this);
  // ignore: non_constant_identifier_names
  late Section510EXT_PVCKLSTSEC003 section510EXT_PVCKLSTSEC003 =
      Section510EXT_PVCKLSTSEC003(this);
  // ignore: non_constant_identifier_names
  late Section510EXT_PVCKLSTSEC004 section510EXT_PVCKLSTSEC004 =
      Section510EXT_PVCKLSTSEC004(this);
  // ignore: non_constant_identifier_names
  late Section510EXT_PVCKLSTSEC005 section510EXT_PVCKLSTSEC005 =
      Section510EXT_PVCKLSTSEC005(this);
  // ignore: non_constant_identifier_names
  late Section510EXT_PVCKLSTSEC006 section510EXT_PVCKLSTSEC006 =
      Section510EXT_PVCKLSTSEC006(this);
  // ignore: non_constant_identifier_names
  late Section510EXT_PVCKLSTSEC007 section510EXT_PVCKLSTSEC007 =
      Section510EXT_PVCKLSTSEC007(this);
  // ignore: non_constant_identifier_names
  late Section510EXT_PVCKLSTSEC008 section510EXT_PVCKLSTSEC008 =
      Section510EXT_PVCKLSTSEC008(this);
  // ignore: non_constant_identifier_names
  late Section510EXT_PVCKLSTSEC009 section510EXT_PVCKLSTSEC009 =
      Section510EXT_PVCKLSTSEC009(this);
  // ignore: non_constant_identifier_names
  late Section510EXT_PVCKLSTSEC010 section510EXT_PVCKLSTSEC010 =
      Section510EXT_PVCKLSTSEC010(this);
  // ignore: non_constant_identifier_names
  late Section510EXT_PVCKLSTSEC011 section510EXT_PVCKLSTSEC011 =
      Section510EXT_PVCKLSTSEC011(this);
  // ignore: non_constant_identifier_names
  late Section510EXT_PVCKLSTSEC012 section510EXT_PVCKLSTSEC012 =
      Section510EXT_PVCKLSTSEC012(this);
  // ignore: non_constant_identifier_names
  late Section510EXT_PVCKLSTSEC013 section510EXT_PVCKLSTSEC013 =
      Section510EXT_PVCKLSTSEC013(this);
  // ignore: non_constant_identifier_names
  late Section510EXT_PVCKLSTSEC014 section510EXT_PVCKLSTSEC014 =
      Section510EXT_PVCKLSTSEC014(this);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      section510EXT_PVCKLSTSEC001,
      section510EXT_PVCKLSTSEC002,
      section510EXT_PVCKLSTSEC003,
      section510EXT_PVCKLSTSEC004,
      section510EXT_PVCKLSTSEC005,
      section510EXT_PVCKLSTSEC006,
      section510EXT_PVCKLSTSEC007,
      section510EXT_PVCKLSTSEC008,
      section510EXT_PVCKLSTSEC009,
      section510EXT_PVCKLSTSEC010,
      section510EXT_PVCKLSTSEC011,
      section510EXT_PVCKLSTSEC012,
      section510EXT_PVCKLSTSEC013,
      section510EXT_PVCKLSTSEC014,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section510_Ext_PV_ALL_F";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
