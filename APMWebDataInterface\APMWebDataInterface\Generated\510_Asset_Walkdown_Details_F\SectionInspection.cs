//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Asset_Walkdown_Details_F
{
  public class SectionInspection : DataModelItem {

    public override String DisplayName { 
      get {
        return "Inspection";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public StringAttribute attributeInspection_Code;
    public StringAttribute attributeYear;
    public StringAttribute attributeAddendum;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionInspection";

    public SectionInspection(DataModelItem parent) : base(parent)
    {
            
        attributeInspection_Code = new StringAttribute(this, displayName: "Inspection Code", databaseName: "510AW_Q116"); 
     
        attributeYear = new StringAttribute(this, displayName: "Year", databaseName: "510AW_Q117"); 
     
        attributeAddendum = new StringAttribute(this, displayName: "Addendum", databaseName: "510AW_Q118"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeInspection_Code,
           attributeYear,
           attributeAddendum,
        };
    }
  }
}
