import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
import 'package:flutter/material.dart';

class DoubleAttributeViewNonEditable extends StatefulWidget {
  final DoubleAttribute _attribute;

  const DoubleAttributeViewNonEditable(this._attribute, {Key? key})
      : super(key: key);

  @override
  _DoubleAttributeViewNonEditableState createState() =>
      _DoubleAttributeViewNonEditableState();
}

class _DoubleAttributeViewNonEditableState
    extends State<DoubleAttributeViewNonEditable> {
  void onAttributeChanged() {
    setState(() {});
  }

  @override
  void dispose() {
    DoubleAttribute attribute = widget._attribute;
    attribute.removeListener(onAttributeChanged);
    super.dispose();
  }

  @override
  void initState() {
    DoubleAttribute attribute = widget._attribute;
    attribute.addListener(onAttributeChanged);

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: const EdgeInsets.fromLTRB(40, 10, 40, 10),
        child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              widget._attribute.getPreviewText(),
              style: const TextStyle(color: Colors.white, fontSize: 22),
            )));
  }
}
