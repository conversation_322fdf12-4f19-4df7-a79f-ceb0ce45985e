//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'SectionTaskDetails.dart';
import 'SectionTaskPhotos.dart';

// ignore: camel_case_types
class SectionFlexible_Task_Data_F extends DataModelSection {
  @override
  String getDisplayName() => "Flexible-Task-Data";
  SectionFlexible_Task_Data_F(DataModelItem? parent)
      : super(parent: parent, sectionName: "Flexible-Task-Data");

// ignore: non_constant_identifier_names
  late SectionTaskDetails sectionTaskDetails = SectionTaskDetails(this);

// ignore: non_constant_identifier_names
  late DataModelCollection<SectionTaskPhotos> sectionTaskPhotos =
      DataModelCollection<SectionTaskPhotos>("TaskPhotos", (parent, entry) {
    return SectionTaskPhotos(entry.key, sectionTaskPhotos);
  }, this);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      sectionTaskDetails,
      sectionTaskPhotos,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionFlexible_Task_Data_F";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
