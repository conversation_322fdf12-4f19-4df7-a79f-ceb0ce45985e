//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Int_PV_ALL_F
{
  public class Section510INT_PVCKLSTSEC003 : DataModelItem {

    public override String DisplayName { 
      get {
        return "CHANNEL - HEX ONLY";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC003Q001;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC003Q002;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC003Q003;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC003Q004;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC003Q005;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC003Q006;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC003Q007;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC003Q008;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC003Q009;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC003Q010;
    public PredefinedValueAttribute attributeIs_the_channel_to_shell_attachment_achieved_via_welding;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC003Q012;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC003Q013;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC003Q014;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC003Q015;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510INT_PVCKLSTSEC003";

    public Section510INT_PVCKLSTSEC003(DataModelItem parent) : base(parent)
    {
            
        attribute510INT_PVCKLSTSEC003Q001 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Impingement", null),
          new PredefinedValueOption("Yes: Pitting", null)
        }, false, this, "Were any corrosion cells, impingement or pitting noted on the channel surfaces: (The dimensions and locations of any damage shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC003_Q001"); 
     
        attribute510INT_PVCKLSTSEC003Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Cracking", null),
          new PredefinedValueOption("Yes: Mechanical Damage", null)
        }, false, this, "Was any mechanical damage or cracking noted on the channel surfaces: (The dimensions and location of any damage shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC003_Q002"); 
     
        attribute510INT_PVCKLSTSEC003Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Weld Corrosion", null),
          new PredefinedValueOption("Yes: Weld Cracking", null)
        }, false, this, "Were there indications of weld corrosion or cracking in the welds or Heat Affected Zones of the shell pressure retaining welds:", databaseName: "510_INT-PV_CKLST_SEC003_Q003"); 
     
        attribute510INT_PVCKLSTSEC003Q004 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Deformation", null),
          new PredefinedValueOption("Yes: Hot Spots", null)
        }, false, this, "Does the channel have any deformations or hot spots: (Bulges, Blisters, Dimpling)", databaseName: "510_INT-PV_CKLST_SEC003_Q004"); 
     
        attribute510INT_PVCKLSTSEC003Q005 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Impingement", null),
          new PredefinedValueOption("Yes: Pitting", null)
        }, false, this, "Were any corrosion cells, impingement or pitting noted on the partition plate: (The dimensions and locations of any damage shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC003_Q005"); 
     
        attribute510INT_PVCKLSTSEC003Q006 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Cracking", null),
          new PredefinedValueOption("Yes: Deformation", null),
          new PredefinedValueOption("Yes: Mechanical Damage", null),
          new PredefinedValueOption("Yes: Knife Edging", null)
        }, false, this, "Was any mechanical damage, deformation, knife edging or cracking noted on the partition plate: (The dimensions and location of any damage shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC003_Q006"); 
     
        attribute510INT_PVCKLSTSEC003Q007 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Weld Corrosion", null),
          new PredefinedValueOption("Yes: Weld Cracking", null)
        }, false, this, "Were there indications of weld corrosion or cracking in the welds or Heat Affected Zones of the partition plate welds:", databaseName: "510_INT-PV_CKLST_SEC003_Q007"); 
     
        attribute510INT_PVCKLSTSEC003Q008 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Deformation", null),
          new PredefinedValueOption("Yes: Hot Spots", null)
        }, false, this, "Does the partition plate have any deformations or hot spots: (Bulges, Blisters, Dimpling)", databaseName: "510_INT-PV_CKLST_SEC003_Q008"); 
     
        attribute510INT_PVCKLSTSEC003Q009 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are channel penetrations and adjacent areas in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC003_Q009"); 
     
        attribute510INT_PVCKLSTSEC003Q010 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was any mechanical damage or impacts from objects noted on the channel:", databaseName: "510_INT-PV_CKLST_SEC003_Q010"); 
     
        attributeIs_the_channel_to_shell_attachment_achieved_via_welding = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the channel to shell attachment achieved via welding:", databaseName: "510_INT-PV_CKLST_SEC003_Q011"); 
     
        attribute510INT_PVCKLSTSEC003Q012 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the channel to shell weld in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC003_Q012"); 
     
        attribute510INT_PVCKLSTSEC003Q013 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the channel to head attachment achieved via flanged connection(s):", databaseName: "510_INT-PV_CKLST_SEC003_Q013"); 
     
        attribute510INT_PVCKLSTSEC003Q014 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the channel to head flanged connection(s) in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC003_Q014"); 
     
        attribute510INT_PVCKLSTSEC003Q015 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the channel of the asset in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC003_Q015"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute510INT_PVCKLSTSEC003Q001,
           attribute510INT_PVCKLSTSEC003Q002,
           attribute510INT_PVCKLSTSEC003Q003,
           attribute510INT_PVCKLSTSEC003Q004,
           attribute510INT_PVCKLSTSEC003Q005,
           attribute510INT_PVCKLSTSEC003Q006,
           attribute510INT_PVCKLSTSEC003Q007,
           attribute510INT_PVCKLSTSEC003Q008,
           attribute510INT_PVCKLSTSEC003Q009,
           attribute510INT_PVCKLSTSEC003Q010,
           attributeIs_the_channel_to_shell_attachment_achieved_via_welding,
           attribute510INT_PVCKLSTSEC003Q012,
           attribute510INT_PVCKLSTSEC003Q013,
           attribute510INT_PVCKLSTSEC003Q014,
           attribute510INT_PVCKLSTSEC003Q015,
        };
    }
  }
}
