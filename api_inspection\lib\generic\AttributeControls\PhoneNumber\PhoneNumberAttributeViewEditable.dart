import 'package:api_inspection/app/batch_helper.dart';
import 'package:api_inspection/generic/UIControls/listener.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
import 'package:url_launcher/url_launcher.dart';

class PhoneNumberAttributeViewEditable extends StatefulWidget {
  final PhoneNumberAttribute _attribute;
  final ListenerWrapper updateListener;
  final bool showPhotoControl;
  final bool showCommentsControl;

  const PhoneNumberAttributeViewEditable(this._attribute, this.updateListener,
      {Key? key, this.showPhotoControl = true, this.showCommentsControl = true})
      : super(key: key);

  @override
  _PhoneNumberAttributeViewEditableState createState() =>
      _PhoneNumberAttributeViewEditableState();
}

class _PhoneNumberAttributeViewEditableState
    extends State<PhoneNumberAttributeViewEditable> {
  TextEditingController? _controller;
  bool initialized = false;

  void updateAttributeValue() {
    TextEditingController? controller = _controller;
    if (controller != null) {
      BatchHelper.saveAndCommitStringAttribute(
          widget._attribute, controller.text);
    }
  }

  void initialize() {
    if (initialized) return;
    initialized = true;
    StringAttribute attribute = widget._attribute;
    attribute.addListener(onAttributeChanged);
  }

  void onAttributeChanged() {
    setState(() {
      TextEditingController? controller = _controller;
      if (controller != null &&
          controller.text != widget._attribute.getValue()) {
        String? attributeValue = widget._attribute.getValue();
        controller.text = attributeValue ?? "";
      }
    });
  }

  @override
  void dispose() {
    widget.updateListener.removeListener(updateAttributeValue);
    StringAttribute attribute = widget._attribute;
    attribute.removeListener(onAttributeChanged);
    _controller?.dispose();
    super.dispose();
  }

  @override
  void initState() {
    widget.updateListener.addListener(updateAttributeValue);
    super.initState();
  }

  @override
  void didUpdateWidget(covariant PhoneNumberAttributeViewEditable oldWidget) {
    oldWidget.updateListener.removeListener(updateAttributeValue);
    widget.updateListener.addListener(updateAttributeValue);
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    initialize();

    _controller ??= TextEditingController(text: widget._attribute.getValue());
    var attributeTextField = Container(
      margin: const EdgeInsets.fromLTRB(20, 10, 10, 10),
      child: Focus(
        onFocusChange: (hasFocus) {
          if (!hasFocus) {
            updateAttributeValue();
          }
        },
        child:
            Column(crossAxisAlignment: CrossAxisAlignment.stretch, children: [
          Text(
            widget._attribute.displayName,
            style: const TextStyle(color: Colors.white, fontSize: 16),
            textAlign: TextAlign.start,
          ),
          TextField(
            key: const ValueKey('PhoneNumberTextboxField'),
            controller: _controller,
            decoration: const InputDecoration(
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: Colors.white, width: 1.0),
              ),
              border: OutlineInputBorder(
                borderSide: BorderSide(color: Colors.white, width: 1.0),
              ),
            ),
            onSubmitted: (String value) {
              updateAttributeValue();
            },
            style: const TextStyle(color: Colors.white),
          ),
        ]),
      ),
    );

    var currentValue = _controller!.text;
    if (currentValue.isEmpty) {
      return attributeTextField;
    } else {
      return Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Expanded(
          child: attributeTextField,
        ),
        IconButton(
            splashRadius: 25,
            iconSize: 40,
            onPressed: () {
              launchUrl(Uri(scheme: 'tel', path: _controller!.text));
            },
            icon: const Icon(
              Icons.phone,
              color: Colors.green,
            ))
      ]);
    }
  }
}
