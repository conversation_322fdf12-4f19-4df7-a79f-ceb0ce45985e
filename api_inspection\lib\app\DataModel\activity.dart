import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/UIControls/listener.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class ProjectActivityItem extends DataModelItem {
  String name;

  @override
  String getDisplayName() => name;

  ProjectActivity parentActivity;
  ProjectActivityItem({required this.parentActivity, required this.name})
      : super(parentActivity);

  late DoubleAttribute duration = DoubleAttribute(
      displayName: "Duration",
      parent: this,
      allowNegatives: false,
      listener: onDurationChanged);
  late DoubleAttribute count = DoubleAttribute(
      displayName: "Count", parent: this, allowNegatives: false);

  void onDurationChanged() {
    parentActivity.activitiesListener.notifyListeners();
  }

  @override
  List<DataModelItem> getChildren() {
    return [duration, count];
  }

  @override
  String getDBName() {
    return name;
  }
}

class ProjectActivity extends DataModelCollectionItem {
  ProjectActivity(DataModelItem parent, String id) : super(id, parent);

  @override
  String getDisplayName() => "Project Activity";

  late StringAttribute workOrderNumber =
      StringAttribute(parent: this, displayName: "Client WO Number");

  late DateAttribute date = DateAttribute(parent: this, displayName: "Date");

  late StringAttribute user =
      StringAttribute(parent: this, displayName: "User", isQueryable: true);

  late List<ProjectActivityItem> activities = [
    ProjectActivityItem(
      parentActivity: this,
      name: "Permitting",
    ),
    ProjectActivityItem(parentActivity: this, name: "Job Setup"),
    ProjectActivityItem(parentActivity: this, name: "Lunch"),
    ProjectActivityItem(parentActivity: this, name: "Post CleanUp"),
    ProjectActivityItem(parentActivity: this, name: "FW-RT"),
    ProjectActivityItem(parentActivity: this, name: "FW-MT"),
    ProjectActivityItem(parentActivity: this, name: "FW-PT"),
    ProjectActivityItem(parentActivity: this, name: "FW-UT"),
    ProjectActivityItem(parentActivity: this, name: "FW-VT"),
    ProjectActivityItem(parentActivity: this, name: "FW-ML"),
    ProjectActivityItem(parentActivity: this, name: "FW-GW"),
    ProjectActivityItem(parentActivity: this, name: "FW-ET"),
    ProjectActivityItem(parentActivity: this, name: "FW-LS"),
    ProjectActivityItem(parentActivity: this, name: "FW-GPR"),
    ProjectActivityItem(parentActivity: this, name: "FW-LT"),
    ProjectActivityItem(parentActivity: this, name: "FW-IR"),
    ProjectActivityItem(parentActivity: this, name: "FW-PMI"),
    ProjectActivityItem(parentActivity: this, name: "FW-AE"),
    ProjectActivityItem(parentActivity: this, name: "FW-VA"),
    ProjectActivityItem(parentActivity: this, name: "FW-API 510"),
    ProjectActivityItem(parentActivity: this, name: "FW-API 570"),
    ProjectActivityItem(parentActivity: this, name: "FW-API 653"),
    ProjectActivityItem(parentActivity: this, name: "FW-AWS CWI"),
    ProjectActivityItem(parentActivity: this, name: "FW-NACE"),
    ProjectActivityItem(parentActivity: this, name: "FW-Other"),
  ];

  ListenerWrapper activitiesListener = ListenerWrapper();

  double getTotalHours() {
    double time = 0;
    for (var activity in activities) {
      var activityTime = activity.duration.getValue();
      if (activityTime != null) time += activityTime;
    }
    return time;
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([workOrderNumber, date, user]);
    children.addAll(activities);
    return children;
  }

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}

class TaskActivity extends DataModelCollectionItem {
  @override
  String getDisplayName() => "Task Activity";

  TaskActivity(DataModelItem parent, String id) : super(id, parent);

  late DateAttribute date = DateAttribute(parent: this, displayName: "Date");

  late List<DoubleAttribute> activities = [
    DoubleAttribute(
        parent: this,
        allowNegatives: false,
        displayName: "Permitting",
        listener: activityChanged),
    DoubleAttribute(
        parent: this,
        allowNegatives: false,
        displayName: "Job Setup",
        listener: activityChanged),
    DoubleAttribute(
        parent: this,
        allowNegatives: false,
        displayName: "Lunch",
        listener: activityChanged),
    DoubleAttribute(
        parent: this,
        allowNegatives: false,
        displayName: "Post CleanUp",
        listener: activityChanged),
    DoubleAttribute(
        parent: this,
        allowNegatives: false,
        displayName: "FW-VT",
        listener: activityChanged),
    DoubleAttribute(
        parent: this,
        allowNegatives: false,
        displayName: "FW-API 510",
        listener: activityChanged),
    DoubleAttribute(
        parent: this,
        allowNegatives: false,
        displayName: "FW-API 570",
        listener: activityChanged),
    DoubleAttribute(
        parent: this,
        allowNegatives: false,
        displayName: "FW-API 653",
        listener: activityChanged),
    DoubleAttribute(
        parent: this,
        allowNegatives: false,
        displayName: "FW-Other",
        listener: activityChanged),
  ];

  void activityChanged() {
    activitiesListener.notifyListeners();
  }

  ListenerWrapper activitiesListener = ListenerWrapper();

  double getTotalHours() {
    double time = 0;
    for (var activity in activities) {
      var activityTime = activity.getValue();
      if (activityTime != null) time += activityTime;
    }
    return time;
  }

  @override
  String getDBName() {
    return id;
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.add(date);
    children.addAll(activities);
    return children;
  }

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
