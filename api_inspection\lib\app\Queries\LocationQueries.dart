import 'dart:async';
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:api_inspection/generic/UIControls/listener.dart';
import 'package:api_inspection/generic/DataModelCommon/DatabaseInterfaces/FirebaseDatabaseHelper.dart';
import '../../generic/AppRoot.dart';
import '../DataModel/location.dart';
import 'package:collection/collection.dart';

import 'package:api_inspection/app/Queries/Queries.dart';

class LocationQueries {
  Future<void> dispose() async {
    for (var element in locationSubscriptions) {
      await element.cancel();
    }
    locationsChangedListener.dispose();
  }

  void initialize(Queries parent) {
    startQuery();
  }

  ListenerWrapper locationsChangedListener = ListenerWrapper();

  List<Location> locations = [];
  List<StreamSubscription<QuerySnapshot<Map<String, dynamic>>>>
      locationSubscriptions = [];

  void startQuery() async {
    var databaseReference = FirebaseDatabaseHelper.global().databaseReference;

    final buIdBatches = AppRoot.global().businessIdsBatches;
    if (buIdBatches != null) {
      for (var buIdBatch in buIdBatches) {
        locationSubscriptions.add(
          databaseReference
              .collection('locations')
              .where('BusinessUnitId.Value', whereIn: buIdBatch.toList())
              .snapshots()
              .listen(onLocationsUpdated),
        );
      }
    }
  }

  void onLocationsUpdated(QuerySnapshot<Map<String, dynamic>> snapshot) {
    List<Location> newlocations = [];

    for (var item in snapshot.docs) {
      if (!LocationCache.containsKey(item.id)) {
        // If we don't have this in the cache yet, go ahead and instantiate
        var location = LocationCache.findEntry(item.id);
        location.updateFromMap(item.data());
        location.setShouldDownloadPhotos(false);
        newlocations.add(location);
      } else {
        // If we already have this location in the cache, check if we need
        // to update our instance with document changes from the server
        var change = snapshot.docChanges
            .firstWhereOrNull((element) => element.doc.id == item.id);
        var location = LocationCache.findEntry(item.id);
        if (change != null) {
          if (change.type == DocumentChangeType.added ||
              change.type == DocumentChangeType.modified) {
            location.updateFromMap(item.data());
            location.setShouldDownloadPhotos(false);
          } else {
            log('We do not support data removal, yet a document with id ${change.doc.id} was removed.');
          }
        }
        newlocations.add(location);
      }
    }

    locations = newlocations;

    locationsChangedListener.notifyListeners();
  }
}
