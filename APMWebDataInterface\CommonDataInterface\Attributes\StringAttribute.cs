﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CommonDataInterface.Attributes
{
  public class StringAttribute : SingleAttributeBase<string> {
    public override String AttributeType => "String";


    public override bool IsValueEqualTo(String other)
    {
      var currentValue = GetValue();
      return AreStringsEqual(other, currentValue);
    }

    public override void SetGenericValueTo(String other)
    {
      this.SetValue(other);
    }


    public String? GetValue() {
      if (this.GetValueChangeLog().entries.Count == 0)
        return null;
      return this.GetValueChangeLog().entries.Last().Value;
    }

    public override String GetPreviewText() {
      var value = GetValue();
      return value == null ? "" : value;
    }

    protected bool AreStringsEqual(String? str1, String? str2){
      if ((String.IsNullOrWhiteSpace(str1)) && (String.IsNullOrWhiteSpace(str2)))
        return true;
      if (str1 == null)
        return false;
      if (str2 == null)
        return false;
      return str1 == str2;
    }

    public virtual void SetValue(String? value){
      if (AreStringsEqual(GetValue(), value))
        return;
        
      this.GetValueChangeLog().PendingChange = new PendingChange<string>{Value = value};

      NotifyListeners();
    }


    public String QueryableValue;

    public override void UpdateFromMap(Dictionary<string, object> map)
    {
      if (map != null && map.ContainsKey("Value")) {
        QueryableValue = map["Value"]?.ToString();
      }
      base.UpdateFromMap(map);
    }


    protected override async Task DoAddPendingChangesToDictionary(Dictionary<string, Object> updates, String user)
    {
      if (IsQueryable && !String.Equals(QueryableValue, CurrentPendingOrValue, StringComparison.InvariantCultureIgnoreCase)) {
        var currentPath = GetDBPath().Split('.');
        var remainingPath = currentPath.Skip(2);
        var path = remainingPath.AggregateEXT((a, b) => a + '.' + b) + ".Value";

        updates[path] = CurrentPendingOrValue?.ToLower();
        QueryableValue = CurrentPendingOrValue?.ToLower();
      }

      await base.DoAddPendingChangesToDictionary(updates, user);
    }


    public override bool GetHasDatabaseChangesPending()
    {
      if (IsQueryable && !String.Equals(QueryableValue, CurrentPendingOrValue, StringComparison.InvariantCultureIgnoreCase)) {
        return true;
      }

      return base.GetHasDatabaseChangesPending();
    }



    public StringAttribute(DataModelItem parent, String displayName, bool areCommentsRequired = false, bool isQueryable = false, String databaseName = null)
      : base(parent, displayName, databaseName, areCommentsRequired, isQueryable)
    {

    }

  }
}