﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CommonDataInterface
{
  public abstract class ConcretePhotoRoot : DataModelRoot
  {
    internal bool includeHistoryInJson = false;

    public void ExcludeHistoryFromJson()
    {
      includeHistoryInJson = false;
    }
    public void IncludeHistoryInJson()
    {
      includeHistoryInJson = true;
    }

    public String id;

    private PhotoCollection _mediaItems;

    internal PhotoCollection GetMediaItems() {
      if (_mediaItems == null) {
        _mediaItems = new PhotoCollection("Photos", this);
      }

      return _mediaItems;
      
    }
    
    internal List<PendingMediaEntry> PendingMedia = new List<PendingMediaEntry>();

    internal ConcretePhotoRoot(DataModelItem? parent) : base(parent)
    {
      Exists = false;
      this.id = Guid.NewGuid().ToString();
    }


    internal ConcretePhotoRoot(String id, DataModelItem? parent, bool exists = true) : base(parent)
    {
      Exists = exists;
      this.id = id;
    }

    internal void AddPendingPhoto(PendingMediaEntry pendingMedia)
    {
      PendingMedia.Add(pendingMedia);
      _mediaItems.AddNewItem(pendingMedia.MediaName, pendingMedia.Extension);
    }


    public MediaEntry GetPhoto(String filename, String extension)
    {
      return _mediaItems.GetEntries().FirstOrDefault(a => a.MediaName == filename && a.Extension == extension);
    }

    public async Task ResolvePhotos()
    {
      await ResolvePhotos(GetMediaItems().CurrentEntries);
    }
    
    public async Task ResolvePhotosWithResize(int width, int height, int quality)
    {
      List<Task> resolveTasks = new List<Task>();
      foreach (var entry in GetMediaItems().CurrentEntries) {
        resolveTasks.Add(entry.ResolveWithResize(width, height, quality));
      }

      foreach (var task in resolveTasks) {
        await task;
      }
    }

    public async Task ResolvePhotos(List<MediaEntry> mediaEntries)
    {
      foreach (var item in mediaEntries) {
        await item.Resolve();
      }
    }
    


    public override DataModelItem[] GetChildren(){
      return new DataModelItem [] {  GetMediaItems() };
    }

    
    protected override async Task DoAddPendingChangesToDictionary(Dictionary<string, Object> updates, String user)
    {
      if (PendingMedia.Any()) {
        foreach (var item in PendingMedia) {
          var newMediaEntry = GetMediaItems().AddNewItem(item.MediaName, item.Extension);

          if (!String.IsNullOrWhiteSpace(item.Description)) {
            newMediaEntry.Description.SetValue(item.Description);
          }
          
        }
        PendingMedia.Clear();
      }

    }

    
    internal bool Exists = false;
    public override async Task SavePendingChanges(String user)
    {
      List<Task> blobUploadTasks = new List<Task>();
      if (PendingMedia.Any()) {
        foreach (var media in PendingMedia) {
          blobUploadTasks.Add(
            BlobStorageHelper.BlobContainerClient.UploadBlobAsync(media.MediaName + "." + media.Extension, BinaryData.FromBytes(media.ImageData))
          );
        }
      }
      blobUploadTasks.ForEach(a => a.Wait());


      if (Exists) {
        await base.SavePendingChanges(user);
        return;
      }

      await DoStartSavingChange(user);

      var updates = new Dictionary<string, object>();

      await AddPendingChangesToDictionary(updates, user);
      AddOneTimeChangesToDictionary(updates);

      var path = GetDBPath().Split('.');      
      var current = await DataManager.ContextManager.ContextForNonListenerAction(db => db.Collection(path[0]).Document(id));
      
      Exists = true;


      Dictionary<string, object> nestedUpdates = new Dictionary<string, object>();
      foreach (var update in updates) {
        var split = update.Key.Split('.');
        var updatePath = update.Key.Split('.').Take(split.Length - 1).AggregateEXT((a, b) => a + '.' + b);
        var obj = GetDictionaryFromPath(updatePath, nestedUpdates);
        obj[split.Last()] = update.Value;
      }

      await current.CreateAsync(nestedUpdates);
    }


    
    private Dictionary<string, object> GetDictionaryFromPath(String path, Dictionary<String, object> baseDictionary)
    {
      if (String.IsNullOrWhiteSpace(path))
        return baseDictionary;

      var parts = path.Split('.');
      System.Collections.Generic.Dictionary<String, object> current = baseDictionary;
      foreach (var part in parts) {
        if (!current.ContainsKey(part))
          current[part] = new Dictionary<string, object>();
        current = current[part] as Dictionary<string, object>;
      }

      return current;
    }
  }
}