﻿using System;

namespace CommonDataInterface.Attributes
{

  public class LocationAttribute : SingleAttributeBase<String>{


    private double? getDoubleFromString(String str)
    {
      if (String.IsNullOrWhiteSpace(str)) {
        return null;
      }

      if (double.TryParse(str, out var parsed)) {
        return parsed;
      }

      throw new Exception("Could not parse double from string: " + str);
    }
    
    public override bool IsValueEqualTo(String other)
    {
      var currentLatValue = GetLatValue();
      var currentLongValue = GetLongValue();
      if (String.IsNullOrWhiteSpace(other)) {
        return currentLatValue == null && currentLongValue == null;
      }

      var split = other.Split(",");
      if (split.Length != 2)
        throw new Exception("Coordinate should be formatted lat,long");

      var newLat = getDoubleFromString(split[0]);
      var newLong = getDoubleFromString(split[0]);

      return areDoublesEqual(newLat, currentLatValue) && areDoublesEqual(newLong, currentLongValue);
    }

    public override void SetGenericValueTo(String other)
    { 
      double? newLat = null;
      double? newLong = null;

      if (!String.IsNullOrWhiteSpace(other)) {
        var split = other.Split(",");
        if (split.Length != 2)
          throw new Exception("Coordinate should be formatted lat,long");

        newLat = getDoubleFromString(split[0]);
        newLong = getDoubleFromString(split[0]);
      }

      SetValue(newLat, newLong);
    }

    public override String AttributeType => "Coordinate";
    public LocationAttribute(DataModelItem parent, String displayName, bool areCommentsRequired = false, String databaseName = null)
    : base(parent, displayName, databaseName,  areCommentsRequired)
    {

    }

    public override String GetPreviewText() {
      var value = getDisplayValue();
      return value == null ? "" : value;
    }

    public bool AreCurrentCoordinatesValid(){
      var lat = GetLatValue();
      var longitude = GetLongValue();
      if (lat == null || longitude == null)
        return true;
      if (lat < -90 || lat > 90)
        return false;

      if (longitude < -180 || longitude > 180)
        return false;

      return true;
    }

    private bool areDoublesEqual(double? a, double? b)
    {
      if (a == null && b == null)
        return true;
      if (a == null || b == null)
        return false;
      var a1 = a.Value;
      var b1 = b.Value;
      if (Math.Abs(a1 - b1) < 0.00001)
        return true;

      return false;
    }


    public void SetValue(double? lat,double? longitude){
      var currentLat = GetLatValue();
      var currentLong = GetLongValue();
      if (areDoublesEqual(currentLat, lat) && areDoublesEqual(currentLong, longitude)){
        return;
      }

      PendingChange<String> pendingChange;
      if (lat == null && longitude == null){
        pendingChange = new PendingChange<String>();
      }
      else{
        String latText = lat == null ? "" : lat.ToString();
        String longText = longitude == null ? "" : longitude.ToString();

        pendingChange =new PendingChange<String>(){ Value = latText+","+longText };
      }

      this.GetValueChangeLog().PendingChange = pendingChange;
      NotifyListeners();
    }



    public double? GetLatValue()
    {
      var currentValue = CurrentPendingOrValue;
      if (currentValue == null)
        return null;

      var split = currentValue.Split(',');
      if (split.Length > 0 && double.TryParse(split[0], out var parsed)) {
        return parsed;
      }

      return null;
    }

    public double? GetLongValue(){
        
      var currentValue = CurrentPendingOrValue;
      if (currentValue == null)
        return null;

      var split = currentValue.Split(',');
      if (split.Length > 1 && double.TryParse(split[1], out var parsed)) {
        return parsed;
      }

      return null;
    }

    public String? getDisplayValue() {
      
      if (CurrentPendingOrValue == null)
        return "Not set";

      var lat = GetLatValue();
      var longitude = GetLongValue();

      var latStr = lat == null ? "" : lat.Value.ToString("F4");
      var longStr = longitude == null ? "" : longitude.Value.ToString("F4");
      return latStr + ", " + longStr;
      
    }


   
  }

}
