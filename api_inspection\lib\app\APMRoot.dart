import '../generic/AppRoot.dart';
import 'Queries/Queries.dart';
import 'Queries/new_queries.dart';

const selectedBusinessUnitIdKey = "SelectedBusinessUnitId";

class APMRoot {
  static APMRoot? _global;
  static APMRoot get global {
    _global ??= APMRoot();
    return _global!;
  }

  Future resetState() async {
    var oldQueries = queries;
    queries = Queries();
    await oldQueries.dispose();
    _isInit = false;
  }

  late Queries queries;
  late NewQueries newQueries;

  bool _isInit = false;

  Future<void> initQueries() async {
    if (_isInit) return;
    _isInit = true;

    queries = Queries();
    newQueries = NewQueries();
    await queries.initialize();
    await newQueries.initialize();
  }

  String? selectedBusinessUnitId =
      AppRoot.global().sharedPreferences.getString(selectedBusinessUnitIdKey);
}
