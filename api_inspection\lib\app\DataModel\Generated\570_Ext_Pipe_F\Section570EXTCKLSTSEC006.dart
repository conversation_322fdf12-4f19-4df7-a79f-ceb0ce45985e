//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class Section570EXTCKLSTSEC006 extends DataModelSection {
  @override
  String getDisplayName() => "VALVES";
  Section570EXTCKLSTSEC006(DataModelItem? parent)
      : super(parent: parent, sectionName: "VALVES");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC006Q001 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are installed valves in accordance with the owner/users piping design specifications:",
          databaseName: "570_EXT_CKLST_SEC006_Q001",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute570EXTCKLSTSEC006Q002 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Were any corrosion, erosion, or pitting cells noted on the installed valves:",
          databaseName: "570_EXT_CKLST_SEC006_Q002",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Corrosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Erosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Pitting", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC006Q003 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Were any crack like indications noted on the installed valves:",
          databaseName: "570_EXT_CKLST_SEC006_Q003",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeWas_any_mechanical_damage_noted_on_the_associated_valves =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Was any mechanical damage noted on the associated valves:",
          databaseName: "570_EXT_CKLST_SEC006_Q004",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute570EXTCKLSTSEC006Q005 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Was evidence of active or prior leakage noted originating from valves within the inspected piping circuit:",
          databaseName: "570_EXT_CKLST_SEC006_Q005",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Active Leakage", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Evidence of Prior Leakage", null,
            isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeAre_valves_in_acceptable_condition_for_continued_service =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are valves in acceptable condition for continued service:",
          databaseName: "570_EXT_CKLST_SEC006_Q006",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attribute570EXTCKLSTSEC006Q001,
      attribute570EXTCKLSTSEC006Q002,
      attribute570EXTCKLSTSEC006Q003,
      attributeWas_any_mechanical_damage_noted_on_the_associated_valves,
      attribute570EXTCKLSTSEC006Q005,
      attributeAre_valves_in_acceptable_condition_for_continued_service,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section570EXTCKLSTSEC006";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
