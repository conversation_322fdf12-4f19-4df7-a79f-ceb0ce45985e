//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'Section510_Int_PV_ALL_F.dart';
import 'Section510INT_PVCKLSTSEC001Page.dart';
import 'Section510INT_PVCKLSTSEC002Page.dart';
import 'Section510INT_PVCKLSTSEC003Page.dart';
import 'Section510INT_PVCKLSTSEC004Page.dart';
import 'Section510INT_PVCKLSTSEC005Page.dart';
import 'Section510INT_PVCKLSTSEC006Page.dart';
import 'Section510INT_PVCKLSTSEC007Page.dart';
import 'Section510INT_PVCKLSTSEC008Page.dart';
import 'Section510INT_PVCKLSTSEC009Page.dart';
import 'Section510INT_PVCKLSTSEC010Page.dart';
import 'Section510INT_PVCKLSTSEC011Page.dart';
import 'Section510INT_PVCKLSTSEC012Page.dart';
import 'Section510INT_PVCKLSTSEC013Page.dart';
import 'Section510INT_PVCKLSTSEC014Page.dart';
import 'Section510INT_PVCKLSTSEC015Page.dart';
import 'Section510INT_PVCKLSTSEC016Page.dart';
import 'Section510INT_PVCKLSTSEC017Page.dart';
import 'Section510INT_PVCKLSTSEC018Page.dart';
import 'Section510INT_PVCKLSTSEC019Page.dart';

// ignore: camel_case_types
class Section510_Int_PV_ALL_FPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final Section510_Int_PV_ALL_F section510_Int_PV_ALL_F;

  const Section510_Int_PV_ALL_FPage(this.section510_Int_PV_ALL_F, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _Section510_Int_PV_ALL_FPageState();
  }
}

class _Section510_Int_PV_ALL_FPageState
    extends State<Section510_Int_PV_ALL_FPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.section510_Int_PV_ALL_F,
        title: "510-Int-PV-ALL",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Int_PV_ALL_F
                            .section510INT_PVCKLSTSEC001,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510INT_PVCKLSTSEC001Page(widget
                                              .section510_Int_PV_ALL_F
                                              .section510INT_PVCKLSTSEC001)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Int_PV_ALL_F
                            .section510INT_PVCKLSTSEC002,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510INT_PVCKLSTSEC002Page(widget
                                              .section510_Int_PV_ALL_F
                                              .section510INT_PVCKLSTSEC002)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Int_PV_ALL_F
                            .section510INT_PVCKLSTSEC003,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510INT_PVCKLSTSEC003Page(widget
                                              .section510_Int_PV_ALL_F
                                              .section510INT_PVCKLSTSEC003)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Int_PV_ALL_F
                            .section510INT_PVCKLSTSEC004,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510INT_PVCKLSTSEC004Page(widget
                                              .section510_Int_PV_ALL_F
                                              .section510INT_PVCKLSTSEC004)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Int_PV_ALL_F
                            .section510INT_PVCKLSTSEC005,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510INT_PVCKLSTSEC005Page(widget
                                              .section510_Int_PV_ALL_F
                                              .section510INT_PVCKLSTSEC005)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Int_PV_ALL_F
                            .section510INT_PVCKLSTSEC006,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510INT_PVCKLSTSEC006Page(widget
                                              .section510_Int_PV_ALL_F
                                              .section510INT_PVCKLSTSEC006)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Int_PV_ALL_F
                            .section510INT_PVCKLSTSEC007,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510INT_PVCKLSTSEC007Page(widget
                                              .section510_Int_PV_ALL_F
                                              .section510INT_PVCKLSTSEC007)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Int_PV_ALL_F
                            .section510INT_PVCKLSTSEC008,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510INT_PVCKLSTSEC008Page(widget
                                              .section510_Int_PV_ALL_F
                                              .section510INT_PVCKLSTSEC008)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Int_PV_ALL_F
                            .section510INT_PVCKLSTSEC009,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510INT_PVCKLSTSEC009Page(widget
                                              .section510_Int_PV_ALL_F
                                              .section510INT_PVCKLSTSEC009)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Int_PV_ALL_F
                            .section510INT_PVCKLSTSEC010,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510INT_PVCKLSTSEC010Page(widget
                                              .section510_Int_PV_ALL_F
                                              .section510INT_PVCKLSTSEC010)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Int_PV_ALL_F
                            .section510INT_PVCKLSTSEC011,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510INT_PVCKLSTSEC011Page(widget
                                              .section510_Int_PV_ALL_F
                                              .section510INT_PVCKLSTSEC011)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Int_PV_ALL_F
                            .section510INT_PVCKLSTSEC012,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510INT_PVCKLSTSEC012Page(widget
                                              .section510_Int_PV_ALL_F
                                              .section510INT_PVCKLSTSEC012)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Int_PV_ALL_F
                            .section510INT_PVCKLSTSEC013,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510INT_PVCKLSTSEC013Page(widget
                                              .section510_Int_PV_ALL_F
                                              .section510INT_PVCKLSTSEC013)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Int_PV_ALL_F
                            .section510INT_PVCKLSTSEC014,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510INT_PVCKLSTSEC014Page(widget
                                              .section510_Int_PV_ALL_F
                                              .section510INT_PVCKLSTSEC014)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Int_PV_ALL_F
                            .section510INT_PVCKLSTSEC015,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510INT_PVCKLSTSEC015Page(widget
                                              .section510_Int_PV_ALL_F
                                              .section510INT_PVCKLSTSEC015)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Int_PV_ALL_F
                            .section510INT_PVCKLSTSEC016,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510INT_PVCKLSTSEC016Page(widget
                                              .section510_Int_PV_ALL_F
                                              .section510INT_PVCKLSTSEC016)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Int_PV_ALL_F
                            .section510INT_PVCKLSTSEC017,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510INT_PVCKLSTSEC017Page(widget
                                              .section510_Int_PV_ALL_F
                                              .section510INT_PVCKLSTSEC017)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Int_PV_ALL_F
                            .section510INT_PVCKLSTSEC018,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510INT_PVCKLSTSEC018Page(widget
                                              .section510_Int_PV_ALL_F
                                              .section510INT_PVCKLSTSEC018)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section510_Int_PV_ALL_F
                            .section510INT_PVCKLSTSEC019,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section510INT_PVCKLSTSEC019Page(widget
                                              .section510_Int_PV_ALL_F
                                              .section510INT_PVCKLSTSEC019)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                ]),
          );
        });
  }
}
