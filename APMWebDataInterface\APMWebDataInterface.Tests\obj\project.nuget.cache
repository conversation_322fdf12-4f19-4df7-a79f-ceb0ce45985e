{"version": 2, "dgSpecHash": "qfMxOhZOqIQ=", "success": false, "projectFilePath": "C:\\Users\\<USER>\\Desktop\\ImageResizing\\apm-mobile\\APMWebDataInterface\\APMWebDataInterface.Tests\\APMWebDataInterface.Tests.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\azure.core\\1.36.0\\azure.core.1.36.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.storage.blobs\\12.19.1\\azure.storage.blobs.12.19.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.storage.common\\12.18.1\\azure.storage.common.12.18.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\firebaseadmin\\2.4.1\\firebaseadmin.2.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\firebaseauthentication.net\\3.7.2\\firebaseauthentication.net.3.7.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.api.commonprotos\\2.15.0\\google.api.commonprotos.2.15.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.api.gax\\4.8.0\\google.api.gax.4.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.api.gax.grpc\\4.8.0\\google.api.gax.grpc.4.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.api.gax.rest\\3.2.0\\google.api.gax.rest.3.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.apis\\1.67.0\\google.apis.1.67.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.apis.auth\\1.67.0\\google.apis.auth.1.67.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.apis.core\\1.67.0\\google.apis.core.1.67.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.cloud.firestore\\3.6.0\\google.cloud.firestore.3.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.cloud.firestore.v1\\3.6.0\\google.cloud.firestore.v1.3.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.cloud.location\\2.2.0\\google.cloud.location.2.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.longrunning\\3.2.0\\google.longrunning.3.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.protobuf\\3.25.0\\google.protobuf.3.25.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.auth\\2.60.0\\grpc.auth.2.60.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.core.api\\2.60.0\\grpc.core.api.2.60.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.net.client\\2.60.0\\grpc.net.client.2.60.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.net.common\\2.60.0\\grpc.net.common.2.60.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\6.0.0\\microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codecoverage\\17.2.0\\microsoft.codecoverage.17.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\6.0.0\\microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\6.0.0\\microsoft.extensions.logging.abstractions.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.test.sdk\\17.2.0\\microsoft.net.test.sdk.17.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\5.0.0\\microsoft.netcore.platforms.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.testplatform.objectmodel\\17.2.0\\microsoft.testplatform.objectmodel.17.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.testplatform.testhost\\17.2.0\\microsoft.testplatform.testhost.17.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\netstandard.library\\2.0.0\\netstandard.library.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.1\\newtonsoft.json.13.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nuget.frameworks\\5.11.0\\nuget.frameworks.5.11.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nunit\\3.13.3\\nunit.3.13.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nunit3testadapter\\4.2.1\\nunit3testadapter.4.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system\\4.3.0\\runtime.native.system.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.io.compression\\4.3.0\\runtime.native.system.io.compression.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sixlabors.fonts\\1.0.0-beta15\\sixlabors.fonts.1.0.0-beta15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sixlabors.imagesharp\\2.1.3\\sixlabors.imagesharp.2.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sixlabors.imagesharp.drawing\\1.0.0-beta13\\sixlabors.imagesharp.drawing.1.0.0-beta13.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.5.1\\system.buffers.4.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.codedom\\7.0.0\\system.codedom.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.concurrent\\4.3.0\\system.collections.concurrent.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.immutable\\6.0.0\\system.collections.immutable.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.debug\\4.3.0\\system.diagnostics.debug.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\6.0.1\\system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.tracing\\4.3.0\\system.diagnostics.tracing.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.compression\\4.3.0\\system.io.compression.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem.primitives\\4.3.0\\system.io.filesystem.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.hashing\\6.0.0\\system.io.hashing.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.unmanagedmemorystream\\4.3.0\\system.io.unmanagedmemorystream.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.async\\6.0.1\\system.linq.async.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.management\\7.0.2\\system.management.7.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.4\\system.memory.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory.data\\1.0.2\\system.memory.data.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.metadata\\1.6.0\\system.reflection.metadata.1.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.handles\\4.3.0\\system.runtime.handles.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.interopservices\\4.3.0\\system.runtime.interopservices.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.codepages\\5.0.0\\system.text.encoding.codepages.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\4.7.2\\system.text.encodings.web.4.7.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\4.7.2\\system.text.json.4.7.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading\\4.3.0\\system.threading.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.parallel\\4.3.0\\system.threading.tasks.parallel.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.valuetuple\\4.5.0\\system.valuetuple.4.5.0.nupkg.sha512"], "logs": [{"code": "NU1603", "level": "Warning", "message": "APMWebDataInterface depends on Azure.Storage.Blobs (>= 12.13.0) but Azure.Storage.Blobs 12.13.0 was not found. Azure.Storage.Blobs 12.19.1 was resolved instead.", "projectPath": "C:\\Users\\<USER>\\Desktop\\ImageResizing\\apm-mobile\\APMWebDataInterface\\APMWebDataInterface.Tests\\APMWebDataInterface.Tests.csproj", "warningLevel": 1, "filePath": "C:\\Users\\<USER>\\Desktop\\ImageResizing\\apm-mobile\\APMWebDataInterface\\APMWebDataInterface.Tests\\APMWebDataInterface.Tests.csproj", "libraryId": "Azure.Storage.Blobs", "targetGraphs": ["net5.0"]}, {"code": "NU1603", "level": "Warning", "message": "APMWebDataInterface depends on FirebaseAdmin (>= 2.3.0) but FirebaseAdmin 2.3.0 was not found. FirebaseAdmin 2.4.1 was resolved instead.", "projectPath": "C:\\Users\\<USER>\\Desktop\\ImageResizing\\apm-mobile\\APMWebDataInterface\\APMWebDataInterface.Tests\\APMWebDataInterface.Tests.csproj", "warningLevel": 1, "filePath": "C:\\Users\\<USER>\\Desktop\\ImageResizing\\apm-mobile\\APMWebDataInterface\\APMWebDataInterface.Tests\\APMWebDataInterface.Tests.csproj", "libraryId": "FirebaseAdmin", "targetGraphs": ["net5.0"]}, {"code": "NU1603", "level": "Warning", "message": "APMWebDataInterface depends on Google.Cloud.Firestore (>= 3.0.0) but Google.Cloud.Firestore 3.0.0 was not found. Google.Cloud.Firestore 3.6.0 was resolved instead.", "projectPath": "C:\\Users\\<USER>\\Desktop\\ImageResizing\\apm-mobile\\APMWebDataInterface\\APMWebDataInterface.Tests\\APMWebDataInterface.Tests.csproj", "warningLevel": 1, "filePath": "C:\\Users\\<USER>\\Desktop\\ImageResizing\\apm-mobile\\APMWebDataInterface\\APMWebDataInterface.Tests\\APMWebDataInterface.Tests.csproj", "libraryId": "Google.Cloud.Firestore", "targetGraphs": ["net5.0"]}, {"code": "NU1101", "level": "Error", "message": "Unable to find package ExcelDataReader.DataSet. No packages exist with this id in source(s): C:\\Program Files\\dotnet\\library-packs, Microsoft Visual Studio Offline Packages", "projectPath": "C:\\Users\\<USER>\\Desktop\\ImageResizing\\apm-mobile\\APMWebDataInterface\\APMWebDataInterface.Tests\\APMWebDataInterface.Tests.csproj", "filePath": "C:\\Users\\<USER>\\Desktop\\ImageResizing\\apm-mobile\\APMWebDataInterface\\APMWebDataInterface.Tests\\APMWebDataInterface.Tests.csproj", "libraryId": "ExcelDataReader.DataSet", "targetGraphs": ["net5.0"]}, {"code": "NU1101", "level": "Error", "message": "Unable to find package ExcelDataReader. No packages exist with this id in source(s): C:\\Program Files\\dotnet\\library-packs, Microsoft Visual Studio Offline Packages", "projectPath": "C:\\Users\\<USER>\\Desktop\\ImageResizing\\apm-mobile\\APMWebDataInterface\\APMWebDataInterface.Tests\\APMWebDataInterface.Tests.csproj", "filePath": "C:\\Users\\<USER>\\Desktop\\ImageResizing\\apm-mobile\\APMWebDataInterface\\APMWebDataInterface.Tests\\APMWebDataInterface.Tests.csproj", "libraryId": "ExcelDataReader", "targetGraphs": ["net5.0"]}, {"code": "NU1605", "level": "Error", "message": "Warning As Error: Detected package downgrade: Newtonsoft.Json from 13.0.3 to 13.0.1. Reference the package directly from the project to select a different version. \r\n APMWebDataInterface.Tests -> APMWebDataInterface -> FirebaseAdmin 2.4.1 -> Google.Api.Gax.Rest 3.2.0 -> Google.Api.Gax 4.8.0 -> Newtonsoft.Json (>= 13.0.3) \r\n APMWebDataInterface.Tests -> APMWebDataInterface -> Newtonsoft.Json (>= 13.0.1)", "projectPath": "C:\\Users\\<USER>\\Desktop\\ImageResizing\\apm-mobile\\APMWebDataInterface\\APMWebDataInterface.Tests\\APMWebDataInterface.Tests.csproj", "filePath": "C:\\Users\\<USER>\\Desktop\\ImageResizing\\apm-mobile\\APMWebDataInterface\\APMWebDataInterface.Tests\\APMWebDataInterface.Tests.csproj", "libraryId": "Newtonsoft.Json", "targetGraphs": ["net5.0"]}]}