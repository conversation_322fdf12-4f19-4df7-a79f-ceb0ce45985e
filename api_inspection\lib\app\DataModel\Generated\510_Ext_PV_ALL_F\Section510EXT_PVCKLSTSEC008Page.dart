//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'Section510EXT_PVCKLSTSEC008.dart';

// ignore: camel_case_types
class Section510EXT_PVCKLSTSEC008Page extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final Section510EXT_PVCKLSTSEC008 section510EXT_PVCKLSTSEC008;

  const Section510EXT_PVCKLSTSEC008Page(this.section510EXT_PVCKLSTSEC008,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _Section510EXT_PVCKLSTSEC008PageState();
  }
}

class _Section510EXT_PVCKLSTSEC008PageState
    extends State<Section510EXT_PVCKLSTSEC008Page> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.section510EXT_PVCKLSTSEC008,
        title: "PRESSURE RELIEF (PRD/PRV)",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC008
                      .attribute510EXT_PVCKLSTSEC008Q001
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC008
                      .attribute510EXT_PVCKLSTSEC008Q002
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC008
                      .attribute510EXT_PVCKLSTSEC008Q003
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC008
                      .attribute510EXT_PVCKLSTSEC008Q004
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC008
                      .attributeWas_any_mechanical_damage_noted_on_the_PRD__PRV
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC008
                      .attributeIs_the_Relief_attachment_achieved_via_flanging
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC008
                      .attribute510EXT_PVCKLSTSEC008Q007
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC008
                      .attribute510EXT_PVCKLSTSEC008Q008
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC008
                      .attribute510EXT_PVCKLSTSEC008Q009
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC008
                      .attribute510EXT_PVCKLSTSEC008Q010
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC008
                      .attribute510EXT_PVCKLSTSEC008Q011
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC008
                      .attributeIs_the_Relief_valve_vent_piping_routed_to_a_safe_location
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC008
                      .attribute510EXT_PVCKLSTSEC008Q013
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC008
                      .attribute510EXT_PVCKLSTSEC008Q014
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC008
                      .attributeIs_the_spring_tamper_car_seal_intact
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC008
                      .attribute510EXT_PVCKLSTSEC008Q016
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC008
                      .attributeIs_Relief_alignment_acceptable
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC008
                      .attribute510EXT_PVCKLSTSEC008Q018
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC008
                      .attributeWas_any_excessive_vibration_of_the_Relief_noted
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC008
                      .attributeWas_the_rupture_device_orientation_correct
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC008
                      .attributeAre_associated_block_valves_car_sealed_in_the_open_position
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC008
                      .attribute510EXT_PVCKLSTSEC008Q022
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC008
                      .attribute510EXT_PVCKLSTSEC008Q023
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
