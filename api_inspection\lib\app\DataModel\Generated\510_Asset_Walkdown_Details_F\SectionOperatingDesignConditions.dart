//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'SectionShellSide.dart';
import 'SectionTubeSide.dart';
import 'SectionDimensions.dart';

// ignore: camel_case_types
class SectionOperatingDesignConditions extends DataModelSection {
  @override
  String getDisplayName() => "Operating/Design Conditions";
  SectionOperatingDesignConditions(DataModelItem? parent)
      : super(parent: parent, sectionName: "Operating/Design Conditions");

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeOperating_Temperature = DoubleAttribute(
    parent: this,
    displayName: "Operating Temperature",
    databaseName: "510AW_Q305",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "",
  );

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeOperation_Status =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Operation Status",
          databaseName: "510AW_Q356",
          availableOptions: [
        PredefinedValueOption("In-Service", null, isCommentRequired: false),
        PredefinedValueOption("Out-Of-Service", null, isCommentRequired: false),
        PredefinedValueOption("Standby", null, isCommentRequired: false),
      ]);

// ignore: non_constant_identifier_names
  late SectionShellSide sectionShellSide = SectionShellSide(this);
  // ignore: non_constant_identifier_names
  late SectionTubeSide sectionTubeSide = SectionTubeSide(this);
  // ignore: non_constant_identifier_names
  late SectionDimensions sectionDimensions = SectionDimensions(this);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      sectionShellSide,
      sectionTubeSide,
      sectionDimensions,
      attributeOperating_Temperature,
      attributeOperation_Status,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionOperatingDesignConditions";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
