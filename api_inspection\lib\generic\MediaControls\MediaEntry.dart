import 'dart:developer';

import 'package:api_inspection/generic/AppRoot.dart';
import 'package:camera/camera.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:api_inspection/generic/MediaControls/file_name_package.dart';
import 'package:flutter/foundation.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/DataModelCommon/DatabaseInterfaces/DatabaseHelperManager.dart';
import 'package:api_inspection/generic/MediaControls/MediaSynchronizer.dart';
import 'package:image/image.dart' as img;
import 'package:flutter_cache_manager/flutter_cache_manager.dart';

class MediaEntry extends DataModelCollectionItem {
  String mediaName;
  String extension;

  String get fullFileName {
    return '$mediaName.$extension';
  }

  String get uploadUrl {
    var global = AppRoot.global();
    var container = global.azureContainer;
    var token = global.containerSasToken;
    var url =
        'https://aznascpiadevsa.blob.core.windows.net$container$fullFileName?$token';
    var parsed = Uri.tryParse(url);
    assert(parsed != null && parsed.hasAbsolutePath);
    return url;
  }

  String get downloadUrl {
    var global = AppRoot.global();
    var url =
        'https://OneInsightCDN.azureedge.net${global.azureContainer}$fullFileName?${global.containerSasToken}&basic=5p';
    var parsed = Uri.tryParse(url);
    assert(parsed != null && parsed.hasAbsolutePath);
    return url;
  }

  @override
  String getDisplayName() => "media";

  int? version;

  int? _uploadedVersion;
  int? get uploadedVersion => _uploadedVersion;

  bool _isLoading = false;
  set isLoading(bool value) {
    if (_isLoading != value) {
      _isLoading = value;
      notifyListeners();
    }
  }

  bool get isLoading => _isLoading;

  bool _isUploading = false;
  set isUploading(bool value) {
    if (_isUploading != value) {
      _isUploading = value;
      notifyListeners();
    }
  }

  bool get isUploading => _isUploading;

  bool _isDownloading = false;

  bool get isDownloading => _isDownloading;

  set isDownloading(bool isDownloading) {
    _isDownloading = isDownloading;
    AppRoot.global().photoDownloadProgressEvent.notifyListeners();
  }

  set uploadedVersion(int? version) {
    if (_uploadedVersion != version) {
      _uploadedVersion = version;
      if (_uploadedVersion != resolvedVersion) {
        IMediaSynchronizer.getMediaSynchronizer()
            .downloadFileForMedia(this, forceDownload: true);
      }
    }
  }

  late StringAttribute description =
      StringAttribute(parent: this, displayName: "Description");

  int? resolvedVersion;

  MediaEntry(this.mediaName, this.extension, DataModelItem parent)
      : super(mediaName, parent);

  void rotate(int degrees) async {
    try {
      var file = await DefaultCacheManager().getSingleFile(downloadUrl);
      var localFile = file;
      isLoading = false;

      final img.Image decodedImage =
          img.decodeImage(await localFile.readAsBytes())!;
      final img.Image rotatedImage = img.copyRotate(decodedImage, degrees);
      final encodedImage = img.encodeJpg(rotatedImage);
      final rotatedBytes = Uint8List.fromList(encodedImage);

      version = version! + 1;

      var batch = FirebaseFirestore.instance.batch();

      DatabaseHelperManager.global()
          .updateItem(getDBPath() + ".V", version, batch);

      await IMediaSynchronizer.getMediaSynchronizer()
          .uploadNewDataForEntry(this, rotatedBytes, batch);

      await batch.commit();
    } catch (e) {
      log(e.toString(), name: 'rotate', time: DateTime.now());
    }
  }

  void upload() async {
    var package = FileNamePackage.fromEntry(this);

    var file = IMediaSynchronizer.getMediaSynchronizer()
        .getFileFromCache(package.buildFileName());

    if (file == null) return;
    var imageBytes = await file.readAsBytes();

    version = version! + 1;

    var batch = FirebaseFirestore.instance.batch();

    DatabaseHelperManager.global()
        .updateItem(getDBPath() + ".V", version, batch);

    await IMediaSynchronizer.getMediaSynchronizer()
        .uploadNewDataForEntry(this, imageBytes, batch);

    await batch.commit();
  }

  late final List<VoidCallback> _listeners =
      List<VoidCallback>.empty(growable: true);

  @override
  void addListener(VoidCallback listener) {
    _listeners.add(listener);
  }

  @override
  void removeListener(VoidCallback listener) {
    _listeners.remove(listener);
  }

  @override
  void notifyListeners() {
    for (var element in _listeners) {
      element.call();
    }
  }

  void saveToDatabase() {
    //var database = DatabaseHelperManager.global();
  }

  @override
  String getDBName() {
    return mediaName;
  }

  @override
  void saveDirectItems(WriteBatch batch) async {
    var helper = DatabaseHelperManager.global();

    helper.updateProperties(getDBPath(),
        {"V": version, "U": uploadedVersion, "E": extension}, batch);
  }

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    if (entry.key == "V") {
      version = entry.value as int?;
      return true;
    }

    if (entry.key == "U") {
      _uploadedVersion = entry.value as int?;
      return true;
    }

    if (entry.key == "Description") {
      description.updateFromMap(entry.value as Map);
      return true;
    }

    if (entry.key == "E") {
      extension = entry.value as String;
      return true;
    }

    return false;
  }
}
