﻿using ExcelCodeGenerator;
using ExcelCodeGenerator.CodeGenerators.CSharp;
using ExcelCodeGenerator.CodeGenerators.Dart;
using ExcelCodeGenerator.ExcelParser;

namespace CheckListGen
{
    public class CodeGeneratorMain
    {
        public static void GenerateCodeFromExcelTables(string excelPath, string dartOutputPath, string cSharpOutputPath)
        {
            var forms = ExcelParser.BuildForms(excelPath, Config.TablesToGenerate);
            foreach (var form in forms)
            {
                DartCodeGenerator.GenerateCodeForForm(dartOutputPath, form);
                CSharpCodeGenerator.GenerateCodeForForm(cSharpOutputPath, form);
            }
        }
    }
}