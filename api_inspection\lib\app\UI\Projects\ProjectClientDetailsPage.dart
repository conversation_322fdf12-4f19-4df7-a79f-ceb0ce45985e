import 'package:api_inspection/generic/AppStyle.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/project.dart';
import 'package:api_inspection/app/UI/Clients/ContactsControl.dart';

class ProjectClientDetailsPage extends StatefulWidget {
  final Project project;
  const ProjectClientDetailsPage(this.project, {Key? key}) : super(key: key);

  @override
  _ProjectClientDetailsPageState createState() =>
      _ProjectClientDetailsPageState();
}

class _ProjectClientDetailsPageState extends State<ProjectClientDetailsPage> {
  @override
  void initState() {
    var project = widget.project;
    project.name.addListener(onNameChanged);
    super.initState();
  }

  @override
  void didUpdateWidget(covariant ProjectClientDetailsPage oldWidget) {
    var oldProject = oldWidget.project;
    oldProject.name.removeListener(onNameChanged);

    var project = widget.project;
    project.name.addListener(onNameChanged);
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    var project = widget.project;
    project.name.removeListener(onNameChanged);
    super.dispose();
  }

  void onNameChanged() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    var project = widget.project;
    return Scaffold(
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        appBar: AppBar(
          title: Text(
            "Client Details",
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Container(
                  margin: const EdgeInsets.fromLTRB(20, 20, 20, 40),
                  child: const Text("Chevron",
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.white, fontSize: 26))),
              Container(
                  margin: const EdgeInsets.fromLTRB(20, 0, 10, 0),
                  child: const Text("Contacts",
                      style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                          fontSize: 18))),
              Expanded(child: ContactsControl(project.clientDetails.contacts)),
            ],
          ),
        ));
  }
}
