import 'package:api_inspection/generic/AttributeControls/Temperature/TemperatureAttributeViewEditable.dart';
import 'package:api_inspection/generic/AttributeControls/Temperature/TemperatureAttributeViewNonEditable.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/TemperatureAttribute.dart';
import 'package:flutter/material.dart';

import '../AttributeStructure.dart';

class TemperatureAttributeView extends StatefulWidget {
  final TemperatureAttribute _attribute;
  final IsEditableController? editingController;
  final bool showPhotos;
  final bool showComments;

  const TemperatureAttributeView(this._attribute,
      {Key? key,
      this.editingController,
      this.showPhotos = true,
      this.showComments = true})
      : super(key: key);

  @override
  _TemperatureAttributeViewState createState() =>
      _TemperatureAttributeViewState();
}

class _TemperatureAttributeViewState extends State<TemperatureAttributeView> {
  @override
  Widget build(BuildContext context) {
    return AttributeStructure(widget._attribute,
        showPhotos: widget.showPhotos,
        showComments: widget.showComments,
        editingController: widget.editingController,
        editingBuilder: (context, updateListener) {
      return TemperatureAttributeViewEditable(
          widget._attribute, updateListener);
    }, nonEditingBuilder: (context) {
      return TemperatureAttributeViewNonEditable(widget._attribute);
    });
  }
}
