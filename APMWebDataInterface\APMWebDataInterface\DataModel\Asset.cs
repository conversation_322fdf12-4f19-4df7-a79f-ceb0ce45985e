﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using APMWebDataInterface.Generated.section510_Asset_Walkdown_Details_F;
using APMWebDataInterface.Generated.section570_Asset_Walkdown_Details_F;
using APMWebDataInterface.Generated.section653_Asset_Walkdown_Details_F;
using APMWebDataInterface.Generated.sectionAsset_Walkdown_Access_F;
using APMWebDataInterface.Generated.sectionAsset_Walkdown_PPE_F;
using System.Text;
using System.Threading.Tasks;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.ExampleDataModel
{
  
  public class Asset : ConcretePhotoRoot {
    internal override String GetDBPath() => "assets." + GetDBName();

    public String assetCategory;

    public DataModelItem walkDown;
    
    public SectionAsset_Walkdown_PPE_F assetPPE; 

    public SectionAsset_Walkdown_Access_F assetAccess;
    
    public override string DisplayName => "Asset";

    public String locationId;
    
    public String[] workOrdersContainedIn;

    public StringAttribute area;

    public StringAttribute unit;

    public StringAttribute businessUnitId;

    public Asset(String assetCategory, String locationId) : base(null)
    {
      this.assetCategory = assetCategory;
      this.locationId = locationId;

      Initialize();
    }

    internal Asset(String assetCategory, String id, String locationId) : base(id, null)
    {
      this.assetCategory = assetCategory;
      this.locationId = locationId;
      Initialize();
    }

    private void Initialize()
    {
      this.area = new StringAttribute(parent: this, displayName: "Area");
      this.unit = new StringAttribute(parent: this, displayName: "Unit");

      businessUnitId = new StringAttribute(this, "BusinessUnitId", isQueryable: true);

      assetPPE = new SectionAsset_Walkdown_PPE_F(this);
      assetAccess = new SectionAsset_Walkdown_Access_F(this);

      if (assetCategory == "Vessel"){
        walkDown = new Section510_Asset_Walkdown_Details_F(this);
      }
      else if (assetCategory == "Piping"){
        walkDown = new Section570_Asset_Walkdown_Details_F(this);
      }
      else if (assetCategory == "Tank"){
        walkDown = new Section653_Asset_Walkdown_Details_F(this);
      }
    }

    public String GetIDFromWalkdown()
    {
      if (walkDown is Section510_Asset_Walkdown_Details_F vessel) {
        return vessel.sectionIdentification.attributeNumber_or_ID.GetValue();
      }
      else if (walkDown is Section570_Asset_Walkdown_Details_F piping) {
        return piping.sectionIdentification.attributeNumber_or_Circuit_ID.GetValue();
      }
      else if (walkDown is Section653_Asset_Walkdown_Details_F tank) {
        return tank.sectionIdentification.attributeNumber_or_ID.GetValue();
      }

      return null;
    }
    
    public override DataModelItem[] GetChildren(){
      var parentChildren = base.GetChildren();

      if (walkDown == null)
        return parentChildren;

      return parentChildren.Concat(new DataModelItem[] { 
        walkDown, 
        assetAccess, 
        assetPPE,
        area,
        unit,
        businessUnitId
      }).ToArray();
    }


    internal override String GetDBName() {
      return id;
    }
    
    public override bool UpdateDirectPropertiesFromMapEntry(KeyValuePair<string, object>? entry) {
      if (entry == null)
        return false;

      if (entry.Value.Key == "WorkOrderIds") {
        if (entry.Value.Value is IEnumerable workOrders) {
          var newWorkOrders = new List<string>();
          foreach (var item in workOrders) {
            newWorkOrders.Add(item.ToString());
          }

          workOrdersContainedIn = newWorkOrders.ToArray();
        }

        return true;
      }

      if (entry.Value.Key == "AssetCategory"){
        assetCategory =  entry.Value.Value as String;
        return true;
      }

      if (entry.Value.Key == "LocationId"){
        locationId =  entry.Value.Value as String;
        return true;
      }

      return false;
    }
    
    public override void DoAddOneTimeChangesToDictionary(Dictionary<string, Object> updates)
    {
      updates["WorkOrderIds"] = workOrdersContainedIn;
      updates["AssetCategory"] = assetCategory;
      updates["LocationId"] = locationId;
    }
    
    public override async Task SavePendingChanges(String user)
    {

      APM_WebDataInterface.Global.AuthorizeWriteToRootObject(this.businessUnitId, user);

      await base.SavePendingChanges(user);

      String assetId = null;
      String assetName = null;
      String assetType = null;
      
      if (walkDown is Section510_Asset_Walkdown_Details_F walkdownA) {
        assetId = walkdownA.sectionIdentification.attributeNumber_or_ID.GetValue();
        assetName = walkdownA.sectionIdentification.attributeName.GetValue();
        assetType = walkdownA.sectionIdentification.attributeAsset_Type.GetValue();
      }
      else if (walkDown is Section570_Asset_Walkdown_Details_F walkdownB){
        assetId = walkdownB.sectionIdentification.attributeNumber_or_Circuit_ID.GetValue();
        assetName = walkdownB.sectionIdentification.attributeName.GetValue();
        assetType = walkdownB.sectionIdentification.attributeAsset_Type.GetValue();
      }
      else if (walkDown is Section653_Asset_Walkdown_Details_F walkdownC){
        assetId = walkdownC.sectionIdentification.attributeNumber_or_ID.GetValue();
        assetName = walkdownC.sectionIdentification.attributeName.GetValue();
        assetType = walkdownC.sectionIdentification.attributeAsset_Type.GetValue();
      }

      var str = (assetName?.Replace("|", " ") ?? " ") + "|" + (assetId?.Replace("|", " ") ?? " ") + "|" + this.assetCategory + "|" + (assetType?.Replace("|", " ") ?? " ");
      var data = Encoding.UTF8.GetBytes(str);
      
      int[] intArray = data.Select(a => (int) a).ToArray();

      // cannot use this until find a way to read this in dart
      //int[] intArray = new int[(int)Math.Ceiling(data.Length / 4.0)];
      //Buffer.BlockCopy(data, 0, intArray, 0, data.Length);

      
      await APM_WebDataInterface.Global.DatabaseContextManager.ContextForNonListenerAction(db => db.Collection("locations").Document(locationId).UpdateAsync("AssetCards." + id, intArray));
    }

  }
}
