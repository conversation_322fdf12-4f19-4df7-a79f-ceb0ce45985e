﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.DataModel.Activity
{
 
  public class ProjectActivityItem : DataModelItem {
    public override string DisplayName => "Project Activity Item";

    public String name;

    public ProjectActivityItem(ProjectActivity parentActivity, String name) : base(parentActivity)
    {
      this.name = name;

      duration = new DoubleAttribute(displayName: "Duration", parent: this, allowNegatives: false);
      count = new DoubleAttribute(displayName: "Count", parent: this, allowNegatives: false);
    }

    public DoubleAttribute duration;
    public DoubleAttribute count;

    public override DataModelItem[] GetChildren()
    {
      return base.GetChildren().Concat(new DataModelItem[]{duration, count}).ToArray();
    }

    internal override string GetDBName()
    {
      return name;
    }

  }
}
