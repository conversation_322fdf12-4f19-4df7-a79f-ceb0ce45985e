import 'package:api_inspection/app/UI/Assets/AssetsMainPage.dart';
import 'package:api_inspection/app/UI/InfoPages/ImageViewPage.dart';
import 'package:api_inspection/app/UI/LeakReport/LeakReportHomePage.dart';
import 'package:api_inspection/app/UI/Projects/ProjectMainPage.dart';
import 'package:api_inspection/generic/AppRoot.dart';
import 'package:api_inspection/generic/MediaControls/MediaDownloadIndicator.dart';
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
import 'package:api_inspection/generic/AppStyle.dart';
import 'package:flutter/material.dart';

class InfoPage extends StatefulWidget {
  const InfoPage({Key? key}) : super(key: key);

  @override
  _InfoPageState createState() => _InfoPageState();
}

class _InfoPageState extends State<InfoPage> {
  void onProjectsChanged() {
    setState(() {});
  }

  void showImage(String imageName, String imagePath) {
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => ImageViewPage(imageName, imagePath)));
  }

  void showLeakReport() {
    Navigator.push(context,
        MaterialPageRoute(builder: (context) => const LeakReportHomePage()));
  }

  void showProjects() {
    Navigator.push(context,
        MaterialPageRoute(builder: (context) => const ProjectsMainPage()));
  }

  void showAssets() {
    Navigator.push(context,
        MaterialPageRoute(builder: (context) => const AssetsMainPage()));
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        AppBar(
          title: Text(
            "Other",
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
          actions: [
            const MediaDownloadIndicator(),
            AppRoot.global().buildMenuButtonWidget(context)
          ],
        ),
        Expanded(
            child: Container(
                margin: const EdgeInsets.fromLTRB(0, 20, 0, 0),
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: <Widget>[
                      Container(
                        margin: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                        child: const Text("Pipe Schedule Charts",
                            style:
                                TextStyle(fontSize: 20, color: Colors.white)),
                      ),
                      TeamToggleButton.withText(
                          "Pipe Size 1/8 to 2 inch\r\nO.D. 0.405 to 2.375", () {
                        showImage("Pipe Chart 1/8 to 2 inches",
                            "assets/UpTo2Inches.png");
                      }),
                      TeamToggleButton.withText(
                          "Pipe Size 2 1/2 to 8 inch\r\nO.D. 2.875 to 8.625",
                          () {
                        showImage("Pipe Chart 2 1/2 to 8 inches",
                            "assets/UpTo8Inches.png");
                      }),
                      TeamToggleButton.withText(
                          "Pipe Size 9 to 24 inch\r\nO.D. 9.625 to 24", () {
                        showImage("Pipe Chart 9 to 24 inches",
                            "assets/UpTo24Inches.png");
                      }),
                      TeamToggleButton.withText(
                          "Pipe Size 26 to 48 inch\r\nO.D. 26 to 48", () {
                        showImage("Pipe Chart 26 to 48 inches",
                            "assets/UpTo48Inches.png");
                      }),
                      Container(
                        margin: const EdgeInsets.fromLTRB(10, 20, 10, 0),
                        child: const Text("Leak Reporting",
                            style:
                                TextStyle(fontSize: 20, color: Colors.white)),
                      ),
                      TeamToggleButton.withText(
                          "Leak Reporting", showLeakReport),
                      Container(
                        margin: const EdgeInsets.fromLTRB(10, 20, 10, 0),
                        child: const Text("Projects & Assets",
                            style:
                                TextStyle(fontSize: 20, color: Colors.white)),
                      ),
                      TeamToggleButton.withText("Projects", showProjects),
                      TeamToggleButton.withText("Assets", showAssets),
                    ],
                  ),
                )))
      ],
    );
  }
}
