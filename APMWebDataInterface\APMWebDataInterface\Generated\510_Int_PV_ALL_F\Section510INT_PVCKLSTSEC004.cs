//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Int_PV_ALL_F
{
  public class Section510INT_PVCKLSTSEC004 : DataModelItem {

    public override String DisplayName { 
      get {
        return "SHELL COVER - HEX ONLY";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC004Q001;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC004Q002;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC004Q003;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC004Q004;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC004Q005;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC004Q006;
    public PredefinedValueAttribute attributeIs_the_shell_cover_to_shell_attachment_achieved_via_welding;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC004Q008;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC004Q009;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC004Q010;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC004Q011;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510INT_PVCKLSTSEC004";

    public Section510INT_PVCKLSTSEC004(DataModelItem parent) : base(parent)
    {
            
        attribute510INT_PVCKLSTSEC004Q001 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Impingement", null),
          new PredefinedValueOption("Yes: Pitting", null)
        }, false, this, "Were any corrosion cells, impingement or pitting noted on the shell cover surfaces: (The dimensions and locations of any damage shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC004_Q001"); 
     
        attribute510INT_PVCKLSTSEC004Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Cracking", null),
          new PredefinedValueOption("Yes: Mechanical Damage", null)
        }, false, this, "Was any mechanical damage or cracking noted on the shell cover surfaces: (The dimensions and location of any damage shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC004_Q002"); 
     
        attribute510INT_PVCKLSTSEC004Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Weld Corrosion", null),
          new PredefinedValueOption("Yes: Weld Cracking", null)
        }, false, this, "Were there indications of weld corrosion or cracking in the welds or Heat Affected Zones of the shell cover pressure retaining welds:", databaseName: "510_INT-PV_CKLST_SEC004_Q003"); 
     
        attribute510INT_PVCKLSTSEC004Q004 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Deformation", null),
          new PredefinedValueOption("Yes: Hot Spots", null)
        }, false, this, "Does the shell cover have any deformations or hot spots: (Bulges, Blisters, Dimpling)", databaseName: "510_INT-PV_CKLST_SEC004_Q004"); 
     
        attribute510INT_PVCKLSTSEC004Q005 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are shell cover penetrations and adjacent areas in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC004_Q005"); 
     
        attribute510INT_PVCKLSTSEC004Q006 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was any mechanical damage or impacts from objects noted on the shell cover:", databaseName: "510_INT-PV_CKLST_SEC004_Q006"); 
     
        attributeIs_the_shell_cover_to_shell_attachment_achieved_via_welding = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the shell cover to shell attachment achieved via welding:", databaseName: "510_INT-PV_CKLST_SEC004_Q007"); 
     
        attribute510INT_PVCKLSTSEC004Q008 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the shell cover to shell weld in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC004_Q008"); 
     
        attribute510INT_PVCKLSTSEC004Q009 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the shell cover to shell attachment achieved via flanged connection(s):", databaseName: "510_INT-PV_CKLST_SEC004_Q009"); 
     
        attribute510INT_PVCKLSTSEC004Q010 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the shell cover to shell  flanged connection(s) in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC004_Q010"); 
     
        attribute510INT_PVCKLSTSEC004Q011 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the shell cover of the asset in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC004_Q011"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute510INT_PVCKLSTSEC004Q001,
           attribute510INT_PVCKLSTSEC004Q002,
           attribute510INT_PVCKLSTSEC004Q003,
           attribute510INT_PVCKLSTSEC004Q004,
           attribute510INT_PVCKLSTSEC004Q005,
           attribute510INT_PVCKLSTSEC004Q006,
           attributeIs_the_shell_cover_to_shell_attachment_achieved_via_welding,
           attribute510INT_PVCKLSTSEC004Q008,
           attribute510INT_PVCKLSTSEC004Q009,
           attribute510INT_PVCKLSTSEC004Q010,
           attribute510INT_PVCKLSTSEC004Q011,
        };
    }
  }
}
