//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'Section570_Ext_Pipe_F.dart';
import 'Section570EXTCKLSTSEC001Page.dart';
import 'Section570EXTCKLSTSEC002Page.dart';
import 'Section570EXTCKLSTSEC003Page.dart';
import 'Section570EXTCKLSTSEC004Page.dart';
import 'Section570EXTCKLSTSEC005Page.dart';
import 'Section570EXTCKLSTSEC006Page.dart';
import 'Section570EXTCKLSTSEC007Page.dart';

// ignore: camel_case_types
class Section570_Ext_Pipe_FPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final Section570_Ext_Pipe_F section570_Ext_Pipe_F;

  const Section570_Ext_Pipe_FPage(this.section570_Ext_Pipe_F, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _Section570_Ext_Pipe_FPageState();
  }
}

class _Section570_Ext_Pipe_FPageState extends State<Section570_Ext_Pipe_FPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.section570_Ext_Pipe_F,
        title: "570-Ext-Pipe",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget
                            .section570_Ext_Pipe_F.section570EXTCKLSTSEC001,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section570EXTCKLSTSEC001Page(widget
                                              .section570_Ext_Pipe_F
                                              .section570EXTCKLSTSEC001)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget
                            .section570_Ext_Pipe_F.section570EXTCKLSTSEC002,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section570EXTCKLSTSEC002Page(widget
                                              .section570_Ext_Pipe_F
                                              .section570EXTCKLSTSEC002)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget
                            .section570_Ext_Pipe_F.section570EXTCKLSTSEC003,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section570EXTCKLSTSEC003Page(widget
                                              .section570_Ext_Pipe_F
                                              .section570EXTCKLSTSEC003)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget
                            .section570_Ext_Pipe_F.section570EXTCKLSTSEC004,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section570EXTCKLSTSEC004Page(widget
                                              .section570_Ext_Pipe_F
                                              .section570EXTCKLSTSEC004)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget
                            .section570_Ext_Pipe_F.section570EXTCKLSTSEC005,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section570EXTCKLSTSEC005Page(widget
                                              .section570_Ext_Pipe_F
                                              .section570EXTCKLSTSEC005)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget
                            .section570_Ext_Pipe_F.section570EXTCKLSTSEC006,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section570EXTCKLSTSEC006Page(widget
                                              .section570_Ext_Pipe_F
                                              .section570EXTCKLSTSEC006)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget
                            .section570_Ext_Pipe_F.section570EXTCKLSTSEC007,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          Section570EXTCKLSTSEC007Page(widget
                                              .section570_Ext_Pipe_F
                                              .section570EXTCKLSTSEC007)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                ]),
          );
        });
  }
}
