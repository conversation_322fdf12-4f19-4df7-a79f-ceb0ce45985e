﻿using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;

namespace CommonDataInterface.Attributes
{

  public abstract class AttributeBase : DataModelItem
  {
    public abstract bool IsValueEqualTo(String other);

    public abstract void SetGenericValueTo(String other);
    
    public abstract String AttributeType { get; }
    
    public bool AreCommentsRequired;

    protected String _DisplayName;
    
    public override string DisplayName => _DisplayName;
    public String DatabaseName { get; set; }

    public bool IsQueryable { get; set; }

    internal override String GetDBName() => (DatabaseName ?? DisplayName).Replace("/", "_").Replace(".", "_");
    
    public String PreviewText => GetPreviewText();

    public abstract String GetPreviewText();
    
    public MediaEntry[] Photos {
      get {
        var photoRoot = this.FindParentOfType<ConcretePhotoRoot>();
        if (photoRoot == null)
          return new MediaEntry[0];
        var currentEntries = GetPhotoChangeLog().GetCurrentEntries();
        List<MediaEntry> mediaEntries = new List<MediaEntry>();
        foreach (var entry in currentEntries) {
          var parts = entry.Split('.');
          var mediaEntry = photoRoot.GetPhoto(parts[0], parts[1]);
          if (mediaEntry != null) {
            mediaEntries.Add(mediaEntry);
          }
        }

        return mediaEntries.ToArray();
      }
    }

    public void AddPhoto(PendingMediaEntry entry)
    {
      if (entry.MediaName == null) {
        entry.MediaName = Guid.NewGuid().ToString();
      }

      var photoRoot = FindParentOfType<ConcretePhotoRoot>();
      if (photoRoot == null)
        throw new Exception("Failed to find photo root");

      photoRoot.AddPendingPhoto(entry);
      GetPhotoChangeLog().AddPendingChange(entry.MediaName + "." + entry.Extension, ChangeAction.Added);
      
      NotifyListeners();
    }

    public void RemovePhoto(MediaEntry entry) {
      var photoRoot = this.FindParentOfType<ConcretePhotoRoot>();
      if (photoRoot == null)
        throw new Exception("No photo root control found in stack");

      entry.RemoveReferencePath(GetDBPath());

      this.GetPhotoChangeLog().AddPendingChange(entry.MediaName + "." + entry.Extension, ChangeAction.Removed);

      NotifyListeners();
    }

    public ListChangeLog<String> GetPhotoChangeLog()
    {
      if (_photoChangeLog == null)
        _photoChangeLog = new ListChangeLog<String>("PhotoChangeLog", this, new List<ChangeLogEntry<String>>());
      return _photoChangeLog;
    }

    private ListChangeLog<String> _photoChangeLog;
    public ListChangeLog<String> PhotoChangeLog {
      get {
        var root = FindParentOfType<ConcretePhotoRoot>();
        if (root != null && !root.includeHistoryInJson)
          return null;

        return GetPhotoChangeLog();
      }
    }

    public ChangeLog<String> GetCommentChangeLog()
    {
      if (_commentChangeLog == null) {
        _commentChangeLog = new ChangeLog<String>("CommentChangeLog", this, new List<ChangeLogEntry<String>>());
      }
      return _commentChangeLog;
    }

    private ChangeLog<String> _commentChangeLog;
    public ChangeLog<String> CommentChangeLog {
      get {
        var root = FindParentOfType<ConcretePhotoRoot>();
        if (root != null && !root.includeHistoryInJson)
          return null;

        return GetCommentChangeLog();
      }
    }
    
    public String? Comment => GetComment();

    public String? GetComment() {
      if (this.GetCommentChangeLog().entries.Count == 0)
        return null;
      return this.GetCommentChangeLog().entries.Last().Value;
    }

    public void SetComment(String value){
      if (String.Equals(GetComment(), value, StringComparison.InvariantCultureIgnoreCase))
        return;
      
    
      this.GetCommentChangeLog().PendingChange = new PendingChange<string>(){Value = value};

      NotifyListeners();
    }


    List<Action> _listeners = new List<Action>();

    public void AddListener(Action listener) {
      _listeners.Add(listener);
    }

    public void RemoveListener(Action listener) {
      _listeners.Remove(listener);
    }

    public void NotifyListeners() {
      _listeners.ForEach((a) => a.Invoke());
    }


    public AttributeBase(DataModelItem parent, String displayName, String databaseName, bool areCommentsRequired = false, bool isQueryable = false)
      : base(parent)
    {
      this.DatabaseName = databaseName;
      this._DisplayName = displayName;
      this.AreCommentsRequired = areCommentsRequired;
      this.IsQueryable = isQueryable;
    }



  }
}