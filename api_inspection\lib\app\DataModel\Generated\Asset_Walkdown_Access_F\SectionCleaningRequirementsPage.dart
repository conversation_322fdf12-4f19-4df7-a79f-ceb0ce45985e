//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionCleaningRequirements.dart';

// ignore: camel_case_types
class SectionCleaningRequirementsPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionCleaningRequirements sectionCleaningRequirements;

  const SectionCleaningRequirementsPage(this.sectionCleaningRequirements,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionCleaningRequirementsPageState();
  }
}

class _SectionCleaningRequirementsPageState
    extends State<SectionCleaningRequirementsPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionCleaningRequirements,
        title: "Cleaning Requirements",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionCleaningRequirements
                      .attributeCleaning_recommendations
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionCleaningRequirements
                      .attributeCleaning_service_review
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
