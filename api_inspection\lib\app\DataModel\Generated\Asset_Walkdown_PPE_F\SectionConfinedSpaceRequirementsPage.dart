//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionConfinedSpaceRequirements.dart';

// ignore: camel_case_types
class SectionConfinedSpaceRequirementsPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionConfinedSpaceRequirements sectionConfinedSpaceRequirements;

  const SectionConfinedSpaceRequirementsPage(
      this.sectionConfinedSpaceRequirements,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionConfinedSpaceRequirementsPageState();
  }
}

class _SectionConfinedSpaceRequirementsPageState
    extends State<SectionConfinedSpaceRequirementsPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionConfinedSpaceRequirements,
        title: "Confined Space Requirements",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionConfinedSpaceRequirements.attributePermit_required
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionConfinedSpaceRequirements
                      .attributeHole_watch_needed
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
