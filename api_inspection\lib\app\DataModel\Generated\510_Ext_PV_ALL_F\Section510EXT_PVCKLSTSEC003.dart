//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class Section510EXT_PVCKLSTSEC003 extends DataModelSection {
  @override
  String getDisplayName() => "FOUNDATIONS AND SUPPORTS";
  Section510EXT_PVCKLSTSEC003(DataModelItem? parent)
      : super(parent: parent, sectionName: "FOUNDATIONS AND SUPPORTS");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510EXT_PVCKLSTSEC003Q001 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Are foundations constructed of steel-reinforced concrete or structural steel fireproofed with concrete experiencing deterioration such as spalling, cracking, or settling:",
          databaseName: "510_EXT-PV_CKLST_SEC003_Q001",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Cracking", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Settling", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Spalling", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC003Q002 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are foundations constructed of steel cradles on concrete or steel piers in acceptable condition for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC003_Q002",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC003Q003 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are the crevices formed between horizontal vessel and cradle supports seal welded or sealed with a mastic compound to prevent the ingress of moisture:",
          databaseName: "510_EXT-PV_CKLST_SEC003_Q003",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC003Q004 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is there any detectable corrosion between unsealed crevices formed between vessel shell and cradle supports:",
          databaseName: "510_EXT-PV_CKLST_SEC003_Q004",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC003Q005 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Has the asset experienced excessive heat, mechanical shock, corrosion of reinforcing steel, or the freezing of entrapped moisture thereby causing cracking in or around supports:",
          databaseName: "510_EXT-PV_CKLST_SEC003_Q005",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC003Q006 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Does the contact area between the anchor bolts and concrete or steel exhibit any signs of corrosion:",
          databaseName: "510_EXT-PV_CKLST_SEC003_Q006",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC003Q007 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are there indications of complete or nearly complete deterioration of the anchor bolt(s) below the base plate:",
          databaseName: "510_EXT-PV_CKLST_SEC003_Q007",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attribute510EXT_PVCKLSTSEC003Q001,
      attribute510EXT_PVCKLSTSEC003Q002,
      attribute510EXT_PVCKLSTSEC003Q003,
      attribute510EXT_PVCKLSTSEC003Q004,
      attribute510EXT_PVCKLSTSEC003Q005,
      attribute510EXT_PVCKLSTSEC003Q006,
      attribute510EXT_PVCKLSTSEC003Q007,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section510EXT_PVCKLSTSEC003";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
