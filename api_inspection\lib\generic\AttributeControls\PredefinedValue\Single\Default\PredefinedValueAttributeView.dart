import 'package:api_inspection/generic/AttributeControls/AttributeStructure.dart';
import 'package:api_inspection/generic/AttributeControls/PredefinedValue/Single/Default/PredefinedValueAttributeViewEditable.dart';
import 'package:api_inspection/generic/AttributeControls/String/StringAttributeViewNonEditable.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
import 'package:flutter/material.dart';

class PredefinedValueAttributeView extends StatefulWidget {
  final PredefinedValueAttribute _attribute;
  final IsEditableController? editingController;
  final bool showPhotos;
  final bool showComments;

  const PredefinedValueAttributeView(this._attribute,
      {Key? key,
      this.editingController,
      this.showPhotos = true,
      this.showComments = true})
      : super(key: key);

  @override
  _PredefinedValueAttributeViewState createState() =>
      _PredefinedValueAttributeViewState();
}

class _PredefinedValueAttributeViewState
    extends State<PredefinedValueAttributeView> {
  @override
  Widget build(BuildContext context) {
    return AttributeStructure(widget._attribute,
        showComments: widget.showComments,
        showPhotos: widget.showPhotos,
        editingController: widget.editingController,
        editingBuilder: (context, updateListener) {
      return PredefinedValueAttributeViewEditable(widget._attribute);
    }, nonEditingBuilder: (context) {
      return StringAttributeViewNonEditable(widget._attribute);
    });
  }
}
