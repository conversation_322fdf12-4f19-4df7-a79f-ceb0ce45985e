//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section653_Asset_Walkdown_Details_F
{
  public class SectionIdentification : DataModelItem {

    public override String DisplayName { 
      get {
        return "Identification";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public StringAttribute attributeName;
    public StringAttribute attributeNumber_or_ID;
    public StringAttribute attributeAsset_Type;
    public StringAttribute attributeEquipment_Description;
    public DateAttribute attributeLast_known_inspection_date;
    public PredefinedValueAttribute attributeLocation;
    public LocationAttribute attributeGIS_Location;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionIdentification";

    public SectionIdentification(DataModelItem parent) : base(parent)
    {
            
        attributeName = new StringAttribute(this, displayName: "Name", databaseName: "653AW_Q001"); 
     
        attributeNumber_or_ID = new StringAttribute(this, displayName: "Number or ID", databaseName: "653AW_Q002", isQueryable: true); 
     
        attributeAsset_Type = new StringAttribute(this, displayName: "Asset Type", databaseName: "653AW_Q003"); 
     
        attributeEquipment_Description = new StringAttribute(this, displayName: "Equipment Description", databaseName: "653AW_Q004"); 
     
        attributeLast_known_inspection_date = new DateAttribute(this, displayName: "Last known inspection date", databaseName: "653AW_Q005", areCommentsRequired: false); 
     
        attributeLocation = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("On-Plot (Facility)", null),
          new PredefinedValueOption("Off-Plot (Field)", null)
        }, false, this, "Location", databaseName: "653AW_Q006"); 
     
        attributeGIS_Location = new LocationAttribute(this, displayName: "GIS Location", databaseName: "653AW_Q007"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeName,
           attributeNumber_or_ID,
           attributeAsset_Type,
           attributeEquipment_Description,
           attributeLast_known_inspection_date,
           attributeLocation,
           attributeGIS_Location,
        };
    }
  }
}
