import 'package:api_inspection/app/DataModel/assetCard.dart';
import 'package:api_inspection/app/Queries/new_queries.dart';
import 'package:api_inspection/generic/UIControls/listener.dart';
import 'package:api_inspection/app/Queries/Queries.dart';
import 'package:collection/collection.dart';
import 'package:darq/darq.dart';

class AssetCards {
  ListenerWrapper assetCardListener = ListenerWrapper();
  void dispose() {
    assetCardListener.dispose();
  }

  late Queries _parent;
  late NewQueries _newParent;

  void initialize(Queries parent, NewQueries newParent) {
    _parent = parent;
    _newParent = newParent;

    newParent.assetListener.addListener(onAssetsParamterChanged);
    parent.selectedProjects.selectedProjectsListener
        .addListener(onAssetsParamterChanged);
    parent.locationQueries.locationsChangedListener
        .addListener(onAssetsParamterChanged);
  }

  void onAssetsParamterChanged() {
    updateCards();
  }

  void _setAssetForCard(AssetCard card) {
    card.asset = _newParent.assets
        .firstWhereOrNull((asset) => asset.id == card.assetDBId);
  }

  void updateCards() {
    var selectedLocations = _parent.selectedProjects
        .getSelectedProjects()
        .select((element, index) => element.location);
    for (var location in _parent.locationQueries.locations) {
      if (selectedLocations.contains(location)) {
        for (var card in location.assetCards.collection) {
          _setAssetForCard(card);
        }
      } else {
        for (var card in location.assetCards.collection) {
          card.asset = null;
        }
      }
    }

    assetCardListener.notifyListeners();
  }
}
