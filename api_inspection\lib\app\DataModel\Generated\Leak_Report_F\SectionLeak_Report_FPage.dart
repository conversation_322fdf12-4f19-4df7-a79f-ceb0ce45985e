//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionLeak_Report_F.dart';
import 'SectionWorkDetailPage.dart';
import 'SectionLeakReportPage.dart';
import 'SectionPhotosPage.dart';

// ignore: camel_case_types
class SectionLeak_Report_FPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionLeak_Report_F sectionLeak_Report_F;

  const SectionLeak_Report_FPage(this.sectionLeak_Report_F, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionLeak_Report_FPageState();
  }
}

class _SectionLeak_Report_FPageState extends State<SectionLeak_Report_FPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionLeak_Report_F,
        title: "Leak Report",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionLeak_Report_F.sectionWorkDetail,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          SectionWorkDetailPage(
                                              widget.sectionLeak_Report_F
                                                  .sectionWorkDetail)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionLeak_Report_F.sectionLeakReport,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          SectionLeakReportPage(
                                              widget.sectionLeak_Report_F
                                                  .sectionLeakReport)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionLeak_Report_F.sectionPhotos,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => SectionPhotosPage(
                                          widget.sectionLeak_Report_F
                                              .sectionPhotos)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                ]),
          );
        });
  }
}
