import 'package:api_inspection/auth/azure_auth_service.dart';
import 'package:api_inspection/data/cosmos_db_service.dart';
import 'package:api_inspection/monitoring/azure_monitoring.dart';
import 'package:api_inspection/providers/azure_services_provider.dart';
import 'package:api_inspection/storage/azure_blob_service.dart';
import 'package:flutter/material.dart';
import '../environment.dart';

class MyApp extends StatelessWidget {
  final Environment environment;
  final Widget Function(BuildContext) buildMainWidget;
  
  const MyApp({
    Key? key,
    required this.environment,
    required this.buildMainWidget,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    // Initialize Azure services
    final azureAuthService = AzureAuthService();
    final azureDbService = CosmosDbService();
    final azureStorageService = AzureBlobService();
    final azureMonitoring = AzureMonitoring();
    
    // Wrap the app with the services provider
    return AzureServicesProvider(
      authService: azureAuthService,
      databaseService: azureDbService,
      storageService: azureStorageService,
      monitoringService: azureMonitoring,
      child: MaterialApp(
        title: 'API Inspection',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          primarySwatch: Colors.blue,
          visualDensity: VisualDensity.adaptivePlatformDensity,
        ),
        home: buildMainWidget(context),
      ),
    );
  }
}
