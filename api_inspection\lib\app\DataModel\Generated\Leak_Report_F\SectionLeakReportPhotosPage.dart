//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/UI/CollectionControl.dart';
import 'package:uuid/uuid.dart';
import 'SectionLeakReportPhotos.dart';

// ignore: camel_case_types
// ignore: camel_case_types
class SectionLeakReportPhotosPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final DataModelCollection<SectionLeakReportPhotos> sectionLeakReportPhotos;
  const SectionLeakReportPhotosPage(this.sectionLeakReportPhotos, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionLeakReportPhotosPageState();
  }
}

class _SectionLeakReportPhotosPageState
    extends State<SectionLeakReportPhotosPage> {
  Widget _cardBuilder(
      BuildContext context, int number, SectionLeakReportPhotos item) {
    return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(number.toString(),
              style: const TextStyle(color: Colors.white, fontSize: 18)),
        ]);
  }

  Widget _editPageBuilder(BuildContext context, SectionLeakReportPhotos item) {
    return SectionScaffold(
        section: item,
        title: "Leak Report Photos",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(item.attributeDescription
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item.attributeComment
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item
                      .attributeArea_of_interest_GIS_position
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item
                      .attributeUpstream_tie_in_GIS_location
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item
                      .attributeDownstream_tie_in_GIS_location
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item
                      .attributeUT_High_Measurement
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item
                      .attributeUT_Low_measurement
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }

  SectionLeakReportPhotos _createNewItem() {
    String id = const Uuid().v4();
    var item = SectionLeakReportPhotos(id, widget.sectionLeakReportPhotos);

    return item;
  }

  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionLeakReportPhotos,
        title: "Leak Report Photos",
        childBuilder: (editableController) {
          return Center(
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: <Widget>[
                Expanded(
                    child: CollectionControl<SectionLeakReportPhotos>(
                  cardTitle: "Leak Report Photos",
                  collection: widget.sectionLeakReportPhotos,
                  cardBuilder: _cardBuilder,
                  createNewItem: _createNewItem,
                  editPageBuilder: _editPageBuilder,
                ))
              ]));
        });
  }
}
