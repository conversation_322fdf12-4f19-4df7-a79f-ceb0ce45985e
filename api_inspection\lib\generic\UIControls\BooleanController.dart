import 'package:flutter/material.dart';

class BooleanController {
  bool? _value;

  final List<VoidCallback> _listeners =
      List<VoidCallback>.empty(growable: true);

  void dispose() {
    _listeners.clear();
  }

  BooleanController(bool? initialValue) {
    _value = initialValue;
  }

  void setValue(bool? newValue) {
    if (_value == newValue) {
      return;
    }
    _value = newValue;
    for (var element in _listeners) {
      element.call();
    }
  }

  void setValueToggleIfSame(bool? newValue) {
    if (_value == newValue) {
      _value = null;
    } else {
      _value = newValue;
    }
    for (var element in _listeners) {
      element.call();
    }
  }

  bool? getValue() {
    return _value;
  }

  void addListener(VoidCallback listener) {
    _listeners.add(listener);
  }

  void removeListener(VoidCallback listener) {
    _listeners.remove(listener);
  }
}
