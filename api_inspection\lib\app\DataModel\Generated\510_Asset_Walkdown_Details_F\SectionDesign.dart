//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class SectionDesign extends DataModelSection {
  @override
  String getDisplayName() => "Design";
  SectionDesign(DataModelItem? parent)
      : super(parent: parent, sectionName: "Design");

  // ignore: non_constant_identifier_names
  late StringAttribute attributeDesign_Code = StringAttribute(
      parent: this,
      displayName: "Design Code",
      databaseName: "510AW_Q111",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeCode_Year = StringAttribute(
      parent: this,
      displayName: "Code Year",
      databaseName: "510AW_Q112",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeAddendum = StringAttribute(
      parent: this,
      displayName: "Addendum",
      databaseName: "510AW_Q113",
      areCommentsRequired: false);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeDesign_Code,
      attributeCode_Year,
      attributeAddendum,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionDesign";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
