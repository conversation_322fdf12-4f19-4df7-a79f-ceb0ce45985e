//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'SectionPhotos.dart';
import 'SectionDesign.dart';
import 'SectionInspection.dart';
import 'SectionManufacturerFabricator.dart';
import 'SectionService.dart';

// ignore: camel_case_types
class SectionGeneralInformation extends DataModelSection {
  @override
  String getDisplayName() => "General Information";
  SectionGeneralInformation(DataModelItem? parent)
      : super(parent: parent, sectionName: "General Information");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributePipe_Class = PredefinedValueAttribute(
      parent: this,
      hasOtherOption: true,
      displayName: "Pipe Class",
      databaseName: "570AW_Q125",
      availableOptions: [
        PredefinedValueOption("Class 1", null, isCommentRequired: false),
        PredefinedValueOption("Class 2", null, isCommentRequired: false),
        PredefinedValueOption("Class 3", null, isCommentRequired: false),
        PredefinedValueOption("Class 4", null, isCommentRequired: false),
        PredefinedValueOption("N/A", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late DateAttribute attributeInstallation_Date = DateAttribute(
      parent: this,
      displayName: "Installation Date",
      databaseName: "570AW_Q143",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late DateAttribute attributeIn_service_Date = DateAttribute(
      parent: this,
      displayName: "In-service Date",
      databaseName: "570AW_Q144",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributePID_No_ = StringAttribute(
      parent: this,
      displayName: "P&ID No.",
      databaseName: "570AW_Q145",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeConstructionDesign_Drawing_Number =
      StringAttribute(
          parent: this,
          displayName: "Construction/Design Drawing Number",
          databaseName: "570AW_Q146",
          areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeLowest_Flange_Rating =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Lowest Flange Rating",
          databaseName: "570AW_Q147",
          availableOptions: [
        PredefinedValueOption("150", null, isCommentRequired: false),
        PredefinedValueOption("300", null, isCommentRequired: false),
        PredefinedValueOption("400", null, isCommentRequired: false),
        PredefinedValueOption("600", null, isCommentRequired: false),
        PredefinedValueOption("900", null, isCommentRequired: false),
        PredefinedValueOption("1500", null, isCommentRequired: false),
        PredefinedValueOption("2500", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeConstruction_Method =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: true,
          displayName: "Construction Method",
          databaseName: "570AW_Q148",
          availableOptions: [
        PredefinedValueOption("Welded", null, isCommentRequired: false),
        PredefinedValueOption("Threaded", null, isCommentRequired: false),
        PredefinedValueOption("Slip Joint (Bell & Spigot)", null,
            isCommentRequired: false),
        PredefinedValueOption("Flanged-Bolted", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late StringAttribute attribute570AWQ149 = StringAttribute(
      parent: this,
      displayName:
          "Does this line have threaded connections? (Injection, Drains, Vents, O-lets)",
      databaseName: "570AW_Q149",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributePipe_Size =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Pipe Size",
          databaseName: "570AW_Q150",
          unit: "in",
          availableOptions: [
        PredefinedValueOption("0.5", null, isCommentRequired: false),
        PredefinedValueOption("0.75", null, isCommentRequired: false),
        PredefinedValueOption("1", null, isCommentRequired: false),
        PredefinedValueOption("1.25", null, isCommentRequired: false),
        PredefinedValueOption("1.5", null, isCommentRequired: false),
        PredefinedValueOption("2", null, isCommentRequired: false),
        PredefinedValueOption("2.5", null, isCommentRequired: false),
        PredefinedValueOption("3", null, isCommentRequired: false),
        PredefinedValueOption("3.5", null, isCommentRequired: false),
        PredefinedValueOption("4", null, isCommentRequired: false),
        PredefinedValueOption("4.5", null, isCommentRequired: false),
        PredefinedValueOption("5", null, isCommentRequired: false),
        PredefinedValueOption("6", null, isCommentRequired: false),
        PredefinedValueOption("8", null, isCommentRequired: false),
        PredefinedValueOption("10", null, isCommentRequired: false),
        PredefinedValueOption("12", null, isCommentRequired: false),
        PredefinedValueOption("14", null, isCommentRequired: false),
        PredefinedValueOption("16", null, isCommentRequired: false),
        PredefinedValueOption("18", null, isCommentRequired: false),
        PredefinedValueOption("20", null, isCommentRequired: false),
        PredefinedValueOption("24", null, isCommentRequired: false),
        PredefinedValueOption("30", null, isCommentRequired: false),
        PredefinedValueOption("36", null, isCommentRequired: false),
        PredefinedValueOption("42", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributePipe_Schedule =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Pipe Schedule",
          databaseName: "570AW_Q151",
          availableOptions: [
        PredefinedValueOption("5", null, isCommentRequired: false),
        PredefinedValueOption("10", null, isCommentRequired: false),
        PredefinedValueOption("20", null, isCommentRequired: false),
        PredefinedValueOption("30", null, isCommentRequired: false),
        PredefinedValueOption("40", null, isCommentRequired: false),
        PredefinedValueOption("50", null, isCommentRequired: false),
        PredefinedValueOption("60", null, isCommentRequired: false),
        PredefinedValueOption("70", null, isCommentRequired: false),
        PredefinedValueOption("80", null, isCommentRequired: false),
        PredefinedValueOption("100", null, isCommentRequired: false),
        PredefinedValueOption("120", null, isCommentRequired: false),
        PredefinedValueOption("140", null, isCommentRequired: false),
        PredefinedValueOption("160", null, isCommentRequired: false),
        PredefinedValueOption("STD", null, isCommentRequired: false),
        PredefinedValueOption("EH", null, isCommentRequired: false),
        PredefinedValueOption("DBL.EH", null, isCommentRequired: false),
      ]);

// ignore: non_constant_identifier_names
  late SectionPhotos sectionPhotos = SectionPhotos(this);
  // ignore: non_constant_identifier_names
  late SectionDesign sectionDesign = SectionDesign(this);
  // ignore: non_constant_identifier_names
  late SectionInspection sectionInspection = SectionInspection(this);
  // ignore: non_constant_identifier_names
  late SectionManufacturerFabricator sectionManufacturerFabricator =
      SectionManufacturerFabricator(this);
  // ignore: non_constant_identifier_names
  late SectionService sectionService = SectionService(this);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      sectionPhotos,
      sectionDesign,
      sectionInspection,
      sectionManufacturerFabricator,
      sectionService,
      attributePipe_Class,
      attributeInstallation_Date,
      attributeIn_service_Date,
      attributePID_No_,
      attributeConstructionDesign_Drawing_Number,
      attributeLowest_Flange_Rating,
      attributeConstruction_Method,
      attribute570AWQ149,
      attributePipe_Size,
      attributePipe_Schedule,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionGeneralInformation";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
