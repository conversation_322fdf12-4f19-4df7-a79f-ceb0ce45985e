﻿<wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
	
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=02075e5a_002Dfbfc_002D4ccf_002Db8ed_002D9d909b891d70/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" Name="addBusinessUnitToUser #2" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;TestAncestor&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.addBusinessUnitToUser&lt;/TestId&gt;&#xD;
  &lt;/TestAncestor&gt;&#xD;
&lt;/SessionState&gt;</s:String>
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=1a098da8_002D7b31_002D4c16_002Dab14_002Dfd03074665a4/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" Name="buildReportJson #3" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;TestAncestor&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.addBusinessUnitToUser&lt;/TestId&gt;&#xD;
  &lt;/TestAncestor&gt;&#xD;
&lt;/SessionState&gt;</s:String>
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=1a8184ad_002D929d_002D40a4_002Da990_002D9b1d6e953bd0/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" Name="fullTestExample4" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;TestAncestor&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::.NETCoreApp,Version=v5.0::APMWebDataInterface.Tests.WebInterfaceTests.fullTestExample4&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::.NETCoreApp,Version=v5.0::APMWebDataInterface.Tests.WebInterfaceTests.fullTestExample254&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::.NETCoreApp,Version=v5.0::APMWebDataInterface.Tests.WebInterfaceTests.fullTestExample2545&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::.NETCoreApp,Version=v5.0::APMWebDataInterface.Tests.WebInterfaceTests.unAssignToMe&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::.NETCoreApp,Version=v5.0::APMWebDataInterface.Tests.WebInterfaceTests.assignToMe&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::.NETCoreApp,Version=v5.0::APMWebDataInterface.Tests.WebInterfaceTests.createProjectTest&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::.NETCoreApp,Version=v5.0::APMWebDataInterface.Tests.WebInterfaceTests.buildReportJson&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::.NETCoreApp,Version=v5.0::APMWebDataInterface.Tests.DataImporter.RunImporter&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::.NETCoreApp,Version=v5.0::APMWebDataInterface.Tests.WebInterfaceTests.testingCompleteTask_DataCopiedOver&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::.NETCoreApp,Version=v5.0::APMWebDataInterface.Tests.WebInterfaceTests.fullTestExample2&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::.NETCoreApp,Version=v5.0::APMWebDataInterface.Tests.WebInterfaceTests.findInfoFromAPMNumber&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::.NETCoreApp,Version=v5.0::APMWebDataInterface.Tests.WebInterfaceTests.updateProject&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::.NETCoreApp,Version=v5.0::APMWebDataInterface.Tests.WebInterfaceTests.updateProject1&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::.NETCoreApp,Version=v5.0::APMWebDataInterface.Tests.WebInterfaceTests.updateProjectMany&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::.NETCoreApp,Version=v5.0::APMWebDataInterface.Tests.WebInterfaceTests.testLeakReportJson&lt;/TestId&gt;&#xD;
  &lt;/TestAncestor&gt;&#xD;
&lt;/SessionState&gt;</s:String>
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=1c1a3739_002Dee13_002D4d61_002Da001_002De9a90048e52a/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" Name="buildReportJson #4" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;TestAncestor&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::.NETCoreApp,Version=v5.0::APMWebDataInterface.Tests.WebInterfaceTests.buildReportJson&lt;/TestId&gt;&#xD;
  &lt;/TestAncestor&gt;&#xD;
&lt;/SessionState&gt;</s:String>
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=29cde906_002De051_002D4b08_002D925f_002D1d46184f42af/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" Name="createProjectTest" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;TestAncestor&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::.NETCoreApp,Version=v5.0::APMWebDataInterface.Tests.WebInterfaceTests.createProjectTest&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::.NETCoreApp,Version=v5.0::APMWebDataInterface.Tests.WebInterfaceTests.testingSetSomething&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::.NETCoreApp,Version=v5.0::APMWebDataInterface.Tests.WebInterfaceTests.testingAddTask&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::.NETCoreApp,Version=v5.0::APMWebDataInterface.Tests.WebInterfaceTests.testCreateProject&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::.NETCoreApp,Version=v5.0::APMWebDataInterface.Tests.WebInterfaceTests.testingAddAsset&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::.NETCoreApp,Version=v5.0::APMWebDataInterface.Tests.WebInterfaceTests.testCreateAsset&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::.NETCoreApp,Version=v5.0::APMWebDataInterface.Tests.WebInterfaceTests.testFindAsset&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::.NETCoreApp,Version=v5.0::APMWebDataInterface.Tests.WebInterfaceTests.fullTestExample4&lt;/TestId&gt;&#xD;
  &lt;/TestAncestor&gt;&#xD;
&lt;/SessionState&gt;</s:String>
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=57d6abc4_002D0732_002D4f63_002D91a2_002D1fc80738cf47/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" Name="buildReportJson #2" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;TestAncestor&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::.NETCoreApp,Version=v5.0::APMWebDataInterface.Tests.WebInterfaceTests.buildReportJson&lt;/TestId&gt;&#xD;
  &lt;/TestAncestor&gt;&#xD;
&lt;/SessionState&gt;</s:String>
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=9c1a075f_002D1ee1_002D40c8_002Dbffa_002Df4305116c623/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" Name="TestGetUsers" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;TestAncestor&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.TestGetUsers&lt;/TestId&gt;&#xD;
  &lt;/TestAncestor&gt;&#xD;
&lt;/SessionState&gt;</s:String>
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=9f0711e4_002D9123_002D46be_002Db675_002D085e214ea09d/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" Name="getAllWorkOrders" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;TestAncestor&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.getAllWorkOrders&lt;/TestId&gt;&#xD;
  &lt;/TestAncestor&gt;&#xD;
&lt;/SessionState&gt;</s:String>
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=c0b464b8_002De8e2_002D4d5f_002Daaba_002D1f1804b8f834/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" Name="buildReportJson" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;TestAncestor&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::.NETCoreApp,Version=v5.0::APMWebDataInterface.Tests.WebInterfaceTests.buildReportJson&lt;/TestId&gt;&#xD;
  &lt;/TestAncestor&gt;&#xD;
&lt;/SessionState&gt;</s:String>
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=ce097ca9_002D4dc4_002D4834_002D9ab0_002D69d7f6ea3729/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" Name="addBusinessUnitToUser" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;TestAncestor&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.addBusinessUnitToUser&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.getAllProjects&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.testCreateBusinessUnit&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.getAllClients&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.getAllBusinessUnits&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.getBusinessUnitsByClientId&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.updateNonVerifiedUserTest&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.testLeakReportJson&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.getLeakReports&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.buildReportJson&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.addBusinessUnitToRootItems&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.getAllLocations&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.getAllWorkOrders&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.createWorkOrder&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.modifyWorkOrdersOnDifferentBusinessUnit&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.modifyWorkOrderToRestrictedBusinessUnit&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.modifyWorkOrdersFromRestrictedBusinessUnit&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.modifyWorkOrdersWithRestrictedBusinessUnit&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.getAssetssByProjects&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.saveAssetsToUpdateAssetCards&lt;/TestId&gt;&#xD;
  &lt;/TestAncestor&gt;&#xD;
&lt;/SessionState&gt;</s:String>
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=ddb736e7_002Db738_002D4d8b_002D864d_002D737be4484ade/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" Name="saveAssetsToUpdateAssetCards" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;TestAncestor&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.getAllProjects&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.TestGetUsers&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.TestGetStuff&lt;/TestId&gt;&#xD;
  &lt;/TestAncestor&gt;&#xD;
&lt;/SessionState&gt;</s:String>
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=f8e81470_002De0b0_002D4c26_002D8339_002Df794d43b5e2c/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" Name="testQuery" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;TestAncestor&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::.NETCoreApp,Version=v5.0::APMWebDataInterface.Tests.WebInterfaceTests&lt;/TestId&gt;&#xD;
  &lt;/TestAncestor&gt;&#xD;
&lt;/SessionState&gt;</s:String>
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=fbc5eb0e_002Dcf12_002D4d11_002D8dcf_002Dc78abc4a7e42/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" IsActive="True" Name="TestGetUsers #2" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;TestAncestor&gt;&#xD;
    &lt;TestId&gt;NUnit3x::38D3ECF1-78B9-40DB-B9DC-17D4D00C7CB9::net5.0::APMWebDataInterface.Tests.WebInterfaceTests.SyncWorkOrderAssignments&lt;/TestId&gt;&#xD;
  &lt;/TestAncestor&gt;&#xD;
&lt;/SessionState&gt;</s:String>
	<s:Boolean x:Key="/Default/UserDictionary/Words/=Firestore/@EntryIndexedValue">True</s:Boolean></wpf:ResourceDictionary>