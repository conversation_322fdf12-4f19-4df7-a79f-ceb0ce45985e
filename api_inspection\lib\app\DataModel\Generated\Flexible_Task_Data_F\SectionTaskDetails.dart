//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class SectionTaskDetails extends DataModelSection {
  @override
  String getDisplayName() => "Task Details";
  SectionTaskDetails(DataModelItem? parent)
      : super(parent: parent, sectionName: "Task Details");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeMethod = PredefinedValueAttribute(
      parent: this,
      hasOtherOption: false,
      displayName: "Method",
      databaseName: "GT_Q005",
      availableOptions: [
        PredefinedValueOption("PT Inspection", null, isCommentRequired: false),
        PredefinedValueOption("MT Inspection", null, isCommentRequired: false),
        PredefinedValueOption("UT Inspection", null, isCommentRequired: false),
        PredefinedValueOption("RT Inspection", null, isCommentRequired: false),
        PredefinedValueOption("EMT Inspection", null, isCommentRequired: false),
        PredefinedValueOption("LKT Inspection", null, isCommentRequired: false),
        PredefinedValueOption("PMI Testing", null, isCommentRequired: false),
        PredefinedValueOption("Hardness Testing", null,
            isCommentRequired: false),
        PredefinedValueOption("LTM Testing", null, isCommentRequired: false),
        PredefinedValueOption("VT Inspection", null, isCommentRequired: false),
        PredefinedValueOption("API 653 External", null,
            isCommentRequired: false),
        PredefinedValueOption("API 653 Internal", null,
            isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeSub_Method = PredefinedValueAttribute(
      parent: this,
      hasOtherOption: false,
      displayName: "Sub-Method",
      databaseName: "GT_Q006",
      availableOptions: []);

  // ignore: non_constant_identifier_names
  late DateAttribute attributePlanned_Start_Date = DateAttribute(
      parent: this,
      displayName: "Planned Start Date",
      databaseName: "GT_Q007",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late DateAttribute attributePlanned_End_Date = DateAttribute(
      parent: this,
      displayName: "Planned End Date",
      databaseName: "GT_Q008",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late DateAttribute attributeActual_Start_Date = DateAttribute(
      parent: this,
      displayName: "Actual Start Date",
      databaseName: "GT_Q009",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late DateAttribute attributeWork_Completion_Date = DateAttribute(
      parent: this,
      displayName: "Work Completion Date",
      databaseName: "GT_Q010",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late DateAttribute attributeDue_Date = DateAttribute(
      parent: this,
      displayName: "Due Date",
      databaseName: "GT_Q011",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeTask_Assignees =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Task Assignees",
          databaseName: "GT_Q012",
          availableOptions: []);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeLead_Technician =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Lead Technician",
          databaseName: "GT_Q013",
          availableOptions: []);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeSupervisor = PredefinedValueAttribute(
      parent: this,
      hasOtherOption: false,
      displayName: "Supervisor",
      databaseName: "GT_Q014",
      availableOptions: []);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeClient_Work_Order_Number = StringAttribute(
      parent: this,
      displayName: "Client Work Order Number",
      databaseName: "GT_Q015",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeClient_Cost_Code = StringAttribute(
      parent: this,
      displayName: "Client Cost Code",
      databaseName: "GT_Q016",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributePurchase_Order_AFE = StringAttribute(
      parent: this,
      displayName: "Purchase Order AFE",
      databaseName: "GT_Q017",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeClient_Work_Order_Description = StringAttribute(
      parent: this,
      displayName: "Client Work Order Description",
      databaseName: "GT_Q018",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeWork_Summary = StringAttribute(
      parent: this,
      displayName: "Work Summary",
      databaseName: "GT_Q019",
      areCommentsRequired: false);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeMethod,
      attributeSub_Method,
      attributePlanned_Start_Date,
      attributePlanned_End_Date,
      attributeActual_Start_Date,
      attributeWork_Completion_Date,
      attributeDue_Date,
      attributeTask_Assignees,
      attributeLead_Technician,
      attributeSupervisor,
      attributeClient_Work_Order_Number,
      attributeClient_Cost_Code,
      attributePurchase_Order_AFE,
      attributeClient_Work_Order_Description,
      attributeWork_Summary,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionTaskDetails";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
