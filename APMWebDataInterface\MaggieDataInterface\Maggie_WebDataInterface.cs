﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using CommonDataInterface;
using Firebase.Auth;
using Google.Cloud.Firestore;
using Google.Cloud.Firestore.V1;
using Google.Protobuf.Reflection;
using Grpc.Core;
using MaggieDataInterface.DataModel;

namespace MaggieDataInterface
{
  public class DatabaseContextManager : IDatabaseContextManager {
    
    public enum Databases
    {
      testing,
      production
    };

    public Databases Database { get; private set; }

    private class ManagedDatabaseContext
    {
      private FirestoreDb _firestoreDB;

      public int NumberOfListeners = 0;

      public FirestoreDb FirestoreDB {
        get {
          if (_firestoreDB == null) {
            throw new Exception("Must call APM_WebDataInterface.Global.Initialize() before calling methods inside nuget package");
          }

          return _firestoreDB;
        }
      }
      
      private static async Task<FirestoreDb> CreateFirestoreDbWithEmailAuthentication(string emailAddress, 
        string password, string firebaseApiKey, string firebaseProjectId)
      {
        // Create a custom authentication mechanism for Email/Password authentication
        // If the authentication is successful, we will get back the current authentication token and the refresh token
        // The authentication expires every hour, so we need to use the obtained refresh token to obtain a new authentication token as the previous one expires
        var authProvider = new FirebaseAuthProvider(new FirebaseConfig(firebaseApiKey));
        var auth = await authProvider.SignInWithEmailAndPasswordAsync(emailAddress, password);
        var callCredentials = CallCredentials.FromInterceptor(async (context, metadata) =>
        {
          if (auth.IsExpired()) auth = await auth.GetFreshAuthAsync();
          if (string.IsNullOrEmpty(auth.FirebaseToken)) return;

          metadata.Clear();
          metadata.Add("authorization", $"Bearer {auth.FirebaseToken}");
        });
        var credentials = ChannelCredentials.Create(new SslCredentials(), callCredentials);
                
        var builder = new FirestoreClientBuilder
        {
            ChannelCredentials = credentials
        };
        var client = await builder.BuildAsync();
                
        return await FirestoreDb.CreateAsync(firebaseProjectId, client);
      }

       
      public async Task Initialize(Databases database = Databases.testing)
      {
        if (_firestoreDB == null) {
          if (database == Databases.testing) {
            _firestoreDB = await CreateFirestoreDbWithEmailAuthentication("<EMAIL>", "testDB24686543", "AIzaSyCy7hM82CoycZD_DGo62vLCbmktsBUsAjE", "asset-performance-management");
          }
          else if (database == Databases.production) {
            _firestoreDB = await CreateFirestoreDbWithEmailAuthentication("<EMAIL>", "LFu/PQG+2$:-NCtk", "AIzaSyCFa-e0RUpUjF4mqSPVxNiMY_UZ0yfTZa8", "apm-prod-da61a");
          }
        }
      }
    }


    private List<ManagedDatabaseContext> _databaseContexts = new List<ManagedDatabaseContext>();

    public async Task ContextForNewListener(Action<FirestoreDb> action)
    {
      ManagedDatabaseContext context;
      if (_databaseContexts.All(a => a.NumberOfListeners > 75)) {
        context = new ManagedDatabaseContext();
        await context.Initialize(Database);
        _databaseContexts.Add(context);
      }
      else {
        context = _databaseContexts.FirstOrDefault(a => a.NumberOfListeners <= 75);
      }
      

      context.NumberOfListeners++;
      action(context.FirestoreDB);
    }
    
    public async Task<T> ContextForNonListenerAction<T>(Func<FirestoreDb, Task<T>> action)
    {
      ManagedDatabaseContext context = _databaseContexts.FirstOrDefault();

      return await action(context.FirestoreDB);
    }

    
    public async Task<T> ContextForNonListenerAction<T>(Func<FirestoreDb, T> action)
    {
      ManagedDatabaseContext context = _databaseContexts.FirstOrDefault();

      return action(context.FirestoreDB);
    }

    public DatabaseContextManager(Databases database = Databases.testing)
    {
      Database = database;
    }


    public async Task Initialize()
    {
      if (_databaseContexts.Count == 0) {
        var initialContext = new ManagedDatabaseContext();
        await initialContext.Initialize(Database);
        _databaseContexts.Add(initialContext);
      }
    }
  }


  public class Maggie_WebDataInterface
  {
    public Maggie_WebDataInterface() { }
    private static Maggie_WebDataInterface _global;

    public static Maggie_WebDataInterface Global {
      get {
        if (_global == null) {
          _global = new Maggie_WebDataInterface();
        }

        return _global;
      }
    }

    
    private DatabaseContextManager _firestoreDBContexts;

    public DatabaseContextManager DatabaseContextManager {
      get {
        if (_firestoreDBContexts == null) {
          throw new Exception("Must call APM_WebDataInterface.Global.Initialize() before calling methods inside nuget package");
        }

        return _firestoreDBContexts;
      }
    }

    
    private bool _isInitialized = false;
    public async Task Initialize(DatabaseContextManager.Databases database = DatabaseContextManager.Databases.testing)
    {
      if (_isInitialized)
        return;
      _isInitialized = true;
      
      _firestoreDBContexts = new DatabaseContextManager(database);
      await _firestoreDBContexts.Initialize();
      DataManager.ContextManager = _firestoreDBContexts;
    }

    
    public async Task<Form> GetForm(String id)
    {
      if (!_isInitialized)
        throw new Exception("Must call Initialize before calling methods inside nuget package");
      var db = DatabaseContextManager;

      var formsQuery = await db.ContextForNonListenerAction(a => a.Collection("forms").WhereEqualTo(FieldPath.DocumentId, id));
      var formsSnapshot = await formsQuery.GetSnapshotAsync();

      if (!formsSnapshot.Any()) {
        return null;
      }

      var formSnapshot = formsSnapshot.FirstOrDefault();

      var forms = new List<Form>();

      var form = new Form(formSnapshot.Id);
      form.UpdateFromMap(formSnapshot.ToDictionary());
      forms.Add(form);

      return form;
    }
    
    public async Task<Form[]> GetForms()
    {
      if (!_isInitialized)
        throw new Exception("Must call Initialize before calling methods inside nuget package");
      var db = DatabaseContextManager;

      var formsQuery = await db.ContextForNonListenerAction(a => a.Collection("forms"));
      var formsSnapshot = await formsQuery.GetSnapshotAsync();
      
      var forms = new List<Form>();

      foreach (var item in formsSnapshot) {
        var form = new Form(item.Id);
        form.UpdateFromMap(item.ToDictionary());
        forms.Add(form);
      }

      return forms.ToArray();
    }
  }
}
