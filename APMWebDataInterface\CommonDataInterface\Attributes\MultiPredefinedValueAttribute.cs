﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace CommonDataInterface.Attributes
{
  public class MultiPredefinedValueAttribute : MultiStringAttribute {
    public override String AttributeType => "MultiPredefinedValue";

    public bool HasOtherOption;
    List<PredefinedValueOption> _availableOptions;

    public List<PredefinedValueOption> Options
    {
      get => _availableOptions;
    }
    
    public List<String> SelectedOptions {
      get {
        return CurrentValue?.Select(a => _availableOptions.Any(b => b.Value.Equals(a, StringComparison.InvariantCultureIgnoreCase)) ? a : "Other").ToList();
      }
    }

    public String Unit { get; set; }


    public MultiPredefinedValueAttribute(List<PredefinedValueOption> availableOptions, bool hasOtherOption, DataModelItem parent, String name, String unit = null, String databaseName = null, bool areCommentsRequired = false)
      : base(parent, name, databaseName, areCommentsRequired)
    {
      Unit = unit;
      _availableOptions = availableOptions;
      HasOtherOption = hasOtherOption;
    }

  }
}