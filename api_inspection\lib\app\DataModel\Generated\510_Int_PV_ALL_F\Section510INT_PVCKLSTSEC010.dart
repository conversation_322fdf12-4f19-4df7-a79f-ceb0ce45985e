//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class Section510INT_PVCKLSTSEC010 extends DataModelSection {
  @override
  String getDisplayName() => "INTERNAL PACKING";
  Section510INT_PVCKLSTSEC010(DataModelItem? parent)
      : super(parent: parent, sectionName: "INTERNAL PACKING");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC010Q001 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Does the vessel operate with internal packing: (Note: The type of packing shall be recorded below as random, structured, grid-style etc.)",
          databaseName: "510_INT-PV_CKLST_SEC010_Q001",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeWas_packing_removed_prior_to_inspection =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Was packing removed prior to inspection:",
          databaseName: "510_INT-PV_CKLST_SEC010_Q002",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeWas_packing_in_acceptable_condition_for_continued_service =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Was packing in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC010_Q003",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC010Q004 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are collector / chimney trays / redistributors in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC010_Q004",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC010Q005 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are draw sumps and draw nozzles in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC010_Q005",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC010Q006 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are accessible support ring attachment welds in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC010_Q006",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC010Q007 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are accessible packing support grid plates and hold-down grids in acceptable condition for continued service: (I.e. intact, positioned correctly, and appropriately clamped to the support ring)",
          databaseName: "510_INT-PV_CKLST_SEC010_Q007",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC010Q008 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are bed packing limiters and attachment hardware in acceptable condition for continued service: (The distance from the packing to the bed limiter should be recorded)",
          databaseName: "510_INT-PV_CKLST_SEC010_Q008",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeAre_there_indications_of_packing_collapse_or_break_up =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Are there indications of packing collapse or break up:",
          databaseName: "510_INT-PV_CKLST_SEC010_Q009",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC010Q010 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is demister pad retention grid & attachment hardware in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC010_Q010",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC010Q011 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Was fouling observed of the demister pad: (Please note the degree of any fouling observed)",
          databaseName: "510_INT-PV_CKLST_SEC010_Q011",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC010Q012 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Was demister pad bypass observed: (If yes, thickness readings or other NDE should be conducted at the impingement areas impacted by the demister pad bypass)",
          databaseName: "510_INT-PV_CKLST_SEC010_Q012",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC010Q013 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are vortex breakers in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC010_Q013",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC010Q014 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are packing components in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC010_Q014",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attribute510INT_PVCKLSTSEC010Q001,
      attributeWas_packing_removed_prior_to_inspection,
      attributeWas_packing_in_acceptable_condition_for_continued_service,
      attribute510INT_PVCKLSTSEC010Q004,
      attribute510INT_PVCKLSTSEC010Q005,
      attribute510INT_PVCKLSTSEC010Q006,
      attribute510INT_PVCKLSTSEC010Q007,
      attribute510INT_PVCKLSTSEC010Q008,
      attributeAre_there_indications_of_packing_collapse_or_break_up,
      attribute510INT_PVCKLSTSEC010Q010,
      attribute510INT_PVCKLSTSEC010Q011,
      attribute510INT_PVCKLSTSEC010Q012,
      attribute510INT_PVCKLSTSEC010Q013,
      attribute510INT_PVCKLSTSEC010Q014,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section510INT_PVCKLSTSEC010";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
