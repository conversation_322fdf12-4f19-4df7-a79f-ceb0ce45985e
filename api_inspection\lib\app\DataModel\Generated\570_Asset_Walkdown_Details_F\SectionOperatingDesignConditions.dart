//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class SectionOperatingDesignConditions extends DataModelSection {
  @override
  String getDisplayName() => "Operating/Design Conditions";
  SectionOperatingDesignConditions(DataModelItem? parent)
      : super(parent: parent, sectionName: "Operating/Design Conditions");

  // ignore: non_constant_identifier_names
  late IntegerAttribute attributeOperating_Temperature = IntegerAttribute(
    parent: this,
    displayName: "Operating Temperature",
    databaseName: "570AW_Q205",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "F",
  );

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeDesign_MAWP = DoubleAttribute(
    parent: this,
    displayName: "Design MAWP",
    databaseName: "570AW_Q206",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "psi",
  );

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeDesign_Temperature = DoubleAttribute(
    parent: this,
    displayName: "Design Temperature",
    databaseName: "570AW_Q207",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "F",
  );

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeOperating_Pressure = DoubleAttribute(
    parent: this,
    displayName: "Operating Pressure",
    databaseName: "570AW_Q208",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "psi",
  );

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributePRV_Set_Pressure = DoubleAttribute(
    parent: this,
    displayName: "PRV Set Pressure",
    databaseName: "570AW_Q209",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "psi",
  );

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeOperation_Status =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Operation Status",
          databaseName: "570AW_Q210",
          availableOptions: [
        PredefinedValueOption("In-Service", null, isCommentRequired: false),
        PredefinedValueOption("Out-Of-Service", null, isCommentRequired: false),
        PredefinedValueOption("Standby", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeOperating_Temperature,
      attributeDesign_MAWP,
      attributeDesign_Temperature,
      attributeOperating_Pressure,
      attributePRV_Set_Pressure,
      attributeOperation_Status,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionOperatingDesignConditions";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
