//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section653_Asset_Walkdown_Details_F
{
  public class SectionService : DataModelItem {

    public override String DisplayName { 
      get {
        return "Service";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public StringAttribute attributeServiceProductContents;
    public DoubleAttribute attributeSpecific_Gravity;
    public StringAttribute attributeIntended_Service;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionService";

    public SectionService(DataModelItem parent) : base(parent)
    {
            
        attributeServiceProductContents = new StringAttribute(this, displayName: "Service/Product/Contents", databaseName: "653AW_Q116"); 
     
        attributeSpecific_Gravity = new DoubleAttribute(this, displayName: "Specific Gravity", databaseName: "653AW_Q117", areCommentsRequired: false, displayUnit: null, allowNegatives: true); 
     
        attributeIntended_Service = new StringAttribute(this, displayName: "Intended Service", databaseName: "653AW_Q118"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeServiceProductContents,
           attributeSpecific_Gravity,
           attributeIntended_Service,
        };
    }
  }
}
