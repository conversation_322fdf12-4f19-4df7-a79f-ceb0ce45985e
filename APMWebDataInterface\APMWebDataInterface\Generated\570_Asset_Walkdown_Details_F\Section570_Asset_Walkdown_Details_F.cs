//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section570_Asset_Walkdown_Details_F
{
  public class Section570_Asset_Walkdown_Details_F : DataModelItem {

    public override String DisplayName { 
      get {
        return "570 Asset Walkdown-Details-F";
      }
    }

    #region [-- Sub Sections --]

    #region [-- SectionIdentification --]
    private SectionIdentification _sectionIdentification;
    public SectionIdentification sectionIdentification {
        get {
            if (_sectionIdentification == null) {
               _sectionIdentification = new SectionIdentification(this);
            }

            return _sectionIdentification;
        }
    }
    #endregion [-- SectionIdentification --]
    
    #region [-- SectionGeneralInformation --]
    private SectionGeneralInformation _sectionGeneralInformation;
    public SectionGeneralInformation sectionGeneralInformation {
        get {
            if (_sectionGeneralInformation == null) {
               _sectionGeneralInformation = new SectionGeneralInformation(this);
            }

            return _sectionGeneralInformation;
        }
    }
    #endregion [-- SectionGeneralInformation --]
    
    #region [-- SectionOperatingDesignConditions --]
    private SectionOperatingDesignConditions _sectionOperatingDesignConditions;
    public SectionOperatingDesignConditions sectionOperatingDesignConditions {
        get {
            if (_sectionOperatingDesignConditions == null) {
               _sectionOperatingDesignConditions = new SectionOperatingDesignConditions(this);
            }

            return _sectionOperatingDesignConditions;
        }
    }
    #endregion [-- SectionOperatingDesignConditions --]
    
    #region [-- SectionComponents --]
    private SectionComponents _sectionComponents;
    public SectionComponents sectionComponents {
        get {
            if (_sectionComponents == null) {
               _sectionComponents = new SectionComponents(this);
            }

            return _sectionComponents;
        }
    }
    #endregion [-- SectionComponents --]
    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section570_Asset_Walkdown_Details_F";

    public Section570_Asset_Walkdown_Details_F(DataModelItem parent) : base(parent)
    {
            
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           sectionIdentification,
           sectionGeneralInformation,
           sectionOperatingDesignConditions,
           sectionComponents,
        };
    }
  }
}
