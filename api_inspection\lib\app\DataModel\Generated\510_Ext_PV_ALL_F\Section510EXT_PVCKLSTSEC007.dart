//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class Section510EXT_PVCKLSTSEC007 extends DataModelSection {
  @override
  String getDisplayName() => "GUY WIRES";
  Section510EXT_PVCKLSTSEC007(DataModelItem? parent)
      : super(parent: parent, sectionName: "GUY WIRES");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeAre_guy_wires_utilized_to_support_the_asset =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Are guy wires utilized to support the asset:",
          databaseName: "510_EXT-PV_CKLST_SEC007_Q001",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510EXT_PVCKLSTSEC007Q002 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Is there any evidence of corrosion, distortion, and or cracking of the attachment welds or adjacent shell areas of the anchor point to asset connection:",
          databaseName: "510_EXT-PV_CKLST_SEC007_Q002",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Cracking", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Corrosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Distortion", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC007Q003 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are the guy wire connections to the asset and to each ground anchor point sufficiently tightened and tensioned correctly:",
          databaseName: "510_EXT-PV_CKLST_SEC007_Q003",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC007Q004 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are the termination points of the guy wires in acceptable condition for continued service: (I.e. concrete dead-man anchors)",
          databaseName: "510_EXT-PV_CKLST_SEC007_Q004",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC007Q005 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are wire rope clips in acceptable condition for continued service:  (I.e. no corrosion and correct clip orientation)",
          databaseName: "510_EXT-PV_CKLST_SEC007_Q005",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC007Q006 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the wire rope clip spacing correct:  (I.e. spaced at least six rope diameters apart)",
          databaseName: "510_EXT-PV_CKLST_SEC007_Q006",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC007Q007 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the number of wire rope clips appropriate based on the diameter of the wire rope:",
          databaseName: "510_EXT-PV_CKLST_SEC007_Q007",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC007Q008 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are the guy wires in acceptable condition for continued service:  (i.e. no corrosion or broken strands noted )",
          databaseName: "510_EXT-PV_CKLST_SEC007_Q008",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeAre_guy_wires_utilized_to_support_the_asset,
      attribute510EXT_PVCKLSTSEC007Q002,
      attribute510EXT_PVCKLSTSEC007Q003,
      attribute510EXT_PVCKLSTSEC007Q004,
      attribute510EXT_PVCKLSTSEC007Q005,
      attribute510EXT_PVCKLSTSEC007Q006,
      attribute510EXT_PVCKLSTSEC007Q007,
      attribute510EXT_PVCKLSTSEC007Q008,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section510EXT_PVCKLSTSEC007";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
