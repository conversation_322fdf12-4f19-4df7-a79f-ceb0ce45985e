import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
import 'package:url_launcher/url_launcher.dart';

class PhoneNumberAttributeViewNonEditable extends StatefulWidget {
  final PhoneNumberAttribute _attribute;

  const PhoneNumberAttributeViewNonEditable(this._attribute, {Key? key})
      : super(key: key);

  @override
  _PhoneNumberAttributeViewNonEditableState createState() =>
      _PhoneNumberAttributeViewNonEditableState();
}

class _PhoneNumberAttributeViewNonEditableState
    extends State<PhoneNumberAttributeViewNonEditable> {
  void onAttributeChanged() {
    setState(() {});
  }

  @override
  void dispose() {
    StringAttribute attribute = widget._attribute;
    attribute.removeListener(onAttributeChanged);
    super.dispose();
  }

  @override
  void initState() {
    StringAttribute attribute = widget._attribute;
    attribute.addListener(onAttributeChanged);

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var currentValue = widget._attribute.getValue();
    if (currentValue == null || currentValue.isEmpty) {
      return Container();
    }

    return Row(mainAxisAlignment: MainAxisAlignment.start, children: [
      Container(
          margin: const EdgeInsets.fromLTRB(40, 10, 20, 10),
          child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                widget._attribute.getValue() ?? "",
                style: const TextStyle(color: Colors.white, fontSize: 22),
              ))),
      IconButton(
          splashRadius: 25,
          iconSize: 40,
          onPressed: () {
            launchUrl(Uri(scheme: 'tel', path: currentValue));
          },
          icon: const Icon(
            Icons.phone,
            color: Colors.green,
          ))
    ]);
  }
}
