﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CommonDataInterface.Attributes;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Drawing.Processing;
using SixLabors.ImageSharp.Formats.Jpeg;
using SixLabors.ImageSharp.Metadata.Profiles.Exif;
using SixLabors.ImageSharp.Processing;


namespace CommonDataInterface
{
  public class PendingMediaEntry
  {
    public String MediaName { get; set; }
    public String Extension { get; set; }

    public byte[] ImageData { get; set; }
    public String Description { get; set; }

  }

  public class MediaEntry : DataModelCollectionItem {
    public MediaEntry Duplicate()
    {
      return new MediaEntry(this.MediaName, this.Extension, this.Parent) {
        _description = this.Description,
        UploadedVersion = this.UploadedVersion,
        Version = this.Version,
        ActiveReferencePaths = this.ActiveReferencePaths,
        PendingActiveReferenceAdditions = this.PendingActiveReferenceAdditions,
        PendingActiveReferenceRemovals = this.PendingActiveReferenceRemovals,

      };
    }
    
    public async Task ResolveWithCompress(int quality, bool pad = false)
    {
      await Resolve(true, 0, 0, quality, pad);

    }

    public async Task ResolveWithResize(int width, int height, int quality, bool pad = false)
    {
      await Resolve(true, width, height, quality, pad);

    }

    public async Task Resolve()
    {
      await Resolve(false, 0, 0, 0, false);
    }

    private int getRotationFromMetaData(Image img)
    {
      int rotation = 0;
      try {
        var orientation = img?.Metadata?.ExifProfile?.GetValue(ExifTag.Orientation);
        if (orientation != null) {
          var orientationStr = orientation.ToString();
          switch (orientationStr) {
            case "Rotate 90 CW":
              rotation = 90;
              break;
            case "Rotate 270 CW":
              rotation = 270;
              break;
            case "Rotate 180":
              rotation = 180;
              break;
          }
        }
      }
      catch (Exception ex) {

      }

      return rotation;
    }

    private byte[] resizeImageWithPadding(byte[] fullImageData, int width, int height, int quality)
    {
      byte[] data;
      using (var stream = new MemoryStream(fullImageData)) {
        var img = Image.Load(stream, out var format);

        int rotation = getRotationFromMetaData(img);

        var encoder = new JpegEncoder(){Quality = quality};
        var options = new ResizeOptions() {Mode = ResizeMode.Pad, Size = new Size(width, height)};
        using (var img2 = img.Clone(ctx => ctx.Resize(options).Rotate(rotation))) {

          using (var outputStream = new MemoryStream()) {
            try {
              img2.Metadata?.ExifProfile?.RemoveValue(ExifTag.Orientation);
            }
            catch {

            }

            img2.Save(outputStream, encoder);
            data = outputStream.ToArray();
          }
        }
      }

      return data;
    }

    private byte[] resizeImageWithoutPadding(byte[] fullImageData, int width, int height, int quality)
    {
      byte[] data;
      using (var stream = new MemoryStream(fullImageData)) {
        var img = Image.Load(stream, out var format);
        int rotation = getRotationFromMetaData(img);

        int desiredWidth = width;
        int desiredHeight = 0;
        if (img.Height > img.Width) {
          desiredHeight = height;
          desiredWidth = 0;
        }
        var encoder = new JpegEncoder(){Quality = quality};

        using (var img2 = img.Clone(ctx => ctx.Resize(new Size(desiredWidth, desiredHeight)).Rotate(rotation))) {

          using (var outputStream = new MemoryStream()) {
            try {
              img2.Metadata?.ExifProfile?.RemoveValue(ExifTag.Orientation);
            }
            catch {

            }
            img2.Save(outputStream, encoder);
            data = outputStream.ToArray();
          }
        }
      }

      return data;
    }

    private async Task Resolve(bool resize, int width, int height, int quality, bool pad)
    {
      if (ImageData != null)
        return;
      var cache = MediaCache.Global(width, height, quality, pad);

      var data = cache.FindEntry(MediaName, Extension, Version ?? 1);
      if (data != null) {
        ImageData = data;
        return;
      }

      byte[] fullData;
      try {
        var fullCache = MediaCache.Global(0, 0, 0, false);
        fullData = fullCache.FindEntry(MediaName, Extension, Version ?? 1);
        if (fullData == null) { 

          var blobClient = BlobStorageHelper.BlobContainerClient.GetBlobClient(MediaName + "." + Extension);
          var response = await blobClient.DownloadAsync();

          if (response?.Value != null) {
            fullData = new byte[response.Value.ContentLength];

            using (var stream = response.Value.Content) {
              int position = 0;
              while (position < fullData.Length) {
                position += stream.Read(fullData, position, fullData.Length - position);
              }
            }

            fullCache.AddEntry(MediaName, Extension, Version ?? 1, fullData);
          }
        }

      }
      catch (Exception ex) {
        return;
      }
      
      if (fullData == null) {
        return;
      }

      if (resize && (width != 0 || height != 0)) {
        if (pad) {
          data = resizeImageWithPadding(fullData, width, height, quality);
        }
        else {
          data = resizeImageWithoutPadding(fullData, width, height, quality);
        }
      }
      else if (resize && quality != 0) {
        using (var stream = new MemoryStream(fullData)) {
          var img = Image.Load(stream, out var format);

          var encoder = new JpegEncoder(){Quality = quality};
          using (var outputStream = new MemoryStream()) {
            try {
              img.Metadata?.ExifProfile?.RemoveValue(ExifTag.Orientation);
            }catch{}

            img.Save(outputStream, encoder);
            data = outputStream.ToArray();
          }
        }
      }
      cache.AddEntry(MediaName, Extension, Version ?? 1,data);

      ImageData = data;
    }

    public override string DisplayName => "Media Entry";

    public String MediaName { get; set; }
    public String Extension { get; set; }

    public String BlobName => MediaName + "." + Extension;

    public int? Version { get; set; }

    public int? UploadedVersion { get; set; }

    private StringAttribute _description;

    public StringAttribute Description {
      get {
        if (_description == null)
          _description = new StringAttribute(this, "Description");
        return _description;
      }
    }
    
    public byte[] ImageData { get; set; }
    
    private List<String> ActiveReferencePaths = new List<string>();
    List<String> PendingActiveReferenceAdditions = new List<string>();
    List<String> PendingActiveReferenceRemovals = new List<string>();

    internal void RemoveReferencePath(String path){
      PendingActiveReferenceRemovals.Add(path);
    }
    
    public override void DoAddOneTimeChangesToDictionary(Dictionary<string, Object> updates)
    {
      var path = GetDBPath().Split('.').Skip(2).AggregateEXT((a, b) => a + "." + b);
      updates[path + ".V"] = Version;
      updates[path + ".U"] = UploadedVersion;
      updates[path + ".E"] = Extension;
    }


    protected override async Task DoAddPendingChangesToDictionary(Dictionary<string, Object> updates, String user)
    {
      if (!GetHasDatabaseChangesPending()) {
        return;
      }


      var referenceMap = new List<string>();
      foreach (var path1 in ActiveReferencePaths.Except(PendingActiveReferenceRemovals).Concat(PendingActiveReferenceAdditions)){
        referenceMap.Add(path1.Replace(".", "|"));
      }


      var currentPath = GetDBPath().Split('.');
      var remainingPath = currentPath.Skip(2);
      
      var path = remainingPath.AggregateEXT((a, b) => a + '.' + b) + ".A";
      updates[path] = referenceMap;
    }


    public override bool GetHasDatabaseChangesPending()
    {
      return PendingActiveReferenceAdditions.Count > 0 || PendingActiveReferenceRemovals.Count > 0 || base.GetHasDatabaseChangesPending();
    }

    public MediaEntry(String mediaName, String extension, DataModelItem parent) : base(mediaName, parent)
    {
      MediaName = mediaName;
      Extension = extension;
    }

    public MediaEntry(String id, DataModelItem parent) : base(id, parent) { }

    internal override string GetDBName() => MediaName;

    public override DataModelItem[] GetChildren()
    {
      return new DataModelItem[] {
        Description
      };
    }

    public override bool UpdateDirectPropertiesFromMapEntry(KeyValuePair<string, object>? entry) {
      if (entry == null)
        return false;

      if (entry.Value.Key == "V"){
        try {
          Version = int.Parse(entry.Value.Value.ToString());
        }
        catch {
          Version = null;
        }


        return true;
      }

      if (entry.Value.Key == "U"){
        try {
          UploadedVersion = entry.Value.Value == null ? null : (int?) int.Parse(entry.Value.Value.ToString());
        }
        catch {
          UploadedVersion = null;
        }
        return true;
      }

      if (entry.Value.Key == "Description"){
        Description.UpdateFromMap(entry.Value.Value as Dictionary<string, object>); 
        return true;
      }
   
   
      if (entry.Value.Key == "E"){
        Extension = entry.Value.Value as String;
        return true;
      }

      if (entry.Value.Key == "A"){
        var collection = entry.Value.Value as IEnumerable;
        List<String> newReferencePaths = new List<string>();
        if (collection != null) {
          foreach (var item in collection) {
            if (item != null) {
              newReferencePaths.Add(item.ToString().Replace("|", "."));
            }
          }
        }

        ActiveReferencePaths = newReferencePaths;
      }
      return false;
    }
  }
}