import 'package:api_inspection/generic/AppStyle.dart';
import 'package:api_inspection/generic/MediaControls/photo_widgets_for_collection.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/MediaControls/MediaEntry.dart';
import 'package:api_inspection/generic/UIControls/SwipeGestureDetector.dart';

class PhotoReviewPageNonEditable extends StatefulWidget {
  final MediaEntry _media;
  late final List<MediaEntry> _otherMedia;

  PhotoReviewPageNonEditable(this._media, List<MediaEntry> otherMedia,
      {Key? key})
      : super(key: key) {
    _otherMedia = otherMedia.toList();
  }

  @override
  _PhotoReviewPageStateNonEditable createState() =>
      _PhotoReviewPageStateNonEditable();
}

class _PhotoReviewPageStateNonEditable
    extends State<PhotoReviewPageNonEditable> {
  late MediaEntry media;

  @override
  void initState() {
    media = widget._media;
    media.addListener(onMediaUpdated);
    super.initState();
  }

  @override
  void dispose() {
    media.removeListener(onMediaUpdated);
    super.dispose();
  }

  void onMediaUpdated() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    Widget imgControl = Expanded(child: MediaEntryImage(mediaEntry: media));

    var uploadImgControls = Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Visibility(
            maintainSize: true,
            maintainAnimation: true,
            maintainState: true,
            visible: media.isUploading,
            child: Container(
                alignment: Alignment.center,
                child: const Text(
                  "Uploading...",
                  textAlign: TextAlign.start,
                  style: TextStyle(color: Colors.white, fontSize: 22),
                ))),
      ],
    );

    var descriptionLabel = Container(
        margin: const EdgeInsets.fromLTRB(25, 5, 25, 0),
        alignment: Alignment.centerLeft,
        child: const Text(
          "Description",
          textAlign: TextAlign.start,
          style: TextStyle(color: Colors.white, fontSize: 22),
        ));

    var descriptionTextField = Container(
        margin: const EdgeInsets.fromLTRB(25, 5, 25, 25),
        child: Text(media.description.getValue() ?? "",
            style: const TextStyle(color: Colors.white)));

    if (media.isLoading) {
      return Scaffold(
          backgroundColor: const Color.fromARGB(255, 24, 28, 32),
          appBar: AppBar(
            title: Text(
              'Photo Review',
              style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
            ),
            toolbarHeight: AppStyle.global.toolBarHeight,
          ),
          body: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [descriptionLabel, descriptionTextField]));
    } else {
      return Scaffold(
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        appBar: AppBar(
          title: Text(
            'Photo Review',
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        body: SwipeGestureRecognizer(
          onSwipeRight: () {
            var others = widget._otherMedia;
            var index = others.indexOf(media);
            if (index > 0) {
              setState(() {
                media.removeListener(onMediaUpdated);
                media = others[index - 1];
                media.addListener(onMediaUpdated);
              });
            }
          },
          onSwipeLeft: () {
            var others = widget._otherMedia;
            var index = others.indexOf(media);
            if (index < others.length - 1) {
              setState(() {
                media.removeListener(onMediaUpdated);
                media = others[index + 1];
                media.addListener(onMediaUpdated);
              });
            }
          },
          child: Column(children: [
            imgControl,
            uploadImgControls,
            descriptionLabel,
            descriptionTextField
          ]),
        ),
      );
    }
  }
}
