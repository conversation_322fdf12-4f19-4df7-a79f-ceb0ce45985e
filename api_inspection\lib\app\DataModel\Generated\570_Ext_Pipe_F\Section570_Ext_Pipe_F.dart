//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'Section570EXTCKLSTSEC001.dart';
import 'Section570EXTCKLSTSEC002.dart';
import 'Section570EXTCKLSTSEC003.dart';
import 'Section570EXTCKLSTSEC004.dart';
import 'Section570EXTCKLSTSEC005.dart';
import 'Section570EXTCKLSTSEC006.dart';
import 'Section570EXTCKLSTSEC007.dart';

// ignore: camel_case_types
class Section570_Ext_Pipe_F extends DataModelSection {
  @override
  String getDisplayName() => "570-Ext-Pipe";
  Section570_Ext_Pipe_F(DataModelItem? parent)
      : super(parent: parent, sectionName: "570-Ext-Pipe");

// ignore: non_constant_identifier_names
  late Section570EXTCKLSTSEC001 section570EXTCKLSTSEC001 =
      Section570EXTCKLSTSEC001(this);
  // ignore: non_constant_identifier_names
  late Section570EXTCKLSTSEC002 section570EXTCKLSTSEC002 =
      Section570EXTCKLSTSEC002(this);
  // ignore: non_constant_identifier_names
  late Section570EXTCKLSTSEC003 section570EXTCKLSTSEC003 =
      Section570EXTCKLSTSEC003(this);
  // ignore: non_constant_identifier_names
  late Section570EXTCKLSTSEC004 section570EXTCKLSTSEC004 =
      Section570EXTCKLSTSEC004(this);
  // ignore: non_constant_identifier_names
  late Section570EXTCKLSTSEC005 section570EXTCKLSTSEC005 =
      Section570EXTCKLSTSEC005(this);
  // ignore: non_constant_identifier_names
  late Section570EXTCKLSTSEC006 section570EXTCKLSTSEC006 =
      Section570EXTCKLSTSEC006(this);
  // ignore: non_constant_identifier_names
  late Section570EXTCKLSTSEC007 section570EXTCKLSTSEC007 =
      Section570EXTCKLSTSEC007(this);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      section570EXTCKLSTSEC001,
      section570EXTCKLSTSEC002,
      section570EXTCKLSTSEC003,
      section570EXTCKLSTSEC004,
      section570EXTCKLSTSEC005,
      section570EXTCKLSTSEC006,
      section570EXTCKLSTSEC007,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section570_Ext_Pipe_F";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
