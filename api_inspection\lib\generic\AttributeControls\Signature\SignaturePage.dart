// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:signature/signature.dart';
// import 'package:api_inspection/app/DataModelCommon/Attributes/StringAttribute.dart';


// class SignaturePage extends StatefulWidget {
//     // ignore: unused_field
//     final StringAttribute _attribute;

//   const SignaturePage(this._attribute);

//   @override
//   _SignaturePageState createState() => _SignaturePageState();
// }

// class _SignaturePageState extends State<SignaturePage> {

//   late SignatureController _controller=SignatureController(
//     penColor: Colors.black,
//     penStrokeWidth: 5,
//      onDrawStart:()=> print('Sign started'),
//     onDrawEnd: ()=>print('Draw Ended'));
//   bool initialized = false;
  
//   void initialize(){
//     if (initialized)
//       return;
//     initialized= true;

//   }

//    @override
//   void initState() {
//     super.initState();  
//     SystemChrome.setPreferredOrientations([
//       DeviceOrientation.landscapeRight,
//       DeviceOrientation.landscapeLeft,
//   ]);
//   }
//    @override
//   void dispose() {
//     _controller.dispose();
//     SystemChrome.setPreferredOrientations([
//     DeviceOrientation.portraitUp,
//     DeviceOrientation.portraitDown,
//   ]);
//     super.dispose();
//   }
//   @override
//   Widget build(BuildContext context) {
//         initialize();
//     return Scaffold(
//        body: Column(
//          children: <Widget>[
//            Spacer(),
//            buildTop(context),
//            Signature(
//              controller: _controller,
//              backgroundColor: Colors.white,
//              height: 200,
//              ),
//              buildButtons(context),
//          ],
//        ),
      
//     );
//   }
//   Widget buildButtons(BuildContext context)=>Container(
//     color: Colors.black,
//     child: Row(
//       mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//       children: <Widget>[buildCancel(),buildCheck(context)],
//     ),

//   );
//   Widget buildCheck(BuildContext context)=>IconButton(onPressed: (){}, icon: Icon(Icons.check,color:Colors.green));
//   Widget buildClear()=>IconButton(onPressed: (){_controller.clear();}, icon: Icon(Icons.restart_alt,color: Colors.blue));
//   Widget buildCancel()=>IconButton(onPressed: (){cancelClicked(context);}, icon: Icon(Icons.cancel_presentation_sharp,color: Colors.red),);

//   Widget buildTop(BuildContext context)=>Container(
//     color: Colors.black,
//     child: Row(
//       mainAxisAlignment: MainAxisAlignment.end,
//       children: <Widget>[buildClear()],
//     ),

//   );

//   void cancelClicked(BuildContext context) {
//     _controller.clear();
//     Navigator.pop(context);
//   }
// }