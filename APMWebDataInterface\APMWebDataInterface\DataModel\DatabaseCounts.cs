﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using APMWebDataInterface.ExampleDataModel;

namespace APMWebDataInterface.DataModel
{
  public class DatabaseCounts
  {
    private static DatabaseCounts _global;
    public static DatabaseCounts Global {
      get {
        if (_global == null) {
          _global = new DatabaseCounts();
        }

        return _global;
      }
    }

    private DatabaseCounts()
    {

    }

    public int CurrentMaxWorkOrderId = 0;
    public int CurrentMaxProjectId = 0;
    public int CurrentMaxTaskId = 0;
    public int CurrentMaxLeakReportId = 0;


    public void UpdateFromMap(Dictionary<string, object> map)
    {
      foreach (var item in map)
      {
        switch (item.Key) {
          case "currentMaxWorkOrderId":
            CurrentMaxWorkOrderId = int.Parse(item.Value.ToString());
            break;
          case "currentMaxProjectId":
            CurrentMaxProjectId = int.Parse(item.Value.ToString());
            break;
          case "currentMaxTaskId":
            CurrentMaxTaskId = int.Parse(item.Value.ToString());
            break;
          case "currentMaxLeakReportId":
            CurrentMaxLeakReportId = int.Parse(item.Value.ToString());
            break;
        }
      }
    }

    public async Task Save()
    {
      await APM_WebDataInterface.Global.DatabaseContextManager.ContextForNonListenerAction(db => db.Collection("utility").Document("Counts").SetAsync(new Dictionary<string,object> {
        {"currentMaxWorkOrderId",CurrentMaxWorkOrderId},
        {"currentMaxProjectId",CurrentMaxProjectId},
        {"currentMaxTaskId",CurrentMaxTaskId},
        {"currentMaxLeakReportId", CurrentMaxLeakReportId},
      }));
    }
  }
}
