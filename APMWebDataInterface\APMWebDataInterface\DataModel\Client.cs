﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.DataModel
{
    public class Client : ConcretePhotoRoot
    {
        public override string DisplayName => "Client";

        public StringAttribute Name { get; set; }

        public Client() : base(null)
        {
            InitializeAtributes();
        }
        

        internal Client(string id) : base(id, null)
        {
            InitializeAtributes();
        }

        private void InitializeAtributes()
        {
            Name = new StringAttribute(this, "Name");
        }

        internal override string GetDBName()
        {
            return id;
        }

        internal override string GetDBPath()
        {
            return "clients." + id;
        }

        public override DataModelItem[] GetChildren()
        {
            var parentChildren = base.GetChildren();
            return parentChildren.Concat(new DataModelItem[] {Name}).ToArray();
        }

        public async Task<BusinessUnit> CreateBusinessUnit(string name, string email)
        {
            var businessUnit = new BusinessUnit() {ClientId = id};
            businessUnit.Name.SetValue(name);
            await businessUnit.SavePendingChanges(email);
            return businessUnit;
        }

    }
}
