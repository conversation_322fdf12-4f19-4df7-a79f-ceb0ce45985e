//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section570_Ext_Pipe_F
{
  public class Section570EXTCKLSTSEC006 : DataModelItem {

    public override String DisplayName { 
      get {
        return "VALVES";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute570EXTCKLSTSEC006Q001;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC006Q002;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC006Q003;
    public PredefinedValueAttribute attributeWas_any_mechanical_damage_noted_on_the_associated_valves;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC006Q005;
    public PredefinedValueAttribute attributeAre_valves_in_acceptable_condition_for_continued_service;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section570EXTCKLSTSEC006";

    public Section570EXTCKLSTSEC006(DataModelItem parent) : base(parent)
    {
            
        attribute570EXTCKLSTSEC006Q001 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are installed valves in accordance with the owner/users piping design specifications:", databaseName: "570_EXT_CKLST_SEC006_Q001"); 
     
        attribute570EXTCKLSTSEC006Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Erosion", null),
          new PredefinedValueOption("Yes: Pitting", null)
        }, false, this, "Were any corrosion, erosion, or pitting cells noted on the installed valves:", databaseName: "570_EXT_CKLST_SEC006_Q002"); 
     
        attribute570EXTCKLSTSEC006Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Were any crack like indications noted on the installed valves:", databaseName: "570_EXT_CKLST_SEC006_Q003"); 
     
        attributeWas_any_mechanical_damage_noted_on_the_associated_valves = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was any mechanical damage noted on the associated valves:", databaseName: "570_EXT_CKLST_SEC006_Q004"); 
     
        attribute570EXTCKLSTSEC006Q005 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Active Leakage", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Evidence of Prior Leakage", null)
        }, false, this, "Was evidence of active or prior leakage noted originating from valves within the inspected piping circuit:", databaseName: "570_EXT_CKLST_SEC006_Q005"); 
     
        attributeAre_valves_in_acceptable_condition_for_continued_service = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are valves in acceptable condition for continued service:", databaseName: "570_EXT_CKLST_SEC006_Q006"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute570EXTCKLSTSEC006Q001,
           attribute570EXTCKLSTSEC006Q002,
           attribute570EXTCKLSTSEC006Q003,
           attributeWas_any_mechanical_damage_noted_on_the_associated_valves,
           attribute570EXTCKLSTSEC006Q005,
           attributeAre_valves_in_acceptable_condition_for_continued_service,
        };
    }
  }
}
