//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Asset_Walkdown_Details_F
{
  public class SectionComponents : DataModelItem {

    public override String DisplayName { 
      get {
        return "Components";
      }
    }

    #region [-- Sub Sections --]

    #region [-- SectionShellCourses --]
    private DataModelCollection<SectionShellCourses> _sectionShellCourses;
    public DataModelCollection<SectionShellCourses> sectionShellCourses {
        get {
            if (_sectionShellCourses == null) {
              _sectionShellCourses = new DataModelCollection<SectionShellCourses>("Shell Courses", (parent, entry) => {
                 return new SectionShellCourses(entry.Key, _sectionShellCourses);
              }, (parent, id) => {
                return new SectionShellCourses(id, _sectionShellCourses);
              }, this);
            }

            return _sectionShellCourses;
        }
    }
    #endregion [-- SectionShellCourses --]
    
    #region [-- SectionChannels --]
    private DataModelCollection<SectionChannels> _sectionChannels;
    public DataModelCollection<SectionChannels> sectionChannels {
        get {
            if (_sectionChannels == null) {
              _sectionChannels = new DataModelCollection<SectionChannels>("Channels", (parent, entry) => {
                 return new SectionChannels(entry.Key, _sectionChannels);
              }, (parent, id) => {
                return new SectionChannels(id, _sectionChannels);
              }, this);
            }

            return _sectionChannels;
        }
    }
    #endregion [-- SectionChannels --]
    
    #region [-- SectionHeads --]
    private DataModelCollection<SectionHeads> _sectionHeads;
    public DataModelCollection<SectionHeads> sectionHeads {
        get {
            if (_sectionHeads == null) {
              _sectionHeads = new DataModelCollection<SectionHeads>("Heads", (parent, entry) => {
                 return new SectionHeads(entry.Key, _sectionHeads);
              }, (parent, id) => {
                return new SectionHeads(id, _sectionHeads);
              }, this);
            }

            return _sectionHeads;
        }
    }
    #endregion [-- SectionHeads --]
    
    #region [-- SectionNozzles --]
    private DataModelCollection<SectionNozzles> _sectionNozzles;
    public DataModelCollection<SectionNozzles> sectionNozzles {
        get {
            if (_sectionNozzles == null) {
              _sectionNozzles = new DataModelCollection<SectionNozzles>("Nozzles", (parent, entry) => {
                 return new SectionNozzles(entry.Key, _sectionNozzles);
              }, (parent, id) => {
                return new SectionNozzles(id, _sectionNozzles);
              }, this);
            }

            return _sectionNozzles;
        }
    }
    #endregion [-- SectionNozzles --]
    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionComponents";

    public SectionComponents(DataModelItem parent) : base(parent)
    {
            
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           sectionShellCourses,
           sectionChannels,
           sectionHeads,
           sectionNozzles,
        };
    }
  }
}
