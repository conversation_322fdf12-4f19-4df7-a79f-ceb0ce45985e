//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section570_Ext_Pipe_F
{
  public class Section570EXTCKLSTSEC002 : DataModelItem {

    public override String DisplayName { 
      get {
        return "PROTECTIVE COATING AND INSULATION";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributeDoes_the_asset_have_a_protective_coating_system_applied;
    public PredefinedValueAttribute attributeWas_condensation_noted_on_the_exterior_of_the_asset;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC002Q003;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC002Q004;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC002Q005;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC002Q006;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC002Q007;
    public PredefinedValueAttribute attributeIs_the_insulation_jacketing_seams_properly_oriented;
    public PredefinedValueAttribute attributeAre_there_indications_of_insulation_jacketing_seal_failure;
    public PredefinedValueAttribute attributeAre_there_locations_of_missing_or_damaged_insulation;
    public PredefinedValueAttribute attributeAre_CML_ports_installed;
    public PredefinedValueAttribute attributeAre_all_CML_port_hole_covers_present_and_properly_sealed;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC002Q013;
    public PredefinedValueAttribute attributeAre_there_areas_of_wet_or_damp_insulation;
    public PredefinedValueAttribute attributeWas_any_insulation_removed_as_part_of_this_inspection;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC002Q016;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC002Q017;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section570EXTCKLSTSEC002";

    public Section570EXTCKLSTSEC002(DataModelItem parent) : base(parent)
    {
            
        attributeDoes_the_asset_have_a_protective_coating_system_applied = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Does the asset have a protective coating system applied:", databaseName: "570_EXT_CKLST_SEC002_Q001"); 
     
        attributeWas_condensation_noted_on_the_exterior_of_the_asset = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was condensation noted on the exterior of the asset:", databaseName: "570_EXT_CKLST_SEC002_Q002"); 
     
        attribute570EXTCKLSTSEC002Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was any protective coating removed as part of this inspection:", databaseName: "570_EXT_CKLST_SEC002_Q003"); 
     
        attribute570EXTCKLSTSEC002Q004 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the protective coating in acceptable condition for continued service:", databaseName: "570_EXT_CKLST_SEC002_Q004"); 
     
        attribute570EXTCKLSTSEC002Q005 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the asset insulated: (The type of insulation system, metal jacket with underlying insulation type, blanket, etc., shall be recorded)", databaseName: "570_EXT_CKLST_SEC002_Q005"); 
     
        attribute570EXTCKLSTSEC002Q006 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Does the asset operate within the CUI range: (10 °F (–12 °C) and 350 °F (177 °C) for carbon and low alloy steels, 140 °F (60 °C) and 350 °F (177 °C) for austenitic stainless steels, and 280 °F (138 °C) and  350 °F (177 °C) for duplex stainless steels or in intermittent service)", databaseName: "570_EXT_CKLST_SEC002_Q006"); 
     
        attribute570EXTCKLSTSEC002Q007 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Following the external visual inspection of susceptible systems for CUI was the guidance of API-570 implemented concerning the Recommended Extent of CUI Inspection Following Visual Inspection for Susceptible Piping:", databaseName: "570_EXT_CKLST_SEC002_Q007"); 
     
        attributeIs_the_insulation_jacketing_seams_properly_oriented = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the insulation jacketing seams properly oriented:", databaseName: "570_EXT_CKLST_SEC002_Q008"); 
     
        attributeAre_there_indications_of_insulation_jacketing_seal_failure = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are there indications of insulation jacketing seal failure:", databaseName: "570_EXT_CKLST_SEC002_Q009"); 
     
        attributeAre_there_locations_of_missing_or_damaged_insulation = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are there locations of missing or damaged insulation:", databaseName: "570_EXT_CKLST_SEC002_Q010"); 
     
        attributeAre_CML_ports_installed = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are CML ports installed:", databaseName: "570_EXT_CKLST_SEC002_Q011"); 
     
        attributeAre_all_CML_port_hole_covers_present_and_properly_sealed = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are all CML port hole covers present and properly sealed:", databaseName: "570_EXT_CKLST_SEC002_Q012"); 
     
        attribute570EXTCKLSTSEC002Q013 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was evidence of active or prior leakage noted from underneath the insulation:", databaseName: "570_EXT_CKLST_SEC002_Q013"); 
     
        attributeAre_there_areas_of_wet_or_damp_insulation = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are there areas of wet or damp insulation:", databaseName: "570_EXT_CKLST_SEC002_Q014"); 
     
        attributeWas_any_insulation_removed_as_part_of_this_inspection = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was any insulation removed as part of this inspection:", databaseName: "570_EXT_CKLST_SEC002_Q015"); 
     
        attribute570EXTCKLSTSEC002Q016 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the asset heat traced and is tracing in acceptable condition for continued service:  (The type of heat tracing, electrical or steam, shall be recorded)", databaseName: "570_EXT_CKLST_SEC002_Q016"); 
     
        attribute570EXTCKLSTSEC002Q017 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the piping insulation system in acceptable condition for continued service:", databaseName: "570_EXT_CKLST_SEC002_Q017"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeDoes_the_asset_have_a_protective_coating_system_applied,
           attributeWas_condensation_noted_on_the_exterior_of_the_asset,
           attribute570EXTCKLSTSEC002Q003,
           attribute570EXTCKLSTSEC002Q004,
           attribute570EXTCKLSTSEC002Q005,
           attribute570EXTCKLSTSEC002Q006,
           attribute570EXTCKLSTSEC002Q007,
           attributeIs_the_insulation_jacketing_seams_properly_oriented,
           attributeAre_there_indications_of_insulation_jacketing_seal_failure,
           attributeAre_there_locations_of_missing_or_damaged_insulation,
           attributeAre_CML_ports_installed,
           attributeAre_all_CML_port_hole_covers_present_and_properly_sealed,
           attribute570EXTCKLSTSEC002Q013,
           attributeAre_there_areas_of_wet_or_damp_insulation,
           attributeWas_any_insulation_removed_as_part_of_this_inspection,
           attribute570EXTCKLSTSEC002Q016,
           attribute570EXTCKLSTSEC002Q017,
        };
    }
  }
}
