//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'Section510EXT_PVCKLSTSEC003.dart';

// ignore: camel_case_types
class Section510EXT_PVCKLSTSEC003Page extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final Section510EXT_PVCKLSTSEC003 section510EXT_PVCKLSTSEC003;

  const Section510EXT_PVCKLSTSEC003Page(this.section510EXT_PVCKLSTSEC003,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _Section510EXT_PVCKLSTSEC003PageState();
  }
}

class _Section510EXT_PVCKLSTSEC003PageState
    extends State<Section510EXT_PVCKLSTSEC003Page> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.section510EXT_PVCKLSTSEC003,
        title: "FOUNDATIONS AND SUPPORTS",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC003
                      .attribute510EXT_PVCKLSTSEC003Q001
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC003
                      .attribute510EXT_PVCKLSTSEC003Q002
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC003
                      .attribute510EXT_PVCKLSTSEC003Q003
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC003
                      .attribute510EXT_PVCKLSTSEC003Q004
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC003
                      .attribute510EXT_PVCKLSTSEC003Q005
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC003
                      .attribute510EXT_PVCKLSTSEC003Q006
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC003
                      .attribute510EXT_PVCKLSTSEC003Q007
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
