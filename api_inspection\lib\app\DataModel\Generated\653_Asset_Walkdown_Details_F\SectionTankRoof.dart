//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';

// ignore: camel_case_types
class SectionTankRoof extends DataModelCollectionItem {
  @override
  String getDisplayName() => "Tank Roof";

  SectionTankRoof(String id, DataModelItem parent) : super(id, parent);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeType =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Type",
          databaseName: "653AW_Q441",
          availableOptions: [
        PredefinedValueOption("Fixed", null, isCommentRequired: false),
        PredefinedValueOption("Internal Floating", null,
            isCommentRequired: false),
        PredefinedValueOption("External Floating", null,
            isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeMaterial_Spec_and_Grade = StringAttribute(
      parent: this,
      displayName: "Material Spec and Grade",
      databaseName: "653AW_Q442",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeNominal_thickness_roof = DoubleAttribute(
    parent: this,
    displayName: "Nominal thickness (roof)",
    databaseName: "653AW_Q443",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "in",
  );

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeCorrosion_Allowance = DoubleAttribute(
    parent: this,
    displayName: "Corrosion Allowance",
    databaseName: "653AW_Q444",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "in",
  );

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeType,
      attributeMaterial_Spec_and_Grade,
      attributeNominal_thickness_roof,
      attributeCorrosion_Allowance,
    ]);
    return children;
  }
}
