import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:collection/collection.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/ConcretePhotoRoot.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/AttributeBase.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/ChangeLog/ChangeLog.dart';
import 'package:uuid/uuid.dart';
import 'package:darq/darq.dart';

import 'Attributes/ChangeLog/ChangeLogEntry.dart';
import 'DatabaseInterfaces/DatabaseHelperManager.dart';

abstract class DataModelItem with ExtendedObserver {
  bool updateFromOther(DataModelItem other, Map<String, dynamic> updates) {
    bool updated = false;
    for (var child in getChildren()) {
      var matchingChild = other.getChildren().firstWhereOrNull(
          (element) => element.getDBName() == child.getDBName());
      if (matchingChild == null) {
        throw Exception("Unexpected null");
      }
      var childMap = <String, dynamic>{};
      var childUpdated = child.updateFromOther(matchingChild, childMap);
      if (childUpdated) {
        updated = true;
        updates[child.getDBName()] = childMap;
      }
    }
    return updated;
  }

  @mustCallSuper
  void onChildrenChanged(DataModelItem childChanged) {
    parent?.onChildrenChanged(childChanged);
  }

  T? findParentOfType<T>({bool incudeSelf = true}) {
    if (incudeSelf) {
      if (this is T) {
        return this as T;
      }
    }
    var localParent = parent;

    if (localParent == null) return null;
    if (localParent is T) {
      return localParent as T;
    }

    return localParent.findParentOfType<T>();
  }

  String getDBName();

  String getDisplayName();

  String getDBPath() {
    DataModelItem? _parent = parent;
    if (_parent == null) {
      return getDBName();
    }
    return _parent.getDBPath() + "." + getDBName();
  }

  String getDisplayPath({DataModelItem? rootToStop}) {
    if (this == rootToStop) return "";
    DataModelItem? _parent = parent;
    if (_parent == null || _parent == rootToStop) {
      return getDisplayName();
    }
    return _parent.getDisplayPath(rootToStop: rootToStop) +
        "." +
        getDisplayName();
  }

  void saveItem(WriteBatch batch) {
    saveDirectItems(batch);
    for (var child in getChildren()) {
      child.saveItem(batch);
    }
    return;
  }

  void updateFromMap(Map? map) {
    if (map == null) {
      for (var child in getChildren()) {
        child.updateFromMap(null);
      }
      updateDirectPropertiesFromMap({});
      return;
    }

    var children = getChildren().toList();
    for (MapEntry entry in map.entries) {
      DataModelItem? match = children
          .firstWhereOrNull((element) => element.getDBName() == entry.key);
      if (match != null) {
        match.updateFromMap(entry.value as Map?);
        children.remove(match);
        continue;
      }

      updateDirectPropertiesFromMapEntry(entry);
    }
    updateDirectPropertiesFromMap(map);

    for (var child in children) {
      child.updateFromMap(null);
    }
  }

  void saveDirectItems(WriteBatch batch) async {}

  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }

  void updateDirectPropertiesFromMap(Map map) {}

  DataModelItem? parent;

  DataModelItem(this.parent);

  List<DataModelItem> getChildren();

  List<DataModelItem> getChildrenRecursive() {
    var children = getChildren();

    var returnList = children.toList();
    for (var child in children) {
      returnList.addAll(child.getChildrenRecursive());
    }

    return returnList;
  }

  int numberOfUIAttachments = 0;

  final List<VoidCallback> _listeners =
      List<VoidCallback>.empty(growable: true);

  void addListener(VoidCallback listener) {
    _listeners.add(listener);
  }

  void removeListener(VoidCallback listener) {
    _listeners.remove(listener);
  }

  @override
  void notifyListeners() {
    for (var element in _listeners) {
      element.call();
    }
  }
}

abstract class DataModelSection extends DataModelItem {
  String sectionName;

  bool get showState {
    return parent is! ConcretePhotoRoot;
  }

  bool isLocked() {
    if (sectionState.getValue() == "Completed" ||
        sectionState.getValue() == "Skipped") return true;
    var parent = findParentOfType<DataModelSection>(incudeSelf: false);
    if (parent != null) {
      return parent.isLocked();
    }
    return false;
  }

  late PredefinedValueAttribute sectionState =
      PredefinedValueAttribute(availableOptions: [
    PredefinedValueOption("Not Started", null),
    PredefinedValueOption("In Progress", null),
    PredefinedValueOption("Completed", null),
    PredefinedValueOption("Skipped", null),
  ], hasOtherOption: false, parent: this, displayName: "SectionState")
        ..isUserFacing = false;

  DataModelSection({required this.sectionName, required DataModelItem? parent})
      : super(parent);

  @override
  @mustCallSuper
  List<DataModelItem> getChildren() {
    return [sectionState];
  }
}

abstract class DataModelCollectionItem extends DataModelSection {
  String id;

  DataModelCollectionItem(this.id, DataModelItem parent)
      : super(parent: parent, sectionName: "Unknown");

  @override
  String getDBName() {
    return id;
  }

  String getId() {
    return getDBName();
  }

  @override
  String getDBPath() {
    DataModelItem? localParent = parent;
    if (localParent == null) {
      return getDBName();
    }

    return localParent.getDBPath() + ".Values." + getDBName();
  }

  @override
  void saveDirectItems(WriteBatch batch) async {
    var helper = DatabaseHelperManager.global();
    var path = getDBPath();
    helper.updateItem(path + ".Id", id, batch);
  }

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    if (entry.key == "Id") {
      id = entry.value as String;
      return true;
    }

    return false;
  }
}

typedef DataModelFactory<T extends DataModelCollectionItem> = T Function(
    DataModelItem parent, MapEntry map);

class DataModelCollection<T extends DataModelCollectionItem>
    extends DataModelSection {
  String name;

  @override
  String getDisplayName() => name;

  final DataModelFactory<dynamic> _factory;
  DataModelCollection(this.name, this._factory, DataModelItem? parent)
      : super(parent: parent, sectionName: name);

  late ListChangeLog changeLog = ListChangeLog<String>(
      "ValueChangeLog", this, <ListChangeLogEntry<String>>[]);

  List<T> _allEntries = [];

  List<T> getEntries() {
    var currentEntryKeys = changeLog.getCurrentEntries();
    var entries = _allEntries
        .where((element) => currentEntryKeys.contains(element.getId()));

    return entries.toList();
  }

  // figure out how we want to deal with these save items
  // may only want to add here, not save
  void addItem(T item) {
    _allEntries.add(item);

    String key = const Uuid().v4().toString();
    var entry = ListChangeLogEntry<String>(
        key, item.getId(), DateTime.now(), "Unknown User", "Added");
    changeLog.addNewItem(entry);
  }

  void removeItem(T item) {
    if (getEntries().contains(item)) {
      String key = const Uuid().v4().toString();
      var entry = ListChangeLogEntry<String>(
          key, item.getId(), DateTime.now(), "Unknown User", "Removed");
      changeLog.addNewItem(entry);
    }
  }

  @override
  bool updateFromOther(DataModelItem other, Map<String, dynamic> updates) {
    if (other is! DataModelCollection<T>) {
      throw "Unexpected type encountered in updateFromOther";
    }

    bool updated = false;

    var changeLogMap = <String, dynamic>{};
    if (changeLog.updateFromOther(other.changeLog, changeLogMap)) {
      updates[changeLog.getDBName()] = changeLogMap;
      updated = true;
    }

    for (var item in other.getEntries()) {
      var childMap = <String, dynamic>{};

      var matchingEntry =
          getEntries().firstWhereOrNull((element) => element.id == item.id);
      if (matchingEntry == null) {
        var newItem = _factory(this, MapEntry(item.id, <String, dynamic>{}));
        if (newItem is T) {
          _allEntries.add(newItem);
          var batch = FirebaseFirestore.instance.batch();
          newItem.saveItem(batch);
          batch.commit();
          newItem.updateFromOther(item, childMap);
          updated = true;
        }
      } else {
        updated |= matchingEntry.updateFromOther(item, childMap);
      }

      if (updated) {
        var map = <String, dynamic>{};
        map[item.getDBName()] = childMap;
        updates["Values"] = map;
      }
    }
    return updated;
  }

  @override
  List<DataModelItem> getChildren() {
    List<DataModelItem> children = super.getChildren().toList();
    children.addAll(_allEntries);

    children.add(changeLog);

    return children;
  }

  @override
  String getDBName() {
    return name;
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }

  void onUpdatedFromMap() {
    notifyListeners();
  }

  @override
  void updateFromMap(Map? map) {
    if (map == null) return;

    List<T> newMediaEntries = _allEntries.toList();
    if (map.containsKey("Values")) {
      var values = map["Values"] as Map?;
      if (values != null) {
        for (var item in values.entries) {
          try {
            if (item.value is Map) {
              var currentEntry = _allEntries.firstWhereOrNull(
                  (element) => element.getDBName() == item.key);
              if (currentEntry != null) {
                currentEntry.updateFromMap(item.value);
              } else {
                var newEntry = _factory(this, item);
                if (newEntry is T) {
                  newEntry.updateFromMap(item.value);
                  newMediaEntries.add(newEntry);
                }
              }
            }
          } catch (e) {
            log("Failed to add item " + e.toString(), name: 'updateFromMap');
          }
        }
      }
    }
    if (map.containsKey("ValueChangeLog")) {
      var changeLogMap = map["ValueChangeLog"] as Map?;
      changeLog.updateFromMap(changeLogMap);
    }

    _allEntries = newMediaEntries;

    onUpdatedFromMap();
  }
}

// ignore: camel_case_types
typedef convertFromBinaryDef<T> = T Function(String key, List<int> binary);
// ignore: camel_case_types
typedef convertToBinaryDef<T> = MapEntry Function(T binary);

class BinaryCollectionWithoutHistory<T> extends DataModelItem {
  String name;

  @override
  String getDisplayName() => name;

  List<T> collection = [];

  convertFromBinaryDef<T> convertFromBinary;
  convertToBinaryDef<T> convertToBinary;

  BinaryCollectionWithoutHistory(
      {required this.name,
      required DataModelItem parent,
      required this.convertFromBinary,
      required this.convertToBinary})
      : super(parent);

  @override
  List<DataModelItem> getChildren() {
    return [];
  }

  @override
  String getDBName() {
    return name;
  }

  @override
  void updateFromMap(Map? map) {
    if (map == null) return;
    List<T> newItems = [];
    for (var entry in map.entries) {
      var dynList = entry.value as List<dynamic>;
      var intList = dynList.select((element, index) => element as int).toList();
      newItems.add(convertFromBinary(entry.key, intList));
    }
    collection = newItems;

    notifyListeners();
  }

  void updateItem(T item, WriteBatch batch) async {
    var mapEntry = convertToBinary(item);
    var path = getDBPath();

    var itemPath = path + "." + mapEntry.key;

    var helper = DatabaseHelperManager.global();
    return helper.updateItem(itemPath, mapEntry.value, batch);
  }
}

abstract class DataModelNode extends DataModelItem {
  DataModelNode(DataModelItem? parent) : super(parent);

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}

abstract class DataModelLeaf extends DataModelItem {
  DataModelLeaf(DataModelItem? parent) : super(parent);

  @override
  List<DataModelItem> getChildren() {
    return List.empty();
  }
}
