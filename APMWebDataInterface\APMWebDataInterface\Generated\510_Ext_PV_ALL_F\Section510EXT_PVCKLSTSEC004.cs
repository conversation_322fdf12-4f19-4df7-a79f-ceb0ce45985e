//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Ext_PV_ALL_F
{
  public class Section510EXT_PVCKLSTSEC004 : DataModelItem {

    public override String DisplayName { 
      get {
        return "ANCHORAGE";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC004Q001;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC004Q002;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC004Q003;
    public PredefinedValueAttribute attributeIs_anchorage_in_acceptable_condition_for_continued_service;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510EXT_PVCKLSTSEC004";

    public Section510EXT_PVCKLSTSEC004(DataModelItem parent) : base(parent)
    {
            
        attribute510EXT_PVCKLSTSEC004Q001 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is there distortion of the anchor bolts that may indicate foundation settlement:", databaseName: "510_EXT-PV_CKLST_SEC004_Q001"); 
     
        attribute510EXT_PVCKLSTSEC004Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are the nuts associated with the anchor bolts properly tightened:", databaseName: "510_EXT-PV_CKLST_SEC004_Q002"); 
     
        attribute510EXT_PVCKLSTSEC004Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are the nuts associated with the anchor bolts acceptable for continued service:", databaseName: "510_EXT-PV_CKLST_SEC004_Q003"); 
     
        attributeIs_anchorage_in_acceptable_condition_for_continued_service = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is anchorage in acceptable condition for continued service:", databaseName: "510_EXT-PV_CKLST_SEC004_Q004"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute510EXT_PVCKLSTSEC004Q001,
           attribute510EXT_PVCKLSTSEC004Q002,
           attribute510EXT_PVCKLSTSEC004Q003,
           attributeIs_anchorage_in_acceptable_condition_for_continued_service,
        };
    }
  }
}
