import 'dart:developer';

import 'package:api_inspection/generic/MediaControls/photo_widgets_for_collection.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/AttributeBase.dart';

import 'package:api_inspection/generic/MediaControls/MediaEntry.dart';

class SinglePhotoCollectionControlNonEditable extends StatefulWidget {
  final AttributeBase _attribute;

  const SinglePhotoCollectionControlNonEditable(this._attribute, {Key? key})
      : super(key: key);

  @override
  _SinglePhotoCollectionControlNonEditableState createState() =>
      _SinglePhotoCollectionControlNonEditableState();
}

class _SinglePhotoCollectionControlNonEditableState
    extends State<SinglePhotoCollectionControlNonEditable> {
  List<MediaEntry> _mediaEntries = List.empty();

  @override
  void initState() {
    widget._attribute.addListener(onAttributeChanged);

    _mediaEntries = widget._attribute.getPhotos().toList();

    super.initState();
  }

  @override
  void didUpdateWidget(SinglePhotoCollectionControlNonEditable old) {
    old._attribute.removeListener(onAttributeChanged);

    _mediaEntries = widget._attribute.getPhotos().toList();

    super.didUpdateWidget(old);
  }

  @override
  void dispose() {
    widget._attribute.removeListener(onAttributeChanged);
    super.dispose();
  }

  void onAttributeChanged() {
    setState(() {
      var currentMediaEntries = widget._attribute.getPhotos();
      _mediaEntries = currentMediaEntries.toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> photoWidgets = [];
    for (var entry in _mediaEntries) {
      try {
        photoWidgets.add(
            resolvedPhotoForCollection(context, entry, _mediaEntries, false));
      } catch (ex) {
        log(ex.toString(), name: 'build');
      }
    }
    return Container(
        margin: const EdgeInsets.fromLTRB(15, 0, 15, 0),
        alignment: Alignment.centerLeft,
        child: Wrap(children: photoWidgets));
  }
}
