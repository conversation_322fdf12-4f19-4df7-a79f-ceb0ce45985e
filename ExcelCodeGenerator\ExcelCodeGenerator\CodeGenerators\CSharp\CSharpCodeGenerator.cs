﻿using System;
using System.IO;
using System.Linq;
using CheckListGen;

namespace ExcelCodeGenerator.CodeGenerators.CSharp
{
    public class CSharpCodeGenerator
    {
        private string _formName;

        private CSharpCodeGenerator()
        {
        }

        public static void GenerateCodeForForm(string outputPath, Section section)
        {
            var generator = new CSharpCodeGenerator();
            generator.DoGenerateCodeForForm(outputPath, section);
        }

        private void DoGenerateCodeForForm(string outputPath, Section section)
        {
            _formName = section.DartVariableName;

            var outputDirectory = outputPath + "/" + Helpers.CleanupVariableName(section.Name);
            Directory.CreateDirectory(outputDirectory);

            BuildFilesForSection(section, outputDirectory);
        }


        private void BuildFilesForSection(Section section, string outputDirectory)
        {
            foreach (var child in section.Children.OfType<Section>()) BuildFilesForSection(child, outputDirectory);

            BuildSectionDataFile(section, outputDirectory);
        }

        private void BuildSectionDataFile(Section section, string outputDirectory)
        {
            if (section.IsCollection)
                BuildSectionDataFile_Collection(section, outputDirectory);
            else
                BuildSectionDataFile_NonCollection(section, outputDirectory);
        }


        private string BuildSectionDeclaration(Section section)
        {
            return section.IsCollection
                ? BuildSectionDeclaration_Collection(section)
                : BuildSectionDeclaration_NonCollection(section);
        }

        private static string BuildSectionDeclaration_NonCollection(Section section)
        {
            return "#region [-- " + section.DartClassName + " --]\r\n    private " + section.DartClassName + " _" +
                   section.DartVariableName + ";" + @"
    public " + section.DartClassName + " " + section.DartVariableName + @" {
        get {
            if (_" + section.DartVariableName + @" == null) {
               _" + section.DartVariableName + " = new " + section.DartClassName + @"(this);
            }

            return _" + section.DartVariableName + @";
        }
    }
    #endregion [-- " + section.DartClassName + " --]\r\n    ";
        }

        private string BuildSectionDeclaration_Collection(Section section)
        {
            return "#region [-- " + section.DartClassName + @" --]
    private DataModelCollection<" + section.DartClassName + "> _" + section.DartVariableName + ";" + @"
    public DataModelCollection<" + section.DartClassName + "> " + section.DartVariableName + @" {
        get {
            if (_" + section.DartVariableName + @" == null) {
              _" + section.DartVariableName + @" = new DataModelCollection<" + section.DartClassName + @">(""" +
                   section.Name + @""", (parent, entry) => {
                 return new " + section.DartClassName + @"(entry.Key, _" + section.DartVariableName + @");
              }, (parent, id) => {
                return new " + section.DartClassName + @"(id, _" + section.DartVariableName + @");
              }, this);
            }

            return _" + section.DartVariableName + @";
        }
    }
    #endregion [-- " + section.DartClassName + " --]\r\n    ";
        }


        private void BuildSectionDataFile_NonCollection(Section section, string outputDirectory)
        {
            var questions = section.Children.OfType<Question>().ToArray();

            var sections = section.Children.OfType<Section>().ToArray();

            var fileContents =
                @"//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated." + _formName + @"
{
  public class " + section.DartClassName + @" : DataModelItem {

    public override String DisplayName { 
      get {
        return """ + section.Name + @""";
      }
    }

    #region [-- Sub Sections --]

    " + sections.Select(BuildSectionDeclaration).AggregateEXT((a, b) => a + Environment.NewLine + "    " + b) + @"

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    " + questions.Select(a => a.BuildGenerator().BuildDeclaration(a)).AggregateEXT((a, b) => a + "\r\n    " + b) + @"

    #endregion [-- Attributes --]

    internal override String GetDBName() => """ + section.DartClassName + @""";

    public " + section.DartClassName + @"(DataModelItem parent) : base(parent)
    {
            " + questions.Select(a => a.BuildGenerator().BuildInitialization(a))
                    .AggregateEXT((a, b) => a + "\r\n     " + b) + @"
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           " +
                section.Children.Select(a => a.DartVariableName + ",")
                    .AggregateEXT((a, b) => a + "\r\n           " + b) + @"
        };
    }
  }
}
";

            File.WriteAllText(outputDirectory + "/" + section.DartClassName + ".cs", fileContents);
        }

        private void BuildSectionDataFile_Collection(Section section, string outputDirectory)
        {
            var questions = section.Children.OfType<Question>().ToArray();

            var sections = section.Children.OfType<Section>().ToArray();

            var fileContents =
                @"//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated." + _formName + @"
{
  public class " + section.DartClassName + @" : DataModelCollectionItem {

    public override String DisplayName { 
      get {
        return """ + section.Name + @""";
      }
    }
    
    #region [-- Sub Sections --]

    " + sections.Select(BuildSectionDeclaration).AggregateEXT((a, b) => a + Environment.NewLine + "    " + b) +
                @"

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    " + questions.Select(a => a.BuildGenerator().BuildDeclaration(a)).AggregateEXT((a, b) => a + "\r\n    " + b) + @"

    #endregion [-- Attributes --]

    public " + section.DartClassName + @"(String id, DataModelItem parent) : base(id, parent)
    {
            " + questions.Select(a => a.BuildGenerator().BuildInitialization(a))
                    .AggregateEXT((a, b) => a + "\r\n     " + b) + @"
    }   
    
    public override DataModelItem[] GetChildren() {
      var parentChildren = base.GetChildren();

      return parentChildren.Concat(new DataModelItem[] { 
        " +
                section.Children.Select(a => a.DartVariableName + ",")
                    .AggregateEXT((a, b) => a + "\r\n           " + b) + @"
      }).ToArray();
    }
  }
}
";

            File.WriteAllText(outputDirectory + "/" + section.DartClassName + ".cs", fileContents);
        }
    }
}