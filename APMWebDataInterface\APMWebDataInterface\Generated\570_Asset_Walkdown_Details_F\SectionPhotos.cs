//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section570_Asset_Walkdown_Details_F
{
  public class SectionPhotos : DataModelItem {

    public override String DisplayName { 
      get {
        return "Photos";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PhotoAttribute attributeFront;
    public PhotoAttribute attributeBack;
    public PhotoAttribute attributeLeft;
    public PhotoAttribute attributeRight;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionPhotos";

    public SectionPhotos(DataModelItem parent) : base(parent)
    {
            
        attributeFront = new PhotoAttribute(this, displayName: "Front", databaseName: "570AW_Q102"); 
     
        attributeBack = new PhotoAttribute(this, displayName: "Back", databaseName: "570AW_Q103"); 
     
        attributeLeft = new PhotoAttribute(this, displayName: "Left", databaseName: "570AW_Q104"); 
     
        attributeRight = new PhotoAttribute(this, displayName: "Right", databaseName: "570AW_Q105"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeFront,
           attributeBack,
           attributeLeft,
           attributeRight,
        };
    }
  }
}
