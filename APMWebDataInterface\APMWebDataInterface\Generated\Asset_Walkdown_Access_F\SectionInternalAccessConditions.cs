//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionAsset_Walkdown_Access_F
{
  public class SectionInternalAccessConditions : DataModelItem {

    public override String DisplayName { 
      get {
        return "Internal Access Conditions";
      }
    }

    #region [-- Sub Sections --]

    #region [-- SectionInternalAccessRequirements --]
    private SectionInternalAccessRequirements _sectionInternalAccessRequirements;
    public SectionInternalAccessRequirements sectionInternalAccessRequirements {
        get {
            if (_sectionInternalAccessRequirements == null) {
               _sectionInternalAccessRequirements = new SectionInternalAccessRequirements(this);
            }

            return _sectionInternalAccessRequirements;
        }
    }
    #endregion [-- SectionInternalAccessRequirements --]
    
    #region [-- SectionCleaningRequirements --]
    private SectionCleaningRequirements _sectionCleaningRequirements;
    public SectionCleaningRequirements sectionCleaningRequirements {
        get {
            if (_sectionCleaningRequirements == null) {
               _sectionCleaningRequirements = new SectionCleaningRequirements(this);
            }

            return _sectionCleaningRequirements;
        }
    }
    #endregion [-- SectionCleaningRequirements --]
    
    #region [-- SectionInternalCoatingLiner --]
    private SectionInternalCoatingLiner _sectionInternalCoatingLiner;
    public SectionInternalCoatingLiner sectionInternalCoatingLiner {
        get {
            if (_sectionInternalCoatingLiner == null) {
               _sectionInternalCoatingLiner = new SectionInternalCoatingLiner(this);
            }

            return _sectionInternalCoatingLiner;
        }
    }
    #endregion [-- SectionInternalCoatingLiner --]
    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributeIs_the_asset_out_of_service;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionInternalAccessConditions";

    public SectionInternalAccessConditions(DataModelItem parent) : base(parent)
    {
            
        attributeIs_the_asset_out_of_service = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Is the asset out of service?", databaseName: "AWA_Q305"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           sectionInternalAccessRequirements,
           sectionCleaningRequirements,
           sectionInternalCoatingLiner,
           attributeIs_the_asset_out_of_service,
        };
    }
  }
}
