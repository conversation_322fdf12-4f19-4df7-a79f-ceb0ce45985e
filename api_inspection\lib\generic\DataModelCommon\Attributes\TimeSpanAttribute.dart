import 'package:api_inspection/generic/AttributeControls/AttributeStructure.dart';
import 'package:api_inspection/generic/AttributeControls/TimeSpan/TimeSpanAttributeView.dart';
import 'package:api_inspection/generic/Common/Types/TimeSpan.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'AttributeBase.dart';
import 'ChangeLog/ChangeLogEntry.dart';

class TimeSpanAttribute extends SingleAttributeBase<double> {
  late Time time;
  late TimeSpan timeSpan;

  double? getValue() {
    if (valueChangeLog.entries.isEmpty) return null;
    return valueChangeLog.entries.last.value;
  }

  @override
  bool hasData() {
    return getValue() != null;
  }

  double? getValueinSeconds() {
    var value = getValue();
    if (value != null) {
      value = value / 1000;
      return value;
    } else {
      return null;
    }
  }

  double? getValueinSecondsAndMinutes() {
    var value = getValueinSeconds();
    if (value == null) return null;
    return value % 60;
  }

  double? getValueinMinutes() {
    var value = getValueinSeconds();
    if (value == null) return null;
    return value / 60;
  }

  void setValue(double? value) {
    if (getValue() == value) return;
    if (value != null) value = value * 1000;
    var entry = ChangeLogEntry<double>.newlyCreated(value);
    valueChangeLog.addNewItem(entry);

    notifyListeners();
  }

  void setValueForMinutesAndSeconds(
      double? minutesValue, double? secondsValue) {
    var currentMin = getValueinMinutes();
    var currentSeconds = getValueinSecondsAndMinutes();

    if (currentMin == minutesValue && currentSeconds == secondsValue) {
      return;
    }

    ChangeLogEntry<double> entry;
    if (minutesValue == null && secondsValue == null) {
      entry = ChangeLogEntry<double>.newlyCreated(null);
    } else {
      double? value = (minutesValue! * 60) + secondsValue!;
      value = value * 1000;
      entry = ChangeLogEntry<double>.newlyCreated(value);
    }

    valueChangeLog.addNewItem(entry);

    notifyListeners();
  }

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  bool allowNegatives;

  TimeSpanAttribute(
      {required DataModelItem parent,
      required String displayName,
      required this.allowNegatives,
      required this.timeSpan,
      Widget? iconWidget,
      bool areCommentsRequired = false,
      String? databaseName})
      : super(parent, displayName, iconWidget, areCommentsRequired,
            databaseName) {
    time.time = getValue();
    valueChangeLog.setConversionMethod(convertDynamicToTimeSpan);
  }

  double? convertDynamicToTimeSpan(dynamic dyn) {
    if (dyn is int) {
      return dyn.toDouble();
    }
    if (dyn is double) {
      return dyn.toDouble();
    }
    return double.tryParse(dyn.toString());
  }

  @override
  Widget buildWidget(
      {IsEditableController? editingController,
      bool showPhotos = true,
      bool showComments = true}) {
    return TimeSpanAttributeView(
      this,
      editingController: editingController,
      showPhotos: showPhotos,
      showComments: showComments,
    );
  }

  @override
  String getPreviewText() {
    var value = getValueinSeconds();
    if (timeSpan == TimeSpan.Seconds) {
      return value == null ? "" : value.toString() + " seconds";
    } else {
      return getValueinMinutes().toString() +
          " minutes " +
          getValueinSecondsAndMinutes().toString() +
          " seconds";
    }
  }
}
