import 'package:api_inspection/generic/AttributeControls/AttributeStructure.dart';
import 'package:api_inspection/generic/AttributeControls/PredefinedValue/Multi/Default/MultiPredefinedValueAttributeView.dart';
import 'package:api_inspection/generic/AttributeControls/PredefinedValue/Multi/WithOther/MultiPredefinedValueAttributeViewWithOther.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:darq/darq.dart';
import '../PredefinedValueAttribute.dart';
import 'MultiStringAttribute.dart';

class MultiPredefinedValueAttribute extends MultiStringAttribute {
  bool hasOtherOption;
  late List<PredefinedValueOption> availableOptions;
  String? unit;

  @override
  String getPreviewText() {
    var value = getValue();
    value?.sort((a, b) =>
        getOptionValues().indexOf(a).compareTo(getOptionValues().indexOf(b)));
    var displayUnit = unit;
    if (displayUnit == null || value == null) {
      return value == null ? "" : value.join(", ");
    }
    return value
        .select((element, index) => element + " " + displayUnit)
        .join(", ");
  }

  List<PredefinedValueOption> getOptions() {
    return availableOptions;
  }

  getOptionValues() {
    List options = [];
    for (var predefvalue in getOptions()) {
      options.add(predefvalue.value);
    }
    return options;
  }

  MultiPredefinedValueAttribute(
      {required this.availableOptions,
      required this.hasOtherOption,
      required DataModelItem parent,
      required String displayName,
      Widget? iconWidget,
      bool areCommentsRequired = false,
      String? databaseName,
      this.unit})
      : super(parent, displayName,
            iconWidget: iconWidget,
            areCommentsRequired: areCommentsRequired,
            databaseName: databaseName);

  @override
  bool getAreCommentsRequired() {
    var currentValue = getValue();
    if (currentValue != null && currentValue.isNotEmpty) {
      var options =
          getOptions().where((element) => currentValue.contains(element.value));
      if (options.any((element) => element.isCommentRequired)) return true;
    }
    return false;
  }

  @override
  Widget buildWidget(
      {IsEditableController? editingController,
      bool showPhotos = true,
      bool showComments = true}) {
    if (hasOtherOption) {
      return MultiPredefinedValueAttributeViewWithOther(
        this,
        editingController: editingController,
        showPhotos: showPhotos,
        showComments: showComments,
      );
    } else {
      return MultiPredefinedValueAttributeView(
        this,
        editingController: editingController,
        showPhotos: showPhotos,
        showComments: showComments,
      );
    }
  }
}
