

// class SignatureAttribute extends DataModelCollectionItem {
//   String technicianName;

//   SignatureAttribute(String id, DataModelItem parent) : super(id, parent);
//   //SignatureAttribute(this.technicianName) : super('');

//   @override
//   List<DataModelItem> getChildren() {
//     // TODO: implement getChildren
//     throw UnimplementedError();
//   }

//   @override
//   String getDBName() {
//     // TODO: implement getDBName
//     throw UnimplementedError();
//   }

//   @override
//   void saveDirectItems(WriteBatch batch) {
//     // TODO: implement saveDirectItems
//     throw UnimplementedError();
//   }

//   @override
//   bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
//     // TODO: implement updateDirectPropertiesFromMapEntry
//     throw UnimplementedError();
//   }
// }
