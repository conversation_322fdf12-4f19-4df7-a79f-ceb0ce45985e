//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section570_Ext_Pipe_F
{
  public class Section570_Ext_Pipe_F : DataModelItem {

    public override String DisplayName { 
      get {
        return "570-Ext-Pipe-F";
      }
    }

    #region [-- Sub Sections --]

    #region [-- Section570EXTCKLSTSEC001 --]
    private Section570EXTCKLSTSEC001 _section570EXTCKLSTSEC001;
    public Section570EXTCKLSTSEC001 section570EXTCKLSTSEC001 {
        get {
            if (_section570EXTCKLSTSEC001 == null) {
               _section570EXTCKLSTSEC001 = new Section570EXTCKLSTSEC001(this);
            }

            return _section570EXTCKLSTSEC001;
        }
    }
    #endregion [-- Section570EXTCKLSTSEC001 --]
    
    #region [-- Section570EXTCKLSTSEC002 --]
    private Section570EXTCKLSTSEC002 _section570EXTCKLSTSEC002;
    public Section570EXTCKLSTSEC002 section570EXTCKLSTSEC002 {
        get {
            if (_section570EXTCKLSTSEC002 == null) {
               _section570EXTCKLSTSEC002 = new Section570EXTCKLSTSEC002(this);
            }

            return _section570EXTCKLSTSEC002;
        }
    }
    #endregion [-- Section570EXTCKLSTSEC002 --]
    
    #region [-- Section570EXTCKLSTSEC003 --]
    private Section570EXTCKLSTSEC003 _section570EXTCKLSTSEC003;
    public Section570EXTCKLSTSEC003 section570EXTCKLSTSEC003 {
        get {
            if (_section570EXTCKLSTSEC003 == null) {
               _section570EXTCKLSTSEC003 = new Section570EXTCKLSTSEC003(this);
            }

            return _section570EXTCKLSTSEC003;
        }
    }
    #endregion [-- Section570EXTCKLSTSEC003 --]
    
    #region [-- Section570EXTCKLSTSEC004 --]
    private Section570EXTCKLSTSEC004 _section570EXTCKLSTSEC004;
    public Section570EXTCKLSTSEC004 section570EXTCKLSTSEC004 {
        get {
            if (_section570EXTCKLSTSEC004 == null) {
               _section570EXTCKLSTSEC004 = new Section570EXTCKLSTSEC004(this);
            }

            return _section570EXTCKLSTSEC004;
        }
    }
    #endregion [-- Section570EXTCKLSTSEC004 --]
    
    #region [-- Section570EXTCKLSTSEC005 --]
    private Section570EXTCKLSTSEC005 _section570EXTCKLSTSEC005;
    public Section570EXTCKLSTSEC005 section570EXTCKLSTSEC005 {
        get {
            if (_section570EXTCKLSTSEC005 == null) {
               _section570EXTCKLSTSEC005 = new Section570EXTCKLSTSEC005(this);
            }

            return _section570EXTCKLSTSEC005;
        }
    }
    #endregion [-- Section570EXTCKLSTSEC005 --]
    
    #region [-- Section570EXTCKLSTSEC006 --]
    private Section570EXTCKLSTSEC006 _section570EXTCKLSTSEC006;
    public Section570EXTCKLSTSEC006 section570EXTCKLSTSEC006 {
        get {
            if (_section570EXTCKLSTSEC006 == null) {
               _section570EXTCKLSTSEC006 = new Section570EXTCKLSTSEC006(this);
            }

            return _section570EXTCKLSTSEC006;
        }
    }
    #endregion [-- Section570EXTCKLSTSEC006 --]
    
    #region [-- Section570EXTCKLSTSEC007 --]
    private Section570EXTCKLSTSEC007 _section570EXTCKLSTSEC007;
    public Section570EXTCKLSTSEC007 section570EXTCKLSTSEC007 {
        get {
            if (_section570EXTCKLSTSEC007 == null) {
               _section570EXTCKLSTSEC007 = new Section570EXTCKLSTSEC007(this);
            }

            return _section570EXTCKLSTSEC007;
        }
    }
    #endregion [-- Section570EXTCKLSTSEC007 --]
    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section570_Ext_Pipe_F";

    public Section570_Ext_Pipe_F(DataModelItem parent) : base(parent)
    {
            
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           section570EXTCKLSTSEC001,
           section570EXTCKLSTSEC002,
           section570EXTCKLSTSEC003,
           section570EXTCKLSTSEC004,
           section570EXTCKLSTSEC005,
           section570EXTCKLSTSEC006,
           section570EXTCKLSTSEC007,
        };
    }
  }
}
