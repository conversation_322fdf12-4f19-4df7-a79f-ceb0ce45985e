﻿using System;
using System.Collections.Generic;

namespace CheckListGen
{
  public class Section : IChildItem
  {
    public String Name;
    public String DataName;
    public String DisplayName;
    
    public bool IsCollection = false;
    public bool IsInline = false;

    public String[] Decorators;

    public String DartClassName => "Section" + Helpers.CleanupVariableName(DataName);
    public String DartUIClassName => "Section" + Helpers.CleanupVariableName(DataName) + "Page";
    public String DartVariableName => "section" + Helpers.CleanupVariableName(DataName);
    
    public double Order { get; set; }

    public List<IChildItem> Children = new List<IChildItem>();
    
  }
}