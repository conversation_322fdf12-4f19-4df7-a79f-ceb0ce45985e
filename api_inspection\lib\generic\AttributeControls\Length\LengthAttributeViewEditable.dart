import 'package:api_inspection/app/batch_helper.dart';
import 'package:api_inspection/generic/Common/Types/Length.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/LengthAttribute.dart';
import 'package:api_inspection/generic/UIControls/NumericTextField.dart';
import 'package:api_inspection/generic/UIControls/listener.dart';
import 'package:flutter/material.dart';

class LengthAttributeViewEditable extends StatefulWidget {
  final LengthAttribute _attribute;
  final ListenerWrapper updateListener;
  const LengthAttributeViewEditable(this._attribute, this.updateListener,
      {Key? key})
      : super(key: key);

  @override
  _LengthAttributeViewEditableState createState() =>
      _LengthAttributeViewEditableState();
}

class _LengthAttributeViewEditableState
    extends State<LengthAttributeViewEditable> {
  void updateAttributeValue() {
    TextEditingController? controller = _controller;

    if (controller != null) {
      double? parsedValue = double.tryParse(controller.text);
      var attr = widget._attribute;
      attr.setValue(parsedValue == null
          ? null
          : Length.fromUnit(parsedValue, attr.displayUnit));

      BatchHelper.saveAndCommit(attr);
    }
  }

  TextEditingController? _controller;

  bool initialized = false;
  void initialize() {
    if (initialized) return;
    initialized = true;
    LengthAttribute attribute = widget._attribute;
    attribute.addListener(onAttributeChanged);
  }

  bool areValuesEqual(String? str, double? value) {
    if (str == null && value == null) return true;
    if (str != null && str.isEmpty && value == null) return true;
    if (str == null || value == null) return false;

    double? strAsValue = double.tryParse(str);
    return strAsValue == value;
  }

  String doubleToString(double? value) {
    if (value == null) {
      return "";
    }
    return value.toString();
  }

  void onAttributeChanged() {
    setState(() {
      TextEditingController? controller = _controller;
      var attr = widget._attribute;
      double? attributeValue = attr.getValue()?.getInUnit(attr.displayUnit);
      if (controller != null &&
          !areValuesEqual(controller.text, attributeValue)) {
        controller.text =
            attributeValue == null ? "" : doubleToString(attributeValue);
      }
    });
  }

  @override
  void dispose() {
    widget.updateListener.removeListener(updateAttributeValue);
    LengthAttribute attribute = widget._attribute;
    attribute.removeListener(onAttributeChanged);
    _controller?.dispose();
    super.dispose();
  }

  @override
  void initState() {
    widget.updateListener.addListener(updateAttributeValue);
    super.initState();
  }

  @override
  void didUpdateWidget(covariant LengthAttributeViewEditable oldWidget) {
    oldWidget.updateListener.removeListener(updateAttributeValue);
    widget.updateListener.addListener(updateAttributeValue);
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    initialize();

    var attr = widget._attribute;

    _controller ??= TextEditingController(
        text: doubleToString(attr.getValue()?.getInUnit(attr.displayUnit)));

    var textFieldwithUnits = Row(
      children: [
        Expanded(
          child: NumericTextField(
              _controller!, widget._attribute.allowNegatives, true,
              (String? value) {
            updateAttributeValue();
          }),
        ),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              widget._attribute.displayUnit.toString(),
              style: const TextStyle(color: Colors.white, fontSize: 16),
              textAlign: TextAlign.end,
            ),
          ),
        )
      ],
    );

    List<Widget> columnChildren = [
      Text(
        widget._attribute.displayName,
        style: const TextStyle(color: Colors.white, fontSize: 16),
        textAlign: TextAlign.start,
      ),
      textFieldwithUnits
    ];

    return Container(
      margin: const EdgeInsets.fromLTRB(20, 10, 20, 10),
      child: Focus(
          onFocusChange: (hasFocus) {
            updateAttributeValue();
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: columnChildren,
          )),
    );
  }
}
