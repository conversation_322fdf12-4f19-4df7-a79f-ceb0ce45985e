//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionAsset_Walkdown_Access_F
{
  public class SectionCorrosion : DataModelItem {

    public override String DisplayName { 
      get {
        return "Corrosion";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public MultiPredefinedValueAttribute attributeCorrosion_identified;
    public MultiPredefinedValueAttribute attributeCorrosion_removal_recommendation;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionCorrosion";

    public SectionCorrosion(DataModelItem parent) : base(parent)
    {
            
        attributeCorrosion_identified = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Rust", null),
          new PredefinedValueOption("Scale", null),
          new PredefinedValueOption("Oxidation", null),
          new PredefinedValueOption("None", null)
        }, true, this, "Corrosion identified?", databaseName: "AWA_Q121"); 
     
        attributeCorrosion_removal_recommendation = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Wire Brush", null),
          new PredefinedValueOption("Power Tools", null),
          new PredefinedValueOption("Sandblasting", null),
          new PredefinedValueOption("None", null)
        }, true, this, "Corrosion removal recommendation", databaseName: "AWA_Q122"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeCorrosion_identified,
           attributeCorrosion_removal_recommendation,
        };
    }
  }
}
