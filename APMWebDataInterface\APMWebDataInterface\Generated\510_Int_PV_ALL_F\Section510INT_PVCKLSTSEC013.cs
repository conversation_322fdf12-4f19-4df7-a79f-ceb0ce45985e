//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Int_PV_ALL_F
{
  public class Section510INT_PVCKLSTSEC013 : DataModelItem {

    public override String DisplayName { 
      get {
        return "LININGS";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC013Q001;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC013Q002;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC013Q003;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC013Q004;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC013Q005;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC013Q006;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC013Q007;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC013Q008;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510INT_PVCKLSTSEC013";

    public Section510INT_PVCKLSTSEC013(DataModelItem parent) : base(parent)
    {
            
        attribute510INT_PVCKLSTSEC013Q001 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Does vessel have a lining system installed:  (Liner material shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC013_Q001"); 
     
        attribute510INT_PVCKLSTSEC013Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Crack(s)", null),
          new PredefinedValueOption("Yes: Hole(s)", null)
        }, false, this, "Do metallic linings have indications of corrosion, holes, or cracks:  (The dimensions and location of any damage shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC013_Q002"); 
     
        attribute510INT_PVCKLSTSEC013Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Buckling", null),
          new PredefinedValueOption("Yes: Bulging", null)
        }, false, this, "Do metallic linings have indications of bulging, or buckling:  (The dimensions and location of any damage shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC013_Q003"); 
     
        attribute510INT_PVCKLSTSEC013Q004 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Holidays", null),
          new PredefinedValueOption("Yes: Bulging", null),
          new PredefinedValueOption("Yes: Blistering", null),
          new PredefinedValueOption("Yes: Cracking", null),
          new PredefinedValueOption("Yes: Chipping", null)
        }, false, this, "Do non-metallic linings have indications of holidays, bulging, blistering, or chipping:  (The dimensions and locations of any damage shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC013_Q004"); 
     
        attribute510INT_PVCKLSTSEC013Q005 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Cracking", null),
          new PredefinedValueOption("Yes: Spalling", null)
        }, false, this, "Do concrete or refractory linings have indications of spalling or cracking:  (The dimensions and locations of any damage shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC013_Q005"); 
     
        attribute510INT_PVCKLSTSEC013Q006 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Broken", null),
          new PredefinedValueOption("Yes: Missing", null)
        }, false, this, "Do refractory linings have broken or missing tiles or bricks:  (The dimensions and locations of any damage shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC013_Q006"); 
     
        attribute510INT_PVCKLSTSEC013Q007 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is corrosion beneath the lining suspected:  (The dimensions and locations of any damage shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC013_Q007"); 
     
        attribute510INT_PVCKLSTSEC013Q008 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the lining system in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC013_Q008"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute510INT_PVCKLSTSEC013Q001,
           attribute510INT_PVCKLSTSEC013Q002,
           attribute510INT_PVCKLSTSEC013Q003,
           attribute510INT_PVCKLSTSEC013Q004,
           attribute510INT_PVCKLSTSEC013Q005,
           attribute510INT_PVCKLSTSEC013Q006,
           attribute510INT_PVCKLSTSEC013Q007,
           attribute510INT_PVCKLSTSEC013Q008,
        };
    }
  }
}
