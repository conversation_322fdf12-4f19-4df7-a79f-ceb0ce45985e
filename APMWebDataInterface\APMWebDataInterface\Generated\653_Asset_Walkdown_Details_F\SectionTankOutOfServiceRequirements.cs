//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section653_Asset_Walkdown_Details_F
{
  public class SectionTankOutOfServiceRequirements : DataModelItem {

    public override String DisplayName { 
      get {
        return "Tank Out-Of-Service Requirements";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute653AWQ331;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionTankOutOfServiceRequirements";

    public SectionTankOutOfServiceRequirements(DataModelItem parent) : base(parent)
    {
            
        attribute653AWQ331 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("N/A", null)
        }, false, this, "Is the tank Out-Of-Service In accordance with Jurisdictional requirements?", databaseName: "653AW_Q331"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute653AWQ331,
        };
    }
  }
}
