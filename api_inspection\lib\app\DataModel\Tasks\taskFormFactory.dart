import 'package:api_inspection/app/DataModel/Generated/510_Asset_Walkdown_Details_F/Section510_Asset_Walkdown_Details_F.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/Generated/510_Asset_Walkdown_Details_F/Section510_Asset_Walkdown_Details_FPage.dart';
import 'package:api_inspection/app/DataModel/Generated/510_Ext_PV_ALL_F/Section510_Ext_PV_ALL_F.dart';
import 'package:api_inspection/app/DataModel/Generated/510_Int_PV_ALL_F/Section510_Int_PV_ALL_F.dart';
import 'package:api_inspection/app/DataModel/Generated/570_Ext_Pipe_F/Section570_Ext_Pipe_F.dart';
import 'package:api_inspection/app/DataModel/Generated/510_Ext_PV_ALL_F/Section510_Ext_PV_ALL_FPage.dart';
import 'package:api_inspection/app/DataModel/Generated/510_Int_PV_ALL_F/Section510_Int_PV_ALL_FPage.dart';
import 'package:api_inspection/app/DataModel/Generated/570_Asset_Walkdown_Details_F/Section570_Asset_Walkdown_Details_F.dart';
import 'package:api_inspection/app/DataModel/Generated/570_Asset_Walkdown_Details_F/Section570_Asset_Walkdown_Details_FPage.dart';
import 'package:api_inspection/app/DataModel/Generated/570_Ext_Pipe_F/Section570_Ext_Pipe_FPage.dart';
import 'package:api_inspection/app/DataModel/Generated/653_Asset_Walkdown_Details_F/Section653_Asset_Walkdown_Details_F.dart';
import 'package:api_inspection/app/DataModel/Generated/653_Asset_Walkdown_Details_F/Section653_Asset_Walkdown_Details_FPage.dart';

class TaskFormFactory {
  static Widget buildTaskDataForm(DataModelItem model) {
    if (model is Section510_Asset_Walkdown_Details_F) {
      return Section510_Asset_Walkdown_Details_FPage(model);
    }
    if (model is Section570_Asset_Walkdown_Details_F) {
      return Section570_Asset_Walkdown_Details_FPage(model);
    }
    if (model is Section653_Asset_Walkdown_Details_F) {
      return Section653_Asset_Walkdown_Details_FPage(model);
    }

    if (model is Section510_Ext_PV_ALL_F) {
      return Section510_Ext_PV_ALL_FPage(model);
    }
    if (model is Section570_Ext_Pipe_F) return Section570_Ext_Pipe_FPage(model);

    if (model is Section510_Int_PV_ALL_F) {
      return Section510_Int_PV_ALL_FPage(model);
    }

    return Container();
  }
}
