//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Int_PV_ALL_F
{
  public class Section510INT_PVCKLSTSEC010 : DataModelItem {

    public override String DisplayName { 
      get {
        return "INTERNAL PACKING";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC010Q001;
    public PredefinedValueAttribute attributeWas_packing_removed_prior_to_inspection;
    public PredefinedValueAttribute attributeWas_packing_in_acceptable_condition_for_continued_service;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC010Q004;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC010Q005;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC010Q006;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC010Q007;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC010Q008;
    public PredefinedValueAttribute attributeAre_there_indications_of_packing_collapse_or_break_up;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC010Q010;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC010Q011;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC010Q012;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC010Q013;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC010Q014;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510INT_PVCKLSTSEC010";

    public Section510INT_PVCKLSTSEC010(DataModelItem parent) : base(parent)
    {
            
        attribute510INT_PVCKLSTSEC010Q001 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Does the vessel operate with internal packing: (Note: The type of packing shall be recorded below as random, structured, grid-style etc.)", databaseName: "510_INT-PV_CKLST_SEC010_Q001"); 
     
        attributeWas_packing_removed_prior_to_inspection = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was packing removed prior to inspection:", databaseName: "510_INT-PV_CKLST_SEC010_Q002"); 
     
        attributeWas_packing_in_acceptable_condition_for_continued_service = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was packing in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC010_Q003"); 
     
        attribute510INT_PVCKLSTSEC010Q004 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are collector / chimney trays / redistributors in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC010_Q004"); 
     
        attribute510INT_PVCKLSTSEC010Q005 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are draw sumps and draw nozzles in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC010_Q005"); 
     
        attribute510INT_PVCKLSTSEC010Q006 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are accessible support ring attachment welds in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC010_Q006"); 
     
        attribute510INT_PVCKLSTSEC010Q007 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are accessible packing support grid plates and hold-down grids in acceptable condition for continued service: (I.e. intact, positioned correctly, and appropriately clamped to the support ring)", databaseName: "510_INT-PV_CKLST_SEC010_Q007"); 
     
        attribute510INT_PVCKLSTSEC010Q008 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are bed packing limiters and attachment hardware in acceptable condition for continued service: (The distance from the packing to the bed limiter should be recorded)", databaseName: "510_INT-PV_CKLST_SEC010_Q008"); 
     
        attributeAre_there_indications_of_packing_collapse_or_break_up = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are there indications of packing collapse or break up:", databaseName: "510_INT-PV_CKLST_SEC010_Q009"); 
     
        attribute510INT_PVCKLSTSEC010Q010 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is demister pad retention grid & attachment hardware in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC010_Q010"); 
     
        attribute510INT_PVCKLSTSEC010Q011 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was fouling observed of the demister pad: (Please note the degree of any fouling observed)", databaseName: "510_INT-PV_CKLST_SEC010_Q011"); 
     
        attribute510INT_PVCKLSTSEC010Q012 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was demister pad bypass observed: (If yes, thickness readings or other NDE should be conducted at the impingement areas impacted by the demister pad bypass)", databaseName: "510_INT-PV_CKLST_SEC010_Q012"); 
     
        attribute510INT_PVCKLSTSEC010Q013 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are vortex breakers in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC010_Q013"); 
     
        attribute510INT_PVCKLSTSEC010Q014 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are packing components in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC010_Q014"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute510INT_PVCKLSTSEC010Q001,
           attributeWas_packing_removed_prior_to_inspection,
           attributeWas_packing_in_acceptable_condition_for_continued_service,
           attribute510INT_PVCKLSTSEC010Q004,
           attribute510INT_PVCKLSTSEC010Q005,
           attribute510INT_PVCKLSTSEC010Q006,
           attribute510INT_PVCKLSTSEC010Q007,
           attribute510INT_PVCKLSTSEC010Q008,
           attributeAre_there_indications_of_packing_collapse_or_break_up,
           attribute510INT_PVCKLSTSEC010Q010,
           attribute510INT_PVCKLSTSEC010Q011,
           attribute510INT_PVCKLSTSEC010Q012,
           attribute510INT_PVCKLSTSEC010Q013,
           attribute510INT_PVCKLSTSEC010Q014,
        };
    }
  }
}
