//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionPPERequirements.dart';

// ignore: camel_case_types
class SectionPPERequirementsPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionPPERequirements sectionPPERequirements;

  const SectionPPERequirementsPage(this.sectionPPERequirements, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionPPERequirementsPageState();
  }
}

class _SectionPPERequirementsPageState
    extends State<SectionPPERequirementsPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionPPERequirements,
        title: "PPE Requirements",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionPPERequirements.attributeHard_Hat
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionPPERequirements.attributeEye_Protection
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionPPERequirements.attributeHearing_Protection
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionPPERequirements.attributeFire_Retardant_Clothing
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionPPERequirements.attributeSafety_Gloves
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionPPERequirements.attributeSnake_Chaps
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionPPERequirements.attributeFoot_Protection
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionPPERequirements.attributeChemical_Suit
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionPPERequirements.attributeFall_Protection
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionPPERequirements.attributeBreathing_Protection
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionPPERequirements.attributeAtmosphere
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
