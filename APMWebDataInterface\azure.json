{"hosting": {"provider": "azure", "public": "public", "ignore": ["azure.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}, "storage": {"provider": "azure", "account": "apmstorageaccount", "containers": ["inspections", "assets", "reports"]}, "database": {"provider": "azure", "service": "cosmosdb", "endpoint": "https://apm-cosmosdb.documents.azure.com:443/", "databaseId": "apm-database", "containers": [{"id": "inspections", "partitionKey": "/id"}, {"id": "assets", "partitionKey": "/id"}, {"id": "users", "partitionKey": "/id"}]}, "functions": {"provider": "azure", "runtime": "dotnet", "location": "API_Inspection_ImageFunctions"}, "authentication": {"provider": "azure", "service": "b2c", "tenant": "apmb2c.onmicrosoft.com", "policies": {"signUpSignIn": "B2C_1_signupsignin1", "resetPassword": "B2C_1_reset", "editProfile": "B2C_1_edit_profile"}}, "monitoring": {"provider": "azure", "service": "application-insights", "instrumentationKey": "YOUR_APP_INSIGHTS_KEY"}}