import 'package:api_inspection/generic/AttributeControls/Length/LengthAttributeViewEditable.dart';
import 'package:api_inspection/generic/AttributeControls/Length/LengthAttributeViewNonEditable.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/LengthAttribute.dart';
import 'package:flutter/material.dart';

import '../AttributeStructure.dart';

class LengthAttributeView extends StatefulWidget {
  final LengthAttribute _attribute;
  final IsEditableController? editingController;
  final bool showPhotos;
  final bool showComments;

  const LengthAttributeView(this._attribute,
      {Key? key,
      this.editingController,
      this.showPhotos = true,
      this.showComments = true})
      : super(key: key);

  @override
  _LengthAttributeViewState createState() => _LengthAttributeViewState();
}

class _LengthAttributeViewState extends State<LengthAttributeView> {
  @override
  Widget build(BuildContext context) {
    return AttributeStructure(widget._attribute,
        showPhotos: widget.showPhotos,
        showComments: widget.showComments,
        editingController: widget.editingController,
        editingBuilder: (context, updateListener) {
      return LengthAttributeViewEditable(widget._attribute, updateListener);
    }, nonEditingBuilder: (context) {
      return LengthAttributeViewNonEditable(widget._attribute);
    });
  }
}
