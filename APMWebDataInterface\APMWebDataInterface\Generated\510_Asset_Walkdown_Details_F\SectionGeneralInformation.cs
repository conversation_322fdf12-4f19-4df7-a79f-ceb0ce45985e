//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Asset_Walkdown_Details_F
{
  public class SectionGeneralInformation : DataModelItem {

    public override String DisplayName { 
      get {
        return "General Information";
      }
    }

    #region [-- Sub Sections --]

    #region [-- SectionPhotos --]
    private SectionPhotos _sectionPhotos;
    public SectionPhotos sectionPhotos {
        get {
            if (_sectionPhotos == null) {
               _sectionPhotos = new SectionPhotos(this);
            }

            return _sectionPhotos;
        }
    }
    #endregion [-- SectionPhotos --]
    
    #region [-- SectionDesign --]
    private SectionDesign _sectionDesign;
    public SectionDesign sectionDesign {
        get {
            if (_sectionDesign == null) {
               _sectionDesign = new SectionDesign(this);
            }

            return _sectionDesign;
        }
    }
    #endregion [-- SectionDesign --]
    
    #region [-- SectionInspection --]
    private SectionInspection _sectionInspection;
    public SectionInspection sectionInspection {
        get {
            if (_sectionInspection == null) {
               _sectionInspection = new SectionInspection(this);
            }

            return _sectionInspection;
        }
    }
    #endregion [-- SectionInspection --]
    
    #region [-- SectionDataPlate --]
    private SectionDataPlate _sectionDataPlate;
    public SectionDataPlate sectionDataPlate {
        get {
            if (_sectionDataPlate == null) {
               _sectionDataPlate = new SectionDataPlate(this);
            }

            return _sectionDataPlate;
        }
    }
    #endregion [-- SectionDataPlate --]
    
    #region [-- SectionManufacturer --]
    private SectionManufacturer _sectionManufacturer;
    public SectionManufacturer sectionManufacturer {
        get {
            if (_sectionManufacturer == null) {
               _sectionManufacturer = new SectionManufacturer(this);
            }

            return _sectionManufacturer;
        }
    }
    #endregion [-- SectionManufacturer --]
    
    #region [-- SectionService --]
    private SectionService _sectionService;
    public SectionService sectionService {
        get {
            if (_sectionService == null) {
               _sectionService = new SectionService(this);
            }

            return _sectionService;
        }
    }
    #endregion [-- SectionService --]
    
    #region [-- SectionInspectionOpenings --]
    private DataModelCollection<SectionInspectionOpenings> _sectionInspectionOpenings;
    public DataModelCollection<SectionInspectionOpenings> sectionInspectionOpenings {
        get {
            if (_sectionInspectionOpenings == null) {
              _sectionInspectionOpenings = new DataModelCollection<SectionInspectionOpenings>("Inspection Openings", (parent, entry) => {
                 return new SectionInspectionOpenings(entry.Key, _sectionInspectionOpenings);
              }, (parent, id) => {
                return new SectionInspectionOpenings(id, _sectionInspectionOpenings);
              }, this);
            }

            return _sectionInspectionOpenings;
        }
    }
    #endregion [-- SectionInspectionOpenings --]
    
    #region [-- SectionRepairRecord --]
    private SectionRepairRecord _sectionRepairRecord;
    public SectionRepairRecord sectionRepairRecord {
        get {
            if (_sectionRepairRecord == null) {
               _sectionRepairRecord = new SectionRepairRecord(this);
            }

            return _sectionRepairRecord;
        }
    }
    #endregion [-- SectionRepairRecord --]
    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributeOrientation;
    public PredefinedValueAttribute attributeRT;
    public DateAttribute attributeInstallation_Date;
    public DateAttribute attributeIn_service_Date;
    public StringAttribute attributePID_Number;
    public StringAttribute attributeConstructionDesign_Drawing_Number;
    public PredefinedValueAttribute attributeLowest_Flange_Rating;
    public IntegerAttribute attributeHydro_Test_Pressure;
    public PredefinedValueAttribute attributeType_of_construction;
    public PredefinedValueAttribute attributePost_Weld_Heat_Treatment;
    public PredefinedValueAttribute attributeHas_the_equipment_been_de_rated_or_re_rated;
    public PredefinedValueAttribute attributeIs_this_a_fired_pressure_vessel;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionGeneralInformation";

    public SectionGeneralInformation(DataModelItem parent) : base(parent)
    {
            
        attributeOrientation = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Horizontal", null),
          new PredefinedValueOption("Vertical", null)
        }, false, this, "Orientation", databaseName: "510AW_Q140"); 
     
        attributeRT = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Spot", null),
          new PredefinedValueOption("Full", null),
          new PredefinedValueOption("None", null)
        }, false, this, "RT", databaseName: "510AW_Q141"); 
     
        attributeInstallation_Date = new DateAttribute(this, displayName: "Installation Date", databaseName: "510AW_Q142", areCommentsRequired: false); 
     
        attributeIn_service_Date = new DateAttribute(this, displayName: "In-service Date", databaseName: "510AW_Q143", areCommentsRequired: false); 
     
        attributePID_Number = new StringAttribute(this, displayName: "P&ID Number", databaseName: "510AW_Q144"); 
     
        attributeConstructionDesign_Drawing_Number = new StringAttribute(this, displayName: "Construction/Design Drawing Number", databaseName: "510AW_Q145"); 
     
        attributeLowest_Flange_Rating = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("150", null),
          new PredefinedValueOption("300", null),
          new PredefinedValueOption("400", null),
          new PredefinedValueOption("600", null),
          new PredefinedValueOption("900", null),
          new PredefinedValueOption("1500", null),
          new PredefinedValueOption("2500", null)
        }, false, this, "Lowest Flange Rating", databaseName: "510AW_Q146"); 
     
        attributeHydro_Test_Pressure = new IntegerAttribute(this, displayName: "Hydro Test Pressure", databaseName: "510AW_Q147", areCommentsRequired: false, displayUnit: null, allowNegatives: true); 
     
        attributeType_of_construction = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Welded", null),
          new PredefinedValueOption("Presssure Welded", null),
          new PredefinedValueOption("Brazed", null),
          new PredefinedValueOption("Resistance Welded", null)
        }, true, this, "Type of construction", databaseName: "510AW_Q148"); 
     
        attributePost_Weld_Heat_Treatment = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Unknown", null)
        }, false, this, "Post Weld Heat Treatment", databaseName: "510AW_Q149"); 
     
        attributeHas_the_equipment_been_de_rated_or_re_rated = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Unknown", null)
        }, false, this, "Has the equipment been de-rated or re-rated?", databaseName: "510AW_Q150"); 
     
        attributeIs_this_a_fired_pressure_vessel = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Is this a fired pressure vessel?", databaseName: "510AW_Q151"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           sectionPhotos,
           sectionDesign,
           sectionInspection,
           sectionDataPlate,
           sectionManufacturer,
           sectionService,
           sectionInspectionOpenings,
           sectionRepairRecord,
           attributeOrientation,
           attributeRT,
           attributeInstallation_Date,
           attributeIn_service_Date,
           attributePID_Number,
           attributeConstructionDesign_Drawing_Number,
           attributeLowest_Flange_Rating,
           attributeHydro_Test_Pressure,
           attributeType_of_construction,
           attributePost_Weld_Heat_Treatment,
           attributeHas_the_equipment_been_de_rated_or_re_rated,
           attributeIs_this_a_fired_pressure_vessel,
        };
    }
  }
}
