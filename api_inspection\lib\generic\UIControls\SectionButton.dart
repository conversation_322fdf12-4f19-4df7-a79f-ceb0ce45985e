import 'package:api_inspection/app/batch_helper.dart';
import 'package:api_inspection/generic/AppStyle.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/AttributeBase.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/UIControls/PositionedTapDetector.dart';
import 'package:flutter/material.dart';

import 'SectionPage.dart';

class TeamSectionButton extends StatefulWidget {
  final DataModelSection section;

  final Function() onPressed;

  const TeamSectionButton(
      {Key? key, required this.section, required this.onPressed})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return TeamSectionButtonState();
  }
}

class TeamSectionButtonState extends State<TeamSectionButton> {
  Widget buildStatus() {
    var section = widget.section;
    var childrenAttrOrSection = section.getChildren().where((a) {
      return (a is DataModelSection || a is AttributeBase) &&
          a != section.sectionState;
    });

    int numberOfChildren = childrenAttrOrSection.length;
    int numberOfCompletedChildren = 0;
    // ignore: unused_local_variable
    int numberOfPartialChildren = 0;
    for (var child in childrenAttrOrSection) {
      if (child is DataModelSection) {
        var sectionState = child.sectionState.getValue();
        if (sectionState == "Completed" || sectionState == "Skipped") {
          numberOfCompletedChildren++;
        } else if (sectionState == "In Progress") {
          numberOfPartialChildren++;
        }
      } else if (child is AttributeBase) {
        if (child.hasData()) numberOfCompletedChildren++;
      }
    }

    if (section.sectionState.getValue() == "Skipped") {
      return Container(
        alignment: Alignment.center,
        color: Colors.blueGrey[800],
        child: const Text("Skipped",
            style: TextStyle(color: Colors.white, fontSize: 16)),
      );
    }
    if (section.sectionState.getValue() == "Completed") {
      return Container(
          color: const Color.fromARGB(255, 88, 118, 95),
          child: Column(mainAxisAlignment: MainAxisAlignment.center, children: [
            const Text("Completed",
                style: TextStyle(color: Colors.white, fontSize: 16)),
            Text(
                numberOfCompletedChildren.toString() +
                    "/" +
                    numberOfChildren.toString(),
                style: const TextStyle(color: Colors.white, fontSize: 16)),
          ]));
    }

    if (section is! DataModelCollection && numberOfCompletedChildren == 0) {
      return Container();
    }

    return Container(
      alignment: Alignment.center,
      child: Text(
          numberOfCompletedChildren.toString() +
              "/" +
              numberOfChildren.toString(),
          style: const TextStyle(color: Colors.white, fontSize: 16)),
    );
  }

  void setCompletedClicked() {
    int unanswered = SectionUtils.getUnansweredQuestions(widget.section);
    if (unanswered > 0) {
      showIncompleteSectionDialog(unanswered, false);
    } else {
      setState(() {
        BatchHelper.saveAndCommitStringAttribute(
            widget.section.sectionState, "Completed");
      });
    }
  }

  void setSkippedClicked() {
    int unanswered = SectionUtils.getUnansweredQuestions(widget.section);
    if (unanswered > 0) {
      showIncompleteSectionDialog(unanswered, true);
    } else {
      setState(() {
        BatchHelper.saveAndCommitStringAttribute(
            widget.section.sectionState, "Skipped");
      });
    }
  }

  void setInProgressClicked() {
    setState(() {
      BatchHelper.saveAndCommitStringAttribute(
          widget.section.sectionState, "In Progress");
    });
  }

  void stateWidgetClicked(TapPosition position) async {
    var overlayPosition = RelativeRect.fromSize(
        Rect.fromLTWH(position.global.dx, position.global.dy, 48, 48),
        const Size(50, 100));

    var currentState = widget.section.sectionState.getValue();
    if (currentState == null ||
        currentState == "Not Started" ||
        currentState == "In Progress") {
      final menuItem = await showMenu<int>(
          context: context,
          position: overlayPosition,
          items: [
            const PopupMenuItem(child: Text('Skipped'), value: 1),
            const PopupMenuItem(child: Text('Completed'), value: 2),
          ]);

      switch (menuItem) {
        case 1:
          setSkippedClicked();
          break;
        case 2:
          setCompletedClicked();
          break;
      }
    }
    if (currentState == "Completed") {
      final menuItem = await showMenu<int>(
          context: context,
          position: overlayPosition,
          items: [
            const PopupMenuItem(child: Text('In Progress'), value: 1),
            const PopupMenuItem(child: Text('Skipped'), value: 2),
          ]);

      switch (menuItem) {
        case 1:
          setInProgressClicked();
          break;
        case 2:
          setSkippedClicked();
          break;
      }
    }
    if (currentState == "Skipped") {
      final menuItem = await showMenu<int>(
          context: context,
          position: overlayPosition,
          items: [
            const PopupMenuItem(child: Text('In Progress'), value: 1),
            const PopupMenuItem(child: Text('Completed'), value: 2),
          ]);

      switch (menuItem) {
        case 1:
          setInProgressClicked();
          break;
        case 2:
          setCompletedClicked();
          break;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    var color = const Color.fromARGB(255, 41, 45, 52);
    var borderColor = const Color.fromARGB(255, 122, 122, 122);

    TextButton button = TextButton(
      onPressed: () {
        SectionUtils.shouldWarn = false;
        widget.onPressed.call();
      },
      child: Container(
          constraints:
              BoxConstraints(minHeight: AppStyle.global.expandButtonSize),
          decoration: BoxDecoration(
            color: color,
            borderRadius: const BorderRadius.all(Radius.circular(8)),
            border: Border.all(
              color: borderColor,
              width: 1,
            ),
          ),
          margin: const EdgeInsets.fromLTRB(0, 0, 0, 0),
          alignment: Alignment.center,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                  child: Container(
                constraints:
                    BoxConstraints(maxHeight: AppStyle.global.pixels80),
                margin: const EdgeInsets.all(10),
                child: Text(
                  widget.section.sectionName,
                  style: const TextStyle(color: Colors.white, fontSize: 16),
                ),
              )),
              PositionedTapDetector(
                  onTap: stateWidgetClicked,
                  child: Container(
                      width: AppStyle.global.pixels100 + 15,
                      height: AppStyle.global.pixels80,
                      color: borderColor,
                      child: buildStatus()))
            ],
          )),
    );
    return button;
  }

  void showIncompleteSectionDialog(int unansweredQuestions, bool isSkipping) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color.fromARGB(255, 41, 45, 52),
          title: const Text(
            'Incomplete section',
            style: TextStyle(color: Colors.white),
          ),
          content: unansweredQuestions == 1
              ? const Text("This section has one unanswered question.",
                  style: TextStyle(color: Colors.white))
              : Text(
                  "This section has " +
                      unansweredQuestions.toString() +
                      " unanswered questions.",
                  style: const TextStyle(color: Colors.white)),
          actions: [
            TextButton(
              child: isSkipping
                  ? const Text('Skip Anyway',
                      style: TextStyle(color: Colors.white))
                  : const Text(
                      'Complete Anyway',
                      style: TextStyle(color: Colors.white),
                    ),
              onPressed: () {
                setState(() {
                  isSkipping
                      ? BatchHelper.saveAndCommitStringAttribute(
                          widget.section.sectionState, "Skipped")
                      : BatchHelper.saveAndCommitStringAttribute(
                          widget.section.sectionState, "Completed");
                  Navigator.of(context).pop();
                });
              },
            ),
            TextButton(
              child:
                  const Text('Cancel', style: TextStyle(color: Colors.white)),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            ElevatedButton(
              child: const Text('Edit', style: TextStyle(color: Colors.white)),
              onPressed: () {
                SectionUtils.shouldWarn = true;
                widget.onPressed.call();
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }
}
