//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionPermittingRequired.dart';
import 'SectionConfinedSpaceRequirementsPage.dart';

// ignore: camel_case_types
class SectionPermittingRequiredPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionPermittingRequired sectionPermittingRequired;

  const SectionPermittingRequiredPage(this.sectionPermittingRequired,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionPermittingRequiredPageState();
  }
}

class _SectionPermittingRequiredPageState
    extends State<SectionPermittingRequiredPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionPermittingRequired,
        title: "Permitting Required",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionPermittingRequired.attributeGeneral_Work
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionPermittingRequired.attributeGeneral_Hot_Work
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionPermittingRequired.attributeOpen_Flame_Hot_Work
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionPermittingRequired
                            .sectionConfinedSpaceRequirements,
                        onPressed: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      SectionConfinedSpaceRequirementsPage(widget
                                          .sectionPermittingRequired
                                          .sectionConfinedSpaceRequirements))).then(
                              (value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(widget
                      .sectionPermittingRequired.attributeControl_Area_of_Permit
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionPermittingRequired.attributeHazardous_Area_Permit
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionPermittingRequired
                      .attributeHazardous_Material_Permit
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
