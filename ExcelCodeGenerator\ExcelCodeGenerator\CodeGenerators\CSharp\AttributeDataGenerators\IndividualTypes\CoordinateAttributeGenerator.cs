﻿using CheckListGen;

namespace ExcelCodeGenerator.CodeGenerators.CSharp
{
    public class CoordinateAttributeGenerator : IAttributeGenerator
    {
        public string BuildInitialization(Question question)
        {
            return
                @"
        " + question.DartVariableName + @" = new LocationAttribute(this, displayName: """ + question.DisplayText +
                @""", databaseName: """ + question.DataName + @"""); ";
        }

        public string BuildDeclaration(Question question)
        {
            return
                @"public LocationAttribute " + question.DartVariableName + ";";
        }
    }
}