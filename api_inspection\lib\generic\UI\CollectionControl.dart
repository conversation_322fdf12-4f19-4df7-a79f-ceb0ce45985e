import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/UIControls/SideSlideoutControl.dart';
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
import 'package:api_inspection/generic/UIControls/attributePadding.dart';

class CollectionControl<T extends DataModelCollectionItem>
    extends StatefulWidget {
  final DataModelCollection<T> collection;
  final Widget Function(BuildContext, int, T) cardBuilder;
  final Widget Function(BuildContext, T) editPageBuilder;
  final T Function() createNewItem;

  final String cardTitle;

  const CollectionControl(
      {required this.collection,
      required this.cardBuilder,
      required this.editPageBuilder,
      required this.cardTitle,
      required this.createNewItem,
      Key? key})
      : super(key: key);

  @override
  _CollectionControlState<T> createState() => _CollectionControlState<T>();
}

class _CollectionControlState<T extends DataModelCollectionItem>
    extends State<CollectionControl<T>> {
  late SlideOutController controller = SlideOutController();

  @override
  void dispose() {
    widget.collection.removeListener(_onCollectionChanged);
    super.dispose();
  }

  @override
  void initState() {
    widget.collection.addListener(_onCollectionChanged);
    super.initState();
  }

  void _onCollectionChanged() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> widgets = [];
    int count = 1;
    for (var entry in widget.collection.getEntries()) {
      leftSliderWidgetBuilder(context) {
        return SizedBox(
            height: 110,
            width: 110,
            child: ElevatedButton(
                onPressed: () {
                  widget.collection.removeItem(entry);
                },
                child: const Text(
                  "Remove",
                  style: TextStyle(fontSize: 18),
                ),
                style: ButtonStyle(
                  backgroundColor: MaterialStateProperty.resolveWith<Color>(
                      (Set<MaterialState> states) {
                    return Colors.red[900]!;
                  }),
                )));
      }

      widgets.add(AttributePadding.WithStdPadding(SideSlideoutControl(
          controller: controller,
          leftSliderWidget: leftSliderWidgetBuilder,
          child: TeamToggleButton(
              borderColor: Colors.lime[900]!,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                      constraints: const BoxConstraints(maxWidth: 180),
                      margin: const EdgeInsets.fromLTRB(20, 0, 10, 0),
                      child: Text(widget.cardTitle,
                          style: const TextStyle(
                              color: Colors.white, fontSize: 18))),
                  Expanded(
                      child: Center(
                          child: widget.cardBuilder(context, count, entry)))
                ],
              ),
              onPressed: () {
                Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) =>
                                widget.editPageBuilder(context, entry)))
                    .then((value) => setState(() {}));
              },
              height: 110))));
      count++;
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(child: ListView(children: widgets)),
        Align(
            alignment: Alignment.centerRight,
            child: Container(
                margin: const EdgeInsets.all(10),
                height: 50,
                child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        var newItem = widget.createNewItem();
                        widget.collection.addItem(newItem);
                        var batch = FirebaseFirestore.instance.batch();
                        widget.collection.saveItem(batch);
                        batch.commit();
                        Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => widget
                                        .editPageBuilder(context, newItem)))
                            .then((value) => setState(() {}));
                      });
                    },
                    child: const Text("Create New"))))
      ],
    );
  }
}
