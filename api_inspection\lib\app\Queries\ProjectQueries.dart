import 'dart:async';
import 'dart:developer';

import 'package:api_inspection/app/APMRoot.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:api_inspection/app/DataModel/project.dart';
import 'package:api_inspection/generic/UIControls/listener.dart';
import 'package:collection/collection.dart';

import 'package:api_inspection/app/Queries/Queries.dart';

import '../../generic/AppRoot.dart';

class ProjectQueries {
  Future dispose() async {
    for (var element in projectsSubscriptions) {
      await element.cancel();
    }
    projectsListener.dispose();
  }

  void initialize(Queries parent) {
    startProjectsQuery();
  }

  List<StreamSubscription<QuerySnapshot<Map<String, dynamic>>>>
      projectsSubscriptions = [];

  List<Project> projects = [];
  ListenerWrapper projectsListener = ListenerWrapper();

  void startProjectsQuery() {
    var databaseReference = FirebaseFirestore.instance;

    final buIdBatches = AppRoot.global().businessIdsBatches;
    if (buIdBatches != null) {
      for (var buIdBatch in buIdBatches) {
        projectsSubscriptions.add(
          databaseReference
              .collection('projects')
              .where('BusinessUnitId.Value', whereIn: buIdBatch.toList())
              .snapshots()
              .listen(onProjectsUpdated, onError: (error) {
            log('error with projects subscription', error: error);
          }),
        );
      }
    }
  }

  void onProjectsUpdated(QuerySnapshot<Map<String, dynamic>> snapshot) {
    bool addedRemovedProjects = false;
    for (var entry in snapshot.docs) {
      var entryData = entry.data();
      var matchingProject =
          projects.firstWhereOrNull((element) => element.id == entry.id);
      if (matchingProject != null) {
        var change = snapshot.docChanges
            .firstWhereOrNull((element) => element.doc.id == entry.id);
        if (change != null) {
          if (change.type == DocumentChangeType.added ||
              change.type == DocumentChangeType.modified) {
            // Update our local cache
            matchingProject.updateFromMap(entryData);
          } else {
            log('We do not support data removal, yet a document with id ${change.doc.id} was removed.');
          }
        }
      } else {
        if (!entryData.containsKey("LocationId")) {
          continue;
        }
        var newProject = Project(entry.id, entryData["LocationId"] as String);
        newProject.updateFromMap(entryData);
        newProject.setShouldDownloadPhotos(false);
        projects.add(newProject);
        addedRemovedProjects = true;
      }
    }
    var len = projects.length;
    projects = projects.toSet().toList();
    if (projects.length != len) {
      addedRemovedProjects = true;
    }
    if (addedRemovedProjects) {
      APMRoot.global.queries.selectedProjects.initailize();
    }
    projectsListener.notifyListeners();
  }
}
