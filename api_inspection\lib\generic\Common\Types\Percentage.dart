class Percentage {
  late double? value;

  Percentage.from0To1(double number) {
    value = number;
  }

  Percentage.from0To100(double number) {
    value = number / 100.0;
  }

  double? get value0To100 {
    if (value == null) return null;
    return value! * 100;
  }

  double? get value0To1 {
    return value;
  }

  
  @override
  bool operator ==(Object other) =>
      other is Percentage &&
      other.runtimeType == runtimeType &&
      other.value == value;

  @override
  int get hashCode => value.hashCode;
}
