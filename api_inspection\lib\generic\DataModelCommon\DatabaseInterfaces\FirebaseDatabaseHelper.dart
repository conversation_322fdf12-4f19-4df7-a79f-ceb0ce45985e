import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/ChangeLog/ChangeLogEntry.dart';

import 'package:flutter/foundation.dart' show kIsWeb;
import 'IDatabaseHelper.dart';

class FirebaseDatabaseHelper implements IDatabaseHelper {
  final databaseReference = FirebaseFirestore.instance;

  FirebaseDatabaseHelper() {
    if (kIsWeb) {
      FirebaseFirestore.instance.enablePersistence();
    }
  }

  @override
  void updateItem(String path, Object? value, WriteBatch batch) async {
    var split = path.split('.');
    String collection = split[0];
    String key = split[1];

    var documentPath = split.skip(2);

    Map<String, dynamic> rootMap = {};
    Map<String, dynamic> map = rootMap;

    for (var path in documentPath.take(documentPath.length - 1)) {
      Map<String, dynamic> newMap = {};
      map[path] = newMap;
      map = newMap;
    }

    map[documentPath.last] = value;

    var docRef = FirebaseFirestore.instance.collection(collection).doc(key);
    batch.set(docRef, rootMap, SetOptions(merge: true));
  }

  @override
  void updateProperties(
      String path, Map<String, dynamic> updates, WriteBatch batch) async {
    var split = path.split('.');
    String collection = split[0];
    String key = split[1];

    var documentPath = split.skip(2);

    Map<String, dynamic> rootMap = {};
    Map<String, dynamic> map = rootMap;

    if (documentPath.isEmpty) {
      rootMap = updates;
    } else {
      for (var path in documentPath.take(documentPath.length - 1)) {
        Map<String, dynamic> newMap = {};
        map[path] = newMap;
        map = newMap;
      }
      map[documentPath.last] = updates;
    }
    try {
      var docRef = FirebaseFirestore.instance.collection(collection).doc(key);
      batch.set(docRef, rootMap, SetOptions(merge: true));
    } catch (e) {
      log('$collection $key $rootMap', name: 'updateProperties');
      log(e.toString(), name: 'updateProperties');
    }
  }

  @override
  void addToCollection(
      String path, ChangeLogEntry value, WriteBatch batch) async {
    return updateProperties(
        path + "." + value.key,
        {
          "T": value.timeChanged.millisecondsSinceEpoch,
          "V": value.value,
          "U": value.userName,
          "R": value.reason
        },
        batch);
  }

  @override
  void addToListCollection(
      String path, ListChangeLogEntry value, WriteBatch batch) async {
    return updateProperties(
        path + "." + value.key,
        {
          "T": value.timeChanged.millisecondsSinceEpoch,
          "V": value.value,
          "U": value.userName,
          "A": value.action
        },
        batch);
  }

  static FirebaseDatabaseHelper? _helper;

  static FirebaseDatabaseHelper global() {
    FirebaseDatabaseHelper? localHelper = _helper;
    if (localHelper == null) {
      localHelper = FirebaseDatabaseHelper();
      _helper = localHelper;
    }
    return localHelper;
  }
}
