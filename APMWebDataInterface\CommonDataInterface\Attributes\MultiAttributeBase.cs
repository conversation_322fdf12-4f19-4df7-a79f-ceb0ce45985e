﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace CommonDataInterface.Attributes
{
  public abstract class MultiAttributeBase<ValueType> : AttributeBase {


    public ListChangeLog<ValueType> GetValueChangeLog() {
      if (_valueChangeLog == null) {
        _valueChangeLog = new ListChangeLog<ValueType>("ValueChangeLog", this, new List<ChangeLogEntry<ValueType>>());
      }
      return _valueChangeLog;
    }

    private ListChangeLog<ValueType> _valueChangeLog;
    public ListChangeLog<ValueType> ValueChangeLog {
      get {
        var root = FindParentOfType<ConcretePhotoRoot>();
        if (root != null && !root.includeHistoryInJson)
          return null;
        return GetValueChangeLog();
      }
    }

    
    public ValueType[] CurrentValue
    {
      get {
        if (this.GetValueChangeLog().entries.Count == 0)
          return new ValueType[0];

        return this.GetValueChangeLog().GetCurrentEntries().ToArray();
      }
    }

    public ValueType[] PendingValue
    {
      get {
        if (this.GetValueChangeLog().entries.Count == 0)
          return new ValueType[0];

        return this.GetValueChangeLog().GetPendingEntries().ToArray();
      }
    }

    public MultiAttributeBase(DataModelItem parent, String displayName, String databaseName, bool areCommentsRequired)
      : base(parent, displayName, databaseName, areCommentsRequired)
    {

    }
    
    public override DataModelItem[] GetChildren()
    {
      return new DataModelItem[]{ GetValueChangeLog(), GetPhotoChangeLog(), GetCommentChangeLog() };
    }
  }
}