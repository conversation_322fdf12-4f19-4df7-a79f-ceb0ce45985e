//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class Section510INT_PVCKLSTSEC014 extends DataModelSection {
  @override
  String getDisplayName() => "TUBE BUNDLE - HEX ONLY";
  Section510INT_PVCKLSTSEC014(DataModelItem? parent)
      : super(parent: parent, sectionName: "TUBE BUNDLE - HEX ONLY");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510INT_PVCKLSTSEC014Q001 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Were any corrosion cells, erosion or pitting noted on the tube sheet surfaces: (The dimensions and locations of any damage shall be recorded)",
          databaseName: "510_INT-PV_CKLST_SEC014_Q001",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Corrosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Erosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Pitting", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510INT_PVCKLSTSEC014Q002 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Was any mechanical damage, deformation, or cracking noted on the tube sheet surfaces: (The dimensions and location of any damage shall be recorded)",
          databaseName: "510_INT-PV_CKLST_SEC014_Q002",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Cracking", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Deformation", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Mechanical Damage", null,
            isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC014Q003 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are the tube sheet sealing surfaces in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC014_Q003",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC014Q004 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are any tubes plugged & vented: (The number and location of plugged tubes shall be recorded)",
          databaseName: "510_INT-PV_CKLST_SEC014_Q004",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510INT_PVCKLSTSEC014Q005 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Are tube ends in acceptable condition for continued service: (If knife edged or damaged the number and location of damaged tubes shall be recorded)",
          databaseName: "510_INT-PV_CKLST_SEC014_Q005",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Other", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Deformation", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Mechanical Damage", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Knife Edged", null,
            isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC014Q006 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are any tubes seal welded: (The number and location of seal welded  tubes shall be recorded)",
          databaseName: "510_INT-PV_CKLST_SEC014_Q006",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC014Q007 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Do any tubes require seal welding: (The number and location of tubes to be seal welded shall be recorded)",
          databaseName: "510_INT-PV_CKLST_SEC014_Q007",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510INT_PVCKLSTSEC014Q008 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Was any mechanical damage, deformation, or cracking noted on the tube surfaces: (The dimensions and location of any damage shall be recorded)",
          databaseName: "510_INT-PV_CKLST_SEC014_Q008",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Cracking", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Deformation", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Mechanical Damage", null,
            isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510INT_PVCKLSTSEC014Q009 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Were there indications of corrosion, erosion or pitting of the tubes: (The dimensions and location of any damage shall be recorded)",
          databaseName: "510_INT-PV_CKLST_SEC014_Q009",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Corrosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Erosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Pitting", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC014Q010 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are the tube u-bends in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC014_Q010",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC014Q011 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are tierods and spacers in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC014_Q011",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attributeAre_tierods_double_nutted =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Are tierods double nutted:",
          databaseName: "510_INT-PV_CKLST_SEC014_Q012",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC014Q013 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are transverse baffles / support plates in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC014_Q013",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC014Q014 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are longitudinal baffles in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC014_Q014",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC014Q015 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is impingement plate in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC014_Q015",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attribute510INT_PVCKLSTSEC014Q001,
      attribute510INT_PVCKLSTSEC014Q002,
      attribute510INT_PVCKLSTSEC014Q003,
      attribute510INT_PVCKLSTSEC014Q004,
      attribute510INT_PVCKLSTSEC014Q005,
      attribute510INT_PVCKLSTSEC014Q006,
      attribute510INT_PVCKLSTSEC014Q007,
      attribute510INT_PVCKLSTSEC014Q008,
      attribute510INT_PVCKLSTSEC014Q009,
      attribute510INT_PVCKLSTSEC014Q010,
      attribute510INT_PVCKLSTSEC014Q011,
      attributeAre_tierods_double_nutted,
      attribute510INT_PVCKLSTSEC014Q013,
      attribute510INT_PVCKLSTSEC014Q014,
      attribute510INT_PVCKLSTSEC014Q015,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section510INT_PVCKLSTSEC014";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
