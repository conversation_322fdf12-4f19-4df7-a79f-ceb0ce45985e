//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Ext_PV_ALL_F
{
  public class Section510EXT_PVCKLSTSEC003 : DataModelItem {

    public override String DisplayName { 
      get {
        return "FOUNDATIONS AND SUPPORTS";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC003Q001;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC003Q002;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC003Q003;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC003Q004;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC003Q005;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC003Q006;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC003Q007;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510EXT_PVCKLSTSEC003";

    public Section510EXT_PVCKLSTSEC003(DataModelItem parent) : base(parent)
    {
            
        attribute510EXT_PVCKLSTSEC003Q001 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Cracking", null),
          new PredefinedValueOption("Yes: Settling", null),
          new PredefinedValueOption("Yes: Spalling", null)
        }, false, this, "Are foundations constructed of steel-reinforced concrete or structural steel fireproofed with concrete experiencing deterioration such as spalling, cracking, or settling:", databaseName: "510_EXT-PV_CKLST_SEC003_Q001"); 
     
        attribute510EXT_PVCKLSTSEC003Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are foundations constructed of steel cradles on concrete or steel piers in acceptable condition for continued service:", databaseName: "510_EXT-PV_CKLST_SEC003_Q002"); 
     
        attribute510EXT_PVCKLSTSEC003Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are the crevices formed between horizontal vessel and cradle supports seal welded or sealed with a mastic compound to prevent the ingress of moisture:", databaseName: "510_EXT-PV_CKLST_SEC003_Q003"); 
     
        attribute510EXT_PVCKLSTSEC003Q004 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is there any detectable corrosion between unsealed crevices formed between vessel shell and cradle supports:", databaseName: "510_EXT-PV_CKLST_SEC003_Q004"); 
     
        attribute510EXT_PVCKLSTSEC003Q005 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Has the asset experienced excessive heat, mechanical shock, corrosion of reinforcing steel, or the freezing of entrapped moisture thereby causing cracking in or around supports:", databaseName: "510_EXT-PV_CKLST_SEC003_Q005"); 
     
        attribute510EXT_PVCKLSTSEC003Q006 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Does the contact area between the anchor bolts and concrete or steel exhibit any signs of corrosion:", databaseName: "510_EXT-PV_CKLST_SEC003_Q006"); 
     
        attribute510EXT_PVCKLSTSEC003Q007 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are there indications of complete or nearly complete deterioration of the anchor bolt(s) below the base plate:", databaseName: "510_EXT-PV_CKLST_SEC003_Q007"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute510EXT_PVCKLSTSEC003Q001,
           attribute510EXT_PVCKLSTSEC003Q002,
           attribute510EXT_PVCKLSTSEC003Q003,
           attribute510EXT_PVCKLSTSEC003Q004,
           attribute510EXT_PVCKLSTSEC003Q005,
           attribute510EXT_PVCKLSTSEC003Q006,
           attribute510EXT_PVCKLSTSEC003Q007,
        };
    }
  }
}
