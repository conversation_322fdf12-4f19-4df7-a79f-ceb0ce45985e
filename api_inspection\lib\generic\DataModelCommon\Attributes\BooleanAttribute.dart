import 'package:api_inspection/generic/AttributeControls/AttributeStructure.dart';
import 'package:api_inspection/generic/AttributeControls/Boolean/DoubleAttributeView.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';

import 'AttributeBase.dart';
import 'ChangeLog/ChangeLogEntry.dart';

class BooleanAttribute extends SingleAttributeBase<bool> {
  bool? getValue() {
    if (valueChangeLog.entries.isEmpty) return null;
    return valueChangeLog.entries.last.value;
  }

  void setValue(bool? value) {
    if (getValue() == value) return;

    var entry = ChangeLogEntry<bool>.newlyCreated(value);
    valueChangeLog.addNewItem(entry);

    changedBySelfListeners.notifyListeners();
    notifyListeners();
  }

  BooleanAttribute(
      {required DataModelItem parent,
      required String displayName,
      Widget? iconWidget,
      bool areCommentsRequired = false,
      String? databaseName})
      : super(
            parent, displayName, iconWidget, areCommentsRequired, databaseName);

  @override
  Widget buildWidget(
      {IsEditableController? editingController,
      bool showPhotos = true,
      bool showComments = true}) {
    return BooleanAttributeView(
      this,
      editingController: editingController,
      showPhotos: showPhotos,
      showComments: showComments,
    );
  }

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  String getPreviewText() {
    var value = getValue();
    if (value == null) return "";
    if (value) {
      return "Yes";
    }
    return "No";
  }

  @override
  bool hasData() {
    return getValue() != null;
  }
}
