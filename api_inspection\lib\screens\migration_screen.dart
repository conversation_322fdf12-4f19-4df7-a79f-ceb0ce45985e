import 'package:flutter/material.dart';
import '../migration/migration_utility.dart';

class MigrationScreen extends StatefulWidget {
  const MigrationScreen({Key? key}) : super(key: key);

  @override
  _MigrationScreenState createState() => _MigrationScreenState();
}

class _MigrationScreenState extends State<MigrationScreen> {
  final MigrationUtility _migrationUtility = MigrationUtility(
    onProgressUpdate: (progress) {},
    onStatusUpdate: (status) {},
    onError: (error) {},
    onComplete: () {},
  );
  
  bool _migrationStarted = false;
  bool _migrationComplete = false;
  double _progress = 0.0;
  String _status = 'Ready to start migration';
  List<String> _logs = [];
  
  void _startMigration() async {
    setState(() {
      _migrationStarted = true;
      _migrationComplete = false;
      _progress = 0.0;
      _status = 'Starting migration...';
      _logs = [];
    });
    
    final migrationUtil = MigrationUtility(
      onProgressUpdate: (progress) {
        setState(() {
          _progress = progress;
        });
      },
      onStatusUpdate: (status) {
        setState(() {
          _status = status;
          _logs.add('[$_getTimestamp] $status');
        });
      },
      onError: (error) {
        setState(() {
          _logs.add('[$_getTimestamp] ERROR: $error');
        });
        _showErrorDialog(error);
      },
      onComplete: () {
        setState(() {
          _migrationComplete = true;
        });
        _showSuccessDialog();
      },
    );
    
    await migrationUtil.startMigration();
  }
  
  String _getTimestamp() {
    final now = DateTime.now();
    return '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}';
  }
  
  void _showErrorDialog(String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Migration Error'),
        content: Text('An error occurred during migration:\n\n$error'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
  
  void _showSuccessDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Migration Complete'),
        content: const Text('The migration from Firebase/GCP to Azure has completed successfully.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Firebase to Azure Migration'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Migration Status: $_status',
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
            ),
            const SizedBox(height: 16),
            
            LinearProgressIndicator(
              value: _progress,
              minHeight: 10,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                _migrationComplete ? Colors.green : Colors.blue,
              ),
            ),
            
            const SizedBox(height: 8),
            Text('${(_progress * 100).toStringAsFixed(1)}%'),
            const SizedBox(height: 24),
            
            const Text(
              'Migration Logs:',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            const SizedBox(height: 8),
            
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ListView.builder(
                  itemCount: _logs.length,
                  itemBuilder: (context, index) {
                    final log = _logs[index];
                    return Text(
                      log,
                      style: TextStyle(
                        fontFamily: 'monospace',
                        color: log.contains('ERROR') ? Colors.red : Colors.green,
                      ),
                    );
                  },
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: _migrationStarted ? null : _startMigration,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                  ),
                  child: const Text('Start Migration'),
                ),
                
                const SizedBox(width: 16),
                
                if (_migrationComplete)
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                    ),
                    child: const Text('Finish'),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
