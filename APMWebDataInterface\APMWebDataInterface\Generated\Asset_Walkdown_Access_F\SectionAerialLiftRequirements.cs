//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionAsset_Walkdown_Access_F
{
  public class SectionAerialLiftRequirements : DataModelItem {

    public override String DisplayName { 
      get {
        return "Aerial Lift Requirements";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributeAerial_Lift_Needed;
    public PredefinedValueAttribute attributeAWAQ222;
    public PredefinedValueAttribute attributeGas_Powered_Permitted;
    public PredefinedValueAttribute attributeBattery_Powered_Permitted;
    public PredefinedValueAttribute attributeClient_required_proof_of_training;
    public PredefinedValueAttribute attributeClient_provided_operator;
    public IntegerAttribute attributeEstimated_distance_to_any_live_electrical_overhead_lines;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionAerialLiftRequirements";

    public SectionAerialLiftRequirements(DataModelItem parent) : base(parent)
    {
            
        attributeAerial_Lift_Needed = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Aerial Lift Needed", databaseName: "AWA_Q221"); 
     
        attributeAWAQ222 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Does the site have access for Aerial Lift for ALL locations at heights?", databaseName: "AWA_Q222"); 
     
        attributeGas_Powered_Permitted = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Gas Powered Permitted", databaseName: "AWA_Q223"); 
     
        attributeBattery_Powered_Permitted = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Battery Powered Permitted", databaseName: "AWA_Q224"); 
     
        attributeClient_required_proof_of_training = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Client required proof of training", databaseName: "AWA_Q225"); 
     
        attributeClient_provided_operator = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Client provided operator", databaseName: "AWA_Q226"); 
     
        attributeEstimated_distance_to_any_live_electrical_overhead_lines = new IntegerAttribute(this, displayName: "Estimated distance to any live electrical overhead lines", databaseName: "AWA_Q227", areCommentsRequired: false, displayUnit: "ft", allowNegatives: true); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeAerial_Lift_Needed,
           attributeAWAQ222,
           attributeGas_Powered_Permitted,
           attributeBattery_Powered_Permitted,
           attributeClient_required_proof_of_training,
           attributeClient_provided_operator,
           attributeEstimated_distance_to_any_live_electrical_overhead_lines,
        };
    }
  }
}
