import 'dart:async';
import 'dart:developer';

import 'package:api_inspection/app/DataModel/LeakReport/leakReport.dart';
import 'package:api_inspection/generic/AppRoot.dart';
import 'package:api_inspection/generic/DataModelCommon/DatabaseInterfaces/FirebaseDatabaseHelper.dart';
import 'package:api_inspection/app/Queries/Queries.dart';
import 'package:api_inspection/generic/UIControls/listener.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:collection/collection.dart';

class LeakReportQueries {
  // ignore: unused_field
  late Queries _parent;

  Future dispose() async {
    for (var element in subscriptions) {
      await element.cancel();
    }
    leakReportsChangedListener.dispose();
  }

  void initialize(Queries parent) {
    _parent = parent;
    startQuery();
  }

  ListenerWrapper leakReportsChangedListener = ListenerWrapper();

  List<LeakReport> leakReports = [];

  List<StreamSubscription<QuerySnapshot<Map<String, dynamic>>>> subscriptions =
      [];

  void startQuery() {
    var databaseReference = FirebaseDatabaseHelper.global().databaseReference;
    var user = AppRoot.global().currentUser!.email;

    final buIdBatches = AppRoot.global().businessIdsBatches;
    if (buIdBatches != null) {
      for (var buIdBatch in buIdBatches) {
        subscriptions.add(
          databaseReference
              .collection('leakreports')
              .where("createdBy", isEqualTo: user)
              .where('BusinessUnitId.Value', whereIn: buIdBatch.toList())
              .snapshots()
              .listen(onReportsUpdated),
        );
      }
    }
  }

  void onReportsUpdated(QuerySnapshot<Map<String, dynamic>> snapshot) {
    List<LeakReport> newReports = [];
    for (var item in snapshot.docs) {
      LeakReport report;

      var itemData = item.data();
      if (leakReports.any((element) => element.id == item.id)) {
        report = leakReports.firstWhere((element) => element.id == item.id);
        var change = snapshot.docChanges
            .firstWhereOrNull((element) => element.doc.id == item.id);
        if (change != null) {
          if (change.type == DocumentChangeType.added ||
              change.type == DocumentChangeType.modified) {
            // Update our local cache
            report.updateFromMap(itemData);
          } else {
            log('We do not support data removal, yet a document with id ${change.doc.id} was removed.');
          }
        }
      } else {
        report = LeakReport(
            item.id,
            itemData.containsKey('createdBy')
                ? item["createdBy"].toString()
                : '',
            itemData.containsKey('status') ? item["status"].toString() : '');

        report.updateFromMap(item.data());
        report.setShouldDownloadPhotos(false);
      }
      newReports.add(report);
    }

    leakReports = newReports;

    leakReportsChangedListener.notifyListeners();
  }
}
