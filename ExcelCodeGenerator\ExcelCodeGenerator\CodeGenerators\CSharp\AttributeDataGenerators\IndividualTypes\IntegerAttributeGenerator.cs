﻿using CheckListGen;

namespace ExcelCodeGenerator.CodeGenerators.CSharp
{
    public class IntegerAttributeGenerator : IAttributeGenerator
    {
        public string BuildInitialization(Question question)
        {
            var unit = string.IsNullOrWhiteSpace(question.Unit) ? "null" : "\"" + question.Unit + "\"";
            return
                @"
        " + question.DartVariableName + @" = new IntegerAttribute(this, displayName: """ + question.DisplayText +
                @""", databaseName: """ + question.DataName + @""", areCommentsRequired: " +
                (question.ForceComment ? "true" : "false") + @", displayUnit: " + unit + @", allowNegatives: true); ";
        }

        public string BuildDeclaration(Question question)
        {
            return
                @"public IntegerAttribute " + question.DartVariableName + ";";
        }
    }
}