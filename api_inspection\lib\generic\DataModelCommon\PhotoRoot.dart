import 'package:api_inspection/generic/MediaControls/MediaEntry.dart';

import '../DataModelCommon/DataModelItem.dart';

abstract class PhotoRoot extends DataModelItem {
  PhotoRoot(DataModelItem? parent) : super(parent);

  bool getShouldDownloadPhotos();
  void setShouldDownloadPhotos(bool newValue);

  MediaEntry addPhoto(String filename, String extension);
  MediaEntry getPhoto(String filename, String extension);
  void removePhoto(MediaEntry entry);
}
