import 'package:api_inspection/generic/AppStyle.dart';
import 'package:flutter/material.dart';

class AttributePadding {
  // ignore: non_constant_identifier_names
  static Widget WithStdPadding(Widget widget) {
    return AppStyle.global.currentAppSize == AppSizes.Regular
        ? Container(
            child: widget, padding: const EdgeInsets.fromLTRB(10, 5, 10, 5))
        : Container(
            child: widget, padding: const EdgeInsets.fromLTRB(8, 4, 8, 4));
  }

  // ignore: non_constant_identifier_names
  static Widget WithStdSidePadding(Widget widget) {
    return AppStyle.global.currentAppSize == AppSizes.Regular
        ? Container(
            child: widget, padding: const EdgeInsets.fromLTRB(10, 0, 10, 0))
        : Container(
            child: widget, padding: const EdgeInsets.fromLTRB(8, 0, 8, 0));
  }
}
