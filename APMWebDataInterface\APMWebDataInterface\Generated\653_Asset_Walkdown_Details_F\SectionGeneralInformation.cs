//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section653_Asset_Walkdown_Details_F
{
  public class SectionGeneralInformation : DataModelItem {

    public override String DisplayName { 
      get {
        return "General Information";
      }
    }

    #region [-- Sub Sections --]

    #region [-- SectionPhotos --]
    private SectionPhotos _sectionPhotos;
    public SectionPhotos sectionPhotos {
        get {
            if (_sectionPhotos == null) {
               _sectionPhotos = new SectionPhotos(this);
            }

            return _sectionPhotos;
        }
    }
    #endregion [-- SectionPhotos --]
    
    #region [-- SectionDesign --]
    private SectionDesign _sectionDesign;
    public SectionDesign sectionDesign {
        get {
            if (_sectionDesign == null) {
               _sectionDesign = new SectionDesign(this);
            }

            return _sectionDesign;
        }
    }
    #endregion [-- SectionDesign --]
    
    #region [-- SectionService --]
    private SectionService _sectionService;
    public SectionService sectionService {
        get {
            if (_sectionService == null) {
               _sectionService = new SectionService(this);
            }

            return _sectionService;
        }
    }
    #endregion [-- SectionService --]
    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionGeneralInformation";

    public SectionGeneralInformation(DataModelItem parent) : base(parent)
    {
            
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           sectionPhotos,
           sectionDesign,
           sectionService,
        };
    }
  }
}
