//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class Section570EXTCKLSTSEC002 extends DataModelSection {
  @override
  String getDisplayName() => "PROTECTIVE COATING AND INSULATION";
  Section570EXTCKLSTSEC002(DataModelItem? parent)
      : super(parent: parent, sectionName: "PROTECTIVE COATING AND INSULATION");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeDoes_the_asset_have_a_protective_coating_system_applied =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Does the asset have a protective coating system applied:",
          databaseName: "570_EXT_CKLST_SEC002_Q001",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeWas_condensation_noted_on_the_exterior_of_the_asset =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Was condensation noted on the exterior of the asset:",
          databaseName: "570_EXT_CKLST_SEC002_Q002",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC002Q003 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Was any protective coating removed as part of this inspection:",
          databaseName: "570_EXT_CKLST_SEC002_Q003",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC002Q004 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the protective coating in acceptable condition for continued service:",
          databaseName: "570_EXT_CKLST_SEC002_Q004",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC002Q005 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the asset insulated: (The type of insulation system, metal jacket with underlying insulation type, blanket, etc., shall be recorded)",
          databaseName: "570_EXT_CKLST_SEC002_Q005",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC002Q006 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Does the asset operate within the CUI range: (10 °F (–12 °C) and 350 °F (177 °C) for carbon and low alloy steels, 140 °F (60 °C) and 350 °F (177 °C) for austenitic stainless steels, and 280 °F (138 °C) and  350 °F (177 °C) for duplex stainless steels or in intermittent service)",
          databaseName: "570_EXT_CKLST_SEC002_Q006",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC002Q007 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Following the external visual inspection of susceptible systems for CUI was the guidance of API-570 implemented concerning the Recommended Extent of CUI Inspection Following Visual Inspection for Susceptible Piping:",
          databaseName: "570_EXT_CKLST_SEC002_Q007",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeIs_the_insulation_jacketing_seams_properly_oriented =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Is the insulation jacketing seams properly oriented:",
          databaseName: "570_EXT_CKLST_SEC002_Q008",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeAre_there_indications_of_insulation_jacketing_seal_failure =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are there indications of insulation jacketing seal failure:",
          databaseName: "570_EXT_CKLST_SEC002_Q009",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeAre_there_locations_of_missing_or_damaged_insulation =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Are there locations of missing or damaged insulation:",
          databaseName: "570_EXT_CKLST_SEC002_Q010",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attributeAre_CML_ports_installed =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Are CML ports installed:",
          databaseName: "570_EXT_CKLST_SEC002_Q011",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeAre_all_CML_port_hole_covers_present_and_properly_sealed =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are all CML port hole covers present and properly sealed:",
          databaseName: "570_EXT_CKLST_SEC002_Q012",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC002Q013 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Was evidence of active or prior leakage noted from underneath the insulation:",
          databaseName: "570_EXT_CKLST_SEC002_Q013",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeAre_there_areas_of_wet_or_damp_insulation =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Are there areas of wet or damp insulation:",
          databaseName: "570_EXT_CKLST_SEC002_Q014",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeWas_any_insulation_removed_as_part_of_this_inspection =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Was any insulation removed as part of this inspection:",
          databaseName: "570_EXT_CKLST_SEC002_Q015",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC002Q016 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the asset heat traced and is tracing in acceptable condition for continued service:  (The type of heat tracing, electrical or steam, shall be recorded)",
          databaseName: "570_EXT_CKLST_SEC002_Q016",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC002Q017 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the piping insulation system in acceptable condition for continued service:",
          databaseName: "570_EXT_CKLST_SEC002_Q017",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeDoes_the_asset_have_a_protective_coating_system_applied,
      attributeWas_condensation_noted_on_the_exterior_of_the_asset,
      attribute570EXTCKLSTSEC002Q003,
      attribute570EXTCKLSTSEC002Q004,
      attribute570EXTCKLSTSEC002Q005,
      attribute570EXTCKLSTSEC002Q006,
      attribute570EXTCKLSTSEC002Q007,
      attributeIs_the_insulation_jacketing_seams_properly_oriented,
      attributeAre_there_indications_of_insulation_jacketing_seal_failure,
      attributeAre_there_locations_of_missing_or_damaged_insulation,
      attributeAre_CML_ports_installed,
      attributeAre_all_CML_port_hole_covers_present_and_properly_sealed,
      attribute570EXTCKLSTSEC002Q013,
      attributeAre_there_areas_of_wet_or_damp_insulation,
      attributeWas_any_insulation_removed_as_part_of_this_inspection,
      attribute570EXTCKLSTSEC002Q016,
      attribute570EXTCKLSTSEC002Q017,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section570EXTCKLSTSEC002";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
