# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  archive:
    dependency: transitive
    description:
      name: archive
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.3.1"
  args:
    dependency: transitive
    description:
      name: args
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.3.1"
  asn1lib:
    dependency: transitive
    description:
      name: asn1lib
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.1"
  async:
    dependency: transitive
    description:
      name: async
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.9.0"
  azblob:
    dependency: "direct main"
    description:
      name: azblob
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.3.0"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  cached_network_image:
    dependency: "direct main"
    description:
      name: cached_network_image
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.2.2"
  cached_network_image_platform_interface:
    dependency: transitive
    description:
      name: cached_network_image_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  cached_network_image_web:
    dependency: transitive
    description:
      name: cached_network_image_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.2"
  camera:
    dependency: "direct main"
    description:
      name: camera
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.10.0+1"
  camera_android:
    dependency: transitive
    description:
      name: camera_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.10.0+2"
  camera_avfoundation:
    dependency: transitive
    description:
      name: camera_avfoundation
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.9.8+5"
  camera_platform_interface:
    dependency: transitive
    description:
      name: camera_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.0"
  camera_web:
    dependency: transitive
    description:
      name: camera_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.3.0"
  characters:
    dependency: transitive
    description:
      name: characters
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.1"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.1"
  cli_util:
    dependency: transitive
    description:
      name: cli_util
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.3.5"
  clock:
    dependency: transitive
    description:
      name: clock
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.1"
  cloud_firestore:
    dependency: "direct main"
    description:
      name: cloud_firestore
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.4.9"
  cloud_firestore_platform_interface:
    dependency: transitive
    description:
      name: cloud_firestore_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.7.5"
  cloud_firestore_web:
    dependency: transitive
    description:
      name: cloud_firestore_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.8.8"
  collection:
    dependency: transitive
    description:
      name: collection
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.16.0"
  connectivity_plus:
    dependency: "direct main"
    description:
      name: connectivity_plus
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.3.7"
  connectivity_plus_linux:
    dependency: transitive
    description:
      name: connectivity_plus_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.1"
  connectivity_plus_macos:
    dependency: transitive
    description:
      name: connectivity_plus_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.4"
  connectivity_plus_platform_interface:
    dependency: transitive
    description:
      name: connectivity_plus_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.1"
  connectivity_plus_web:
    dependency: transitive
    description:
      name: connectivity_plus_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.3"
  connectivity_plus_windows:
    dependency: transitive
    description:
      name: connectivity_plus_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.2"
  convert:
    dependency: transitive
    description:
      name: convert
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.2"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.3.3+2"
  crypto:
    dependency: transitive
    description:
      name: crypto
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.2"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.5"
  darq:
    dependency: "direct main"
    description:
      name: darq
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.1"
  dbus:
    dependency: transitive
    description:
      name: dbus
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.7.4"
  encrypt:
    dependency: "direct main"
    description:
      name: encrypt
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.0.1"
  equatable:
    dependency: transitive
    description:
      name: equatable
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.5"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.1"
  ffi:
    dependency: transitive
    description:
      name: ffi
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.1"
  file:
    dependency: transitive
    description:
      name: file
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.1.4"
  file_picker:
    dependency: "direct main"
    description:
      name: file_picker
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.6.1"
  file_saver:
    dependency: "direct main"
    description:
      name: file_saver
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.1"
  firebase_analytics:
    dependency: "direct main"
    description:
      name: firebase_analytics
      url: "https://pub.dartlang.org"
    source: hosted
    version: "9.3.6"
  firebase_analytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_analytics_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.3.5"
  firebase_analytics_web:
    dependency: transitive
    description:
      name: firebase_analytics_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.2+5"
  firebase_auth:
    dependency: "direct main"
    description:
      name: firebase_auth
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.10.0"
  firebase_auth_platform_interface:
    dependency: transitive
    description:
      name: firebase_auth_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.9.0"
  firebase_auth_web:
    dependency: transitive
    description:
      name: firebase_auth_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.5.0"
  firebase_core:
    dependency: "direct main"
    description:
      name: firebase_core
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.23.0"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.5.1"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.7.2"
  firebase_crashlytics:
    dependency: "direct main"
    description:
      name: firebase_crashlytics
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.8.11"
  firebase_crashlytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_crashlytics_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.2.17"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_appauth:
    dependency: "direct main"
    description:
      name: flutter_appauth
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.2.0"
  flutter_appauth_platform_interface:
    dependency: transitive
    description:
      name: flutter_appauth_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.2.0"
  flutter_blurhash:
    dependency: transitive
    description:
      name: flutter_blurhash
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.7.0"
  flutter_cache_manager:
    dependency: transitive
    description:
      name: flutter_cache_manager
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.3.0"
  flutter_launcher_icons:
    dependency: "direct dev"
    description:
      name: flutter_launcher_icons
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.10.0"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.1"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.7"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_uploader:
    dependency: "direct main"
    description:
      name: flutter_uploader
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.0-beta.4"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  gallery_saver:
    dependency: "direct main"
    description:
      name: gallery_saver
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.3.2"
  geolocator:
    dependency: "direct main"
    description:
      name: geolocator
      url: "https://pub.dartlang.org"
    source: hosted
    version: "9.0.2"
  geolocator_android:
    dependency: transitive
    description:
      name: geolocator_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.1.3"
  geolocator_apple:
    dependency: transitive
    description:
      name: geolocator_apple
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.2"
  geolocator_platform_interface:
    dependency: transitive
    description:
      name: geolocator_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.6"
  geolocator_web:
    dependency: transitive
    description:
      name: geolocator_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.6"
  geolocator_windows:
    dependency: transitive
    description:
      name: geolocator_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.1"
  http:
    dependency: transitive
    description:
      name: http
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.13.5"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.1"
  image:
    dependency: "direct main"
    description:
      name: image
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.2.0"
  intl:
    dependency: "direct main"
    description:
      name: intl
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.17.0"
  js:
    dependency: transitive
    description:
      name: js
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.6.4"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.7.0"
  lints:
    dependency: transitive
    description:
      name: lints
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  map_launcher:
    dependency: "direct main"
    description:
      name: map_launcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.4.0"
  matcher:
    dependency: transitive
    description:
      name: matcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.12.12"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.5"
  meta:
    dependency: transitive
    description:
      name: meta
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.0"
  nm:
    dependency: transitive
    description:
      name: nm
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.5.0"
  octo_image:
    dependency: transitive
    description:
      name: octo_image
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.2"
  open_file:
    dependency: "direct main"
    description:
      name: open_file
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.2.1"
  package_info_plus:
    dependency: "direct main"
    description:
      name: package_info_plus
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.4.2"
  package_info_plus_linux:
    dependency: transitive
    description:
      name: package_info_plus_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.5"
  package_info_plus_macos:
    dependency: transitive
    description:
      name: package_info_plus_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.0"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.2"
  package_info_plus_web:
    dependency: transitive
    description:
      name: package_info_plus_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.5"
  package_info_plus_windows:
    dependency: transitive
    description:
      name: package_info_plus_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.5"
  path:
    dependency: transitive
    description:
      name: path
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.2"
  path_provider:
    dependency: "direct main"
    description:
      name: path_provider
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.11"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.20"
  path_provider_ios:
    dependency: transitive
    description:
      name: path_provider_ios
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.11"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.7"
  path_provider_macos:
    dependency: transitive
    description:
      name: path_provider_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.6"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.4"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.7"
  pedantic:
    dependency: transitive
    description:
      name: pedantic
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.11.1"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.0.0"
  photo_view:
    dependency: "direct main"
    description:
      name: photo_view
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.14.0"
  platform:
    dependency: transitive
    description:
      name: platform
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.0"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.3"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.6.2"
  process:
    dependency: transitive
    description:
      name: process
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.2.4"
  quiver:
    dependency: transitive
    description:
      name: quiver
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.0"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.27.5"
  shared_preferences:
    dependency: "direct main"
    description:
      name: shared_preferences
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.15"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.13"
  shared_preferences_ios:
    dependency: transitive
    description:
      name: shared_preferences_ios
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.1"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.1"
  shared_preferences_macos:
    dependency: transitive
    description:
      name: shared_preferences_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.4"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.4"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.1"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  source_span:
    dependency: transitive
    description:
      name: source_span
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.9.0"
  sqflite:
    dependency: transitive
    description:
      name: sqflite
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.3.0"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.10.0"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.1"
  synchronized:
    dependency: "direct main"
    description:
      name: synchronized
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.0+3"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.1"
  test_api:
    dependency: transitive
    description:
      name: test_api
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.12"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.1"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.1.5"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.0.19"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.0.17"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.13"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  uuid:
    dependency: "direct main"
    description:
      name: uuid
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.6"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.2"
  win32:
    dependency: transitive
    description:
      name: win32
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.6.1"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.0+2"
  xml:
    dependency: transitive
    description:
      name: xml
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.1.0"
  yaml:
    dependency: transitive
    description:
      name: yaml
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.1"
sdks:
  dart: ">=2.18.0 <3.0.0"
  flutter: ">=3.3.0"
