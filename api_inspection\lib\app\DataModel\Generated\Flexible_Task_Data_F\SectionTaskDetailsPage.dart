//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionTaskDetails.dart';

// ignore: camel_case_types
class SectionTaskDetailsPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionTaskDetails sectionTaskDetails;

  const SectionTaskDetailsPage(this.sectionTaskDetails, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionTaskDetailsPageState();
  }
}

class _SectionTaskDetailsPageState extends State<SectionTaskDetailsPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionTaskDetails,
        title: "Task Details",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionTaskDetails.attributeMethod
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionTaskDetails.attributeSub_Method
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionTaskDetails.attributePlanned_Start_Date
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionTaskDetails.attributePlanned_End_Date
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionTaskDetails.attributeActual_Start_Date
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionTaskDetails.attributeWork_Completion_Date
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionTaskDetails.attributeDue_Date
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionTaskDetails.attributeTask_Assignees
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionTaskDetails.attributeLead_Technician
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionTaskDetails.attributeSupervisor
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionTaskDetails.attributeClient_Work_Order_Number
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionTaskDetails.attributeClient_Cost_Code
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionTaskDetails.attributePurchase_Order_AFE
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionTaskDetails.attributeClient_Work_Order_Description
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionTaskDetails.attributeWork_Summary
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
