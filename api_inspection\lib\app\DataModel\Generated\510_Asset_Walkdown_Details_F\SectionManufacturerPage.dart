//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionManufacturer.dart';

// ignore: camel_case_types
class SectionManufacturerPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionManufacturer sectionManufacturer;

  const SectionManufacturerPage(this.sectionManufacturer, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionManufacturerPageState();
  }
}

class _SectionManufacturerPageState extends State<SectionManufacturerPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionManufacturer,
        title: "Manufacturer",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionManufacturer.attributeName
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionManufacturer.attributeDate
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionManufacturer.attributeSerial_Number
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionManufacturer.attributeNational_Board_Number
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionManufacturer.attributeRT_Number
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
