import 'package:api_inspection/app/APMRoot.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/AttributeBase.dart';
import 'package:api_inspection/generic/MediaControls/MediaSynchronizer.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

class DeleteAllPhotosButtonWidget extends StatefulWidget {
  final AttributeBase attribute;

  const DeleteAllPhotosButtonWidget(this.attribute, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => _DeleteAllPhotosButtonWidgetState();
}

class _DeleteAllPhotosButtonWidgetState
    extends State<DeleteAllPhotosButtonWidget> {
  @override
  Widget build(BuildContext context) {
    return TextButton(
        onPressed: onDeleteAllClicked,
        child: Container(
            width: 65,
            height: 65,
            padding: const EdgeInsets.all(20),
            decoration: const BoxDecoration(
                color: Color.fromARGB(255, 41, 44, 52),
                borderRadius: BorderRadius.all(Radius.circular(8))),
            child: const FittedBox(
                fit: BoxFit.fill,
                child: Icon(Icons.delete_sweep, color: Colors.grey))));
  }

  Future<void> onDeleteAllClicked() async {
    APMRoot.global.newQueries.pause();
    for (var entry in widget.attribute.getPhotos()) {
      await widget.attribute.removePhoto(entry);
      await IMediaSynchronizer.getMediaSynchronizer().removePendingFile(
          'DBPath:${entry.getDBPath()}^V:${entry.version}^${entry.fullFileName}');
      await CachedNetworkImage.evictFromCache(entry.downloadUrl);
    }

    var batch = FirebaseFirestore.instance.batch();
    widget.attribute.saveItem(batch);
    await batch.commit();

    APMRoot.global.newQueries.resume();
  }
}
