import 'dart:developer';

import 'package:api_inspection/app/APMRoot.dart';
import 'package:api_inspection/app/DataModel/Generated/510_Asset_Walkdown_Details_F/Section510_Asset_Walkdown_Details_F.dart';
import 'package:api_inspection/app/DataModel/Generated/570_Asset_Walkdown_Details_F/Section570_Asset_Walkdown_Details_F.dart';
import 'package:api_inspection/app/DataModel/Generated/653_Asset_Walkdown_Details_F/Section653_Asset_Walkdown_Details_F.dart';
import 'package:api_inspection/app/DataModel/Tasks/IMultiTopLevelInspection.dart';
import 'package:api_inspection/app/DataModel/Tasks/Section510_FullInspection.dart';
import 'package:api_inspection/app/DataModel/Tasks/taskFormFactory.dart';
import 'package:api_inspection/app/DataModel/asset.dart';
import 'package:api_inspection/app/UI/Tasks/TaskCompletionBarWrapper.dart';
import 'package:api_inspection/app/batch_helper.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/ConcretePhotoRoot.dart';
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/Generated/510_Ext_PV_ALL_F/Section510_Ext_PV_ALL_F.dart';
import 'package:api_inspection/app/DataModel/Generated/510_Int_PV_ALL_F/Section510_Int_PV_ALL_F.dart';
import 'package:api_inspection/app/DataModel/Generated/570_Ext_Pipe_F/Section570_Ext_Pipe_F.dart';
import 'package:api_inspection/app/DataModel/activity.dart';
import 'package:api_inspection/app/DataModel/workorder.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/AttributeBase.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/DataModelCommon/DatabaseInterfaces/DatabaseHelperManager.dart';

class TaskDetails extends DataModelItem {
  TaskDetails(DataModelItem? parent) : super(parent);

  late StringAttribute projectNumber =
      StringAttribute(parent: this, displayName: "Project Number");
  late StringAttribute supervisor =
      StringAttribute(parent: this, displayName: "Supervisor");

  @override
  List<DataModelItem> getChildren() {
    return [projectNumber, supervisor];
  }

  @override
  String getDBName() {
    return "TaskDetails";
  }

  @override
  String getDisplayName() => "TaskDetails";

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}

class Task extends ConcretePhotoRoot {
  late String projectId;

  /// 'Asset Walkdown', 'External Visual', 'Internal Visual', or 'Full'
  String taskType;

  late Asset asset;
  late WorkOrder workOrder;

  static const String STATUS_NOT_STARTED = "not started";
  static const String STATUS_SCHEDULED = "scheduled";
  static const String STATUS_IN_PROGRESS = "in progress";
  static const String STATUS_ON_HOLD = "on hold";
  static const String STATUS_COMPLETED = "completed";
  static const String STATUS_CANCELED = "canceled";
  static const String STATUS_PUBLISHED = "published";

  static const String TYPE_FULL = "full";
  static const String TYPE_INTERNAL_VISUAL = "internal visual";
  static const String TYPE_EXTERNAL_VISUAL = "external visual";

  bool isInACompletedState() {
    return status.getValue()?.toLowerCase() == "completed" ||
        status.getValue()?.toLowerCase() == "canceled";
  }

  bool isInProgress() {
    var currentStatus = status.getValue()?.toLowerCase();
    return currentStatus == STATUS_NOT_STARTED || currentStatus == STATUS_SCHEDULED ||
    currentStatus == STATUS_IN_PROGRESS || currentStatus == STATUS_ON_HOLD;
  }

  late StringAttribute name =
      StringAttribute(parent: this, displayName: "Task Name");

  late StringAttribute businessUnitId = StringAttribute(
      parent: this, displayName: "BusinessUnitId", isQueryable: true);

  late StringAttribute clientWorkOrderNumber = StringAttribute(
      parent: this, displayName: "Client Work Order Number", isQueryable: true);
  late StringAttribute clientWorkOrderDescription = StringAttribute(
      parent: this, displayName: "Client Work Order Description");
  late StringAttribute leadTech =
      StringAttribute(parent: this, displayName: "Lead Technician");

  late DateAttribute dueDate =
      DateAttribute(parent: this, displayName: "Due Date");
  late DateAttribute plannedStart =
      DateAttribute(parent: this, displayName: "Planned Start");
  late DateAttribute plannedEnd =
      DateAttribute(parent: this, displayName: "Planned End");

  late StringAttribute clientCostCode =
      StringAttribute(parent: this, displayName: "Client Cost Code");

  late DataModelItem assetDetails;
  late DataModelItem? taskData;

  late TaskDetails taskDetails = TaskDetails(this);

  late DataModelCollection<TaskActivity> activityTracker =
      DataModelCollection<TaskActivity>("Activity Tracker", (parent, entry) {
    return TaskActivity(activityTracker, entry.key);
  }, this);

  late PredefinedValueAttribute status = PredefinedValueAttribute(
      hasOtherOption: false,
      parent: this,
      displayName: "Status",
      availableOptions: [
        PredefinedValueOption("Not Started", null),
        PredefinedValueOption("In Progress", null),
        PredefinedValueOption("On Hold", null),
        PredefinedValueOption("Scheduled", null),
        PredefinedValueOption("Completed", null),
      ]);

  List<String> assignedUsers = [];

  String getAssignedToPreviewText() {
    if (assignedUsers.length > 3) {
      return "Crew";
    } else if (assignedUsers.isEmpty) {
      return "None";
    }
    return assignedUsers.join(", ");
  }

  List<Widget> buildFormButtons(BuildContext context) {
    if (taskData is IMultiTopLevelInspection) {
      return (taskData as IMultiTopLevelInspection)
          .buildFormButtons(context, this);
    }

    String buttonText = taskType;
    if (buttonText == "Asset Walkdown") buttonText = "Asset Details";
    return [
      AttributePadding.WithStdPadding(
        TeamToggleButton.withText(buttonText, () {
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => TaskCompletionBarWrapper(
                      task: this, child: buildTaskDataForm())));
        }),
      )
    ];
  }

  Widget buildTaskDataForm() {
    DataModelItem taskFormData;
    if (taskType == "Asset Walkdown") {
      taskFormData = asset.walkDown;
    } else {
      var localTaskData = taskData;
      if (localTaskData == null) {
        log("Could not find task data for task: " + id);
        return Container();
      }
      taskFormData = localTaskData;
    }
    return TaskFormFactory.buildTaskDataForm(taskFormData);
  }

  DataModelItem? buildTaskDataFromType(
      String inspectionType, String assetCategory) {
    if (taskType == "Asset Walkdown") {
      return null;
    } else if (inspectionType == "External Visual") {
      switch (assetCategory) {
        case "Vessel":
          return Section510_Ext_PV_ALL_F(this);
        case "Piping":
          return Section570_Ext_Pipe_F(this);
        case "Tank":
          throw "External visual is not supported for tanks";
      }
    } else if (inspectionType == "Internal Visual") {
      switch (assetCategory) {
        case "Vessel":
          return Section510_Int_PV_ALL_F(this);
        case "Piping":
          throw "Internal Visual inspection is not supported for pipes";
        case "Tank":
          throw "Internal Visual inspection is not supported for tanks";
      }
    } else if (inspectionType == "Full") {
      switch (assetCategory) {
        case "Vessel":
          return Section510_FullInspection(this);
        case "Piping":
          throw "Full Visual inspection is not supported for pipes";
        case "Tank":
          throw "Full Visual inspection is not supported for tanks";
      }
    }
    throw "Unexpected inspection and or asset type encountered";
  }

  Task.createNew(this.workOrder, String id, this.taskType) : super(id) {
    asset = workOrder.asset;
    projectId = workOrder.projectId;

    assetDetails = asset.walkDown;

    taskData = buildTaskDataFromType(taskType, asset.assetCategory);

    initializeCardListeners();
  }

  Task(this.workOrder, String id, this.taskType) : super(id) {
    asset = workOrder.asset;
    projectId = workOrder.projectId;

    assetDetails = asset.walkDown;

    taskData = buildTaskDataFromType(taskType, asset.assetCategory);

    initializeCardListeners();
  }

  void onCardAttributeChangedBySelf() {
    var batch = FirebaseFirestore.instance.batch();
    asset.updateDBCard(batch);
    batch.commit();
  }

  void initializeCardListeners() {
    var data = taskData;
    if (data is Section510_Asset_Walkdown_Details_F) {
      data.sectionIdentification.attributeAsset_Type.changedBySelfListeners
          .addListener(onCardAttributeChangedBySelf);
      data.sectionIdentification.attributeName.changedBySelfListeners
          .addListener(onCardAttributeChangedBySelf);
      data.sectionIdentification.attributeNumber_or_ID.changedBySelfListeners
          .addListener(onCardAttributeChangedBySelf);
    } else if (data is Section570_Asset_Walkdown_Details_F) {
      data.sectionIdentification.attributeAsset_Type.changedBySelfListeners
          .addListener(onCardAttributeChangedBySelf);
      data.sectionIdentification.attributeName.changedBySelfListeners
          .addListener(onCardAttributeChangedBySelf);
      data.sectionIdentification.attributeNumber_or_Circuit_ID
          .changedBySelfListeners
          .addListener(onCardAttributeChangedBySelf);
    } else if (data is Section653_Asset_Walkdown_Details_F) {
      data.sectionIdentification.attributeAsset_Type.changedBySelfListeners
          .addListener(onCardAttributeChangedBySelf);
      data.sectionIdentification.attributeName.changedBySelfListeners
          .addListener(onCardAttributeChangedBySelf);
      data.sectionIdentification.attributeNumber_or_ID.changedBySelfListeners
          .addListener(onCardAttributeChangedBySelf);
    }
  }

  bool _checkedStatus = false;

  @override
  void onChildrenChanged(DataModelItem childChanged) {
    super.onChildrenChanged(childChanged);

    if (!_checkedStatus) {
      var childAttr = childChanged.findParentOfType<AttributeBase>();
      List<String> blackListAttributeNames = [
        "Assignee",
        "Supervisor",
        "Status",
        "BusinessUnitId"
      ];
      if (status.getValue() == "Not Started" || status.getValue() == null) {
        if (childAttr != null &&
            !blackListAttributeNames.contains(childAttr.displayName)) {
          _checkedStatus = true;
          BatchHelper.saveAndCommitStringAttribute(status, "In Progress");
          APMRoot.global.newQueries.workOrderListener.notifyListeners();
        }
      }
    }
  }

  @override
  List<DataModelItem> getChildren() {
    var list = super.getChildren().toList();

    list.addAll([
      taskDetails,
      status,
      activityTracker,
      clientWorkOrderNumber,
      clientWorkOrderDescription,
      leadTech,
      dueDate,
      plannedStart,
      plannedEnd,
      clientCostCode,
      businessUnitId
    ]);
    var data = taskData;
    if (data != null) list.add(data);

    return list;
  }

  @override
  String getDBPath() => "tasks." + getDBName();

  void setStatus(String newStatus, {String? reason}) {
    if (newStatus == "Completed") {
      markCompleted();
    } else {
      var batch = FirebaseFirestore.instance.batch();
      status.setValueWithReason(newStatus, reason, batch);
      status.saveItem(batch);
      batch.commit();
      APMRoot.global.newQueries.workOrderListener.notifyListeners();
    }
  }

  void clearCompleted() {
    BatchHelper.saveAndCommitStringAttribute(status, "In Progress");
    APMRoot.global.newQueries.workOrderListener.notifyListeners();
  }

  void markCompleted() async {
    var batch = FirebaseFirestore.instance.batch();
    status.setValue("Completed", batch);
    status.saveItem(batch);
    batch.commit();
    APMRoot.global.newQueries.workOrderListener.notifyListeners();
  }

  @override
  void saveDirectItems(WriteBatch batch) async {
    var helper = DatabaseHelperManager.global();
    var path = getDBPath();

    helper.updateProperties(
        path,
        {
          "TaskType": taskType,
          "ProjectId": projectId,
          "AssignedUsers": assignedUsers,
          "AssetId": asset.id,
          "WorkOrderId": workOrder.id
        },
        batch);
  }

  @override
  void updateDirectPropertiesFromMap(Map map) {
    dynamic assignedUsersFromMap =
        map.containsKey("AssignedUsers") ? map["AssignedUsers"] : null;
    if (assignedUsersFromMap is List<dynamic>) {
      List<String> users = [];
      for (var item in assignedUsersFromMap) {
        users.add(item.toString());
      }

      assignedUsers = users;
    } else if (assignedUsersFromMap == null) {
      assignedUsers = [];
    }

    notifyListeners();
  }

  @override
  String getDisplayName() {
    return "Task";
  }
}
