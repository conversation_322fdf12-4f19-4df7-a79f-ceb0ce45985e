﻿using System;
using System.Collections.Generic;
using System.Linq;
namespace CommonDataInterface
{
  public class ChangeLogBase<ValueType> : DataModelItem {
    public override string DisplayName => "Change Log";

    public List<ChangeLogEntry<ValueType>> entries;
    [Newtonsoft.Json.JsonIgnore]
    private DataModelItem _parent;
    private String name;
    protected Func<object, ValueType> _conversion;

    public override DateTime? GetLastChangedTime()
    {
      if (entries.Count == 0) {
        return null;
      }

      return entries.OrderBy(a => a.TimeChanged).LastOrDefault()?.TimeChanged;
    }

    public ChangeLogBase(String name, DataModelItem parent, List<ChangeLogEntry<ValueType>> entries) : base(parent)
    {
      this.name = name;
      this.entries = entries;
    }

    public void SetConversionMethod(Func<object, ValueType> conversionMethod)
    {
      _conversion = conversionMethod;
    }


    public override DataModelItem[] GetChildren() {
      return new DataModelItem[0];
    }

    
    internal override String GetDBName() {
      return name;
    }

    public override void UpdateFromMap(Dictionary<string, object> map){
      if (map == null)
        return;

      foreach (var entry in map) {
        try { 
          var subMap = entry.Value as Dictionary<string, object>;
          if (subMap == null)
            continue;

          if (entries.FirstOrDefault((element) => element.Key == entry.Key) != null){
            continue;
          }

          String userName = null;
          long? timeChanged = null;
          ValueType value = default(ValueType);

          foreach (var item in subMap){
            if (item.Key == "U") {
              userName = item.Value?.ToString();
            }
            if (item.Key == "T"){
              timeChanged = long.Parse(item.Value.ToString());
            }
            if (item.Key == "V"){
              var conversion = _conversion;
              if (conversion != null){
                value = conversion.Invoke(item.Value);
              }
              else{
                value = item.Value is ValueType valueAsType ? valueAsType : default(ValueType);
              }
              
            }
          }

          if (userName == null || timeChanged == null)
            continue;
          String key = entry.Key;

          var dateTime = DateTimeOffset.FromUnixTimeMilliseconds(timeChanged.Value).DateTime;

          var utcDateTime = new DateTime(dateTime.Ticks, DateTimeKind.Utc);
          var local = utcDateTime.ToLocalTime();

          ChangeLogEntry<ValueType> entry1 = new ChangeLogEntry<ValueType>(key, value, local, userName);
          entries.Add(entry1);

        }
        catch (Exception ex) {
          Console.WriteLine("Error: Failed to read change log entry");
          continue;
        }
      }

      entries.Sort((entryA, entryB) => entryA.TimeChanged.CompareTo(entryB.TimeChanged));
     
    }

    
  }
}