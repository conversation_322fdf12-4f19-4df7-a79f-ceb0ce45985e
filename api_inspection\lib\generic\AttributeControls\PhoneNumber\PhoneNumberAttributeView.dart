import 'package:api_inspection/generic/AttributeControls/PhoneNumber/PhoneNumberAttributeViewEditable.dart';
import 'package:api_inspection/generic/AttributeControls/PhoneNumber/PhoneNumberAttributeViewNonEditable.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
import 'package:flutter/material.dart';

import '../AttributeStructure.dart';

class PhoneNumberAttributeView extends StatefulWidget {
  final PhoneNumberAttribute _attribute;
  final IsEditableController? editingController;
  final bool showPhotos;
  final bool showComments;

  const PhoneNumberAttributeView(this._attribute,
      {Key? key,
      this.editingController,
      this.showPhotos = true,
      this.showComments = true})
      : super(key: key);

  @override
  _PhoneNumberAttributeViewState createState() =>
      _PhoneNumberAttributeViewState();
}

class _PhoneNumberAttributeViewState extends State<PhoneNumberAttributeView> {
  @override
  Widget build(BuildContext context) {
    return AttributeStructure(widget._attribute,
        showComments: widget.showComments,
        showPhotos: widget.showPhotos,
        editingController: widget.editingController,
        editingBuilder: (context, updateListener) {
      return PhoneNumberAttributeViewEditable(
          widget._attribute, updateListener);
    }, nonEditingBuilder: (context) {
      return PhoneNumberAttributeViewNonEditable(widget._attribute);
    });
  }
}
