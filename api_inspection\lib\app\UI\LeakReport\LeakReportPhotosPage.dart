import 'dart:developer';

import 'package:api_inspection/app/DataModel/LeakReport/leakReport.dart';
import 'package:api_inspection/app/DataModel/LeakReport/leakReportPhoto.dart';
import 'package:api_inspection/generic/MediaControls/CameraPreviewPage.dart';
import 'package:api_inspection/generic/MediaControls/MediaSynchronizer.dart';
import 'package:api_inspection/app/UI/LeakReport/LeakReportPhotoPage.dart';
import 'package:api_inspection/generic/MediaControls/photo_widgets_for_collection.dart';
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'package:api_inspection/generic/AppStyle.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'package:darq/darq.dart';

class LeakReportPhotosPage extends StatefulWidget {
  final LeakReport leakReport;

  const LeakReportPhotosPage(this.leakReport, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return LeakReportPhotosPageState();
  }
}

class LeakReportPhotosPageState extends State<LeakReportPhotosPage> {
  void onPhotoSelected(LeakReportPhoto photo) {
    Navigator.of(context)
        .push(
            MaterialPageRoute(builder: (context) => LeakReportPhotoPage(photo)))
        .then((value) => setState(() {}));
  }

  void takePhotoClicked() {
    var newPhoto = LeakReportPhoto(
        widget.leakReport.leakReportPhotos, const Uuid().v4().toString());
    widget.leakReport.leakReportPhotos.addItem(newPhoto);
    var batch = FirebaseFirestore.instance.batch();
    widget.leakReport.leakReportPhotos.saveItem(batch);
    batch.commit();
    Navigator.of(context)
        .push(MaterialPageRoute(
            builder: (context) => LeakReportPhotoPage(newPhoto)))
        .then((value) => setState(() {}));

    Navigator.of(context, rootNavigator: true)
        .push(MaterialPageRoute(
            builder: (context) => PhotoPreviewControl((photo) {
                  setState(() {
                    IMediaSynchronizer.getMediaSynchronizer()
                        .onPhotoTaken(photo, newPhoto.photos);
                  });
                })))
        .then((value) => setState(() {}));
  }

  Widget buildPhotoCard(LeakReportPhoto photo) {
    List<Widget> photoWidgets = [];

    for (var mediaEntry in photo.photos.getPhotos()) {
      try {
        photoWidgets.add(Container(
            width: AppStyle.global.pixels100,
            height: AppStyle.global.pixels100,
            margin: const EdgeInsets.all(8),
            padding: EdgeInsets.all(AppStyle.global.pixels20),
            decoration: const BoxDecoration(
              color: Color.fromARGB(255, 41, 44, 52),
              borderRadius: BorderRadius.all(Radius.circular(8)),
            ),
            child: FittedBox(
              fit: BoxFit.fill,
              child: MediaEntryImage(mediaEntry: mediaEntry),
            )));
      } catch (e) {
        log(e.toString(),
            name: 'LeakReportPhotosPageState:buildPhotoCard',
            time: DateTime.now());
      }
    }

    return Container(
        margin: const EdgeInsets.all(10),
        padding: const EdgeInsets.all(5),
        color: Colors.blueGrey[700],
        child: Row(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text("Photo",
                    style: TextStyle(color: Colors.white, fontSize: 16)),
                Container(
                    constraints: BoxConstraints(
                        maxWidth: AppStyle.global.pixels100 +
                            AppStyle.global.pixels50),
                    child: Text(
                        photo.description.getValue() ?? "No description",
                        style:
                            const TextStyle(color: Colors.white, fontSize: 14)))
              ],
            ),
            Expanded(child: Row(children: photoWidgets))
          ],
        ));
  }

  @override
  void dispose() {
    super.dispose();
    widget.leakReport.leakReportPhotos
        .removeListener(leakReportCollectionChanged);
  }

  @override
  void didUpdateWidget(LeakReportPhotosPage oldWidget) {
    for (var item in photosListeningTo) {
      item.removeListener(leakReportChanged);
    }
    oldWidget.leakReport.leakReportPhotos
        .removeListener(leakReportCollectionChanged);
    widget.leakReport.leakReportPhotos.addListener(leakReportCollectionChanged);

    for (var item in widget.leakReport.leakReportPhotos.getEntries()) {
      photosListeningTo.add(item);
      item.addListener(leakReportChanged);
    }

    super.didUpdateWidget(oldWidget);
  }

  List<LeakReportPhoto> photosListeningTo = [];

  @override
  void initState() {
    widget.leakReport.leakReportPhotos.addListener(leakReportCollectionChanged);
    super.initState();
  }

  void leakReportChanged() {
    setState(() {});
  }

  void leakReportCollectionChanged() {
    var removedEntries = photosListeningTo
        .except(widget.leakReport.leakReportPhotos.getEntries());
    var missingEntries = widget.leakReport.leakReportPhotos
        .getEntries()
        .except(photosListeningTo);

    for (var entry in removedEntries) {
      entry.removeListener(leakReportChanged);
      photosListeningTo.remove(entry);
    }
    for (var entry in missingEntries) {
      entry.addListener(leakReportChanged);
      photosListeningTo.add(entry);
    }

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    var photos = widget.leakReport.leakReportPhotos.getEntries();

    return Scaffold(
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        appBar: AppBar(
          title: Text(
            "Leak Report Photos",
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        body: Column(
          children: [
            Expanded(
                child: ListView.builder(
                    itemBuilder: (context, index) {
                      var photo = photos[index];
                      return GestureDetector(
                          onTap: () {
                            onPhotoSelected(photo);
                          },
                          child: buildPhotoCard(photo));
                    },
                    itemCount: photos.length)),
            Divider(color: Colors.grey[100], indent: 10, endIndent: 10),
            AttributePadding.WithStdSidePadding(Container(
              margin: const EdgeInsets.fromLTRB(0, 0, 0, 10),
              child: ElevatedButton(
                  child: const Text(
                    'Take new photo',
                    style: TextStyle(fontSize: 16),
                  ),
                  onPressed: takePhotoClicked),
            )),
          ],
        ));
  }
}
