//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionAsset_Walkdown_Access_F
{
  public class SectionAsset_Walkdown_Access_F : DataModelItem {

    public override String DisplayName { 
      get {
        return "Asset Walkdown-Access-F";
      }
    }

    #region [-- Sub Sections --]

    #region [-- SectionInspectionAccessConditions --]
    private SectionInspectionAccessConditions _sectionInspectionAccessConditions;
    public SectionInspectionAccessConditions sectionInspectionAccessConditions {
        get {
            if (_sectionInspectionAccessConditions == null) {
               _sectionInspectionAccessConditions = new SectionInspectionAccessConditions(this);
            }

            return _sectionInspectionAccessConditions;
        }
    }
    #endregion [-- SectionInspectionAccessConditions --]
    
    #region [-- SectionExternalSurfaceConditions --]
    private SectionExternalSurfaceConditions _sectionExternalSurfaceConditions;
    public SectionExternalSurfaceConditions sectionExternalSurfaceConditions {
        get {
            if (_sectionExternalSurfaceConditions == null) {
               _sectionExternalSurfaceConditions = new SectionExternalSurfaceConditions(this);
            }

            return _sectionExternalSurfaceConditions;
        }
    }
    #endregion [-- SectionExternalSurfaceConditions --]
    
    #region [-- SectionAccessibility --]
    private SectionAccessibility _sectionAccessibility;
    public SectionAccessibility sectionAccessibility {
        get {
            if (_sectionAccessibility == null) {
               _sectionAccessibility = new SectionAccessibility(this);
            }

            return _sectionAccessibility;
        }
    }
    #endregion [-- SectionAccessibility --]
    
    #region [-- SectionInternalAccessConditions --]
    private SectionInternalAccessConditions _sectionInternalAccessConditions;
    public SectionInternalAccessConditions sectionInternalAccessConditions {
        get {
            if (_sectionInternalAccessConditions == null) {
               _sectionInternalAccessConditions = new SectionInternalAccessConditions(this);
            }

            return _sectionInternalAccessConditions;
        }
    }
    #endregion [-- SectionInternalAccessConditions --]
    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionAsset_Walkdown_Access_F";

    public SectionAsset_Walkdown_Access_F(DataModelItem parent) : base(parent)
    {
            
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           sectionInspectionAccessConditions,
           sectionExternalSurfaceConditions,
           sectionAccessibility,
           sectionInternalAccessConditions,
        };
    }
  }
}
