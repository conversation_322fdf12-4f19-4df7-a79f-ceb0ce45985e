import 'package:api_inspection/app/batch_helper.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/UIControls/BooleanController.dart';
import 'package:api_inspection/generic/UIControls/TeamBooleanButtons.dart';

class BooleanAttributeViewNonEditable extends StatefulWidget {
  final BooleanAttribute _attribute;
  const BooleanAttributeViewNonEditable(this._attribute, {Key? key})
      : super(key: key);

  @override
  _BooleanAttributeViewNonEditableState createState() =>
      _BooleanAttributeViewNonEditableState();
}

class _BooleanAttributeViewNonEditableState
    extends State<BooleanAttributeViewNonEditable> {
  BooleanController? _controller;
  bool initialized = false;

  void initialize() {
    if (initialized) return;
    initialized = true;
    BooleanAttribute attribute = widget._attribute;
    attribute.addListener(onAttributeChanged);
  }

  void onAttributeChanged() {
    setState(() {
      BooleanController? controller = _controller;
      if (controller != null &&
          controller.getValue() != widget._attribute.getValue()) {
        bool? attributeValue = widget._attribute.getValue();
        controller.setValue(attributeValue);
      }
    });
  }

  @override
  void dispose() {
    BooleanAttribute attribute = widget._attribute;
    attribute.removeListener(onAttributeChanged);
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    initialize();
    BooleanController? controller = _controller;
    if (controller == null) {
      controller = BooleanController(widget._attribute.getValue());
      controller.addListener(() {
        setState(() {
          widget._attribute.setValue(controller!.getValue());
          BatchHelper.saveAndCommit(widget._attribute);
        });
      });
      _controller = controller;
    }
    return Container(
        margin: const EdgeInsets.fromLTRB(0, 10, 0, 10),
        child: TeamBooleanButtons(controller));
  }
}
