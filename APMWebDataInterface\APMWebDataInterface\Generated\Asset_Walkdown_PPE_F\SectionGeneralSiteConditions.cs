//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionAsset_Walkdown_PPE_F
{
  public class SectionGeneralSiteConditions : DataModelItem {

    public override String DisplayName { 
      get {
        return "General Site Conditions";
      }
    }

    #region [-- Sub Sections --]

    #region [-- SectionPermittingRequired --]
    private SectionPermittingRequired _sectionPermittingRequired;
    public SectionPermittingRequired sectionPermittingRequired {
        get {
            if (_sectionPermittingRequired == null) {
               _sectionPermittingRequired = new SectionPermittingRequired(this);
            }

            return _sectionPermittingRequired;
        }
    }
    #endregion [-- SectionPermittingRequired --]
    
    #region [-- SectionPersonnelAccessConditions --]
    private SectionPersonnelAccessConditions _sectionPersonnelAccessConditions;
    public SectionPersonnelAccessConditions sectionPersonnelAccessConditions {
        get {
            if (_sectionPersonnelAccessConditions == null) {
               _sectionPersonnelAccessConditions = new SectionPersonnelAccessConditions(this);
            }

            return _sectionPersonnelAccessConditions;
        }
    }
    #endregion [-- SectionPersonnelAccessConditions --]
    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributeAre_there_any_on_site_leaks;
    public PredefinedValueAttribute attributeVehicle_Accessibility;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionGeneralSiteConditions";

    public SectionGeneralSiteConditions(DataModelItem parent) : base(parent)
    {
            
        attributeAre_there_any_on_site_leaks = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Are there any on-site leaks?", databaseName: "PPEAW_Q061"); 
     
        attributeVehicle_Accessibility = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Readily accessible", null),
          new PredefinedValueOption("Partially accessible", null),
          new PredefinedValueOption("Difficult or No Access", null)
        }, false, this, "Vehicle Accessibility", databaseName: "PPEAW_Q065"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           sectionPermittingRequired,
           sectionPersonnelAccessConditions,
           attributeAre_there_any_on_site_leaks,
           attributeVehicle_Accessibility,
        };
    }
  }
}
