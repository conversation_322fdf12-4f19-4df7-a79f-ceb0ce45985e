import 'package:api_inspection/generic/AppStyle.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/project.dart';
import 'package:api_inspection/app/UI/Projects/ProjectAccountingDetailsPage.dart';
import 'package:api_inspection/app/UI/Projects/ProjectClientDetailsPage.dart';
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
import 'package:api_inspection/generic/UIControls/attributePadding.dart';

import 'LocationPage.dart';

class ProjectEditPage extends StatefulWidget {
  final Project project;
  const ProjectEditPage(this.project, {Key? key}) : super(key: key);

  @override
  _ProjectEditPageState createState() => _ProjectEditPageState();
}

class _ProjectEditPageState extends State<ProjectEditPage> {
  String getTitle() {
    var projectName = widget.project.name.getValue();
    if (projectName == null || projectName.isEmpty) return "Project Edit Page";
    return projectName;
  }

  void showClientDetailsPage() {
    var project = widget.project;
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => ProjectClientDetailsPage(project)));
  }

  void showAccountingDetailsPage() {
    var project = widget.project;
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) =>
                ProjectAccountingDetailsPage(project.accountingDetails)));
  }

  void showLocationPage() {
    var project = widget.project;
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => LocationPage(project.location)));
  }

  @override
  Widget build(BuildContext context) {
    var project = widget.project;
    return Scaffold(
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        appBar: AppBar(
          title: Text(
            getTitle(),
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        body: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              AttributePadding.WithStdPadding(project.name.buildWidget()),
              AttributePadding.WithStdPadding(
                  project.description.buildWidget()),
              AttributePadding.WithStdPadding(
                TeamToggleButton.withText(
                    'Client Details', showClientDetailsPage),
              ),
              AttributePadding.WithStdPadding(
                TeamToggleButton.withText(
                    'Accounting Details', showAccountingDetailsPage),
              ),
              AttributePadding.WithStdPadding(
                TeamToggleButton.withText('Location', showLocationPage),
              ),
            ],
          ),
        ));
  }
}
