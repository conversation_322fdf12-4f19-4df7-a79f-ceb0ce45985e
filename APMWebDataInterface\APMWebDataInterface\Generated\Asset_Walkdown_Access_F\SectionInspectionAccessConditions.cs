//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionAsset_Walkdown_Access_F
{
  public class SectionInspectionAccessConditions : DataModelItem {

    public override String DisplayName { 
      get {
        return "Inspection Access Conditions";
      }
    }

    #region [-- Sub Sections --]

    #region [-- SectionInsulation --]
    private SectionInsulation _sectionInsulation;
    public SectionInsulation sectionInsulation {
        get {
            if (_sectionInsulation == null) {
               _sectionInsulation = new SectionInsulation(this);
            }

            return _sectionInsulation;
        }
    }
    #endregion [-- SectionInsulation --]
    
    #region [-- SectionInspectionPorts --]
    private SectionInspectionPorts _sectionInspectionPorts;
    public SectionInspectionPorts sectionInspectionPorts {
        get {
            if (_sectionInspectionPorts == null) {
               _sectionInspectionPorts = new SectionInspectionPorts(this);
            }

            return _sectionInspectionPorts;
        }
    }
    #endregion [-- SectionInspectionPorts --]
    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionInspectionAccessConditions";

    public SectionInspectionAccessConditions(DataModelItem parent) : base(parent)
    {
            
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           sectionInsulation,
           sectionInspectionPorts,
        };
    }
  }
}
