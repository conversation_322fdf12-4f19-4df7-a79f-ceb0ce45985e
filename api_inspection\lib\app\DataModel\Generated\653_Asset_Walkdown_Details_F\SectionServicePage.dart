//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionService.dart';

// ignore: camel_case_types
class SectionServicePage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionService sectionService;

  const SectionServicePage(this.sectionService, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionServicePageState();
  }
}

class _SectionServicePageState extends State<SectionServicePage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionService,
        title: "Service",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionService.attributeServiceProductContents
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionService.attributeSpecific_Gravity
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionService.attributeIntended_Service
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
