//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class SectionManufacturerFabricator extends DataModelSection {
  @override
  String getDisplayName() => "Manufacturer (Fabricator)";
  SectionManufacturerFabricator(DataModelItem? parent)
      : super(parent: parent, sectionName: "Manufacturer (Fabricator)");

  // ignore: non_constant_identifier_names
  late StringAttribute attributeMFG_Name = StringAttribute(
      parent: this,
      displayName: "MFG Name",
      databaseName: "570AW_Q141",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late DateAttribute attributeMFG_Date = DateAttribute(
      parent: this,
      displayName: "MFG Date",
      databaseName: "570AW_Q142",
      areCommentsRequired: false);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeMFG_Name,
      attributeMFG_Date,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionManufacturerFabricator";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
