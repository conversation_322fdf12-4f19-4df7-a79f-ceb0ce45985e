﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace CommonDataInterface.Attributes
{
  public class MultiStringAttribute : MultiAttributeBase<string> {
    
    public override bool IsValueEqualTo(String other)
    {
      var values = other.Split(",");
      var currentValue = GetValue();

      var valuesToAdd = values.Where(a => !currentValue.Any(b => AreStringsEqual(a, b))).ToArray();

      var valuesToRemove = currentValue.Where(a => !values.Any(b => AreStringsEqual(a, b))).ToArray();

      return valuesToRemove.Length == 0 && valuesToAdd.Length == 0;
    }

    public override void SetGenericValueTo(String other)
    {
      var values = other.Split(",");
      SetValue(values.ToArray());
    }

    public void SetValue(String[] values)
    {
      var currentValue = GetValue();

      var valuesToAdd = values.Where(a => !currentValue.Any(b => AreStringsEqual(a, b))).ToArray();

      var valuesToRemove = currentValue.Where(a => !values.Any(b => AreStringsEqual(a, b))).ToArray();

      foreach (var value in valuesToAdd) {
        AddValue(value);
      }

      foreach (var value in valuesToRemove) {
        RemoveValue(value);
      }
    }

    public override String AttributeType => "MultiString";
    public List<String>? GetValue() {
      if (this.GetValueChangeLog().entries.Count == 0)
        return new List<string>();
      return this.GetValueChangeLog().GetCurrentEntries();
    }

    public override String GetPreviewText(){
      var value = GetValue();
      return value == null ? "" : value.AggregateEXT((a,b) => a + ", " + b);
    }
    
    protected bool AreStringsEqual(String? str1, String? str2){
      if ((String.IsNullOrWhiteSpace(str1)) && (String.IsNullOrWhiteSpace(str2)))
        return true;
      if (str1 == null)
        return false;
      if (str2 == null)
        return false;
      return str1 == str2;
    }



    private bool AreListsEqual(List<String>? str1, List<String>? str2){
      if ((str1 == null || str1.Count == 0) && (str2 == null || str2.Count == 0))
        return true;
      if (str1 == null || str2 == null)
        return false;
      if (str1.Count != str2.Count)
        return false;

      for (int i = 0; i < str1.Count; i++){
        if (!AreStringsEqual(str1[i], str2[i])){
          return false;
        }
      }
      return true;
    }

    public void RemoveValue(String? value){
      var currentValue = GetValue();
      if (currentValue != null && !currentValue.Any((element) => AreStringsEqual(element, value)))
        return;

      if (value == null || value.Length == 0){
        throw new Exception("Cannot add a null or empty value");
      }
      
      this.GetValueChangeLog().AddPendingChange(value, ChangeAction.Removed);

      NotifyListeners();
    }

    public void AddValue(String? value){
      var currentValue = GetValue();
      if (currentValue != null && currentValue.Any((element) => AreStringsEqual(element, value)))
        return;

      if (value == null || value.Length == 0){
        throw new Exception("Cannot add a null or empty value");
      }
      
      this.GetValueChangeLog().AddPendingChange(value, ChangeAction.Added);
      
      NotifyListeners();
    }

    public MultiStringAttribute(DataModelItem parent, String name, String databaseName, bool areCommentsRequired)
      : base(parent, name, databaseName, areCommentsRequired)
    {

    }

  }
}