import 'package:api_inspection/generic/AttributeControls/AttributeStructure.dart';
import 'package:api_inspection/generic/AttributeControls/PredefinedValue/Multi/Default/MultiPredefinedValueAttributeViewEditable.dart';
import 'package:api_inspection/generic/AttributeControls/PredefinedValue/Multi/MultiPredefinedValueAttributeViewNonEditable.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
import 'package:flutter/material.dart';

class MultiPredefinedValueAttributeView extends StatefulWidget {
  final MultiPredefinedValueAttribute _attribute;
  final IsEditableController? editingController;
  final bool showPhotos;
  final bool showComments;

  const MultiPredefinedValueAttributeView(this._attribute,
      {Key? key,
      this.editingController,
      this.showPhotos = true,
      this.showComments = true})
      : super(key: key);

  @override
  _MultiPredefinedValueAttributeViewState createState() =>
      _MultiPredefinedValueAttributeViewState();
}

class _MultiPredefinedValueAttributeViewState
    extends State<MultiPredefinedValueAttributeView> {
  @override
  Widget build(BuildContext context) {
    return AttributeStructure(widget._attribute,
        showComments: widget.showComments,
        showPhotos: widget.showPhotos,
        editingController: widget.editingController,
        editingBuilder: (context, updateListener) {
      return MultiPredefinedValueAttributeViewEditable(widget._attribute);
    }, nonEditingBuilder: (context) {
      return MultiPredefinedValueAttributeViewNonEditable(widget._attribute);
    });
  }
}
