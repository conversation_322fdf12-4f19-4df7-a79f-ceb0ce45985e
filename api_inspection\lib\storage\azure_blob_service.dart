import 'dart:typed_data';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import 'dart:io';

class AzureBlobService {
  static final AzureBlobService _instance = AzureBlobService._internal();
  
  factory AzureBlobService() {
    return _instance;
  }
  
  AzureBlobService._internal();
  
  // Azure Storage account configuration
  final String _storageAccountName = 'YOUR_STORAGE_ACCOUNT_NAME';
  final String _storageAccountKey = 'YOUR_STORAGE_ACCOUNT_KEY';
  final String _containerName = 'YOUR_CONTAINER_NAME';
  
  String get _blobEndpoint => 'https://$_storageAccountName.blob.core.windows.net';
  
  // Upload a file from path to Azure Blob Storage
  Future<String?> uploadFile(String filePath, {String? blobName, String? contentType}) async {
    try {
      final File file = File(filePath);
      final Uint8List fileBytes = await file.readAsBytes();
      
      // Generate a unique blob name if not provided
      blobName ??= '${const Uuid().v4()}_${filePath.split('/').last}';
      
      return await uploadBytes(fileBytes, blobName, contentType: contentType);
    } catch (e) {
      debugPrint('Error uploading file: $e');
      return null;
    }
  }
  
  // Upload bytes to Azure Blob Storage
  Future<String?> uploadBytes(Uint8List bytes, String blobName, {String? contentType}) async {
    try {
      final String url = '$_blobEndpoint/$_containerName/$blobName';
      final Uri uri = Uri.parse(url);
      
      // Get current time and format for headers
      final DateTime now = DateTime.now().toUtc();
      final String dateString = _formatDateForStorage(now);
      
      // Create headers
      final Map<String, String> headers = {
        'x-ms-date': dateString,
        'x-ms-version': '2020-04-08',
        'x-ms-blob-type': 'BlockBlob',
        'Content-Length': bytes.length.toString(),
      };
      
      if (contentType != null) {
        headers['Content-Type'] = contentType;
      }
      
      // Add authorization header
      final String stringToSign = _createStringToSign('PUT', bytes.length, contentType ?? '', headers, '/$_storageAccountName/$_containerName/$blobName');
      final String signature = _generateSignature(stringToSign);
      headers['Authorization'] = 'SharedKey $_storageAccountName:$signature';
      
      // Make the request
      final http.Response response = await http.put(
        uri,
        headers: headers,
        body: bytes,
      );
      
      if (response.statusCode == 201) {
        // Return the URL to the uploaded blob
        return url;
      } else {
        debugPrint('Failed to upload to blob storage: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      debugPrint('Error uploading bytes: $e');
      return null;
    }
  }
  
  // Download a file from Azure Blob Storage
  Future<Uint8List?> downloadFile(String blobName) async {
    try {
      final String url = '$_blobEndpoint/$_containerName/$blobName';
      final Uri uri = Uri.parse(url);
      
      // Get current time and format for headers
      final DateTime now = DateTime.now().toUtc();
      final String dateString = _formatDateForStorage(now);
      
      // Create headers
      final Map<String, String> headers = {
        'x-ms-date': dateString,
        'x-ms-version': '2020-04-08',
      };
      
      // Add authorization header
      final String stringToSign = _createStringToSign('GET', 0, '', headers, '/$_storageAccountName/$_containerName/$blobName');
      final String signature = _generateSignature(stringToSign);
      headers['Authorization'] = 'SharedKey $_storageAccountName:$signature';
      
      // Make the request
      final http.Response response = await http.get(uri, headers: headers);
      
      if (response.statusCode == 200) {
        return response.bodyBytes;
      } else {
        debugPrint('Failed to download from blob storage: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      debugPrint('Error downloading file: $e');
      return null;
    }
  }
  
  // Delete a file from Azure Blob Storage
  Future<bool> deleteFile(String blobName) async {
    try {
      final String url = '$_blobEndpoint/$_containerName/$blobName';
      final Uri uri = Uri.parse(url);
      
      // Get current time and format for headers
      final DateTime now = DateTime.now().toUtc();
      final String dateString = _formatDateForStorage(now);
      
      // Create headers
      final Map<String, String> headers = {
        'x-ms-date': dateString,
        'x-ms-version': '2020-04-08',
      };
      
      // Add authorization header
      final String stringToSign = _createStringToSign('DELETE', 0, '', headers, '/$_storageAccountName/$_containerName/$blobName');
      final String signature = _generateSignature(stringToSign);
      headers['Authorization'] = 'SharedKey $_storageAccountName:$signature';
      
      // Make the request
      final http.Response response = await http.delete(uri, headers: headers);
      
      return response.statusCode == 202;
    } catch (e) {
      debugPrint('Error deleting file: $e');
      return false;
    }
  }
  
  // Generate a Shared Access Signature (SAS) URL for a blob
  Future<String?> generateSasUrl(String blobName, {Duration expiryDuration = const Duration(hours: 1)}) async {
    try {
      final DateTime now = DateTime.now().toUtc();
      final DateTime expiry = now.add(expiryDuration);
      
      // Format times as required by Azure Storage
      final String startTime = _formatDateForSas(now);
      final String expiryTime = _formatDateForSas(expiry);
      
      // Build the string to sign
      final String stringToSign = [
        'r', // Read permission
        startTime,
        expiryTime,
        '/$_storageAccountName/$_containerName/$blobName',
        '', // Signed identifier
        '', // IP range
        'https', // Protocol
        '2020-04-08', // Storage service version
      ].join('\n');
      
      // Generate the signature
      final String signature = _generateSignature(stringToSign);
      final String encodedSignature = Uri.encodeComponent(signature);
      
      // Build the SAS token
      final String sasToken = 'sv=2020-04-08'
          '&sr=b'
          '&sig=$encodedSignature'
          '&st=$startTime'
          '&se=$expiryTime'
          '&spr=https'
          '&sp=r';
      
      return '$_blobEndpoint/$_containerName/$blobName?$sasToken';
    } catch (e) {
      debugPrint('Error generating SAS URL: $e');
      return null;
    }
  }
  
  // Format date for storage header
  String _formatDateForStorage(DateTime dateTime) {
    return '${dateTime.toIso8601String().replaceAll('T', ' ').split('.')[0]} GMT';
  }
  
  // Format date for SAS token
  String _formatDateForSas(DateTime dateTime) {
    final String isoDate = dateTime.toIso8601String().split('.')[0] + 'Z';
    return Uri.encodeComponent(isoDate);
  }
  
  // Create the string to sign for authentication
  String _createStringToSign(
    String verb,
    int contentLength,
    String contentType,
    Map<String, String> headers,
    String resourcePath,
  ) {
    final Map<String, String> canonicalizedHeaders = {};
    
    headers.forEach((key, value) {
      if (key.startsWith('x-ms-')) {
        canonicalizedHeaders[key.toLowerCase()] = value;
      }
    });
    
    final List<String> canonicalizedHeadersList = canonicalizedHeaders.keys.toList()..sort();
    final String canonicalizedHeadersString = canonicalizedHeadersList
        .map((key) => '$key:${canonicalizedHeaders[key]}')
        .join('\n');
    
    return [
      verb,
      '', // Content-Encoding
      '', // Content-Language
      contentLength > 0 ? contentLength.toString() : '',
      '', // Content-MD5
      contentType,
      '', // Date
      '', // If-Modified-Since
      '', // If-Match
      '', // If-None-Match
      '', // If-Unmodified-Since
      '', // Range
      canonicalizedHeadersString,
      resourcePath,
    ].join('\n');
  }
  
  // Generate HMAC-SHA256 signature
  String _generateSignature(String stringToSign) {
    final List<int> keyBytes = base64.decode(_storageAccountKey);
    final List<int> messageBytes = utf8.encode(stringToSign);
    
    final Hmac hmac = Hmac(sha256, keyBytes);
    final Digest digest = hmac.convert(messageBytes);
    
    return base64.encode(digest.bytes);
  }
}
