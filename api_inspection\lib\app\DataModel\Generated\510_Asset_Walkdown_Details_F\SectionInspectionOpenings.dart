//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';

// ignore: camel_case_types
class SectionInspectionOpenings extends DataModelCollectionItem {
  @override
  String getDisplayName() => "Inspection Openings";

  SectionInspectionOpenings(String id, DataModelItem parent)
      : super(id, parent);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeOpening_Type = StringAttribute(
      parent: this,
      displayName: "Opening Type",
      databaseName: "653AW_Q171",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late IntegerAttribute attributeOpening_Number = IntegerAttribute(
    parent: this,
    displayName: "Opening Number",
    databaseName: "653AW_Q172",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "",
  );

  // ignore: non_constant_identifier_names
  late StringAttribute attributeOpening_Size = StringAttribute(
      parent: this,
      displayName: "Opening Size",
      databaseName: "653AW_Q173",
      areCommentsRequired: false);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeOpening_Type,
      attributeOpening_Number,
      attributeOpening_Size,
    ]);
    return children;
  }
}
