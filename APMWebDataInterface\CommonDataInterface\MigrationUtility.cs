using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Azure.Cosmos;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace CommonDataInterface
{
    public class MigrationUtility
    {
        private readonly string _cosmosEndpoint;
        private readonly string _cosmosKey;
        private readonly string _databaseId;
        private readonly string _storageConnectionString;
        private readonly ILogger _logger;

        public MigrationUtility(
            string cosmosEndpoint,
            string cosmosKey,
            string databaseId,
            string storageConnectionString,
            ILogger logger)
        {
            _cosmosEndpoint = cosmosEndpoint;
            _cosmosKey = cosmosKey;
            _databaseId = databaseId;
            _storageConnectionString = storageConnectionString;
            _logger = logger;
        }

        /// <summary>
        /// Migrates data from a JSON export file to Cosmos DB
        /// </summary>
        /// <param name="jsonFilePath">Path to the exported Firestore JSON file</param>
        /// <param name="containerId">Target Cosmos DB container ID</param>
        /// <param name="partitionKeyPath">Partition key path for Cosmos DB</param>
        /// <returns>Number of documents migrated</returns>
        public async Task<int> MigrateJsonDataToCosmosDb(string jsonFilePath, string containerId, string partitionKeyPath)
        {
            try
            {
                _logger.LogInformation($"Starting migration from {jsonFilePath} to container {containerId}");
                
                // Read the JSON file
                string jsonContent = await File.ReadAllTextAsync(jsonFilePath);
                var documents = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(jsonContent);
                
                if (documents == null || documents.Count == 0)
                {
                    _logger.LogWarning($"No documents found in file {jsonFilePath}");
                    return 0;
                }
                
                // Initialize Cosmos DB client
                using CosmosClient cosmosClient = new CosmosClient(_cosmosEndpoint, _cosmosKey);
                
                // Get or create the database
                Database database = await cosmosClient.CreateDatabaseIfNotExistsAsync(_databaseId);
                
                // Create container if it doesn't exist
                ContainerProperties containerProperties = new ContainerProperties(containerId, partitionKeyPath);
                Container container = await database.CreateContainerIfNotExistsAsync(containerProperties);
                
                int migrated = 0;
                
                // Migrate each document
                foreach (var document in documents)
                {
                    try
                    {
                        // Ensure document has an 'id' property
                        if (!document.ContainsKey("id"))
                        {
                            document["id"] = Guid.NewGuid().ToString();
                        }
                        
                        // Add migration metadata
                        document["migratedAt"] = DateTime.UtcNow;
                        document["migratedFrom"] = "Firebase";
                        
                        // Create the item in Cosmos DB
                        await container.CreateItemAsync(document);
                        migrated++;
                        
                        if (migrated % 100 == 0)
                        {
                            _logger.LogInformation($"Migrated {migrated} documents so far");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"Error migrating document: {ex.Message}");
                    }
                }
                
                _logger.LogInformation($"Migration complete. {migrated} documents migrated to Cosmos DB");
                return migrated;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Migration failed: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// Migrates files from a directory to Azure Blob Storage
        /// </summary>
        /// <param name="sourceFolderPath">Source folder containing files</param>
        /// <param name="containerName">Target Azure Storage container name</param>
        /// <returns>Number of files migrated</returns>
        public async Task<int> MigrateFilesToBlobStorage(string sourceFolderPath, string containerName)
        {
            try
            {
                _logger.LogInformation($"Starting file migration from {sourceFolderPath} to container {containerName}");
                
                // Initialize Blob Service Client
                BlobServiceClient blobServiceClient = new BlobServiceClient(_storageConnectionString);
                
                // Get or create the container
                BlobContainerClient containerClient = blobServiceClient.GetBlobContainerClient(containerName);
                await containerClient.CreateIfNotExistsAsync();
                
                // Get all files from source directory
                string[] filePaths = Directory.GetFiles(sourceFolderPath, "*", SearchOption.AllDirectories);
                
                int migrated = 0;
                
                // Upload each file
                foreach (string filePath in filePaths)
                {
                    try
                    {
                        string fileName = Path.GetFileName(filePath);
                        string relativePath = filePath.Substring(sourceFolderPath.Length).TrimStart(Path.DirectorySeparatorChar);
                        
                        BlobClient blobClient = containerClient.GetBlobClient(relativePath);
                        
                        // Upload the file
                        await blobClient.UploadAsync(filePath, true);
                        migrated++;
                        
                        if (migrated % 50 == 0)
                        {
                            _logger.LogInformation($"Migrated {migrated} files so far");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"Error uploading file {filePath}: {ex.Message}");
                    }
                }
                
                _logger.LogInformation($"File migration complete. {migrated} files migrated to Azure Blob Storage");
                return migrated;
            }
            catch (Exception ex)
            {
                _logger.LogError($"File migration failed: {ex.Message}");
                throw;
            }
        }
    }
}
