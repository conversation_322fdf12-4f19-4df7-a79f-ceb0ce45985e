//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'Section510EXT_PVCKLSTSEC001.dart';

// ignore: camel_case_types
class Section510EXT_PVCKLSTSEC001Page extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final Section510EXT_PVCKLSTSEC001 section510EXT_PVCKLSTSEC001;

  const Section510EXT_PVCKLSTSEC001Page(this.section510EXT_PVCKLSTSEC001,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _Section510EXT_PVCKLSTSEC001PageState();
  }
}

class _Section510EXT_PVCKLSTSEC001PageState
    extends State<Section510EXT_PVCKLSTSEC001Page> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.section510EXT_PVCKLSTSEC001,
        title: "GENERAL INFORMATION",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC001
                      .attributeIs_the_asset_appropriately_prepared_for_inspection
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC001
                      .attribute510EXT_PVCKLSTSEC001Q002
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
