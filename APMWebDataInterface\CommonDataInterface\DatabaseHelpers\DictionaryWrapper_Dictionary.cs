﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace CommonDataInterface.DatabaseHelpers
{
  public class DictionaryWrapper_Dictionary : IDictionaryWrapper
  {
    private Dictionary<string, object> _dictionary;


    public DictionaryWrapper_Dictionary(Dictionary<string, object> dictionary)
    {
      _dictionary = dictionary;
    }

    
    public DataEntry this[String key] {
      get {
        var item = _dictionary[key];
        return new DataEntry{Key = key, Value = item};
      }
    }

    public bool ContainsKey(String key)
    {
      return _dictionary.ContainsKey(key);
    }

    public DataEntry[] GetEntries()
    {
      return _dictionary.Select(a => new DataEntry {Key = a.Key, Value = a.Value}).ToArray();
    }
  }
}