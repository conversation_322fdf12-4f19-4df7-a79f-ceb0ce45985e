﻿using CheckListGen;

namespace ExcelCodeGenerator.CodeGenerators.CSharp
{
    public class BooleanAttributeGenerator : IAttributeGenerator
    {
        public string BuildInitialization(Question question)
        {
            return
                @"
        " + question.DartVariableName + @" = new BooleanAttribute(this, displayName: """ + question.DisplayText +
                @""", databaseName: """ + question.DataName + @"""); ";
        }

        public string BuildDeclaration(Question question)
        {
            return
                @"public BooleanAttribute " + question.DartVariableName + ";";
        }
    }
}