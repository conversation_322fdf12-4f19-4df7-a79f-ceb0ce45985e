//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionAsset_Walkdown_PPE_F
{
  public class SectionOvergrownvegetation : DataModelItem {

    public override String DisplayName { 
      get {
        return "Overgrown vegetation";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributeIs_there_overgrown_vegation;
    public PredefinedValueAttribute attributeAbatement_required;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionOvergrownvegetation";

    public SectionOvergrownvegetation(DataModelItem parent) : base(parent)
    {
            
        attributeIs_there_overgrown_vegation = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Is there overgrown vegation?", databaseName: "PPEAW_Q111"); 
     
        attributeAbatement_required = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Abatement required?", databaseName: "PPEAW_Q112"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeIs_there_overgrown_vegation,
           attributeAbatement_required,
        };
    }
  }
}
