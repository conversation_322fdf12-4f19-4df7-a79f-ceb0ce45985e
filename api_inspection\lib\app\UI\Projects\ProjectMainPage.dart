import 'package:api_inspection/app/APMRoot.dart';
import 'package:api_inspection/app/DataModel/project.dart';
import 'package:api_inspection/app/UI/BusinessUnits/BusinessUnitSelector.dart';
import 'package:api_inspection/generic/AppRoot.dart';
import 'package:api_inspection/generic/AppStyle.dart';
import 'package:api_inspection/generic/MediaControls/MediaDownloadIndicator.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/UI/Projects/ProjectCardView.dart';

class ProjectsMainPage extends StatefulWidget {
  final String title = "Projects";

  const ProjectsMainPage({Key? key}) : super(key: key);

  @override
  _ProjectsMainPageState createState() => _ProjectsMainPageState();
}

class _ProjectsMainPageState extends State<ProjectsMainPage> {
  TextEditingController searchTextController = TextEditingController();

  bool doesProjectMatchFilters(Project project) {
    var buId = project.businessUnitId.getValue();
    if (buId == null || buId != APMRoot.global.selectedBusinessUnitId) {
      return false;
    }

    var searchText = searchTextController.text
        .toLowerCase()
        .split(' ')
        .where((element) => element.isNotEmpty)
        .toList();

    if (searchText.isEmpty) return true;

    List<String> filteredStrings = [];

    filteredStrings.add(project.name.getValue() ?? "");
    filteredStrings
        .add(project.accountingDetails.apmProjectNumber.getValue() ?? "");
    filteredStrings
        .add(project.accountingDetails.workOrderNumber.getValue() ?? "Not Set");
    filteredStrings.add(project.location.name.getValue() ?? "");
    filteredStrings.add(project.description.getValue() ?? "No Description");

    Map<String, bool> searchTextMap = <String, bool>{};
    for (var item in filteredStrings) {
      for (var text in searchText) {
        if (item.toLowerCase().contains(text)) searchTextMap[text] = true;
      }
    }
    if (searchTextMap.length == searchText.length) return true;

    return false;
  }

  @override
  void deactivate() {
    super.deactivate();
  }

  @override
  void dispose() {
    APMRoot.global.queries.projectQueries.projectsListener
        .removeListener(onProjectsChanged);
    APMRoot.global.queries.businessUnitQueries.selectedBusinessUnitIdListener
        .removeListener(onSelectedBusinessUnitIdChanged);
    APMRoot.global.queries.businessUnitQueries.businessUnitsChangedListener
        .removeListener(onBusinessUnitsChanged);
    APMRoot.global.queries.userQuery.userListener.removeListener(onUserChanged);

    super.dispose();
  }

  @override
  void initState() {
    super.initState();

    APMRoot.global.queries.projectQueries.projectsListener
        .addListener(onProjectsChanged);
    APMRoot.global.queries.businessUnitQueries.selectedBusinessUnitIdListener
        .addListener(onSelectedBusinessUnitIdChanged);
    APMRoot.global.queries.businessUnitQueries.businessUnitsChangedListener
        .addListener(onBusinessUnitsChanged);
    APMRoot.global.queries.userQuery.userListener.addListener(onUserChanged);
  }

  void onProjectsChanged() {
    setState(() {});
  }

  void onSelectedBusinessUnitIdChanged() {
    setState(() {});
  }

  void onUserChanged() {
    setState(() {});
  }

  void onBusinessUnitsChanged() {
    setState(() {});
  }

  int compareProjects(Project a, Project b) {
    return (a.name.getValue() ?? "").compareTo(b.name.getValue() ?? "");
  }

  InputDecoration getSearchFieldDecoration() {
    return InputDecoration(
      hintText: "projects search",
      hintStyle: TextStyle(color: Colors.grey[400], fontSize: 16),
      contentPadding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
      enabledBorder: const OutlineInputBorder(
        borderSide: BorderSide(color: Colors.white, width: 1.0),
      ),
      border: const OutlineInputBorder(
        borderSide: BorderSide(color: Colors.white, width: 1.0),
      ),
    );
  }

  void onSearchTextChanged(String? newText) {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> projectList = [];
    var projects = APMRoot.global.queries.projectQueries.projects;
    projects.sort(compareProjects);

    var user = AppRoot.global().currentUser!;
    var businessUnits = APMRoot.global.queries.businessUnitQueries.businessUnits
        .where((element) => user.effectiveBusinessUnitIds.contains(element.id));

    for (var project in projects) {
      if (doesProjectMatchFilters(project)) {
        projectList.add(ProjectCardView(project));
      }
    }

    return Column(
      children: [
        AppBar(
          title: Text(
            "Projects",
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
            overflow: TextOverflow.visible,
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
          actions: [
            BusinessUnitSelector(businessUnits: businessUnits.toList()),
            const MediaDownloadIndicator(),
            AppRoot.global().buildMenuButtonWidget(context)
          ],
          titleSpacing: 0,
        ),
        Container(
            margin: const EdgeInsets.fromLTRB(50, 10, 50, 0),
            height: AppStyle.global.pixels50,
            child: TextField(
                autocorrect: false,
                enableSuggestions: false,
                keyboardType: TextInputType.text,
                key: const ValueKey("ProjectSearchField"),
                decoration: getSearchFieldDecoration(),
                controller: searchTextController,
                onChanged: onSearchTextChanged,
                style: const TextStyle(color: Colors.white, fontSize: 16))),
        Divider(color: Colors.grey[100], indent: 10, endIndent: 10),
        Expanded(
            child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Expanded(
                  child: ListView(
                children: projectList,
              )),
            ],
          ),
        ))
      ],
    );
  }
}
