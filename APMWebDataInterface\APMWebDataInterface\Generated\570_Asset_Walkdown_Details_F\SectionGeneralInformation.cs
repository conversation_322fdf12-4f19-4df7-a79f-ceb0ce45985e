//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section570_Asset_Walkdown_Details_F
{
  public class SectionGeneralInformation : DataModelItem {

    public override String DisplayName { 
      get {
        return "General Information";
      }
    }

    #region [-- Sub Sections --]

    #region [-- SectionPhotos --]
    private SectionPhotos _sectionPhotos;
    public SectionPhotos sectionPhotos {
        get {
            if (_sectionPhotos == null) {
               _sectionPhotos = new SectionPhotos(this);
            }

            return _sectionPhotos;
        }
    }
    #endregion [-- SectionPhotos --]
    
    #region [-- SectionDesign --]
    private SectionDesign _sectionDesign;
    public SectionDesign sectionDesign {
        get {
            if (_sectionDesign == null) {
               _sectionDesign = new SectionDesign(this);
            }

            return _sectionDesign;
        }
    }
    #endregion [-- SectionDesign --]
    
    #region [-- SectionInspection --]
    private SectionInspection _sectionInspection;
    public SectionInspection sectionInspection {
        get {
            if (_sectionInspection == null) {
               _sectionInspection = new SectionInspection(this);
            }

            return _sectionInspection;
        }
    }
    #endregion [-- SectionInspection --]
    
    #region [-- SectionManufacturerFabricator --]
    private SectionManufacturerFabricator _sectionManufacturerFabricator;
    public SectionManufacturerFabricator sectionManufacturerFabricator {
        get {
            if (_sectionManufacturerFabricator == null) {
               _sectionManufacturerFabricator = new SectionManufacturerFabricator(this);
            }

            return _sectionManufacturerFabricator;
        }
    }
    #endregion [-- SectionManufacturerFabricator --]
    
    #region [-- SectionService --]
    private SectionService _sectionService;
    public SectionService sectionService {
        get {
            if (_sectionService == null) {
               _sectionService = new SectionService(this);
            }

            return _sectionService;
        }
    }
    #endregion [-- SectionService --]
    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributePipe_Class;
    public DateAttribute attributeInstallation_Date;
    public DateAttribute attributeIn_service_Date;
    public StringAttribute attributePID_No_;
    public StringAttribute attributeConstructionDesign_Drawing_Number;
    public PredefinedValueAttribute attributeLowest_Flange_Rating;
    public MultiPredefinedValueAttribute attributeConstruction_Method;
    public StringAttribute attribute570AWQ149;
    public MultiPredefinedValueAttribute attributePipe_Size;
    public MultiPredefinedValueAttribute attributePipe_Schedule;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionGeneralInformation";

    public SectionGeneralInformation(DataModelItem parent) : base(parent)
    {
            
        attributePipe_Class = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Class 1", null),
          new PredefinedValueOption("Class2", null),
          new PredefinedValueOption("Class 3", null),
          new PredefinedValueOption("Class 4", null),
          new PredefinedValueOption("N/A", null)
        }, true, this, "Pipe Class", databaseName: "570AW_Q125"); 
     
        attributeInstallation_Date = new DateAttribute(this, displayName: "Installation Date", databaseName: "570AW_Q143", areCommentsRequired: false); 
     
        attributeIn_service_Date = new DateAttribute(this, displayName: "In-service Date", databaseName: "570AW_Q144", areCommentsRequired: false); 
     
        attributePID_No_ = new StringAttribute(this, displayName: "P&ID No.", databaseName: "570AW_Q145"); 
     
        attributeConstructionDesign_Drawing_Number = new StringAttribute(this, displayName: "Construction/Design Drawing Number", databaseName: "570AW_Q146"); 
     
        attributeLowest_Flange_Rating = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("150", null),
          new PredefinedValueOption("300", null),
          new PredefinedValueOption("400", null),
          new PredefinedValueOption("600", null),
          new PredefinedValueOption("900", null),
          new PredefinedValueOption("1500", null),
          new PredefinedValueOption("2500", null)
        }, false, this, "Lowest Flange Rating", databaseName: "570AW_Q147"); 
     
        attributeConstruction_Method = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Welded", null),
          new PredefinedValueOption("Threaded", null),
          new PredefinedValueOption("Slip Joint (Bell & Spigot)", null),
          new PredefinedValueOption("Flanged-Bolted", null)
        }, true, this, "Construction Method", databaseName: "570AW_Q148"); 
     
        attribute570AWQ149 = new StringAttribute(this, displayName: "Does this line have threaded connections? (Injection, Drains, Vents, O-lets)", databaseName: "570AW_Q149"); 
     
        attributePipe_Size = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("0.5", null),
          new PredefinedValueOption("0.75", null),
          new PredefinedValueOption("1", null),
          new PredefinedValueOption("1.25", null),
          new PredefinedValueOption("1.5", null),
          new PredefinedValueOption("2", null),
          new PredefinedValueOption("2.5", null),
          new PredefinedValueOption("3", null),
          new PredefinedValueOption("3.5", null),
          new PredefinedValueOption("4", null),
          new PredefinedValueOption("4.5", null),
          new PredefinedValueOption("5", null),
          new PredefinedValueOption("6", null),
          new PredefinedValueOption("8", null),
          new PredefinedValueOption("10", null),
          new PredefinedValueOption("12", null),
          new PredefinedValueOption("14", null),
          new PredefinedValueOption("16", null),
          new PredefinedValueOption("18", null),
          new PredefinedValueOption("20", null),
          new PredefinedValueOption("24", null),
          new PredefinedValueOption("30", null),
          new PredefinedValueOption("36", null),
          new PredefinedValueOption("42", null)
        }, false, this, "Pipe Size", databaseName: "570AW_Q150", unit: "in"); 
     
        attributePipe_Schedule = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("5", null),
          new PredefinedValueOption("10", null),
          new PredefinedValueOption("20", null),
          new PredefinedValueOption("30", null),
          new PredefinedValueOption("40", null),
          new PredefinedValueOption("50", null),
          new PredefinedValueOption("60", null),
          new PredefinedValueOption("70", null),
          new PredefinedValueOption("80", null),
          new PredefinedValueOption("100", null),
          new PredefinedValueOption("120", null),
          new PredefinedValueOption("140", null),
          new PredefinedValueOption("160", null),
          new PredefinedValueOption("STD", null),
          new PredefinedValueOption("EH", null),
          new PredefinedValueOption("DBL.EH", null)
        }, false, this, "Pipe Schedule", databaseName: "570AW_Q151"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           sectionPhotos,
           sectionDesign,
           sectionInspection,
           sectionManufacturerFabricator,
           sectionService,
           attributePipe_Class,
           attributeInstallation_Date,
           attributeIn_service_Date,
           attributePID_No_,
           attributeConstructionDesign_Drawing_Number,
           attributeLowest_Flange_Rating,
           attributeConstruction_Method,
           attribute570AWQ149,
           attributePipe_Size,
           attributePipe_Schedule,
        };
    }
  }
}
