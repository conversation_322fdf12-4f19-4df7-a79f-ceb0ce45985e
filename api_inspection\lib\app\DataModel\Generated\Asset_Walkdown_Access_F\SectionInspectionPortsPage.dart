//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionInspectionPorts.dart';

// ignore: camel_case_types
class SectionInspectionPortsPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionInspectionPorts sectionInspectionPorts;

  const SectionInspectionPortsPage(this.sectionInspectionPorts, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionInspectionPortsPageState();
  }
}

class _SectionInspectionPortsPageState
    extends State<SectionInspectionPortsPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionInspectionPorts,
        title: "Inspection Ports",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionInspectionPorts.attributeExisting_inspection_ports
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionInspectionPorts.attributeInsulation_plugs_missing
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionInspectionPorts.attributeAdditional_ports_needed
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
