import 'package:api_inspection/app/DataModel/LeakReport/leakReport.dart';
import 'package:api_inspection/app/UI/BusinessUnits/BusinessUnitSelector.dart';
import 'package:api_inspection/generic/AppRoot.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/AttributeBase.dart';
import 'package:api_inspection/app/UI/LeakReport/LeakReportEditPage.dart';
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'package:api_inspection/app/APMRoot.dart';
import 'package:api_inspection/generic/AppStyle.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';

class LeakReportHomePage extends StatefulWidget {
  const LeakReportHomePage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return LeakReportHomePageState();
  }
}

class LeakReportHomePageState extends State<LeakReportHomePage> {
  String filter = "Active";
  TextEditingController searchTextController = TextEditingController();

  bool doesLeakReportMatchFilters(LeakReport leakReport) {
    bool isClosed =
        leakReport.status == "closed" || leakReport.status == "complete";

    var buId = leakReport.businessUnitId.getValue();
    if (buId == null || buId != APMRoot.global.selectedBusinessUnitId) {
      return false;
    }

    if (filter == "Active") {
      if (isClosed) return false;
    } else {
      if (!isClosed) return false;
    }

    var searchText = searchTextController.text
        .toLowerCase()
        .split(' ')
        .where((element) => element.isNotEmpty)
        .toList();

    if (searchText.isEmpty) return true;

    List<String> filteredStrings = [];

    filteredStrings.add(leakReport.status);
    filteredStrings.add(leakReport.apmNumber.getValue() ?? "");
    filteredStrings.add(leakReport.workDetail.attributeArea.getValue() ?? "");
    filteredStrings.add(leakReport.workDetail.attributeLease.getValue() ?? "");
    filteredStrings
        .add(leakReport.report.attributeEquipment_ID.getValue() ?? "");
    filteredStrings
        .add(leakReport.report.attributeEquipment_Description.getValue() ?? "");

    Map<String, bool> searchTextMap = <String, bool>{};
    for (var item in filteredStrings) {
      for (var text in searchText) {
        if (item.toLowerCase().contains(text)) searchTextMap[text] = true;
      }
    }
    if (searchTextMap.length == searchText.length) return true;

    return false;
  }

  void createNewLeakRepair() {
    var user = AppRoot.global().currentUser!.email;

    var report = LeakReport(const Uuid().v4().toString(), user, "active");
    var batch = FirebaseFirestore.instance.batch();
    report.businessUnitId
        .setValue(APMRoot.global.selectedBusinessUnitId, batch);

    report.saveItem(batch);
    batch.commit();

    APMRoot.global.queries.leakReports.leakReports.add(report);
  }

  void onLeakReportsChanged() {
    setState(() {});
  }

  @override
  void dispose() {
    APMRoot.global.queries.leakReports.leakReportsChangedListener
        .removeListener(onLeakReportsChanged);
    APMRoot.global.queries.businessUnitQueries.selectedBusinessUnitIdListener
        .removeListener(onSelectedBusinessUnitIdChanged);
    APMRoot.global.queries.businessUnitQueries.businessUnitsChangedListener
        .removeListener(onBusinessUnitsChanged);

    APMRoot.global.queries.userQuery.userListener.removeListener(onUserChanged);

    super.dispose();
  }

  @override
  void initState() {
    APMRoot.global.queries.leakReports.leakReportsChangedListener
        .addListener(onLeakReportsChanged);
    APMRoot.global.queries.businessUnitQueries.selectedBusinessUnitIdListener
        .addListener(onSelectedBusinessUnitIdChanged);
    APMRoot.global.queries.userQuery.userListener.addListener(onUserChanged);
    APMRoot.global.queries.businessUnitQueries.businessUnitsChangedListener
        .addListener(onBusinessUnitsChanged);
    super.initState();
  }

  void onSelectedBusinessUnitIdChanged() {
    setState(() {});
  }

  void onUserChanged() {
    setState(() {});
  }

  void onBusinessUnitsChanged() {
    setState(() {});
  }

  void leakReportSelected(LeakReport report) {
    Navigator.push(context,
            MaterialPageRoute(builder: (context) => LeakReportEditPage(report)))
        .then((value) => setState(() {}));
  }

  Widget buildCardItem(AttributeBase attribute) {
    return Container(
        margin: const EdgeInsets.all(2),
        child: Row(
          children: [
            Text(attribute.displayName + ": ",
                style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold)),
            Container(width: 5),
            Expanded(
              child: Text(attribute.getPreviewText(),
                  style: const TextStyle(color: Colors.white, fontSize: 15)),
            ),
          ],
        ));
  }

  Widget buildLeakReportCard(LeakReport report) {
    return Container(
        margin: const EdgeInsets.all(10),
        padding: const EdgeInsets.all(10),
        color: Colors.blueGrey[700],
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: buildCardItem(report.workDetail.attributeArea),
                ),
                Text(report.status == "null" ? "not started" : report.status,
                    style: const TextStyle(color: Colors.white, fontSize: 15))
              ],
            ),
            buildCardItem(report.workDetail.attributeLease),
            buildCardItem(report.report.attributeEquipment_ID),
            buildCardItem(report.workDetail.attributeJob_Description),
          ],
        ));
  }

  Widget getFilterWidget() {
    Widget allAssetsWidget;
    Widget myAssetsWidget;

    if (filter == "Active") {
      allAssetsWidget = Row(children: [
        SizedBox(
            width: AppStyle.global.pixels20,
            child: const Icon(
              Icons.arrow_forward,
              size: 16,
            )),
        Text("Active", style: TextStyle(color: Colors.blue[400], fontSize: 14))
      ]);
      myAssetsWidget = Row(children: [
        Container(width: AppStyle.global.pixels20),
        const Text("Closed",
            style: TextStyle(color: Colors.white, fontSize: 14))
      ]);
    } else {
      allAssetsWidget = Row(children: [
        Container(width: AppStyle.global.pixels20),
        const Text("Active",
            style: TextStyle(color: Colors.white, fontSize: 14))
      ]);
      myAssetsWidget = Row(children: [
        SizedBox(
          width: AppStyle.global.pixels20,
          child: const Icon(
            Icons.arrow_forward,
            size: 16,
          ),
        ),
        Text("Closed", style: TextStyle(color: Colors.blue[400], fontSize: 14))
      ]);
    }

    return TextButton(
        onPressed: () {
          setState(() {
            if (filter == "Active") {
              filter = "Closed";
            } else {
              filter = "Active";
            }
          });
        },
        child: Container(
            margin: const EdgeInsets.fromLTRB(0, 0, 10, 0),
            child: Column(children: [
              allAssetsWidget,
              Container(height: 5),
              myAssetsWidget
            ])));
  }

  InputDecoration getSearchFieldDecoration() {
    return InputDecoration(
      hintText: "leak reports",
      hintStyle: TextStyle(color: Colors.grey[400], fontSize: 16),
      contentPadding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
      enabledBorder: const OutlineInputBorder(
        borderSide: BorderSide(color: Colors.white, width: 1.0),
      ),
      border: const OutlineInputBorder(
        borderSide: BorderSide(color: Colors.white, width: 1.0),
      ),
    );
  }

  void onSearchTextChanged(String? newText) {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    var reports = APMRoot.global.queries.leakReports.leakReports;

    var user = AppRoot.global().currentUser!;
    var businessUnits = APMRoot.global.queries.businessUnitQueries.businessUnits
        .where((element) => user.effectiveBusinessUnitIds.contains(element.id));

    var filteredReports = reports
        .where((element) => doesLeakReportMatchFilters(element))
        .toList();

    return Scaffold(
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        appBar: AppBar(
          title: Text(
            "Leak Reporting",
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
            overflow: TextOverflow.visible,
          ),
          actions: [
            BusinessUnitSelector(businessUnits: businessUnits.toList())
          ],
          toolbarHeight: AppStyle.global.toolBarHeight,
          titleSpacing: 0,
        ),
        body: Column(
          children: [
            Container(height: 10),
            Row(
              children: [
                getFilterWidget(),
                Expanded(
                    child: SizedBox(
                        height: AppStyle.global.pixels50,
                        child: TextField(
                            autocorrect: false,
                            enableSuggestions: false,
                            keyboardType: TextInputType.text,
                            key: const ValueKey("AssetSearchField"),
                            decoration: getSearchFieldDecoration(),
                            controller: searchTextController,
                            onChanged: onSearchTextChanged,
                            style: const TextStyle(
                                color: Colors.white, fontSize: 16)))),
                Container(width: 75)
              ],
            ),
            Expanded(
                child: ListView.builder(
                    itemBuilder: (context, index) {
                      return GestureDetector(
                          onTap: () {
                            var leakReport = filteredReports[index];
                            leakReportSelected(leakReport);
                          },
                          child: buildLeakReportCard(filteredReports[index]));
                    },
                    itemCount: filteredReports.length)),
            Divider(color: Colors.grey[100], indent: 10, endIndent: 10),
            AttributePadding.WithStdSidePadding(Container(
              margin: const EdgeInsets.fromLTRB(0, 0, 0, 10),
              child: ElevatedButton(
                  child: const Text(
                    'Create New Leak Report',
                    style: TextStyle(fontSize: 16),
                  ),
                  onPressed: createNewLeakRepair),
            )),
          ],
        ));
  }
}
