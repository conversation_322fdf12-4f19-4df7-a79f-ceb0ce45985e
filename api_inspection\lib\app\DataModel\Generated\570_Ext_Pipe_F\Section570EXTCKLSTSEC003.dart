//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class Section570EXTCKLSTSEC003 extends DataModelSection {
  @override
  String getDisplayName() => "STEEL SUPPORTS";
  Section570EXTCKLSTSEC003(DataModelItem? parent)
      : super(parent: parent, sectionName: "STEEL SUPPORTS");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeIs_the_piping_properly_supported =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Is the piping properly supported:",
          databaseName: "570_EXT_CKLST_SEC003_Q001",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC003Q002 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are there broken, missing or insecure pipe anchorage components:  (Brackets, U-bolts, or clamps)",
          databaseName: "570_EXT_CKLST_SEC003_Q002",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC003Q003 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is piping misalignment present due to an issue with piping supports:",
          databaseName: "570_EXT_CKLST_SEC003_Q003",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeAre_pipe_rollers_shoes_or_slide_plates_restricted =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Are pipe rollers, shoes, or slide plates restricted:",
          databaseName: "570_EXT_CKLST_SEC003_Q004",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeIs_there_evidence_of_piping_distortion_due_to_pipe_movement =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is there evidence of piping distortion due to pipe movement:",
          databaseName: "570_EXT_CKLST_SEC003_Q005",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC003Q006 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are springs cans and or pipe hangers properly adjusted: (Spring can values obtained from the index plate shall be recorded)",
          databaseName: "570_EXT_CKLST_SEC003_Q006",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC003Q007 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are there indications of movement or deterioration of concrete footings:",
          databaseName: "570_EXT_CKLST_SEC003_Q007",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute570EXTCKLSTSEC003Q008 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Is there any evidence of corrosion, distortion, and or cracking of the steel supports:",
          databaseName: "570_EXT_CKLST_SEC003_Q008",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Cracking", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Corrosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Distortion", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC003Q009 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Has the remaining thickness of corroded supporting elements been determined:",
          databaseName: "570_EXT_CKLST_SEC003_Q009",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute570EXTCKLSTSEC003Q010 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Is there any evidence of corrosion, buckling or excessive deflection of the columns and or load-carrying beams:",
          databaseName: "570_EXT_CKLST_SEC003_Q010",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Buckling", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Corrosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Deflection", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeAre_foundation_anchor_bolts_loose_or_corroded =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Are foundation anchor bolts loose or corroded:",
          databaseName: "570_EXT_CKLST_SEC003_Q011",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC003Q012 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are small branch connections contacting pipe supports as a result of movement of a larger line:",
          databaseName: "570_EXT_CKLST_SEC003_Q012",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeAre_dummy_legs_retaining_moisture_or_water =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Are dummy legs retaining moisture or water:",
          databaseName: "570_EXT_CKLST_SEC003_Q013",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC003Q014 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is contact point / touch point corrosion present at piping to support interfaces:",
          databaseName: "570_EXT_CKLST_SEC003_Q014",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC003Q015 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are there indications of corrosion of pipe walls inside open ended Trunnion supports:",
          databaseName: "570_EXT_CKLST_SEC003_Q015",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeAre_supports_in_acceptable_condition_for_continued_service =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are supports in acceptable condition for continued service:",
          databaseName: "570_EXT_CKLST_SEC003_Q016",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeIs_the_piping_properly_supported,
      attribute570EXTCKLSTSEC003Q002,
      attribute570EXTCKLSTSEC003Q003,
      attributeAre_pipe_rollers_shoes_or_slide_plates_restricted,
      attributeIs_there_evidence_of_piping_distortion_due_to_pipe_movement,
      attribute570EXTCKLSTSEC003Q006,
      attribute570EXTCKLSTSEC003Q007,
      attribute570EXTCKLSTSEC003Q008,
      attribute570EXTCKLSTSEC003Q009,
      attribute570EXTCKLSTSEC003Q010,
      attributeAre_foundation_anchor_bolts_loose_or_corroded,
      attribute570EXTCKLSTSEC003Q012,
      attributeAre_dummy_legs_retaining_moisture_or_water,
      attribute570EXTCKLSTSEC003Q014,
      attribute570EXTCKLSTSEC003Q015,
      attributeAre_supports_in_acceptable_condition_for_continued_service,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section570EXTCKLSTSEC003";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
