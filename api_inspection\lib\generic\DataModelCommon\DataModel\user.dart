import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiStringAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/ConcretePhotoRoot.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/DataModelCommon/DatabaseInterfaces/FirebaseDatabaseHelper.dart';

class UnverifiedUserProfile extends ConcretePhotoRoot {
  @override
  String getDBPath() => "nonverifiedusers." + getDBName();

  @override
  String getDisplayName() => "user:" + email;

  String email;
  UnverifiedUserProfile(this.email) : super(email.replaceAll(".", "|"));

  void addFavoritedAsset(String assetId) {
    favoritedAssets.add(assetId);

    FirebaseDatabaseHelper helper = FirebaseDatabaseHelper.global();

    var batch = FirebaseFirestore.instance.batch();
    helper.updateItem(getDBPath() + ".favoritedAssets",
        FieldValue.arrayUnion([assetId]), batch);
    batch.commit();
  }

  void removeFavoritedAsset(String assetId) {
    favoritedAssets.remove(assetId);

    FirebaseDatabaseHelper helper = FirebaseDatabaseHelper.global();
    var batch = FirebaseFirestore.instance.batch();
    helper.updateItem(getDBPath() + ".favoritedAssets",
        FieldValue.arrayRemove([assetId]), batch);
    batch.commit();
  }

  List<String> favoritedAssets = [];

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    if (entry.key == "favoritedAssets" && entry.value is List<dynamic>) {
      List<String> assetIds = [];
      for (var item in entry.value as List<dynamic>) {
        assetIds.add(item.toString());
      }

      favoritedAssets = assetIds;
      notifyListeners();
      return true;
    }

    return false;
  }
}

class UserProfile extends ConcretePhotoRoot {
  UserProfile(this.email) : super(email.replaceAll(".", "|"));

  @override
  String getDBPath() => "users." + getDBName();

  @override
  String getDisplayName() => "user:" + email;

  String email;

  late StringAttribute name =
      StringAttribute(parent: this, displayName: "Name");

  late MultiStringAttribute businessUnitIds =
      MultiStringAttribute(this, "BusinessUnitIds");

  late List<String> effectiveBusinessUnitIds = [];

  String? role;

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([name, businessUnitIds]);
    return children;
  }

  @override
  String getDBName() {
    return email.replaceAll(".", "|");
  }

  @override
  void saveDirectItems(WriteBatch batch) async {
    FirebaseDatabaseHelper helper = FirebaseDatabaseHelper.global();
    helper.updateItem(getDBPath() + ".role", role, batch);
  }

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    if (entry.key == "role") {
      role = entry.value as String;
      return true;
    }
    if (entry.key == "EffectiveBusinessUnitIds") {
      for (var buId in entry.value) {
        effectiveBusinessUnitIds.add(buId as String);
      }
      return true;
    }
    return false;
  }
}
