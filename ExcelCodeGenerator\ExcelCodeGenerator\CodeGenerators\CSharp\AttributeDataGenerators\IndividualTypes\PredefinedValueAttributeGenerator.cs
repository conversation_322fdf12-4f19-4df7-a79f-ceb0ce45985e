﻿using System.Linq;
using CheckListGen;

namespace ExcelCodeGenerator.CodeGenerators.CSharp
{
    public class PredefinedValueAttributeGenerator : IAttributeGenerator
    {
        public string BuildInitialization(Question question)
        {
            var hasOther = question.AttributeType == Question.AttributeTypes.PredefinedValue ? "false" : "true";
            var unitField = string.IsNullOrWhiteSpace(question.Unit) ? "" : ", unit: \"" + question.Unit + "\"";
            return
                @"
        " + question.DartVariableName + @" = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          " + question.Choices.Select(a => "new PredefinedValueOption(\"" + a.<PERSON>sp<PERSON> + "\", null)")
                    .AggregateEXT((a, b) => a + ",\r\n          " + b) + @"
        }, " + hasOther + @", this, """ + question.DisplayText + @""", databaseName: """ + question.DataName + @"""" +
                unitField + @"); ";
        }

        public string BuildDeclaration(Question question)
        {
            return
                @"public PredefinedValueAttribute " + question.DartVariableName + ";";
        }
    }
}