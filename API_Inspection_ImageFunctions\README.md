# API_Inspection_ImageFunctions

API Inspection Project Azure Function Image handling functions.

  ## Image Fucntions:

  * ResizeImage - Function to resize images to minimal required quality.

## Setup and Configuration

### Prerequisites
  * Azure Subscription
  * Azure Resource Group
  * Azure Storage Account
  * Azure Storage Account Blob Containers
  * Azure Function App

### Setup
  #### Azure Resource Group
  PowerShell
  $resourceGroupName="AZNASC-API-DEV-RG"
  $location="southcentralus"
  New-AzResourceGroup -Name $resourceGroupName -Location $location

  Azure CLI
  resourceGroupName="AZNASC-API-DEV-RG"
  location="southcentralus"
  az group create --name $resourceGroupName --location $location

  #### Azure Storage Account
  PowerShell
  $functionstorage="aznascapidevstorage"
  New-AzStorageAccount -ResourceGroupName $resourceGroupName -AccountName $functionstorage -Location $location -SkuName Standard_LRS -Kind StorageV2        
  
  Azure CLI
  functionstorage="aznascapidevstorage"
  az storage account create --name $functionstorage --location $location --resource-group $resourceGroupName --sku Standard_LRS --kind StorageV2

  #### Azure Storage Account Blob Containers
  PowerShell
  $blobStorageAccountKey = (Get-AzStorageAccountKey -ResourceGroupName $resourceGroupName -Name $functionstorage).Key1
  $blobStorageContext = New-AzStorageContext -StorageAccountName $functionstorage -StorageAccountKey $blobStorageAccountKey
  New-AzStorageContainer -Name images -Context $blobStorageContext
  New-AzStorageContainer -Name thumbnails -Permission Container -Context $blobStorageContext
  
  Azure CLI
  blobStorageAccountKey=$(az storage account keys list -g $resourceGroupName \
  -n $functionstorage --query "[0].value" --output tsv)

  az storage container create --name images \
    --account-name $functionstorage \
    --account-key $blobStorageAccountKey

  az storage container create --name thumbnails \
    --account-name $functionstorage \
    --account-key $blobStorageAccountKey --public-access container

  #### Azure Function App
  PowerShell
  $functionapp="aznascapidevimageresizefunc"
  New-AzFunctionApp -Location $location -Name $functionapp -ResourceGroupName $resourceGroupName -Runtime PowerShell -StorageAccountName $functionstorage
  
  Azure CLI
  functionapp="aznascapidevimageresizefunc"
  az functionapp create --name $functionapp --storage-account $functionstorage --resource-group $resourceGroupName --consumption-plan-location $location --functions-version 3 

  ### Config
  PowerShell
  $storageConnectionString=$(az storage account show-connection-string --resource-group $resourceGroupName --name $functionstorage --query connectionString --output tsv)     
  Update-AzFunctionAppSetting -Name $functionapp -ResourceGroupName $resourceGroupName -AppSetting @{AzureWebJobsStorage=$storageConnectionString; DESTINATION_CONTAINER_NAME=resizedimages; RESIZE_WIDTH=360 RESIZE_QUALITY=75 RESIZE_COMPRESSIONLEVEL=9 FUNCTIONS_EXTENSION_VERSION=~2; 'FUNCTIONS_WORKER_RUNTIME'='dotnet'}

  Azure CLI
  storageConnectionString=$(az storage account show-connection-string --resource-group $resourceGroupName --name $functionstorage --query connectionString --output tsv)
  az functionapp config appsettings set --name $functionapp --resource-group $resourceGroupName --settings AzureWebJobsStorage=$storageConnectionString DESTINATION_CONTAINER_NAME=resizedimages RESIZE_WIDTH=360 RESIZE_QUALITY=75 RESIZE_COMPRESSIONLEVEL=9 FUNCTIONS_EXTENSION_VERSION=~2 FUNCTIONS_WORKER_RUNTIME=dotnet

  ### Deploy

