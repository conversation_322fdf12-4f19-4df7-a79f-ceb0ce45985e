﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using APMWebDataInterface.DataModel;
using APMWebDataInterface.ExampleDataModel;
using APMWebDataInterface.Generated.section510_Asset_Walkdown_Details_F;
using APMWebDataInterface.Generated.section570_Asset_Walkdown_Details_F;
using APMWebDataInterface.Generated.section653_Asset_Walkdown_Details_F;
using ExcelDataReader;
using NUnit.Framework;

namespace APMWebDataInterface.Tests
{

    public class ExcelRowImportData
    {
        public int RowNumber;

        public String ClientName;
        public String BusinessUnitName;
        public int Year;
        public String AssetId;
        public String Area;

        private String _category;
        public String AssetCategory
        {
            get
            {
                return _category;
            }
            set
            {

                var newValue = value;
                if (String.Equals(value, "exchanger", StringComparison.InvariantCultureIgnoreCase))
                    newValue = "Vessel";
                else if (value.ToLowerInvariant().Contains("f-line"))
                    newValue = "Piping";
                else if (value.ToLowerInvariant().Contains("filter"))
                    newValue = "Vessel";

                if (newValue.Equals("Vessel", StringComparison.InvariantCultureIgnoreCase))
                {
                    _category = "Vessel";
                }
                else if (newValue.Equals("Piping", StringComparison.InvariantCultureIgnoreCase))
                {
                    _category = "Piping";
                }
                else if (newValue.Contains("Tank", StringComparison.InvariantCultureIgnoreCase))
                {
                    _category = "Tank";
                }
                else
                {
                    throw new Exception("Unable to find asset category: " + value);
                }

            }
        }

        public String Month;
        public String PMDueDate;
        public String FullTaskType;
        public String TaskType;
        public String AssetDescription;

        public String FieldStartDate;
        public String FieldEndDate;

        public String Supervisor;
        public String ClientCostCode;

        public String ClientWorkOrderNumber;
        public String OperatingStatus;
        public String FieldStatus;
        public String TaskAssignee;
        public String Completed;

        public String QCStatus;
        public String QCStartDate;
        public String QCEndDate;
        public String CADStatus;
        public String CADStartDate;
        public String CADEndDate;
        public String FinalQCStatus;
        public String FinalQCStartDate;
        public String FinalQCEndDate;

        public String TaskComment;

        public String ProjectName;
        public String ProjectLocationName;
        public String ProjectCity;
        public String ProjectState;
        public String TeamDistrictNumber;
        public String TeamProjectNumber;
        public String ClientProjectNumber;
        public String ProjectStatus;
        public String ProjectDescription;

        public String ProjectPlannedStartDate;
        public String ProjectPlannedEndDate;


        public String WorkOrderPlannedStartDate;
        public String WorkOrderPlannedEndDate;
    }

    public class ImportProject
    {
        public String ClientName;
        public String BusinessUnitName;
        public String ProjectName;
        public String ProjectLocationName;
        public String ProjectCity;
        public String ProjectState;
        public String TeamDistrictNumber;
        public String TeamProjectNumber;
        public String ClientProjectNumber;
        public String ProjectStatus;
        public String ProjectDescription;

        public String ProjectPlannedStartDate;
        public String ProjectPlannedEndDate;

        public List<ImportAsset> Assets = new List<ImportAsset>();
    }

    public class ImportAsset
    {
        public String AssetDescription;

        public String OperatingStatus;
        public String AssetId;
        public String Area;
        public String AssetCategory;

        public String TeamProjectNumber;
        public String WorkOrderPlannedStartDate;
        public String WorkOrderPlannedEndDate;


        public List<ImportTask> Tasks = new List<ImportTask>();
    }

    public class ImportTask
    {
        public int rowNumber;

        public String PMDueDate;
        public String TaskType;

        public String ClientCostCode;
        public String ClientWorkOrderNumber;
        public String OperatingStatus;
        public String FieldStartDate;
        public String FieldEndDate;
        public String Supervisor;

        public String Comments;

    }

    ///
    /// Operating status matching the list
    ///
    /// Now there is a task type column
    /// </summary>
    class DataImporter
    {

        string filePath = "C:\\Users\\<USER>\\Documents\\data.xlsx";

        [TestCase("test")]
        [TestCase("2022-01")]
        [TestCase("2022-02")]
        [TestCase("2022-03")]
        [TestCase("2022-04")]
        [TestCase("2022-05")]
        [TestCase("2022-06")]
        [TestCase("2022-07")]
        [TestCase("2022-08")]
        [TestCase("2022-09")]
        [TestCase("2022-10")]
        [TestCase("2022-11")]
        [TestCase("2022-12")]
        public void RunImporter(String projectName)
        {
            var importer = new DataImporter();
            importer.ImportDataSet(projectName);
        }
        public void ImportDataSet(String projectName)
        {
            System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);
            List<ExcelRowImportData> dataToImport = new List<ExcelRowImportData>();
            using (var stream = File.Open(filePath, FileMode.Open, FileAccess.Read))
            {
                using (var reader = ExcelReaderFactory.CreateReader(stream))
                {
                    var result = reader.AsDataSet();

                    var table = result.Tables["Sheet1"];
                    int rowNum = 1;
                    foreach (System.Data.DataRow row in table.Rows)
                    {
                        rowNum = rowNum + 1;
                        if (rowNum <= 8)
                            continue;

                        ExcelRowImportData rowData = new ExcelRowImportData
                        {
                            RowNumber = rowNum - 1,

                            ClientName = row[1].ToString(),
                            BusinessUnitName = row[2].ToString(),
                            Year = int.Parse(row[3].ToString()),
                            AssetId = row[4].ToString(),
                            Area = row[5].ToString(),
                            AssetCategory = row[6].ToString(),
                            PMDueDate = row[7].ToString(),
                            FullTaskType = row[8].ToString(),
                            AssetDescription = row[9].ToString(),
                            ClientCostCode = row[10].ToString(),
                            ClientWorkOrderNumber = row[11].ToString(),
                            //ExternalPMWorkOrderNumber= row[12].ToString(),

                            OperatingStatus = row[13].ToString(),
                            Supervisor = row[14].ToString(),
                            TaskType = row[15].ToString(),
                            FieldStatus = row[16].ToString(),
                            TaskAssignee = row[17].ToString(),
                            FieldStartDate = row[18].ToString(),
                            FieldEndDate = row[19].ToString(),

                            ProjectName = row[20].ToString(),
                            ProjectLocationName = row[21].ToString(),
                            ProjectCity = row[22].ToString(),
                            ProjectState = row[23].ToString(),
                            TeamDistrictNumber = row[24].ToString(),
                            TeamProjectNumber = row[25].ToString(),
                            ClientProjectNumber = row[26].ToString(),
                            ProjectStatus = row[27].ToString(),
                            ProjectDescription = row[28].ToString(),
                            ProjectPlannedStartDate = row[29].ToString(),
                            ProjectPlannedEndDate = row[30].ToString(),
                            WorkOrderPlannedStartDate = row[31].ToString(),
                            WorkOrderPlannedEndDate = row[32].ToString(),

                            Completed = row[33].ToString(),

                            QCStatus = row[34].ToString(),
                            QCStartDate = row[35].ToString(),
                            QCEndDate = row[36].ToString(),
                            CADStatus = row[37].ToString(),
                            CADStartDate = row[38].ToString(),
                            CADEndDate = row[39].ToString(),
                            FinalQCStatus = row[40].ToString(),
                            FinalQCStartDate = row[41].ToString(),
                            FinalQCEndDate = row[42].ToString(),
                            // sent to data = row[43]
                            TaskComment = row[44].ToString(),
                        };
                        dataToImport.Add(rowData);

                    }
                }
            }


            dataToImport = dataToImport.Where(a => !a.FullTaskType.ToLower().Equals("piping - hydrotest")).ToList();

            var dataGroupedByAssetId = dataToImport.GroupBy(a => a.AssetId.ToLowerInvariant()).ToList();
            int numBeforeTrim = dataGroupedByAssetId.Count;

            var groupings = new Dictionary<string, List<ExcelRowImportData>>();

            foreach (var group in dataGroupedByAssetId.ToArray())
            {

                var byTaskType = group.GroupBy(a => a.TaskType).ToArray();

                var trimmedTasks = byTaskType.Select(a => a.First()).ToArray();
                groupings.Add(group.Key, trimmedTasks.ToList());


                var first = trimmedTasks.First();
                foreach (var item in trimmedTasks.Skip(1))
                {
                    if (!VerifyAssetFieldsAreEqual(first, item))
                    {

                        Console.WriteLine("Asset fields do not match for rows: " + first.RowNumber + " and " + item.RowNumber);
                        Console.WriteLine(first.RowNumber);
                        writeAssetInfo(first);
                        Console.WriteLine(item.RowNumber);
                        writeAssetInfo(item);
                        Console.WriteLine("\r\n\r\n");
                        dataGroupedByAssetId.Remove(group);
                        groupings.Remove(group.Key);
                        break;

                    }
                }
            }

            int numAfterTime = groupings.Count;

            int numTrimmed = (numBeforeTrim - numAfterTime);
            Console.WriteLine(numTrimmed + " assets trimmed from import because of data issues");

            List<ImportProject> projects = new List<ImportProject>();

            foreach (var assetGroup in groupings)
            {
                CreateAssetObjects(projects, assetGroup.Value);
            }



            foreach (var proj in projects)
            {
                foreach (var asset in proj.Assets)
                {
                    if (!(asset.AssetCategory == "Vessel" || asset.AssetCategory == "Piping" || asset.AssetCategory == "Tank"))
                    {
                        Console.WriteLine("Unable to find asset category");
                    }
                }

            }

            foreach (var proj in projects.OrderBy(a => a.ProjectName))
            {
                Console.WriteLine("[TestCase(\"" + proj.ProjectName + "\")]");
            }

            var project = projects.FirstOrDefault(a => a.ProjectName == projectName);
            if (project == null)
                return;

            SemaphoreSlim semaphore = new SemaphoreSlim(0);
            Task task = new Task(async () =>
            {
                await DoDBImport(new List<ImportProject>() { project });
                semaphore.Release();
            });
            task.Start();

            semaphore.Wait();

        }

        private string importEmail = "<EMAIL>";

        public async Task DoDBImport(List<ImportProject> projects)
        {
            var webInterface = APM_WebDataInterface.Global;
            await webInterface.Initialize(APM_WebDataInterface.Databases.localEmulator);

            foreach (var project in projects)
            {
                Console.WriteLine(project);
                var dbProject = await FindOrCreateDBProject(project);

                foreach (var asset in project.Assets)
                {
                    await CreateOrUpdateAsset(dbProject, asset);
                }
            }
        }

        public async Task<Project> FindOrCreateDBProject(ImportProject project)
        {

            var allProjects = APM_WebDataInterface.Global.GetProjects(importEmail);
            var match = allProjects.FirstOrDefault(a => String.Equals(a.name.GetValue(), project.ProjectName, StringComparison.InvariantCultureIgnoreCase));
            if (match != null)
            {
                return match;
            }

            var allClients = APM_WebDataInterface.Global.GetClients();
            var client = allClients.FirstOrDefault(c => string.Equals(c.Name.GetValue(), project.ClientName, StringComparison.InvariantCultureIgnoreCase));

            if (client == null)
            {
                client = new Client();
                client.id = Guid.NewGuid().ToString();
                client.Name.SetValue(project.ClientName);
                await client.SavePendingChanges(importEmail);
            }

            var allBusinessUnits = APM_WebDataInterface.Global.GetBusinessUnits();
            var businessUnit = allBusinessUnits.FirstOrDefault(b => string.Equals(b.Name.GetValue(), project.BusinessUnitName, StringComparison.InvariantCultureIgnoreCase));

            if (businessUnit == null)
            {
                businessUnit = new BusinessUnit();
                businessUnit.id = Guid.NewGuid().ToString();
                businessUnit.Name.SetValue(project.BusinessUnitName);
                businessUnit.ClientId = client.id;
                await businessUnit.SavePendingChanges(importEmail);
            }

            var location = new Location();
            location.businessUnitId.SetValue(businessUnit.id);
            location.name.SetValue(project.ProjectLocationName);
            location.city.SetValue(project.ProjectCity);
            location.region.SetValue(project.ProjectState);
            await location.SavePendingChanges(importEmail);
            var newProject = new Project(location.id);

            newProject.name.SetValue(project.ProjectName);
            newProject.businessUnitId.SetValue(businessUnit.id);
            newProject.accountingDetails.teamDistrictNumber.SetValue(project.TeamDistrictNumber);
            newProject.accountingDetails.teamProjectNumber.SetValue(project.TeamProjectNumber);
            newProject.accountingDetails.projectNumber.SetValue(project.ClientProjectNumber);
            newProject.status.SetValue(project.ProjectStatus);
            newProject.description.SetValue(project.ProjectDescription);

            if (DateTime.TryParse(project.ProjectPlannedStartDate, out var startDate))
            {
                newProject.plannedStart.SetValue(startDate);
            }
            if (DateTime.TryParse(project.ProjectPlannedEndDate, out var endDate))
            {
                newProject.plannedEnd.SetValue(endDate);
            }

            await newProject.SavePendingChanges(importEmail);
            return newProject;
        }

        public async Task CreateOrUpdateAsset(Project project, ImportAsset asset)
        {

            var assets = await APM_WebDataInterface.Global.GetAssetsAtLocation(new[] { project.locationId }, "<EMAIL>");

            var match = assets.FirstOrDefault(a => getAssetIdForAsset(a) == asset.AssetId);
            if (match == null)
            {
                match = new Asset(asset.AssetCategory, project.locationId);
            }
            match.businessUnitId.SetValue(project.businessUnitId.GetValue());
            match.area.SetValue(asset.Area);

            if (match.walkDown is Section510_Asset_Walkdown_Details_F vessel)
            {
                vessel.sectionIdentification.attributeNumber_or_ID.SetValue(asset.AssetId);
                vessel.sectionIdentification.attributeName.SetValue(asset.AssetId);
                vessel.sectionIdentification.attributeEquipment_Description.SetValue(asset.AssetDescription);
                vessel.sectionOperatingDesignConditions.attributeOperation_Status.SetValue(asset.OperatingStatus);

            }

            if (match.walkDown is Section570_Asset_Walkdown_Details_F piping)
            {
                piping.sectionIdentification.attributeNumber_or_Circuit_ID.SetValue(asset.AssetId);
                piping.sectionIdentification.attributeName.SetValue(asset.AssetId);
                piping.sectionIdentification.attributeEquipment_Description.SetValue(asset.AssetDescription);
                piping.sectionOperatingDesignConditions.attributeOperation_Status.SetValue(asset.OperatingStatus);

            }
            if (match.walkDown is Section653_Asset_Walkdown_Details_F tank)
            {
                tank.sectionIdentification.attributeName.SetValue(asset.AssetId);
                tank.sectionIdentification.attributeNumber_or_ID.SetValue(asset.AssetId);
                tank.sectionIdentification.attributeEquipment_Description.SetValue(asset.AssetDescription);
                tank.sectionOperatingDesignConditions.attributeOperation_Status.SetValue(asset.OperatingStatus);
            }

            await match.SavePendingChanges(importEmail);

            System.Threading.Thread.Sleep(5);

            var workOrders = await APM_WebDataInterface.Global.GetWorkOrders(new[] { project.id }, "<EMAIL>");
            var matchingWO = workOrders.FirstOrDefault(a => a.asset.id == match.id);

            if (matchingWO == null)
            {
                matchingWO = new WorkOrder(match, project.id);

            }

            if (DateTime.TryParse(asset.WorkOrderPlannedStartDate, out var woStartDate))
            {
                matchingWO.plannedStart.SetValue(woStartDate);
            }
            if (DateTime.TryParse(asset.WorkOrderPlannedEndDate, out var woEndDate))
            {
                matchingWO.plannedEnd.SetValue(woEndDate);
            }

            matchingWO.businessUnitId.SetValue(project.businessUnitId.GetValue());
            matchingWO.dueDate.SetValue(DateTime.Parse(asset.Tasks[0].PMDueDate));
            await matchingWO.SavePendingChanges(importEmail);

            foreach (var task in asset.Tasks)
            {
                try
                {
                    var matchingTask = matchingWO.tasks.FirstOrDefault(a => a.taskType == task.TaskType);
                    if (matchingTask == null)
                    {
                        if (task.TaskType == AssetWalkdownType)
                        {
                            matchingTask = matchingWO.AddNewTask(WorkOrder.TaskTypes.AssetWalkdown);
                        }
                        else if (task.TaskType == ExternalVisualType)
                        {
                            if (asset.AssetCategory == "Tank")
                            {
                                Console.WriteLine("Skipping request for external for tank " + task.rowNumber);
                                continue;
                            }
                            matchingTask = matchingWO.AddNewTask(WorkOrder.TaskTypes.ExternalVisual);
                        }
                        else if (task.TaskType == InternalVisualType)
                        {
                            if (asset.AssetCategory != "Vessel")
                            {
                                Console.WriteLine("Skipping request for Internal for a non vessel " + task.rowNumber);
                                continue;
                            }
                            matchingTask = matchingWO.AddNewTask(WorkOrder.TaskTypes.InternalVisual);
                        }
                        else if (task.TaskType == FullType)
                        {
                            if (asset.AssetCategory != "Vessel")
                            {
                                Console.WriteLine("Skipping request for Full for a non vessel " + task.rowNumber);
                                continue;
                            }
                            matchingTask = matchingWO.AddNewTask(WorkOrder.TaskTypes.Full);
                        }
                    }

                    matchingTask.businessUnitId.SetValue(project.businessUnitId.GetValue());
                    matchingTask.clientCostCode.SetValue(task.ClientCostCode);
                    if (DateTime.TryParse(task.FieldEndDate, out var endDate))
                    {
                        matchingTask.plannedEnd.SetValue(endDate);
                    }
                    if (DateTime.TryParse(task.FieldStartDate, out var startDate))
                    {
                        matchingTask.plannedStart.SetValue(startDate);
                    }

                    if (DateTime.TryParse(task.PMDueDate, out var pmDueDate))
                    {
                        matchingTask.dueDate.SetValue(pmDueDate);
                    }

                    matchingTask.ChangeStatus(APMTask.Statuses.NotStarted);
                    matchingTask.taskDetails.supervisor.SetValue(task.Supervisor);

                    matchingTask.dueDate.SetValue(DateTime.Parse(task.PMDueDate));
                    matchingTask.taskComment.SetValue(task.Comments);
                    matchingTask.clientWorkOrderNumber.SetValue(task.ClientWorkOrderNumber);

                    await matchingTask.SavePendingChanges(importEmail);

                }
                catch (Exception ex)
                {
                    Console.Write(ex);
                }
            }

            await matchingWO.SavePendingChanges(importEmail);

            var projectAssetIds = project.assetIds.GetValue();
            if (projectAssetIds == null || !projectAssetIds.Contains(match.id))
            {
                project.assetIds.AddValue(match.id);
                await project.SavePendingChanges(importEmail);
            }
        }

        public String getAssetIdForAsset(Asset asset)
        {
            if (asset.walkDown is Section510_Asset_Walkdown_Details_F vessel)
            {
                return vessel.sectionIdentification.attributeNumber_or_ID.GetValue();
            }
            if (asset.walkDown is Section570_Asset_Walkdown_Details_F piping)
            {
                return piping.sectionIdentification.attributeNumber_or_Circuit_ID.GetValue();
            }
            if (asset.walkDown is Section653_Asset_Walkdown_Details_F tank)
            {
                return tank.sectionIdentification.attributeNumber_or_ID.GetValue();
            }

            return "";
        }

        public bool VerifyAssetFieldsAreEqual(ExcelRowImportData rowA, ExcelRowImportData rowB)
        {
            if (!String.Equals(rowA.AssetId, rowB.AssetId, StringComparison.InvariantCultureIgnoreCase))
                return false;
            if (!String.Equals(rowA.Area, rowB.Area, StringComparison.InvariantCultureIgnoreCase))
                return false;
            if (!String.Equals(rowA.AssetCategory, rowB.AssetCategory, StringComparison.InvariantCultureIgnoreCase))
                return false;
            if (!String.Equals(rowA.AssetDescription, rowB.AssetDescription, StringComparison.InvariantCultureIgnoreCase))
                return false;
            if (!String.Equals(rowA.ClientCostCode, rowB.ClientCostCode, StringComparison.InvariantCultureIgnoreCase))
                return false;

            if (!String.Equals(rowA.OperatingStatus, rowB.OperatingStatus, StringComparison.InvariantCultureIgnoreCase))
                return false;

            if (String.Equals(rowA.TaskType, rowB.TaskType, StringComparison.InvariantCultureIgnoreCase))
                return false;

            return true;
        }

        private String AssetWalkdownType => "Asset Walkdown";
        private String ExternalVisualType => "External Visual";
        private String InternalVisualType => "Internal Visual";
        private String FullType => "Full";

       
        public void CreateAssetObjects(List<ImportProject> projects, List<ExcelRowImportData> assetRows)
        {
            bool failed = false;
            try
            {
                var dueDates = assetRows.Select(a => DateTime.Parse(a.PMDueDate));
                var soonestDueDate = dueDates.OrderBy(a => a).FirstOrDefault();
                var row1 = assetRows[0];

                var month = soonestDueDate.Month;
                var year = soonestDueDate.Year;

                var project = projects.FirstOrDefault(a => a.ProjectName == row1.ProjectName);
                if (project == null)
                {
                    project = new ImportProject
                    {
                        ClientName = row1.ClientName,
                        BusinessUnitName = row1.BusinessUnitName,
                        ProjectName = row1.ProjectName,
                        ProjectDescription = row1.ProjectDescription,
                        ProjectPlannedEndDate = row1.ProjectPlannedEndDate,
                        ProjectPlannedStartDate = row1.ProjectPlannedStartDate,
                        ClientProjectNumber = row1.ClientProjectNumber,
                        ProjectCity = row1.ProjectCity,
                        ProjectLocationName = row1.ProjectLocationName,
                        ProjectState = row1.ProjectState,
                        ProjectStatus = row1.ProjectStatus,
                        TeamDistrictNumber = row1.TeamDistrictNumber,
                        TeamProjectNumber = row1.TeamProjectNumber
                    };
                    projects.Add(project);
                }

                var asset = new ImportAsset
                {
                    AssetId = assetRows[0].AssetId,
                    AssetCategory = assetRows[0].AssetCategory,
                    AssetDescription = assetRows[0].AssetDescription,
                    Area = assetRows[0].Area,
                    OperatingStatus = assetRows[0].OperatingStatus,
                    WorkOrderPlannedEndDate = row1.WorkOrderPlannedEndDate,
                    WorkOrderPlannedStartDate = row1.WorkOrderPlannedStartDate,
                    TeamProjectNumber = row1.TeamProjectNumber
                };

                if (assetRows.All(a => CreateTask(a, asset)))
                {
                    project.Assets.Add(asset);
                }
                else
                {
                    failed = true;
                }

            }
            catch (Exception ex)
            {
                failed = true;

            }

            if (failed)
            {
                Console.WriteLine("Failed to import asset from rows: ");
                foreach (var asset in assetRows)
                {
                    Console.Write(asset.RowNumber);
                }
                Console.WriteLine("\r\n\r\n");
            }
        }

        public bool CreateTask(ExcelRowImportData dataRow, ImportAsset asset)
        {
            String taskType = "";

            switch (dataRow.TaskType.ToLowerInvariant())
            {
                case "asset walkdown":
                case "walkdown":
                    taskType = AssetWalkdownType;
                    break;
                case "external":
                    taskType = ExternalVisualType;
                    break;
                case "internal":
                    taskType = InternalVisualType;
                    break;
                case "none":
                    return true;
            }

            var task = new ImportTask
            {
                rowNumber = dataRow.RowNumber,
                ClientWorkOrderNumber = dataRow.ClientWorkOrderNumber,
                OperatingStatus = dataRow.OperatingStatus,
                PMDueDate = dataRow.PMDueDate,
                FieldEndDate = dataRow.FieldEndDate,
                FieldStartDate = dataRow.FieldStartDate,
                ClientCostCode = dataRow.ClientCostCode,
                Supervisor = dataRow.Supervisor,
                TaskType = taskType,
                Comments = dataRow.TaskComment
            };
            asset.Tasks.Add(task);

            return true;
        }



        void writeAssetInfo(ExcelRowImportData row)
        {
            Console.WriteLine("\tAssetId: " + row.AssetId);
            Console.WriteLine("\tArea: " + row.Area);
            Console.WriteLine("\tAssetCategory: " + row.AssetCategory);
            Console.WriteLine("\tAssetDescription: " + row.AssetDescription);
            Console.WriteLine("\tFieldStatus: " + row.FieldStatus);
            Console.WriteLine("\tClientCostCode: " + row.ClientCostCode);
            Console.WriteLine("\tTaskType: " + row.TaskType);
            Console.WriteLine("\tOperatingStatus: " + row.OperatingStatus);
        }
    }

}
