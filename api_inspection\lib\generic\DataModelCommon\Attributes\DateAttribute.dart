import 'dart:developer';

import 'package:api_inspection/generic/AttributeControls/AttributeStructure.dart';
import 'package:api_inspection/generic/AttributeControls/Date/DateAttributeView.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/AttributeBase.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/ChangeLog/ChangeLogEntry.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

class DateAttribute extends SingleAttributeBase<String> {
  DateAttribute(
      {required DataModelItem parent,
      required String displayName,
      Widget? iconWidget,
      bool areCommentsRequired = false,
      String? databaseName})
      : super(
            parent, displayName, iconWidget, areCommentsRequired, databaseName);

  DateTime? _parseDateTime(String value) {
    try {
      var split = value.split("-");
      if (split.length != 3) {
        return DateTime.tryParse(value);
      }

      var year = int.tryParse(split[0]);
      var month = int.tryParse(split[1]);
      var day = int.tryParse(split[2]);
      if (year != null && month != null && day != null) {
        return DateTime(year, month, day);
      }
    } catch (e) {
      log(e.toString(),
          name: 'DateAttribute:_parseDateTime', time: DateTime.now());
    }

    return DateTime.tryParse(value);
  }

  DateTime? getValue() {
    if (valueChangeLog.entries.isEmpty) return null;
    var currentValue = valueChangeLog.entries.last.value;
    if (currentValue == null) {
      return null;
    }
    return _parseDateTime(currentValue);
  }

  @override
  bool hasData() {
    return getValue() != null;
  }

  @override
  Widget buildWidget(
      {IsEditableController? editingController,
      bool showPhotos = true,
      bool showComments = true}) {
    return DateAttributeView(
      this,
      editingController: editingController,
      showPhotos: showPhotos,
      showComments: showComments,
    );
  }

  void setValue(DateTime? value) {
    var date = value;
    if (date == getValue()) return;
    var entry = ChangeLogEntry<String>.newlyCreated(
        date?.toString().substring(0, 10) == null
            ? ""
            : date.toString().substring(0, 10));
    valueChangeLog.addNewItem(entry);

    changedBySelfListeners.notifyListeners();
    notifyListeners();
  }

  @override
  String getPreviewText() {
    var date = getValue();
    return date == null ? "" : date.toString().substring(0, 10);
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
