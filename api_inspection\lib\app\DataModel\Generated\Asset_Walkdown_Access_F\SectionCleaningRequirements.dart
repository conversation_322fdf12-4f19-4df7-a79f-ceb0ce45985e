//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class SectionCleaningRequirements extends DataModelSection {
  @override
  String getDisplayName() => "Cleaning Requirements";
  SectionCleaningRequirements(DataModelItem? parent)
      : super(parent: parent, sectionName: "Cleaning Requirements");

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeCleaning_recommendations =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: true,
          displayName: "Cleaning recommendations",
          databaseName: "AWA_Q321",
          availableOptions: [
        PredefinedValueOption("Water Spray", null, isCommentRequired: false),
        PredefinedValueOption("Pressure Wash", null, isCommentRequired: false),
        PredefinedValueOption("Sandblasted", null, isCommentRequired: false),
        PredefinedValueOption("Acid Wash", null, isCommentRequired: false),
        PredefinedValueOption("Steam", null, isCommentRequired: false),
        PredefinedValueOption("None", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeCleaning_service_review =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Cleaning service review",
          databaseName: "AWA_Q322",
          availableOptions: [
        PredefinedValueOption("Acceptable", null, isCommentRequired: false),
        PredefinedValueOption("Concern", null, isCommentRequired: true),
        PredefinedValueOption("N/A", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeCleaning_recommendations,
      attributeCleaning_service_review,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionCleaningRequirements";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
