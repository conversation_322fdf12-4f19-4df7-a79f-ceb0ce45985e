//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Ext_PV_ALL_F
{
  public class Section510EXT_PVCKLSTSEC014 : DataModelItem {

    public override String DisplayName { 
      get {
        return "JACKETING";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributeIs_the_vessel_equipped_with_a_jacketing_system;
    public PredefinedValueAttribute attributeIs_the_jacket_constructed_of_stainless_steel;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC014Q003;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC014Q004;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC014Q005;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC014Q006;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC014Q007;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC014Q008;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC014Q009;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC014Q010;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510EXT_PVCKLSTSEC014";

    public Section510EXT_PVCKLSTSEC014(DataModelItem parent) : base(parent)
    {
            
        attributeIs_the_vessel_equipped_with_a_jacketing_system = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the vessel equipped with a jacketing system:", databaseName: "510_EXT-PV_CKLST_SEC014_Q001"); 
     
        attributeIs_the_jacket_constructed_of_stainless_steel = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the jacket constructed of stainless steel:", databaseName: "510_EXT-PV_CKLST_SEC014_Q002"); 
     
        attribute510EXT_PVCKLSTSEC014Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the MAWP of the jacket known: (If yes record MAWP in comment section)", databaseName: "510_EXT-PV_CKLST_SEC014_Q003"); 
     
        attribute510EXT_PVCKLSTSEC014Q004 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the MAWT of the jacket known: (If yes record MAWT in comment section)", databaseName: "510_EXT-PV_CKLST_SEC014_Q004"); 
     
        attribute510EXT_PVCKLSTSEC014Q005 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the nominal thickness of the jacket known:  (If yes record nominal thickness in the comment section)", databaseName: "510_EXT-PV_CKLST_SEC014_Q005"); 
     
        attribute510EXT_PVCKLSTSEC014Q006 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Pitting", null)
        }, false, this, "Were any corrosion cells or pitting noted on the jacketing surfaces:  (The dimensions and locations of any damage shall be recorded)", databaseName: "510_EXT-PV_CKLST_SEC014_Q006"); 
     
        attribute510EXT_PVCKLSTSEC014Q007 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Cracking", null),
          new PredefinedValueOption("Yes: Leakage", null),
          new PredefinedValueOption("Yes: Mechanical Damage", null)
        }, false, this, "Was any mechanical damage, leakage or cracking noted on the jacketing surfaces:  (The dimensions and location of any damage shall be recorded)", databaseName: "510_EXT-PV_CKLST_SEC014_Q007"); 
     
        attribute510EXT_PVCKLSTSEC014Q008 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Deformation", null),
          new PredefinedValueOption("Yes: Hot Spots", null),
          new PredefinedValueOption("Yes: Cold Spots", null)
        }, false, this, "Does the jacketing have any deformations, hot or cold spots: (Bulges, Blisters, Dimpling)", databaseName: "510_EXT-PV_CKLST_SEC014_Q008"); 
     
        attribute510EXT_PVCKLSTSEC014Q009 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Cracking", null)
        }, false, this, "Were there indications of weld corrosion or cracking in the welds or Heat Affected Zones of the pressure retaining welds:", databaseName: "510_EXT-PV_CKLST_SEC014_Q009"); 
     
        attribute510EXT_PVCKLSTSEC014Q010 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the jacketing of the asset in acceptable condition for continued service:", databaseName: "510_EXT-PV_CKLST_SEC014_Q010"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeIs_the_vessel_equipped_with_a_jacketing_system,
           attributeIs_the_jacket_constructed_of_stainless_steel,
           attribute510EXT_PVCKLSTSEC014Q003,
           attribute510EXT_PVCKLSTSEC014Q004,
           attribute510EXT_PVCKLSTSEC014Q005,
           attribute510EXT_PVCKLSTSEC014Q006,
           attribute510EXT_PVCKLSTSEC014Q007,
           attribute510EXT_PVCKLSTSEC014Q008,
           attribute510EXT_PVCKLSTSEC014Q009,
           attribute510EXT_PVCKLSTSEC014Q010,
        };
    }
  }
}
