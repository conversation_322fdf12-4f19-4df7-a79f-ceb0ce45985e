//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionAsset_Walkdown_PPE_F
{
  public class SectionPPERequirements : DataModelItem {

    public override String DisplayName { 
      get {
        return "PPE Requirements";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributeHard_Hat;
    public MultiPredefinedValueAttribute attributeEye_Protection;
    public MultiPredefinedValueAttribute attributeHearing_Protection;
    public MultiPredefinedValueAttribute attributeFire_Retardant_Clothing;
    public MultiPredefinedValueAttribute attributeSafety_Gloves;
    public PredefinedValueAttribute attributeSnake_Chaps;
    public MultiPredefinedValueAttribute attributeFoot_Protection;
    public MultiPredefinedValueAttribute attributeChemical_Suit;
    public MultiPredefinedValueAttribute attributeFall_Protection;
    public MultiPredefinedValueAttribute attributeBreathing_Protection;
    public MultiPredefinedValueAttribute attributeAtmosphere;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionPPERequirements";

    public SectionPPERequirements(DataModelItem parent) : base(parent)
    {
            
        attributeHard_Hat = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Hard Hat", databaseName: "PPEAW_Q001"); 
     
        attributeEye_Protection = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Clear Safety Glasses", null),
          new PredefinedValueOption("Dark Safety Glasses", null),
          new PredefinedValueOption("Safety Goggles", null),
          new PredefinedValueOption("Face Shield", null)
        }, false, this, "Eye Protection", databaseName: "PPEAW_Q010"); 
     
        attributeHearing_Protection = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Ear Plugs", null),
          new PredefinedValueOption("Ear Muffs", null)
        }, false, this, "Hearing Protection", databaseName: "PPEAW_Q015"); 
     
        attributeFire_Retardant_Clothing = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Shirt/Jean", null),
          new PredefinedValueOption("Overall", null)
        }, false, this, "Fire Retardant Clothing", databaseName: "PPEAW_Q020"); 
     
        attributeSafety_Gloves = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Standard", null),
          new PredefinedValueOption("Cut Resistant", null),
          new PredefinedValueOption("Heat Resistant", null),
          new PredefinedValueOption("Impact Resistant", null)
        }, false, this, "Safety Gloves", databaseName: "PPEAW_Q025"); 
     
        attributeSnake_Chaps = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Snake Chaps", databaseName: "PPEAW_Q030"); 
     
        attributeFoot_Protection = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Steel Toed Boots", null),
          new PredefinedValueOption("Rubber Boots", null)
        }, false, this, "Foot Protection", databaseName: "PPEAW_Q035"); 
     
        attributeChemical_Suit = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Chemical Rated", null),
          new PredefinedValueOption("Tyvek", null)
        }, false, this, "Chemical Suit", databaseName: "PPEAW_Q040"); 
     
        attributeFall_Protection = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Body Harness", null),
          new PredefinedValueOption("Lanyard", null),
          new PredefinedValueOption("Life Line", null)
        }, false, this, "Fall Protection", databaseName: "PPEAW_Q045"); 
     
        attributeBreathing_Protection = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Dust Mask", null),
          new PredefinedValueOption("Half-face Respirator", null),
          new PredefinedValueOption("Full-face Respirator", null),
          new PredefinedValueOption("SCBA", null),
          new PredefinedValueOption("Supplied Air", null)
        }, false, this, "Breathing Protection", databaseName: "PPEAW_Q050"); 
     
        attributeAtmosphere = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("H2S Monitor", null),
          new PredefinedValueOption("Cl Personal Monitor", null),
          new PredefinedValueOption("Quad Gas Monitor", null)
        }, false, this, "Atmosphere", databaseName: "PPEAW_Q055"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeHard_Hat,
           attributeEye_Protection,
           attributeHearing_Protection,
           attributeFire_Retardant_Clothing,
           attributeSafety_Gloves,
           attributeSnake_Chaps,
           attributeFoot_Protection,
           attributeChemical_Suit,
           attributeFall_Protection,
           attributeBreathing_Protection,
           attributeAtmosphere,
        };
    }
  }
}
