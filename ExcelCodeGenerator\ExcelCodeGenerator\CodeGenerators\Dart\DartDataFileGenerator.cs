﻿using System;
using System.IO;
using System.Linq;
using CheckListGen;

namespace ExcelCodeGenerator.CodeGenerators.Dart
{
    internal class DartDataFileGenerator
    {
        public static void BuildSectionDataFile(Section section, string outputDirectory)
        {
            var gen = new DartDataFileGenerator();
            gen.DoBuildSectionDataFile(section, outputDirectory);
        }

        private void DoBuildSectionDataFile(Section section, string outputDirectory)
        {
            if (section.IsCollection)
                BuildSectionDataFile_Collection(section, outputDirectory);
            else
                BuildSectionDataFile_NonCollection(section, outputDirectory);
        }


        private static string BuildDataFileHeader()
        {
            return
                @"//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';";
        }

        private void BuildSectionDataFile_Collection(Section section, string outputDirectory)
        {
            var questions = section.Children.OfType<Question>().ToArray();

            var sections = section.Children.OfType<Section>().ToArray();

            var fileContents = BuildDataFileHeader() +
                               @"
" +
                               sections.Select(a => a.DartClassName).Select(a => "import '" + a + ".dart';")
                                   .AggregateEXT((a, b) => a + Environment.NewLine + b)
                               +
                               @"

// ignore: camel_case_types
class " + section.DartClassName + @" extends DataModelCollectionItem {
  @override
  String getDisplayName() => """ + section.DisplayName + @""";

  " + section.DartClassName + @"(String id, DataModelItem parent) : super(id, parent);

" + questions.Select(BuildAttributeDataLine).AggregateEXT((a, b) => a + "\r\n\r\n" + b) + @"

" + sections.Where(a => !a.IsCollection)
                                   .Select(a =>
                                       "// ignore: non_constant_identifier_names\r\n" + "late " + a.DartClassName +
                                       " " + a.DartVariableName + " = " + a.DartClassName + "(this);")
                                   .AggregateEXT((a, b) => a + Environment.NewLine + "  " + b) + @"

" + sections.Where(a => a.IsCollection).Select(a => "// ignore: non_constant_identifier_names\r\n" +
                                                    "late DataModelCollection<" + a.DartClassName + "> " +
                                                    a.DartVariableName + @" = DataModelCollection<" + a.DartClassName +
                                                    @">(""" + a.Name + @""", (parent, entry){
     return " + a.DartClassName + @"(entry.key, " + a.DartVariableName + @");
  }, this);").AggregateEXT((a, b) => a + Environment.NewLine + "  " + b) + @"


  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([ 
         " +
                               section.Children.Select(a => a.DartVariableName + ",")
                                   .AggregateEXT((a, b) => a + "\r\n         " + b) + @"
    ]);
    return children;
  }

}
";

            File.WriteAllText(outputDirectory + "/" + section.DartClassName + ".dart", fileContents);
        }

        private void BuildSectionDataFile_NonCollection(Section section, string outputDirectory)
        {
            var questions = section.Children.OfType<Question>().ToArray();

            var sections = section.Children.OfType<Section>().ToArray();

            var fileContents = BuildDataFileHeader() +
                               @"

" +
                               sections.Select(a => a.DartClassName).Select(a => "import '" + a + ".dart';")
                                   .AggregateEXT((a, b) => a + Environment.NewLine + b)
                               +
                               @"

// ignore: camel_case_types
class " + section.DartClassName + @" extends DataModelSection {
  @override
  String getDisplayName() => """ + section.DisplayName + @""";
  " + section.DartClassName + @"(DataModelItem? parent) : super(parent: parent, sectionName:""" + section.DisplayName +
                               @""");

" + questions.Select(BuildAttributeDataLine).AggregateEXT((a, b) => a + "\r\n\r\n" + b) + @"

" + sections.Where(a => !a.IsCollection)
                                   .Select(a =>
                                       "// ignore: non_constant_identifier_names\r\n" + "late " + a.DartClassName +
                                       " " + a.DartVariableName + " = " + a.DartClassName + "(this);")
                                   .AggregateEXT((a, b) => a + Environment.NewLine + "  " + b) + @"

" + sections.Where(a => a.IsCollection).Select(a => "// ignore: non_constant_identifier_names\r\n" +
                                                    "late DataModelCollection<" + a.DartClassName + "> " +
                                                    a.DartVariableName + @" = DataModelCollection<" + a.DartClassName +
                                                    @">(""" + a.Name + @""", (parent, entry){
     return " + a.DartClassName + @"(entry.key, " + a.DartVariableName + @");
  }, this);").AggregateEXT((a, b) => a + Environment.NewLine + "  " + b) + @"

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([ 
         " +
                               section.Children.Select(a => a.DartVariableName + ",")
                                   .AggregateEXT((a, b) => a + "\r\n         " + b) + @"
    ]);
    return children;
  }

  @override
    String getDBName() {
    return """ + section.DartClassName + @""";
  }

  @override
  Future<void> saveDirectItems() async {
  }

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }

}
";

            File.WriteAllText(outputDirectory + "/" + section.DartClassName + ".dart", fileContents);
        }


        private string BuildAttributeDataLine(Question question)
        {
            switch (question.AttributeType)
            {
                case Question.AttributeTypes.PhoneNumber:
                    return BuildPhoneNumberAttribute(question);
                case Question.AttributeTypes.Coordinate:
                    return BuildCoordinateAttribute(question);
                case Question.AttributeTypes.PhotoCollection:
                    return BuildPhotoAttribute(question);
                case Question.AttributeTypes.Date:
                    return BuildDateAttribute(question);
                case Question.AttributeTypes.String:
                    return BuildStringAttribute(question);
                case Question.AttributeTypes.Decimal:
                    return BuildDecimalAttribute(question);
                case Question.AttributeTypes.Integer:
                    return BuildIntegerAttribute(question);
                case Question.AttributeTypes.Boolean:
                    return BuildBooleanAttribute(question);
                case Question.AttributeTypes.MultiPredefinedValue:
                case Question.AttributeTypes.MultiPredefinedValueWithOther:
                    return BuildMultiPredefAttribute(question);
                case Question.AttributeTypes.PredefinedValue:
                case Question.AttributeTypes.PredefinedValueWithOther:
                    return BuildPredefAttribute(question);
                default:
                    throw new NotSupportedException("Unexpected attribute type encountered");
            }
        }

        private static string BuildDecimalAttribute(Question question)
        {
            var forceComment = question.ForceComment ? "true" : "false";

            return
                @"  // ignore: non_constant_identifier_names
  late DoubleAttribute " + question.DartVariableName + @" = DoubleAttribute(parent: this, 
    displayName: """ + question.DisplayText + @""",
    databaseName: """ + question.DataName + @""",
    areCommentsRequired: " + forceComment + @",
    allowNegatives: true,
    displayUnit: """ + question.Unit + @""",
  ); ";
        }

        private static string BuildPhoneNumberAttribute(Question question)
        {
            var forceComment = question.ForceComment ? "true" : "false";

            return
                @"  // ignore: non_constant_identifier_names
  late PhoneNumberAttribute " + question.DartVariableName + @" = PhoneNumberAttribute(parent: this, 
    displayName: """ + question.DisplayText + @""",
    databaseName: """ + question.DataName + @""",
    areCommentsRequired: " + forceComment + @",
  ); ";
        }


        private static string BuildIntegerAttribute(Question question)
        {
            var forceComment = question.ForceComment ? "true" : "false";

            return
                @"  // ignore: non_constant_identifier_names
  late IntegerAttribute " + question.DartVariableName + @" = IntegerAttribute(parent: this, 
    displayName: """ + question.DisplayText + @""",
    databaseName: """ + question.DataName + @""",
    areCommentsRequired: " + forceComment + @",
    allowNegatives: true,
    displayUnit: """ + question.Unit + @""",
  ); ";
        }

        private static string BuildDateAttribute(Question question)
        {
            var forceComment = question.ForceComment ? "true" : "false";

            return
                @"  // ignore: non_constant_identifier_names
  late DateAttribute " + question.DartVariableName + @" = DateAttribute(parent: this, 
    displayName: """ + question.DisplayText + @""",
    databaseName: """ + question.DataName + @""",
    areCommentsRequired: " + forceComment + @"
  ); ";
        }

        private static string BuildBooleanAttribute(Question question)
        {
            var forceComment = question.ForceComment ? "true" : "false";

            return
                @"  // ignore: non_constant_identifier_names
  late BooleanAttribute " + question.DartVariableName + @" = BooleanAttribute(parent: this, 
    displayName: """ + question.DisplayText + @""",
    databaseName: """ + question.DataName + @""",
    areCommentsRequired: " + forceComment + @"
  ); ";
        }

        private static string BuildPhotoAttribute(Question question)
        {
            var forceComment = question.ForceComment ? "true" : "false";

            return
                @"  // ignore: non_constant_identifier_names
  late PhotoAttribute " + question.DartVariableName + @" = PhotoAttribute(parent: this, 
    displayName: """ + question.DisplayText + @""",
    databaseName: """ + question.DataName + @""",
    areCommentsRequired: " + forceComment + @"
  ); ";
        }


        private static string BuildCoordinateAttribute(Question question)
        {
            var forceComment = question.ForceComment ? "true" : "false";

            return
                @"  // ignore: non_constant_identifier_names
  late LocationAttribute " + question.DartVariableName + @" = LocationAttribute(parent: this, 
    displayName: """ + question.DisplayText + @""",
    databaseName: """ + question.DataName + @""",
    areCommentsRequired: " + forceComment + @"
  ); ";
        }

        private static string BuildStringAttribute(Question question)
        {
            var forceComment = question.ForceComment ? "true" : "false";

            return
                @"  // ignore: non_constant_identifier_names
  late StringAttribute " + question.DartVariableName + @" = StringAttribute(parent: this, 
    displayName: """ + question.DisplayText + @""",
    databaseName: """ + question.DataName + @""",
    areCommentsRequired: " + forceComment + @"
  ); ";
        }

        private string BuildMultiPredefAttribute(Question question)
        {
            var unitField = string.IsNullOrWhiteSpace(question.Unit) ? "" : "\r\n     unit: \"" + question.Unit + "\",";
            var hasOther = question.AttributeType == Question.AttributeTypes.MultiPredefinedValue ? "false" : "true";

            return
                @"  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute " + question.DartVariableName + @" = MultiPredefinedValueAttribute(parent: this,
    hasOtherOption: " + hasOther + @",
    displayName: """ + question.DisplayText + @""", 
    databaseName: """ + question.DataName + @"""," + unitField + @"
    availableOptions: [
      " +
                question.Choices
                    .Select(a =>
                        "PredefinedValueOption(\"" + a.Display + "\", null, isCommentRequired: " +
                        (a.Decorator == "RC" ? "true" : "false") + @"),")
                    .AggregateEXT((a, b) => a + Environment.NewLine + "      " + b)
                + @"
  ]);";
        }

        private string BuildPredefAttribute(Question question)
        {
            var unitField = string.IsNullOrWhiteSpace(question.Unit) ? "" : "\r\n     unit: \"" + question.Unit + "\",";

            if (question.Choices.Count == 3 && question.Choices.Any(a =>
                                                a.Display.Equals("yes", StringComparison.InvariantCultureIgnoreCase))
                                            && question.Choices.Any(a =>
                                                a.Display.Equals("no", StringComparison.InvariantCultureIgnoreCase))
                                            && question.Choices.Any(a =>
                                                a.Display.Equals("n/a", StringComparison.InvariantCultureIgnoreCase)))
                return
                    @"  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA " + question.DartVariableName +
                    @" = PredefinedValueAttribute_YesNoNA(parent: this,
    displayName: """ + question.DisplayText + @""", 
    databaseName: """ + question.DataName + @""",
    availableOptions: [
      " +
                    question.Choices
                        .Select(a =>
                            "PredefinedValueOption(\"" + a.Display + "\", null, isCommentRequired: " +
                            (a.Decorator == "RC" ? "true" : "false") + @"),")
                        .AggregateEXT((a, b) => a + Environment.NewLine + "      " + b)
                    + @"
  ]);";

            var hasOther = question.AttributeType == Question.AttributeTypes.PredefinedValue ? "false" : "true";

            return
                @"  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute " + question.DartVariableName + @" = PredefinedValueAttribute(parent: this,
    hasOtherOption: " + hasOther + @",
    displayName: """ + question.DisplayText + @""", 
    databaseName: """ + question.DataName + @"""," + unitField + @"
    availableOptions: [
      " +
                question.Choices
                    .Select(a =>
                        "PredefinedValueOption(\"" + a.Display + "\", null, isCommentRequired: " +
                        (a.Decorator == "RC" ? "true" : "false") + @"),")
                    .AggregateEXT((a, b) => a + Environment.NewLine + "      " + b)
                + @"
  ]);";
        }
    }
}