// Azure Configuration Options for API Inspection app
import 'package:api_inspection/environment.dart';
import 'package:flutter/foundation.dart' show defaultTargetPlatform, kIsWeb, TargetPlatform;

class AzureOptions {
  // Azure Storage settings
  final String storageAccountName;
  final String storageContainer;
  final String storageConnectionString;
  
  // Azure Cosmos DB settings
  final String cosmosDbEndpoint;
  final String cosmosDbKey;
  final String cosmosDbDatabaseId;
  
  // Azure AD B2C settings
  final String adB2cClientId;
  final String adB2cTenant;
  final String adB2cPolicy;
  final String adB2cRedirectUri;
  final String adB2cScope;
  
  // Application Insights settings
  final String appInsightsKey;
  
  // Constructor
  const AzureOptions({
    required this.storageAccountName,
    required this.storageContainer,
    required this.storageConnectionString,
    required this.cosmosDbEndpoint,
    required this.cosmosDbKey,
    required this.cosmosDbDatabaseId,
    required this.adB2cClientId,
    required this.adB2cTenant,
    required this.adB2cPolicy,
    required this.adB2cRedirectUri,
    required this.adB2cScope,
    required this.appInsightsKey,
  });
  
  // Get platform-specific options
  static AzureOptions currentPlatform(Environment environment) {
    if (kIsWeb) {
      return web;
    }
    
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        if (environment == EnvironmentValue.production) {
          return androidProd;
        }
        return androidDev;
      case TargetPlatform.iOS:
        if (environment == EnvironmentValue.production) {
          return iosProd;
        }
        return iosDev;
      case TargetPlatform.macOS:
      case TargetPlatform.windows:
      case TargetPlatform.linux:
        throw UnsupportedError(
          'Platform ${defaultTargetPlatform.toString()} is not supported for this app.',
        );
      default:
        throw UnsupportedError(
          'Unknown platform ${defaultTargetPlatform.toString()}',
        );
    }
  }

  // Web platform configuration
  static const AzureOptions web = AzureOptions(
    storageAccountName: 'apmstorageweb',
    storageContainer: 'inspections',
    storageConnectionString: 'DefaultEndpointsProtocol=https;AccountName=apmstorageweb;AccountKey=YOUR_KEY;EndpointSuffix=core.windows.net',
    cosmosDbEndpoint: 'https://apm-cosmosdb-dev.documents.azure.com:443/',
    cosmosDbKey: 'YOUR_COSMOS_DB_KEY',
    cosmosDbDatabaseId: 'apm-database',
    adB2cClientId: 'YOUR_CLIENT_ID',
    adB2cTenant: 'apmdevb2c.onmicrosoft.com',
    adB2cPolicy: 'B2C_1_signupsignin1',
    adB2cRedirectUri: 'https://apmapp.azurewebsites.net/authentication/callback',
    adB2cScope: 'https://apmdevb2c.onmicrosoft.com/api/user_impersonation',
    appInsightsKey: 'YOUR_APP_INSIGHTS_KEY',
  );

  // Android development configuration
  static const AzureOptions androidDev = AzureOptions(
    storageAccountName: 'apmstorageDev',
    storageContainer: 'inspections',
    storageConnectionString: 'DefaultEndpointsProtocol=https;AccountName=apmstorageDev;AccountKey=YOUR_KEY;EndpointSuffix=core.windows.net',
    cosmosDbEndpoint: 'https://apm-cosmosdb-dev.documents.azure.com:443/',
    cosmosDbKey: 'YOUR_COSMOS_DB_KEY',
    cosmosDbDatabaseId: 'apm-database',
    adB2cClientId: 'YOUR_CLIENT_ID',
    adB2cTenant: 'apmdevb2c.onmicrosoft.com',
    adB2cPolicy: 'B2C_1_signupsignin1',
    adB2cRedirectUri: 'com.apm.app://oauth/redirect',
    adB2cScope: 'https://apmdevb2c.onmicrosoft.com/api/user_impersonation',
    appInsightsKey: 'YOUR_APP_INSIGHTS_KEY',
  );

  // Android production configuration
  static const AzureOptions androidProd = AzureOptions(
    storageAccountName: 'apmStorageProd',
    storageContainer: 'inspections',
    storageConnectionString: 'DefaultEndpointsProtocol=https;AccountName=apmStorageProd;AccountKey=YOUR_KEY;EndpointSuffix=core.windows.net',
    cosmosDbEndpoint: 'https://apm-cosmosdb-prod.documents.azure.com:443/',
    cosmosDbKey: 'YOUR_COSMOS_DB_KEY',
    cosmosDbDatabaseId: 'apm-database-prod',
    adB2cClientId: 'YOUR_CLIENT_ID',
    adB2cTenant: 'apmprod.onmicrosoft.com',
    adB2cPolicy: 'B2C_1_signupsignin1',
    adB2cRedirectUri: 'com.apm.prod://oauth/redirect',
    adB2cScope: 'https://apmprod.onmicrosoft.com/api/user_impersonation',
    appInsightsKey: 'YOUR_APP_INSIGHTS_KEY',
  );

  // iOS development configuration
  static const AzureOptions iosDev = AzureOptions(
    storageAccountName: 'apmstorageDev',
    storageContainer: 'inspections',
    storageConnectionString: 'DefaultEndpointsProtocol=https;AccountName=apmstorageDev;AccountKey=YOUR_KEY;EndpointSuffix=core.windows.net',
    cosmosDbEndpoint: 'https://apm-cosmosdb-dev.documents.azure.com:443/',
    cosmosDbKey: 'YOUR_COSMOS_DB_KEY',
    cosmosDbDatabaseId: 'apm-database',
    adB2cClientId: 'YOUR_CLIENT_ID',
    adB2cTenant: 'apmdevb2c.onmicrosoft.com',
    adB2cPolicy: 'B2C_1_signupsignin1',
    adB2cRedirectUri: 'com.apm.app://oauth/redirect',
    adB2cScope: 'https://apmdevb2c.onmicrosoft.com/api/user_impersonation',
    appInsightsKey: 'YOUR_APP_INSIGHTS_KEY',
  );
  
  // iOS production configuration
  static const AzureOptions iosProd = AzureOptions(
    storageAccountName: 'apmStorageProd',
    storageContainer: 'inspections',
    storageConnectionString: 'DefaultEndpointsProtocol=https;AccountName=apmStorageProd;AccountKey=YOUR_KEY;EndpointSuffix=core.windows.net',
    cosmosDbEndpoint: 'https://apm-cosmosdb-prod.documents.azure.com:443/',
    cosmosDbKey: 'YOUR_COSMOS_DB_KEY',
    cosmosDbDatabaseId: 'apm-database-prod',
    adB2cClientId: 'YOUR_CLIENT_ID',
    adB2cTenant: 'apmprod.onmicrosoft.com',
    adB2cPolicy: 'B2C_1_signupsignin1',
    adB2cRedirectUri: 'com.apm.prod://oauth/redirect',
    adB2cScope: 'https://apmprod.onmicrosoft.com/api/user_impersonation',
    appInsightsKey: 'YOUR_APP_INSIGHTS_KEY',
  );
}
