// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:api_inspection/environment.dart';
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

class DefaultFirebaseOptions {
  static FirebaseOptions currentPlatform(Environment environment) {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        if (environment == EnvironmentValue.production) {
          return androidProd;
        }
        return androidDev;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCy7hM82CoycZD_DGo62vLCbmktsBUsAjE',
    appId: '1:375236612863:web:17c93b95060b3103bc72ca',
    messagingSenderId: '375236612863',
    projectId: 'asset-performance-management',
    authDomain: 'asset-performance-management.firebaseapp.com',
    databaseURL:
        'https://asset-performance-management-default-rtdb.firebaseio.com',
    storageBucket: 'asset-performance-management.appspot.com',
    measurementId: 'G-L9HJZB3N14',
  );

  static const FirebaseOptions androidDev = FirebaseOptions(
    apiKey: 'AIzaSyAyfN6-jDt9eZgz_eFo7t9RrqXiuWMburw',
    appId: '1:375236612863:android:40c5609c7d35e83dbc72ca',
    messagingSenderId: '375236612863',
    projectId: 'asset-performance-management',
    databaseURL:
        'https://asset-performance-management-default-rtdb.firebaseio.com',
    storageBucket: 'asset-performance-management.appspot.com',
  );

  static const FirebaseOptions androidProd = FirebaseOptions(
    apiKey: 'AIzaSyCFa-e0RUpUjF4mqSPVxNiMY_UZ0yfTZa8',
    appId: '1:921626284568:android:df5388b0b5c8a8a5a9e96d',
    messagingSenderId: '921626284568',
    projectId: 'apm-prod-da61a',
    storageBucket: 'apm-prod-da61a.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBch_DWAHEFB0K659KzgR32qx5d-RTkmSM',
    appId: '1:375236612863:ios:033adf795f94b147bc72ca',
    messagingSenderId: '375236612863',
    projectId: 'asset-performance-management',
    databaseURL:
        'https://asset-performance-management-default-rtdb.firebaseio.com',
    storageBucket: 'asset-performance-management.appspot.com',
    androidClientId:
        '375236612863-h3dovqjvpitbe9cn8iut0770m9f7ed2p.apps.googleusercontent.com',
    iosClientId:
        '375236612863-9288t80t32q2tvb8302nj9sijbmje91n.apps.googleusercontent.com',
    iosBundleId: 'teamdigital.flutter.firebase.poc',
  );
}
