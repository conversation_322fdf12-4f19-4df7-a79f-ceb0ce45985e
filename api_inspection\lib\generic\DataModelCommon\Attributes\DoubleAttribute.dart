import 'package:api_inspection/generic/AttributeControls/AttributeStructure.dart';
import 'package:api_inspection/generic/AttributeControls/Double/DoubleAttributeView.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/AttributeControls/Double/DoubleAttributeTextField.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';

import 'AttributeBase.dart';
import 'ChangeLog/ChangeLogEntry.dart';

class DoubleAttribute extends SingleAttributeBase<double> {
  String? displayUnit;
  double? minValue;
  double? maxValue;

  bool isValueWithinRange() {
    double? value = getValue();
    if (maxValue != null && minValue != null) {
      if (value == null) return true;
      if (value > maxValue! || value < minValue!) return false;
    }

    return true;
  }

  @override
  bool hasData() {
    return getValue() != null;
  }

  double? getValue() {
    if (valueChangeLog.entries.isEmpty) return null;
    return valueChangeLog.entries.last.value;
  }

  void setValue(double? value) {
    if (getValue() == value) return;

    var entry = ChangeLogEntry<double>.newlyCreated(value);
    valueChangeLog.addNewItem(entry);

    changedBySelfListeners.notifyListeners();
    notifyListeners();
  }

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  bool allowNegatives;

  DoubleAttribute(
      {required DataModelItem parent,
      required String displayName,
      required this.allowNegatives,
      String? displayUnit,
      Widget? iconWidget,
      bool areCommentsRequired = false,
      String? databaseName,
      double? mininumValue,
      double? maximumValue,
      VoidCallback? listener})
      : super(parent, displayName, iconWidget, areCommentsRequired,
            databaseName) {
    if (displayUnit != "") this.displayUnit = displayUnit;

    if (listener != null) addListener(listener);
    minValue = mininumValue;
    minValue = maximumValue;
    valueChangeLog.setConversionMethod(convertDynamicToDouble);
  }

  double? convertDynamicToDouble(dynamic dyn) {
    if (dyn is int) {
      return dyn.toDouble();
    }
    if (dyn is double) {
      return dyn;
    }
    return double.tryParse(dyn.toString());
  }

  @override
  Widget buildWidget(
      {IsEditableController? editingController,
      bool showPhotos = true,
      bool showComments = true}) {
    return DoubleAttributeView(
      this,
      editingController: editingController,
      showPhotos: showPhotos,
      showComments: showComments,
    );
  }

  // ignore: non_constant_identifier_names
  Widget buildWidget_EntryOnly() {
    return DoubleAttributeTextField(this);
  }

  @override
  String getPreviewText() {
    var value = getValue();
    if (value == null) return "";
    var unit = displayUnit;
    if (unit == null) {
      return value.toString();
    }
    return value.toString() + " " + unit;
  }
}
