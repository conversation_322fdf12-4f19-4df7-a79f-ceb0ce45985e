import 'dart:developer';
import 'dart:typed_data';
import 'dart:developer' as developer;

import 'package:api_inspection/app/APMRoot.dart';
import 'package:api_inspection/generic/AppRoot.dart';
import 'package:api_inspection/generic/MediaControls/add_photo_button.dart';
import 'package:api_inspection/generic/MediaControls/add_photo_with_comment_button.dart';
import 'package:api_inspection/generic/MediaControls/delete_all_photos_button.dart';
import 'package:api_inspection/generic/MediaControls/photo_widgets_for_collection.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:camera/camera.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:file_picker/file_picker.dart';
import 'package:api_inspection/generic/MediaControls/upload_photo_button.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/AttributeBase.dart';

import 'package:api_inspection/generic/MediaControls/MediaEntry.dart';

import 'MediaSynchronizer.dart';

class SinglePhotoCollectionControl extends StatefulWidget {
  final AttributeBase _attribute;

  const SinglePhotoCollectionControl(this._attribute, {Key? key})
      : super(key: key);

  @override
  _SinglePhotoCollectionControlState createState() =>
      _SinglePhotoCollectionControlState();
}

class _SinglePhotoCollectionControlState
    extends State<SinglePhotoCollectionControl> {
  List<MediaEntry> _mediaEntries = List.empty();

  @override
  void initState() {
    widget._attribute.addListener(onAttributeChanged);

    _mediaEntries = widget._attribute.getPhotos().toList();

    IMediaSynchronizer.getMediaSynchronizer()
        .listener
        .addListener(_onMediaSynchronizer);

    super.initState();
  }

  void _onMediaSynchronizer() {
    _refresh();
  }

  @override
  void didUpdateWidget(SinglePhotoCollectionControl old) {
    old._attribute.removeListener(onAttributeChanged);

    widget._attribute.addListener(onAttributeChanged);
    super.didUpdateWidget(old);
  }

  @override
  void dispose() {
    widget._attribute.removeListener(onAttributeChanged);
    IMediaSynchronizer.getMediaSynchronizer()
        .listener
        .removeListener(_onMediaSynchronizer);
    super.dispose();
  }

  void onAttributeChanged() {
    _refresh();
  }

  @override
  Widget build(BuildContext context) {
    log('building SinglePhotoCollectionControl',
        name: 'SinglePhotoCollectionControl');
    var addPhotoButton = AddPhotoButtonWidget(widget._attribute);
    var addPhotoWithCommentButton =
        AddPhotoWithCommentButtonWidget(widget._attribute);
    var uploadPhotoButton = UploadPhotoButtonWidget(widget._attribute);
    var deleteAllButton = DeleteAllPhotosButtonWidget(widget._attribute);

    List<Widget> photoButtons = [
      addPhotoButton,
      addPhotoWithCommentButton,
      uploadPhotoButton
    ];

    var usersThatCanDeleteAllPhotos = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];
    if (usersThatCanDeleteAllPhotos
        .contains(AppRoot.global().currentUser?.email)) {
      photoButtons.add(deleteAllButton);
    }
    return Container(
        margin: const EdgeInsets.fromLTRB(15, 15, 15, 0),
        alignment: Alignment.centerLeft,
        child:
            Column(crossAxisAlignment: CrossAxisAlignment.stretch, children: [
          _mediaEntries.isEmpty
              ? const SizedBox()
              : ConstrainedBox(
                  constraints:
                      const BoxConstraints(minHeight: 0.0, maxHeight: 265),
                  child: GridView.builder(
                      itemCount: _mediaEntries.length,
                      primary: false,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 4),
                      itemBuilder: (BuildContext context, int index) {
                        var entry = _mediaEntries[index];
                        StatelessWidget item = Container();

                        try {
                          item = GestureDetector(
                              onLongPress: () async {
                                await _confirmDeletion(entry);
                              },
                              child: resolvedPhotoForCollection(
                                  context, entry, _mediaEntries, true));
                        } catch (ex) {
                          developer.log(ex.toString(), name: 'build');
                        }

                        return item;
                      })),
          Row(
            children: photoButtons,
          )
        ]));
  }

  _refresh() {
    if (mounted) {
      setState(() {
        _mediaEntries = widget._attribute.getPhotos();
      });
    }
  }

  _confirmDeletion(MediaEntry entry) async {
    var photoWidget = Container(
      width: 200,
      height: 200,
      margin: const EdgeInsets.all(0),
      child: ClipRRect(
          borderRadius: const BorderRadius.all(Radius.circular(8)),
          child: FittedBox(
            key: const ValueKey("AddedPhoto"),
            fit: BoxFit.contain,
            child: MediaEntryImage(mediaEntry: entry),
          )),
    );

    var description = entry.description.getValue();
    Widget contentWidget;
    if (description == null) {
      contentWidget = photoWidget;
    } else {
      contentWidget = SizedBox(
          width: 200,
          height: 240,
          child: Column(children: [
            photoWidget,
            Text(
              description,
              style: const TextStyle(color: Colors.white),
            )
          ]));
    }
    return await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color.fromARGB(255, 41, 45, 52),
          title: const Text(
            'Delete Photo?',
            style: TextStyle(color: Colors.white),
          ),
          content: contentWidget,
          actions: [
            TextButton(
              child: const Text('Yes', style: TextStyle(color: Colors.white)),
              onPressed: () async {
                widget._attribute.removePhoto(entry);
                await IMediaSynchronizer.getMediaSynchronizer().removePendingFile(
                    'DBPath:${entry.getDBPath()}^V:${entry.version}^${entry.fullFileName}');
                var batch = FirebaseFirestore.instance.batch();
                widget._attribute.saveItem(batch);
                await batch.commit();
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text('No', style: TextStyle(color: Colors.white)),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }
}
