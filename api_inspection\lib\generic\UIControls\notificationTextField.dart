import 'package:flutter/material.dart';

class NotificationTextField extends StatefulWidget {
  final Function(String) _valueChangedCallback;
  final String _initialValue;
  const NotificationTextField(this._valueChangedCallback, this._initialValue,
      {Key? key})
      : super(key: key);

  @override
  _NotificationTextFieldState createState() => _NotificationTextFieldState();
}

class _NotificationTextFieldState extends State<NotificationTextField> {
  TextEditingController? _controller;

  @override
  void didUpdateWidget(covariant NotificationTextField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (_controller != null) {
      _controller!.text = widget._initialValue;
    }
  }

  @override
  void dispose() {
    updateValue();
    _controller?.dispose();
    super.dispose();
  }

  void updateValue() {
    TextEditingController? controller = _controller;
    if (controller != null) {
      widget._valueChangedCallback.call(controller.text);
    }
  }

  @override
  Widget build(BuildContext context) {
    _controller ??= TextEditingController(text: widget._initialValue);

    return Focus(
        onFocusChange: (hasFocus) {
          if (!hasFocus) {
            updateValue();
          }
        },
        child: TextField(
          controller: _controller,
          decoration: const InputDecoration(
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: Colors.white, width: 1.0),
            ),
            border: OutlineInputBorder(
              borderSide: BorderSide(color: Colors.white, width: 1.0),
            ),
          ),
          onSubmitted: (String value) {
            updateValue();
          },
          style: const TextStyle(color: Colors.white),
        ));
  }
}
