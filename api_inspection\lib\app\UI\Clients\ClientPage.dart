import 'package:api_inspection/generic/AppStyle.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/project.dart';
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'ContactsControl.dart';

class ClientEditPage extends StatefulWidget {
  final Client client;
  const ClientEditPage(this.client, {Key? key}) : super(key: key);

  @override
  _ClientEditPageState createState() => _ClientEditPageState();
}

class _ClientEditPageState extends State<ClientEditPage> {
  @override
  void initState() {
    var client = widget.client;
    client.name.addListener(onNameChanged);
    super.initState();
  }

  @override
  void didUpdateWidget(covariant ClientEditPage oldWidget) {
    var oldClient = oldWidget.client;
    oldClient.name.removeListener(onNameChanged);

    var client = widget.client;
    client.name.addListener(onNameChanged);
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    var client = widget.client;
    client.name.removeListener(onNameChanged);
    super.dispose();
  }

  void onNameChanged() {
    setState(() {});
  }

  String getTitle() {
    var clientName = widget.client.name.getValue();
    if (clientName == null || clientName.isEmpty) return "Client Edit Page";
    return clientName;
  }

  @override
  Widget build(BuildContext context) {
    var client = widget.client;
    return Scaffold(
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        appBar: AppBar(
          title: Text(
            getTitle(),
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              AttributePadding.WithStdPadding(client.name.buildWidget()),
              Expanded(child: ContactsControl(client.contacts)),
            ],
          ),
        ));
  }
}
