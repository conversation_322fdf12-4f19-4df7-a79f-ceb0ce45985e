import 'package:api_inspection/generic/DataModelCommon/Attributes/TemperatureAttribute.dart';
import 'package:flutter/material.dart';

class TemperatureAttributeViewNonEditable extends StatefulWidget {
  final TemperatureAttribute _attribute;

  const TemperatureAttributeViewNonEditable(this._attribute, {Key? key})
      : super(key: key);

  @override
  _TemperatureAttributeViewNonEditableState createState() =>
      _TemperatureAttributeViewNonEditableState();
}

class _TemperatureAttributeViewNonEditableState
    extends State<TemperatureAttributeViewNonEditable> {
  void onAttributeChanged() {
    setState(() {});
  }

  @override
  void dispose() {
    TemperatureAttribute attribute = widget._attribute;
    attribute.removeListener(onAttributeChanged);
    super.dispose();
  }

  @override
  void initState() {
    TemperatureAttribute attribute = widget._attribute;
    attribute.addListener(onAttributeChanged);

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: const EdgeInsets.fromLTRB(40, 10, 40, 10),
        child: Row(
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                widget._attribute.getPreviewText(),
                style: const TextStyle(color: Colors.white, fontSize: 22),
              ),
            ),
          ],
        ));
  }
}
