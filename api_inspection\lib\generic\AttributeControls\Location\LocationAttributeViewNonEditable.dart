import 'package:api_inspection/app/batch_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';

class LocationAttributeViewNonEditable extends StatefulWidget {
  final LocationAttribute _attribute;
  final bool showPhotoControl;
  const LocationAttributeViewNonEditable(this._attribute,
      {Key? key, this.showPhotoControl = true})
      : super(key: key);

  @override
  _LocationAttributeViewNonEditableState createState() =>
      _LocationAttributeViewNonEditableState();
}

class _LocationAttributeViewNonEditableState
    extends State<LocationAttributeViewNonEditable> {
  TextEditingController? _latitudeController;
  TextEditingController? _longitudeController;

  bool initialized = false;

  void updateAttributeValue() {
    var latitudeController = _latitudeController;
    var longitudeController = _longitudeController;

    if (latitudeController != null && longitudeController != null) {
      var parsedLat = double.tryParse(latitudeController.text);
      var parsedLong = double.tryParse(longitudeController.text);

      widget._attribute.setValue(parsedLat, parsedLong);
      BatchHelper.saveAndCommit(widget._attribute);
    }
  }

  void initialize() {
    if (initialized) return;
    initialized = true;
    LocationAttribute attribute = widget._attribute;
    attribute.addListener(onAttributeChanged);
  }

  void showErrorMessage(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color.fromARGB(255, 41, 45, 52),
          title: const Text(
            'Error',
            style: TextStyle(color: Colors.white),
          ),
          content: Text(message, style: const TextStyle(color: Colors.white)),
          actions: [
            ElevatedButton(
              child: const Text('Ok', style: TextStyle(color: Colors.white)),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  void onAttributeChanged() {
    setState(() {
      TextEditingController? latitudeController = _latitudeController;
      TextEditingController? longitudeController = _longitudeController;

      if (latitudeController != null &&
          latitudeController.text !=
              widget._attribute.getLatValue()?.toString()) {
        String? latvalue = widget._attribute.getLatValue()?.toString();
        latitudeController.text = latvalue ?? "";
      }
      if (longitudeController != null &&
          longitudeController.text !=
              widget._attribute.getLongValue()?.toString()) {
        String? longValue = widget._attribute.getLongValue()?.toString();
        longitudeController.text = longValue ?? "";
      }
    });
  }

  @override
  void dispose() {
    LocationAttribute attribute = widget._attribute;
    attribute.removeListener(onAttributeChanged);
    _latitudeController?.dispose();
    _longitudeController?.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
  }

  void onTextFieldFocusChanged(bool hasFocus) {
    if (!hasFocus) {
      updateAttributeValue();
    }
  }

  Widget buildTextField(String title, TextEditingController? controller) {
    var textDecorator = const InputDecoration(
      enabledBorder: OutlineInputBorder(
        borderSide: BorderSide(color: Colors.white, width: 1.0),
      ),
      border: OutlineInputBorder(
        borderSide: BorderSide(color: Colors.white, width: 1.0),
      ),
    );

    return Column(children: [
      Container(
        alignment: Alignment.centerLeft,
        child: Text(title,
            style: const TextStyle(color: Colors.white, fontSize: 16)),
      ),
      Container(
          margin: const EdgeInsets.fromLTRB(0, 0, 0, 10),
          child: Focus(
            onFocusChange: onTextFieldFocusChanged,
            child: TextField(
              style: const TextStyle(color: Colors.white),
              controller: controller,
              keyboardType: const TextInputType.numberWithOptions(
                  signed: true, decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(
                    RegExp(r"^[+-]?([0-9]*\.?[0-9]*|\.[0-9]+)")),
              ],
              decoration: textDecorator,
              onSubmitted: (String value) {
                updateAttributeValue();
              },
            ),
          ))
    ]);
  }

  Future<Position> _getGeoLocationPosition() async {
    bool serviceEnabled;
    LocationPermission permission;
    // Test if location services are enabled.
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      // Location services are not enabled don't continue
      // accessing the position and request users of the
      // App to enable the location services.
      await Geolocator.openLocationSettings();
      return Future.error('Location services are disabled.');
    }
    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return Future.error('Location permissions are denied');
      }
    }
    if (permission == LocationPermission.deniedForever) {
      // Permissions are denied forever, handle appropriately.
      return Future.error(
          'Location permissions are permanently denied, we cannot request permissions.');
    }
    // When we reach here, permissions are granted and we can
    // continue accessing the position of the device.
    return await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high);
  }

  @override
  Widget build(BuildContext context) {
    _latitudeController ??= TextEditingController(
        text: widget._attribute.getLatValue()?.toString());
    _longitudeController ??= TextEditingController(
        text: widget._attribute.getLongValue()?.toString());

    List<Widget> children = [
      buildTextField("Latitude", _latitudeController),
      buildTextField("Longitude", _longitudeController)
    ];
    if (!widget._attribute.areCurrentCoordinatesValid()) {
      children.add(Container(
          alignment: Alignment.centerLeft,
          child: const Text(
            'Error - Current coordinates are invalid',
            style: TextStyle(color: Colors.red, fontSize: 16),
          )));
    }

    children.add(ElevatedButton(
        onPressed: () async {
          try {
            var location = await _getGeoLocationPosition();
            _latitudeController!.text = location.latitude.toString();
            _longitudeController!.text = location.longitude.toString();
            widget._attribute.setValue(location.latitude, location.longitude);
            BatchHelper.saveAndCommit(widget._attribute);
          } catch (ex) {
            showErrorMessage("Unable to get current location");
          }
        },
        child: const Text("Set from current location")));

    return Container(
        margin: const EdgeInsets.fromLTRB(20, 10, 20, 10),
        child: Column(children: children));
  }
}
