import 'dart:async';
import 'dart:typed_data';

import 'package:api_inspection/auth/azure_auth_service.dart';
import 'package:api_inspection/config/environment_config.dart';
import 'package:api_inspection/generic/AppRoot.dart';
import 'package:api_inspection/monitoring/azure_monitoring.dart';
import 'package:api_inspection/uploader_handler.dart';
import 'app/myApp.dart';
import 'generic/MediaControls/IGallerySaver.dart';
import 'generic/MediaControls/MediaSynchronizer.dart';
import 'package:flutter/material.dart';
import 'app/AppScaffold.dart';
import 'environment.dart';
import 'package:gallery_saver/gallery_saver.dart';
import 'package:flutter_uploader/flutter_uploader.dart';

void main() async {
  runZonedGuarded<Future<void>>(() async {
    WidgetsFlutterBinding.ensureInitialized();
    AppRoot.global().gallerySaver = ConcreteGallerySaver();
    AppRoot.global().environment = EnvironmentValue.development;

    // Initialize Azure services
    await _initializeAzureServices();

    FlutterError.onError = AzureMonitoring().recordFlutterError;
    FlutterUploader().setBackgroundHandler(backgroundUploadHandler);
    await IMediaSynchronizer.getMediaSynchronizer().ensureInitialization();

    runApp(MyApp(
        environment: EnvironmentValue.development,
        buildMainWidget: (context) {
          return const TeamAppScaffold();
        }));
  },
      (error, stack) =>
          AzureMonitoring().recordError(error, stack, fatal: true));
}

Future<void> _initializeAzureServices() async {
  // Initialize environment config
  final config = EnvironmentConfig();
  await config.initialize(EnvironmentValue.development);
  
  // Initialize monitoring
  await AzureMonitoring().initialize();
  
  // Initialize authentication
  await AzureAuthService().init();
}

// probably want to fix gallery saver so that it's not using this interface anymore
class ConcreteGallerySaver with IGallerySaver {
  @override
  Future<void> saveImageToGallery(
      {required Uint8List photoData,
      required int quality,
      required String name,
      required String path,
      required String albumName}) async {
    try {
      await GallerySaver.saveImage(
        path,
        albumName: albumName,
      );
    } catch (e) {
      throw e;
    }
  }
}
