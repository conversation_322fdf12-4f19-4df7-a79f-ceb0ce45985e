//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Int_PV_ALL_F
{
  public class Section510INT_PVCKLSTSEC012 : DataModelItem {

    public override String DisplayName { 
      get {
        return "PROTECTIVE COATING";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributeDoes_the_vessel_have_a_protective_coating_system_applied;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC012Q002;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC012Q003;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510INT_PVCKLSTSEC012";

    public Section510INT_PVCKLSTSEC012(DataModelItem parent) : base(parent)
    {
            
        attributeDoes_the_vessel_have_a_protective_coating_system_applied = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Does the vessel have a protective coating system applied:", databaseName: "510_INT-PV_CKLST_SEC012_Q001"); 
     
        attribute510INT_PVCKLSTSEC012Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Do vessel supports and or miscellaneous components have a protective coating system applied:", databaseName: "510_INT-PV_CKLST_SEC012_Q002"); 
     
        attribute510INT_PVCKLSTSEC012Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the protective coating system in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC012_Q003"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeDoes_the_vessel_have_a_protective_coating_system_applied,
           attribute510INT_PVCKLSTSEC012Q002,
           attribute510INT_PVCKLSTSEC012Q003,
        };
    }
  }
}
