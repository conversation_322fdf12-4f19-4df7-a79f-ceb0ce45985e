﻿using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface.DatabaseHelpers;

namespace CommonDataInterface
{

  public interface IDataModelCollection {
    ListChangeLog<String> changeLog { get; }
  }

  public class DataModelCollection<T> : DataModelItem, IDataModelCollection  
    where T : DataModelCollectionItem
  {
    public override string DisplayName => Name;

    public String Name { get; set; }
    private Func<DataModelItem, KeyValuePair<String, object>, T> _factory;
    private Func<DataModelItem, string, T> _newItemFactory;
    public DataModelCollection(String name, Func<DataModelItem, KeyValuePair<string, object>, T> factory, Func<DataModelItem, string, T> newItemFactory, DataModelItem? parent) : base(parent)
    {
      this.Name = name;
      _factory = factory;
      _newItemFactory = newItemFactory;
    }

    
    internal override bool updateFromOther(DataModelItem other1) {
      if (!(other1 is DataModelCollection<T>)){
        throw new Exception("Unexpected type encountered in updateFromOther");
      }

      var other = other1 as DataModelCollection<T>;

      bool updated =false;

      var changeLogMap = new Dictionary<String, object>();
      updated |= changeLog.updateFromOther(other.changeLog);

      foreach (var item in other.GetEntries()){
        var matchingEntry = this.GetEntries().FirstOrDefault((element) => element.GetId() == item.GetId());
        if (matchingEntry == null) {
          var newItem = _factory(this, new KeyValuePair<string,object>(item.GetId(), new Dictionary<String, object>()));
          if (newItem == null)
            continue;
          _allEntries.Add(newItem);
          changeLog.AddPendingChange(newItem.id, ChangeAction.Added);
          
          newItem.updateFromOther(item);

          updated = true;
        }
        else{
          updated |= matchingEntry.updateFromOther(item);
        }

      }
      return updated;
    }

    private ListChangeLog<String> _changeLog;

    public ListChangeLog<String> changeLog {
      get {
        if (_changeLog == null)
          _changeLog = new ListChangeLog<String>("ValueChangeLog", this, new List<ChangeLogEntry<string>>());
        return _changeLog;
      }
    } 

    protected List<T> _allEntries = new List<T>();
    
    public List<T> GetEntries() { 
      var currentEntryKeys = changeLog.GetCurrentEntries();
      var entries = _allEntries.Where((element) => currentEntryKeys.Any(b => String.Equals(b,element.GetId())));
      
      return  entries.ToList();
    }
    

    public List<T> CurrentEntries {
      get => GetEntries();
    }
    
    public List<T> PendingEntries {
      get {
        var currentEntryKeys = changeLog.GetPendingEntries();
        var entries = _allEntries.Where((element) => currentEntryKeys.Any(b => String.Equals(b,element.GetId())));
      
        return  entries.ToList();
      }
    }
    //public T[] Children {
    //  get {
    //    return _allEntries.ToArray();
    //  }
    //  set {

    //  }
    //}

    internal void AddItem(T item){
      _allEntries.Add(item);  
      changeLog.AddPendingChange(item.GetId(), ChangeAction.Added);
    }
    
    public virtual T AddNewItem()
    {
      var id = Guid.NewGuid().ToString();
      var newItem = _newItemFactory(this, id);

      _allEntries.Add(newItem);  
      changeLog.AddPendingChange(newItem.GetId(), ChangeAction.Added);
      return newItem;
    }

    public void RemoveItem(T item){
      if (GetEntries().Any(b => b.Equals(item))){
        changeLog.AddPendingChange(item.GetId(), ChangeAction.Removed);
      }
    }

    public override DataModelItem[] GetChildren() {
      List<DataModelItem> children = new List<DataModelItem>();
      foreach (var entry in _allEntries){
        children.Add(entry);
      }
      children.Add(changeLog);

      return children.ToArray();
    }

    internal override String GetDBName() {
      return Name;
    }

    public virtual void onUpdatedFromMap(){

    }

    public override void UpdateFromMap(Dictionary<string, object> map){
      if (map == null)
        return;


      List<T> newMediaEntries = new List<T>();
      if (map.ContainsKey("Values")){
        var values = map["Values"] as Dictionary<string, object>;
        if (values != null) {
          foreach (var item in values) {
            if (item.Value is Dictionary<string, object> wrapper){
              var currentEntry = _allEntries.FirstOrDefault((element) => element.GetDBName() == item.Key);
              if (currentEntry != null) {
                currentEntry.UpdateFromMap(wrapper);
                newMediaEntries.Add(currentEntry);
              }
              else {
                var newEntry = _factory(this, item);
                if (newEntry == null) {
                  continue;
                }
                newEntry.UpdateFromMap(wrapper);
                newMediaEntries.Add(newEntry);
              }
            }
          }
        }
      }
      if (map.ContainsKey("ValueChangeLog")){
        var changeLogMap = map["ValueChangeLog"] as Dictionary<string, object>;
        changeLog.UpdateFromMap(changeLogMap); 
      }
     
      
      _allEntries = newMediaEntries;

      onUpdatedFromMap();
    }

  }


  public class PhotoCollection : DataModelCollection<MediaEntry>
  {
    public PhotoCollection(string name, DataModelItem parent) :
      base(name, ((item, wrapper) => MediaCache.FromMapEntry(wrapper.Key, wrapper.Value as Dictionary<string, object>, item)),  (a,b)=> {
        throw new NotSupportedException();
      }, parent)
    {

    }
    
    public override MediaEntry AddNewItem()
    {
      throw new NotSupportedException();
    }
    
    public MediaEntry AddNewItem(string fileName, string fileExtension)
    {

      var newItem = new MediaEntry(fileName, fileExtension, this);
      newItem.id = fileName;
      newItem.UploadedVersion = 1;
      newItem.Version = 1;

      _allEntries.Add(newItem);  
      changeLog.AddPendingChange(newItem.GetId(), ChangeAction.Added);
      return newItem;
    }

    
    internal override bool updateFromOther(DataModelItem other1) {
      if (!(other1 is DataModelCollection<MediaEntry>)){
        throw new Exception("Unexpected type encountered in updateFromOther");
      }

      var other = other1 as DataModelCollection<MediaEntry>;

      bool updated =false;

      var changeLogMap = new Dictionary<String, object>();
      updated |= changeLog.updateFromOther(other.changeLog);

      foreach (var item in other.GetEntries()){
        var matchingEntry = this.GetEntries().FirstOrDefault((element) => element.GetId() == item.GetId());
        if (matchingEntry == null) {
          var newItem = new MediaEntry(item.MediaName, item.Extension, this);
          newItem.Version = item.Version;
          newItem.UploadedVersion = item.UploadedVersion;

          _allEntries.Add(newItem);

          newItem.updateFromOther(item);

          updated = true;
        }
        else{
          updated |= matchingEntry.updateFromOther(item);
        }

      }
      return updated;
    }
  }
}