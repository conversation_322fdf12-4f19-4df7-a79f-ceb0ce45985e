import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/UIControls/SwipeGestureDetector.dart';

class SlideOutController {
  void closeSlideOuts() {
    var state = _state;
    if (state == null) throw "Must call init before using";
    if (state._isLeftExpanded) {
      state._isLeftExpanded = false;
      state._leftController.reverse();
    }
    if (state._isRightExpanded) {
      state._isRightExpanded = false;
      state._rightController.reverse();
    }
  }

  _SideSlideoutControlState? _state;
  void init(_SideSlideoutControlState state) {
    _state = state;
  }

  void dispose() {
    _state = null;
  }
}

class SideSlideoutControl extends StatefulWidget {
  final Widget child;
  final WidgetBuilder? leftSliderWidget;
  final WidgetBuilder? rightSliderWidget;
  final SlideOutController? controller;

  const SideSlideoutControl(
      {Key? key,
      required this.child,
      this.leftSliderWidget,
      this.rightSliderWidget,
      this.controller})
      : super(key: key);

  @override
  _SideSlideoutControlState createState() => _SideSlideoutControlState();
}

class _SideSlideoutControlState extends State<SideSlideoutControl>
    with TickerProviderStateMixin {
  late final AnimationController _rightController;
  late final Animation<double> _rightAnimation;

  late final AnimationController _leftController;
  late final Animation<double> _leftAnimation;

  bool _isLeftExpanded = false;
  bool _isRightExpanded = false;

  @override
  void dispose() {
    var controller = widget.controller;
    if (controller != null) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  void didUpdateWidget(SideSlideoutControl oldWidget) {
    super.didUpdateWidget(oldWidget);

    _isLeftExpanded = false;
    _leftController.reset();
    _isRightExpanded = false;
    _rightController.reset();

    var controller = widget.controller;
    if (controller != null) {
      controller.init(this);
    }
  }

  @override
  void initState() {
    var controller = widget.controller;
    if (controller != null) {
      controller.init(this);
    }

    _leftController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _leftAnimation = CurvedAnimation(
      parent: _leftController,
      curve: Curves.fastOutSlowIn,
    );

    _leftController.addStatusListener((status) {
      setState(() {});
    });

    _rightController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _rightAnimation = CurvedAnimation(
      parent: _rightController,
      curve: Curves.fastOutSlowIn,
    );

    _rightController.addStatusListener((status) {
      setState(() {});
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> gestureChildren = [];

    if (_isLeftExpanded || _leftController.isAnimating) {
      var widgetBuilder = widget.leftSliderWidget;
      if (widgetBuilder != null) {
        gestureChildren.add(SizeTransition(
            sizeFactor: _leftAnimation,
            axis: Axis.horizontal,
            child: widgetBuilder.call(context)));
      }
    }

    gestureChildren.add(Expanded(child: widget.child));

    if (_isRightExpanded || _rightController.isAnimating) {
      var widgetBuilder = widget.rightSliderWidget;
      if (widgetBuilder != null) {
        gestureChildren.add(SizeTransition(
            sizeFactor: _rightAnimation,
            axis: Axis.horizontal,
            child: widgetBuilder.call(context)));
      }
    }

    return SwipeGestureRecognizer(
        child: Row(children: gestureChildren),
        onSwipeLeft: () {
          if (_isLeftExpanded) {
            _isLeftExpanded = false;
            _leftController.reverse();
            return;
          }
          if (widget.rightSliderWidget == null) return;
          if (!_isRightExpanded) {
            _isRightExpanded = true;
            _rightController.forward();
          }
        },
        onSwipeRight: () {
          if (_isRightExpanded) {
            _isRightExpanded = false;
            _rightController.reverse();
            return;
          }
          if (widget.leftSliderWidget == null) return;

          if (!_isLeftExpanded) {
            _isLeftExpanded = true;
            _leftController.forward();
          }
        });
  }
}
