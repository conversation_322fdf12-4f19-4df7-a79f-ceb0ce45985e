//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class Section570EXTCKLSTSEC004 extends DataModelSection {
  @override
  String getDisplayName() => "PIPING AND COMPONENTS";
  Section570EXTCKLSTSEC004(DataModelItem? parent)
      : super(parent: parent, sectionName: "PIPING AND COMPONENTS");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeWas_the_piping_in_operation_during_this_inspection =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Was the piping in operation during this inspection:",
          databaseName: "570_EXT_CKLST_SEC004_Q001",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC004Q002 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Was the piping operating within its design parameters:  (i.e. temperature and pressure)",
          databaseName: "570_EXT_CKLST_SEC004_Q002",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC004Q003 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is installed piping in accordance with owner / user design specification:",
          databaseName: "570_EXT_CKLST_SEC004_Q003",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC004Q004 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are installed flanges in accordance with owners / users piping design specifications:",
          databaseName: "570_EXT_CKLST_SEC004_Q004",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC004Q005 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are installed fasteners (bolting) in accordance with owners / users piping design specifications:",
          databaseName: "570_EXT_CKLST_SEC004_Q005",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC004Q006 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is a lack of engagement of more than ~1 thread present on any bolting fastener:",
          databaseName: "570_EXT_CKLST_SEC004_Q006",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC004Q007 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are threaded  connections in accordance with owner/ users piping design specifications:",
          databaseName: "570_EXT_CKLST_SEC004_Q007",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC004Q008 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Was evidence of vibration present within the inspected piping:",
          databaseName: "570_EXT_CKLST_SEC004_Q008",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeWas_there_evidence_of_piping_components_swaying =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Was there evidence of piping components swaying:",
          databaseName: "570_EXT_CKLST_SEC004_Q009",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC004Q010 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are piping joints misaligned:  (welded, flanged, or threaded joints)",
          databaseName: "570_EXT_CKLST_SEC004_Q010",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute570EXTCKLSTSEC004Q011 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Were any corrosion, erosion, or pitting cells noted on the piping or welds:",
          databaseName: "570_EXT_CKLST_SEC004_Q011",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Corrosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Erosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Pitting", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC004Q012 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Were any crack like indications noted on the piping or welds:",
          databaseName: "570_EXT_CKLST_SEC004_Q012",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute570EXTCKLSTSEC004Q013 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Was evidence of prior or active leakage noted to be originating from the piping or welded connections:",
          databaseName: "570_EXT_CKLST_SEC004_Q013",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Active Leakage", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Evidence of Prior Leakage", null,
            isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute570EXTCKLSTSEC004Q014 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Was evidence of prior or active leakage noted to be originating from flanged joint connections:",
          databaseName: "570_EXT_CKLST_SEC004_Q014",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Active Leakage", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Evidence of Prior Leakage", null,
            isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC004Q015 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are installed flange gaskets in accordance with owner/ users piping design specifications:",
          databaseName: "570_EXT_CKLST_SEC004_Q015",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC004Q016 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are flange gaskets installed properly and in acceptable condition for continued service:",
          databaseName: "570_EXT_CKLST_SEC004_Q016",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute570EXTCKLSTSEC004Q017 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Was evidence of prior or active leakage noted to be originating from threaded connections:",
          databaseName: "570_EXT_CKLST_SEC004_Q017",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Active Leakage", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Evidence of Prior Leakage", null,
            isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC004Q018 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Was corrosion found to be present at flanged joint connections:",
          databaseName: "570_EXT_CKLST_SEC004_Q018",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute570EXTCKLSTSEC004Q019 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Was corrosion or stretching found to be present of flange bolting fasteners:",
          databaseName: "570_EXT_CKLST_SEC004_Q019",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Corrosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Stretching", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute570EXTCKLSTSEC004Q020 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Are there locations of steam impingement, dripping fluid, or spilled liquids:",
          databaseName: "570_EXT_CKLST_SEC004_Q020",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Dripping Liquids", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Impingement", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Spilled Liquids", null,
            isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute570EXTCKLSTSEC004Q021 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Are there indications of bulging, scaling, buckling, hot spots, or metallurgical changes:",
          databaseName: "570_EXT_CKLST_SEC004_Q021",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Buckling", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Bulging", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Hot Spots", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Metallurgical Changes", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Spalling", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC004Q022 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Does the piping currently have any field modifications or temporary repairs that were not previously recorded:",
          databaseName: "570_EXT_CKLST_SEC004_Q022",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC004Q023 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the piping and associated components in acceptable condition for continued service:",
          databaseName: "570_EXT_CKLST_SEC004_Q023",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeWas_the_piping_in_operation_during_this_inspection,
      attribute570EXTCKLSTSEC004Q002,
      attribute570EXTCKLSTSEC004Q003,
      attribute570EXTCKLSTSEC004Q004,
      attribute570EXTCKLSTSEC004Q005,
      attribute570EXTCKLSTSEC004Q006,
      attribute570EXTCKLSTSEC004Q007,
      attribute570EXTCKLSTSEC004Q008,
      attributeWas_there_evidence_of_piping_components_swaying,
      attribute570EXTCKLSTSEC004Q010,
      attribute570EXTCKLSTSEC004Q011,
      attribute570EXTCKLSTSEC004Q012,
      attribute570EXTCKLSTSEC004Q013,
      attribute570EXTCKLSTSEC004Q014,
      attribute570EXTCKLSTSEC004Q015,
      attribute570EXTCKLSTSEC004Q016,
      attribute570EXTCKLSTSEC004Q017,
      attribute570EXTCKLSTSEC004Q018,
      attribute570EXTCKLSTSEC004Q019,
      attribute570EXTCKLSTSEC004Q020,
      attribute570EXTCKLSTSEC004Q021,
      attribute570EXTCKLSTSEC004Q022,
      attribute570EXTCKLSTSEC004Q023,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section570EXTCKLSTSEC004";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
