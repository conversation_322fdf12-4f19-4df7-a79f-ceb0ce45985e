import 'package:api_inspection/generic/AttributeControls/AttributeStructure.dart';
import 'package:api_inspection/generic/AttributeControls/Temperature/TemperatureAttributeView.dart';
import 'package:api_inspection/generic/Common/Types/Temperatures.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

import 'AttributeBase.dart';
import 'ChangeLog/ChangeLogEntry.dart';

class TemperatureAttribute extends SingleAttributeBase<double> {
  late Temperature? temperature;

  TemperatureUnits displayUnit;

  @override
  bool hasData() {
    return getValue() != null;
  }

  Temperature? getValue() {
    if (valueChangeLog.entries.isEmpty) return null;
    var lastValue = valueChangeLog.entries.last.value;
    if (lastValue == null) return null;
    return Temperature.fromKelvin(lastValue);
  }

  void setValue(Temperature? value) {
    if (getValue() == value) return;

    double? dbValue = value?.inKelvin;

    var entry = ChangeLogEntry<double>.newlyCreated(dbValue);
    valueChangeLog.addNewItem(entry);

    notifyListeners();
  }

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  bool allowNegatives;

  TemperatureAttribute(
      {required DataModelItem parent,
      required String displayName,
      required this.allowNegatives,
      required this.displayUnit,
      Widget? iconWidget,
      bool areCommentsRequired = false,
      String? databaseName})
      : super(parent, displayName, iconWidget, areCommentsRequired,
            databaseName) {
    temperature = getValue();
    valueChangeLog.setConversionMethod(convertDynamicToTemperature);
  }

  double? convertDynamicToTemperature(dynamic dyn) {
    if (dyn is int) {
      return dyn.toDouble();
    }
    return dyn as double?;
  }

  @override
  Widget buildWidget(
      {IsEditableController? editingController,
      bool showPhotos = true,
      bool showComments = true}) {
    return TemperatureAttributeView(
      this,
      editingController: editingController,
      showPhotos: showPhotos,
      showComments: showComments,
    );
  }

  @override
  String getPreviewText() {
    var value = getValue();
    return value == null
        ? ""
        : value.toString() + " " + displayUnit.abbreviation();
  }
}
