import 'package:api_inspection/generic/AttributeControls/AttributeStructure.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/DataModelCommon/PhotoRoot.dart';
import 'package:api_inspection/generic/MediaControls/MediaEntry.dart';
import 'package:api_inspection/generic/UIControls/listener.dart';

import 'ChangeLog/ChangeLog.dart';
import 'ChangeLog/ChangeLogEntry.dart';

class DatabaseList<T> extends DataModelItem {
  List<T> items = [];

  final String _name;

  @override
  String getDisplayName() => _name;

  void addItem(T item) {
    items.add(item);
    // add to db
  }

  void removeItem(T item) {
    items.remove(item);
  }

  DatabaseList(this._name, parent) : super(parent);

  @override
  List<DataModelItem> getChildren() {
    return List.empty();
  }

  @override
  String getDBName() {
    return _name;
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }

  @override
  void updateFromMap(Map? map) {
    if (map == null) return;
  }
}

abstract class ExtendedObserver {
  void notifyListeners();
}

abstract class AttributeBase extends DataModelItem with ExtendedObserver {
  bool isUserFacing = true;

  Widget? iconWidget = const Image(
      width: 30, height: 30, image: AssetImage('assets/indications.png'));
  String displayName = 'Not Set';
  String? databaseName;

  @override
  String getDisplayName() => displayName;

  final bool _areCommentsRequired;

  bool hasData();

  bool getIsInErrorState() {
    if (getAreCommentsRequired()) {
      var comment = getComment();
      if (comment == null || comment == "") return true;
    }
    return false;
  }

  bool getAreCommentsRequired() {
    return _areCommentsRequired;
  }

  Widget buildWidget(
      {IsEditableController? editingController,
      bool showPhotos = true,
      bool showComments = true});

  @override
  String getDBName() =>
      (databaseName ?? displayName).replaceAll("/", "_").replaceAll(".", "_");

  String getPreviewText();

  List<MediaEntry> getPhotos() {
    var photoRoot = findParentOfType<PhotoRoot>();
    if (photoRoot == null) return List.empty();
    var currentEntries = photoChangeLog.getCurrentEntries();
    List<MediaEntry> mediaEntries = [];
    for (var entry in currentEntries) {
      var parts = entry.split('.');
      mediaEntries.add(photoRoot.getPhoto(parts[0], parts[1]));
    }
    return mediaEntries;
  }

  void addPhoto(MediaEntry entry) {
    var newEntry =
        ListChangeLogEntry<String>.newlyCreated(entry.fullFileName, "Added");
    photoChangeLog.addNewItem(newEntry);

    notifyListeners();
  }

  removePhoto(MediaEntry entry) async {
    var photoRoot = findParentOfType<PhotoRoot>();
    if (photoRoot == null) throw "No photo root control found in stack";

    var changeEntry =
        ListChangeLogEntry<String>.newlyCreated(entry.fullFileName, "Removed");
    await photoChangeLog.addNewItem(changeEntry);

    notifyListeners();
  }

  late ChangeLogSingleItem<String> commentChangeLog =
      ChangeLogSingleItem<String>(
          "CommentChangeLog", this, <ChangeLogEntry<String>>[]);

  late ListChangeLog<String> photoChangeLog = ListChangeLog<String>(
      "PhotoChangeLog", this, <ListChangeLogEntry<String>>[]);

  ListenerWrapper changedBySelfListeners = ListenerWrapper();

  late final List<VoidCallback> _listeners =
      List<VoidCallback>.empty(growable: true);

  @override
  void addListener(VoidCallback listener) {
    _listeners.add(listener);
  }

  @override
  void removeListener(VoidCallback listener) {
    _listeners.remove(listener);
  }

  @override
  void notifyListeners() {
    for (var element in _listeners) {
      element.call();
    }
  }

  AttributeBase(DataModelItem parent, this.displayName, this.iconWidget,
      this._areCommentsRequired, this.databaseName)
      : super(parent);

  String? getComment() {
    if (commentChangeLog.entries.isEmpty) return null;
    return commentChangeLog.entries.last.value;
  }

  bool areStringsEqual(String? str1, String? str2) {
    if ((str1 == null || str1.isEmpty) && (str2 == null || str2.isEmpty)) {
      return true;
    }
    if (str1 == null) return false;
    if (str2 == null) return false;
    return str1 == str2;
  }

  void setComment(String? value, {bool notify = true}) {
    if (areStringsEqual(getComment(), value)) return;

    var entry = ChangeLogEntry<String>.newlyCreated(value ?? "");
    commentChangeLog.addNewItem(entry);

    if (notify) {
      notifyListeners();
    }
  }

  @override
  @mustCallSuper
  List<DataModelItem> getChildren() {
    return [photoChangeLog, commentChangeLog];
  }
}

abstract class SingleAttributeBase<ValueType> extends AttributeBase {
  late ChangeLogSingleItem<ValueType> valueChangeLog =
      ChangeLogSingleItem<ValueType>("ValueChangeLog", this, []);

  SingleAttributeBase(DataModelItem parent, String name, Widget? iconWidget,
      bool areCommentsRequired, String? databaseName)
      : super(parent, name, iconWidget, areCommentsRequired, databaseName);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.add(valueChangeLog);
    return children;
  }
}

abstract class MultiAttributeBase<ValueType> extends AttributeBase {
  late ListChangeLog<ValueType> valueChangeLog = ListChangeLog<ValueType>(
      "ValueChangeLog", this, <ListChangeLogEntry<ValueType>>[]);

  MultiAttributeBase(DataModelItem parent, String displayName,
      Widget? iconWidget, bool areCommentsRequired, String? databaseName)
      : super(
            parent, displayName, iconWidget, areCommentsRequired, databaseName);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.add(valueChangeLog);
    return children;
  }
}
