//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionShellSide.dart';

// ignore: camel_case_types
class SectionShellSidePage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionShellSide sectionShellSide;

  const SectionShellSidePage(this.sectionShellSide, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionShellSidePageState();
  }
}

class _SectionShellSidePageState extends State<SectionShellSidePage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionShellSide,
        title: "Shell Side",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionShellSide.attributeDesign_MAWP
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionShellSide.attributeDesign_Temperature
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionShellSide.attributeOperating_Temperature
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionShellSide.attributeOperating_Pressure
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionShellSide.attributePRV_Set_Pressure
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
