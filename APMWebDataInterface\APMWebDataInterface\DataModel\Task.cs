﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CommonDataInterface;
using CommonDataInterface.Attributes;
using APMWebDataInterface.DataModel;
using APMWebDataInterface.DataModel.Activity;
using APMWebDataInterface.Generated;
using APMWebDataInterface.Generated.section510_Asset_Walkdown_Details_F;
using APMWebDataInterface.Generated.section510_Ext_PV_ALL_F;
using APMWebDataInterface.Generated.section510_Int_PV_ALL_F;
using APMWebDataInterface.Generated.section570_Asset_Walkdown_Details_F;
using APMWebDataInterface.Generated.section570_Ext_Pipe_F;
using APMWebDataInterface.Generated.section653_Asset_Walkdown_Details_F;
using Newtonsoft.Json;

namespace APMWebDataInterface.ExampleDataModel
{

  public class TaskDetails : DataModelItem
  {
    public override string DisplayName => "TaskDetails";

    public TaskDetails(DataModelItem? parent) : base(parent)
    {
      projectNumber = new StringAttribute(parent: this, displayName: "Project Number");
      supervisor = new StringAttribute(parent: this, displayName: "Supervisor");

    }

    public StringAttribute projectNumber;
    public StringAttribute supervisor;
    
    public override DataModelItem[] GetChildren(){
      var parentChildren = base.GetChildren();

      return parentChildren.Concat(new DataModelItem[] { 
        projectNumber,  supervisor
      }).ToArray();
    }


    internal override String GetDBName() =>"TaskDetails";
    
  }


  public class UnexpectedTaskTypeException : Exception
  {
    public String TypeEncountered { get; set; }
  }

  public abstract class APMTask : ConcretePhotoRoot
  {
    public override string DisplayName => "Task";

    internal override String GetDBPath() => "tasks." + GetDBName();
    internal override String GetDBName() => id;

    public String projectId;
    
    [JsonIgnore]
    public Asset asset;
    [JsonIgnore]
    public WorkOrder workOrder;
    
    [JsonIgnore]
    public DataModelItem assetDetails;

    public TaskDetails taskDetails;

    public PredefinedValueAttribute status;
    
    public StringAttribute leadTech;
    public StringAttribute clientWorkOrderNumber;
    public StringAttribute clientWorkOrderDescription;
    public DateAttribute dueDate;
    public DateAttribute plannedStart;
    public DateAttribute plannedEnd;
    
    public StringAttribute taskAPMNumber;
    
    public StringAttribute clientCostCode;
    public StringAttribute purchaseOrderAFE;
     
    public StringAttribute taskComment;
    public StringAttribute businessUnitId;

    public enum Statuses {NotStarted, InProgress, Completed, Canceled, Scheduled, OnHold, Published }
    public void ChangeStatus(Statuses newStatus)
    {
      if (newStatus == Statuses.NotStarted) {
        status.SetValue("Not Started");
      }
      else if (newStatus == Statuses.InProgress) {
        status.SetValue("In Progress");
      }
      else if (newStatus == Statuses.Completed) {
        status.SetValue("Completed");
      }
      else if (newStatus == Statuses.Canceled) {
        status.SetValue("Canceled");
      }
      else if (newStatus == Statuses.Scheduled) {
        status.SetValue("Scheduled");
      }
      else if (newStatus == Statuses.OnHold) {
        status.SetValue("On Hold");
      }
      else if (newStatus == Statuses.Published) {
        status.SetValue("Published");
      }
    }

    List<String> _assignedUsers = new List<string>();
    internal PendingChange<List<String>> pendingAssignedUsers = null;

    public abstract DataModelItem getTaskData();
    public abstract void setTaskData(DataModelItem data);

    public String[] assignedUsers {
      get {
        if (pendingAssignedUsers == null)
          return _assignedUsers.ToArray();
        return pendingAssignedUsers.Value?.ToArray() ?? new String[0];
      }
      set {
        pendingAssignedUsers = new PendingChange<List<string>> { Value = value?.ToList() ?? new List<string>() };
      }
    }

    public DataModelCollection<TaskActivity> activities;

    public String taskType;

    public DateTime? LastChangedTime => GetLastChangedTime();

    public String CreatedBy {
      get {
        
        if (status.GetValueChangeLog().entries.All(a => a.Value != "Not Started"))
          return null;
        return status.GetValueChangeLog().entries.FirstOrDefault(a => a.Value == "Not Started").UserName;
      }
    }

    public DateTime? CreatedTime {
      get {
        if (status.GetValueChangeLog().entries.All(a => a.Value != "Not Started"))
          return null;

        return status.GetValueChangeLog().entries.Where(a => a.Value == "Not Started").Select(a => a.TimeChanged).OrderBy(a => a).FirstOrDefault();
      }  
    }

    public DateTime? StartedTime {
      get {
        if (status.GetValueChangeLog().entries.All(a => a.Value != "In Progress"))
          return null;

        return status.GetValueChangeLog().entries.Where(a => a.Value == "In Progress").Select(a => a.TimeChanged).OrderBy(a => a).FirstOrDefault();
      }  
    }

    public DateTime? CompletedTime {
      get {
        if (status.GetValueChangeLog().entries.All(a => a.Value != "Completed"))
          return null;

        return status.GetValueChangeLog().entries.Where(a => a.Value == "Completed").Select(a => a.TimeChanged).OrderBy(a => a).FirstOrDefault();
      }  
    }

    
    public DateTime? PublishedTime {
      get {
        if (status.GetValue() == "Published")
          return status.GetValueChangeLog().GetLastChangedTime();
        return null;
      }  
    }


    public String? CompletedTime_General => CompletedTime?.ToString("G");

    internal static APMTask Create(WorkOrder workOrder,String id, String taskType)
    {
      var asset = workOrder.asset;

      APMTask newTask = null;
      if (taskType.Equals("Asset Walkdown", StringComparison.InvariantCultureIgnoreCase)) {
        return new APMTask_Blank(workOrder, id, "Asset Walkdown"); 
      }
      else if (taskType.Equals("External Visual", StringComparison.InvariantCultureIgnoreCase)) {
        switch (asset.assetCategory){
          case "Vessel":
            newTask = new APMTaskGeneric<Section510_Ext_PV_ALL_F>(workOrder, id, "External Visual", (parent) => new Section510_Ext_PV_ALL_F(parent));
            break;
          case "Piping":
            newTask = new APMTaskGeneric<Section570_Ext_Pipe_F>(workOrder, id, "External Visual", (parent) => new Section570_Ext_Pipe_F(parent));
            break;
          case "Tank":
            throw new NotImplementedException("This is not implemented for MVP,  tanks only supports walkdown");
        }
      }
      else if (taskType.Equals("Internal Visual", StringComparison.InvariantCultureIgnoreCase)) {
        switch (asset.assetCategory){
          case "Vessel":
            newTask = new APMTaskGeneric<Section510_Int_PV_ALL_F>(workOrder, id, "Internal Visual", (parent) => new Section510_Int_PV_ALL_F(parent));
            break;
          case "Piping":
            throw new NotImplementedException("This is not implemented for MVP,  piping only supports walkdown and external visuals");
          case "Tank":
            throw new NotImplementedException("This is not implemented for MVP,  tanks only supports walkdown");
        }
      }
      else if (taskType.Equals("Full", StringComparison.InvariantCultureIgnoreCase)) {
        switch (asset.assetCategory){
          case "Vessel":
            newTask = new APMTaskGeneric<Section510_FullInspection>(workOrder, id, "Full", (parent) => new Section510_FullInspection(parent));
            break;
          case "Piping":
            throw new NotImplementedException("This is not implemented for MVP,  piping only supports walkdown and external visuals");
          case "Tank":
            throw new NotImplementedException("This is not implemented for MVP,  tanks only supports walkdown");
        }
      }
      else {
        throw new UnexpectedTaskTypeException {
          TypeEncountered = taskType
        };
      }


      return newTask;

    }

    protected APMTask(WorkOrder workOrder, String id, String taskType) : base(id, null)
    {
      clientWorkOrderNumber = new StringAttribute(parent: this, displayName: "Client Work Order Number", isQueryable: true);
      clientWorkOrderDescription = new StringAttribute(parent: this, displayName: "Client Work Order Description");
      leadTech = new StringAttribute(parent: this, displayName:"Lead Technician");
      
      dueDate = new DateAttribute(parent: this, displayName: "Due Date");
      plannedStart = new DateAttribute(parent: this, displayName:"Planned Start");
      plannedEnd = new DateAttribute(parent: this, displayName:"Planned End");

      taskAPMNumber = new StringAttribute(parent: this, displayName: "Task APM Number");
      
      clientCostCode = new StringAttribute(parent: this, displayName: "Client Cost Code");
      purchaseOrderAFE = new StringAttribute(parent: this, displayName: "Purchase Order/AFE");

      taskComment = new StringAttribute(parent: this, displayName: "Task Comment");
      businessUnitId = new StringAttribute(this, "BusinessUnitId", isQueryable: true);

            this.taskType = taskType;
      taskDetails = new TaskDetails(this);
      this.workOrder = workOrder;

      status = new PredefinedValueAttribute(hasOtherOption: false,
        parent : this,
        displayName: "Status",
        availableOptions: new List<PredefinedValueOption> { 
          new PredefinedValueOption("Canceled", null),
          new PredefinedValueOption("Not Started", null),
          new PredefinedValueOption("In Progress", null),
          new PredefinedValueOption("Completed", null),
        }
      );

      asset = workOrder.asset;
      projectId = workOrder.projectId;

      assetDetails = asset.walkDown;

      
      activities = new DataModelCollection<TaskActivity>("Activity Tracker", (parent, entry) => {
        return new TaskActivity(parent, entry.Key);
      }, (parent, id) => {
        return new TaskActivity(parent, id);
      }, this);
    }

    
    public override bool UpdateDirectPropertiesFromMapEntry(KeyValuePair<string, object>? entry) {
      if (entry == null)
        return false;

      if (entry.Value.Key == "AssignedUsers") {
        if (entry.Value.Value is IEnumerable assignedUsers) {
          var newUsers = new List<string>();
          foreach (var item in assignedUsers) {
            newUsers.Add(item.ToString());
          }

          _assignedUsers = newUsers;
        }

        return true;
      }

      return false;
    }

    public override bool GetHasDatabaseChangesPending()
    {
      return  pendingAssignedUsers != null || base.GetHasDatabaseChangesPending();
    }

    protected override async Task DoAddPendingChangesToDictionary(Dictionary<string, object> updates, string user)
    {

      if (pendingAssignedUsers != null) {
        updates["AssignedUsers"] = pendingAssignedUsers.Value;
        _assignedUsers = pendingAssignedUsers.Value.ToList();
        pendingAssignedUsers = null;
      }

      await base.DoAddPendingChangesToDictionary(updates, user);
    }

    public override void DoAddOneTimeChangesToDictionary(Dictionary<string, object> updates)
    {
      base.DoAddOneTimeChangesToDictionary(updates);

      updates["TaskType"] = taskType;
      updates["ProjectId"] = projectId;
      updates["AssetId"] = asset.id;
      updates["WorkOrderId"] = workOrder.id;
    }
    
    public override DataModelItem[] GetChildren(){
      var parentChildren = base.GetChildren();

      return parentChildren.Concat(new DataModelItem[] { 
        taskDetails, status, activities,leadTech, clientWorkOrderNumber, clientWorkOrderDescription,
        dueDate, plannedStart, plannedEnd, taskAPMNumber, clientCostCode,
        purchaseOrderAFE, businessUnitId
      }).ToArray();
    }


    public override Task SavePendingChanges(string user)
    {
        APM_WebDataInterface.Global.AuthorizeWriteToRootObject(this.businessUnitId, user);
        return base.SavePendingChanges(user);
    }

    public async Task UpdateFromFullTask(DataModelItem other)
    {
        bool updated = false;
        foreach (var child in GetChildren())
        {
            if (child is Section510_Int_PV_ALL_F || child is Section510_Ext_PV_ALL_F)
                continue;

            var matchingChild = other.GetChildren().FirstOrDefault((element) => element.GetDBName() == child.GetDBName());
            if (matchingChild == null)
            {
                throw new Exception("Unexpected null");
            }
            updated |= child.updateFromOther(matchingChild);

            var changes = new Dictionary<string, object>();
            await child.AddPendingChangesToDictionary(changes, "<EMAIL>");
        }

    }
  }


  public class APMTask_Blank : APMTask
  {
    public override DataModelItem getTaskData()
    {
      return null;
    }

    public override void setTaskData(DataModelItem item)
    {

    }

    public APMTask_Blank(WorkOrder workOrder, String id, String taskType) : base(workOrder, id, taskType)
    {
    }
  }

  public class APMTaskGeneric<T> : APMTask
    where T : DataModelItem 
  {
    public T taskData { get; private set; }


    public override DataModelItem getTaskData()
    {
      return taskData;
    }

    public override void setTaskData(DataModelItem data)
    {
      if (!(data is T)) {
        throw new Exception("Task data is of the wrong type");
      }
      taskData = data as T;
    }

    public APMTaskGeneric(WorkOrder workOrder, String id, String taskType, Func<APMTaskGeneric<T>, T> taskFactory) : base(workOrder, id, taskType)
    {
      taskData = taskFactory(this);
    }


    public override DataModelItem[] GetChildren(){
      var parentChildren = base.GetChildren();

      return parentChildren.Concat(new DataModelItem[] { 
        taskData
      }).ToArray();
    }

  }
  
}
