import 'package:api_inspection/generic/DataModelCommon/Attributes/AttributeBase.dart';
import 'package:api_inspection/generic/MediaControls/CameraPreviewPage.dart';
import 'package:api_inspection/generic/MediaControls/MediaSynchronizer.dart';
import 'package:api_inspection/generic/MediaControls/PhotoConfirmationPage.dart';
import 'package:flutter/material.dart';

class AddPhotoWithCommentButtonWidget extends StatefulWidget {
  final AttributeBase attribute;

  const AddPhotoWithCommentButtonWidget(this.attribute, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() =>
      _AddPhotoWithCommentButtonWidgetState();
}

class _AddPhotoWithCommentButtonWidgetState
    extends State<AddPhotoWithCommentButtonWidget> {
  @override
  Widget build(BuildContext context) {
    return TextButton(
        onPressed: () async {
          Navigator.of(context, rootNavigator: true).push(MaterialPageRoute(
              builder: (context) => PhotoPreviewControl((photo) {
                    setState(() {
                      onPhotoTaken(photo);
                    });
                  })));
        },
        child: Container(
            width: 65,
            height: 65,
            padding: const EdgeInsets.fromLTRB(5, 20, 5, 20),
            decoration: const BoxDecoration(
              color: Color.fromARGB(255, 41, 44, 52),
              borderRadius: BorderRadius.all(Radius.circular(8)),
            ),
            child: Container(
                alignment: Alignment.center,
                child: Stack(alignment: Alignment.center, children: [
                  Container(
                      margin: const EdgeInsets.fromLTRB(0, 0, 10, 10),
                      child: const Icon(
                        Icons.camera_alt,
                        key: ValueKey('AddphotoButton'),
                        color: Colors.grey,
                      )),
                  Container(
                      margin: const EdgeInsets.fromLTRB(15, 10, 0, 0),
                      child: const Icon(
                        Icons.note_add,
                        size: 22,
                        key: ValueKey('AddphotoButton'),
                        color: Colors.blue,
                      ))
                ]))));
  }

  Future<void> onPhotoTaken(MediaPackage photo) async {
    return await IMediaSynchronizer.getMediaSynchronizer()
        .onPhotoTaken(photo, widget.attribute);
  }
}
