//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'SectionRepairs.dart';

// ignore: camel_case_types
class SectionRepairRecord extends DataModelSection {
  @override
  String getDisplayName() => "Repair Record";
  SectionRepairRecord(DataModelItem? parent)
      : super(parent: parent, sectionName: "Repair Record");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute
      attributeDoes_the_asset_have_a_repair_or_alteration_plate =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Does the asset have a repair or alteration plate?",
          databaseName: "510AW_Q181",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late BooleanAttribute attributeRepairAlteration_Plates_Legible =
      BooleanAttribute(
          parent: this,
          displayName: "Repair/Alteration Plate(s) Legible?",
          databaseName: "510AW_Q182",
          areCommentsRequired: false);

// ignore: non_constant_identifier_names
  late DataModelCollection<SectionRepairs> sectionRepairs =
      DataModelCollection<SectionRepairs>("Repairs", (parent, entry) {
    return SectionRepairs(entry.key, sectionRepairs);
  }, this);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      sectionRepairs,
      attributeDoes_the_asset_have_a_repair_or_alteration_plate,
      attributeRepairAlteration_Plates_Legible,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionRepairRecord";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
