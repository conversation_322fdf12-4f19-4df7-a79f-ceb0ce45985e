import 'dart:io';
import 'dart:typed_data';
import 'package:api_inspection/generic/MediaControls/MediaSynchronizer.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:collection/collection.dart';

class FileCache {
  bool initialized = false;

  final String _cacheName;
  Future<String> getCacheDirectoryPath() async {
    Directory appDocDirectory = await getApplicationDocumentsDirectory();
    return appDocDirectory.path + '/' + _cacheName;
  }

  final List<File> _filesInCache = [];

  FileCache(this._cacheName);

  List<File> getFilesFromCache() {
    if (!initialized) {
      throw Exception("Must call initialize before getFilesFromCache");
    }

    return _filesInCache;
  }

  File? getFileFromCache(String fileName) {
    if (!initialized) {
      throw Exception("Must call initialize before getFilesFromCache");
    }

    return _filesInCache.firstWhereOrNull(
        (element) => getNameFromPath(element.path) == fileName);
  }

  Future<File> addFileToCache(String fileName, Uint8List bytes) async {
    var directoryPath = await getCacheDirectoryPath();
    var dir = Directory(directoryPath);
    await dir.create();
    File file = File(dir.path + "/" + fileName);

    await file.writeAsBytes(bytes);

    _filesInCache.add(file);

    return file;
  }

  Future<File> moveFileToOtherCache(File file, FileCache otherCache) async {
    String fileName = file.path.substring(file.path.lastIndexOf('/') + 1);

    var otherDirectoryPath = await otherCache.getCacheDirectoryPath();
    var renamedFile = await file.rename(otherDirectoryPath + '/' + fileName);
    _filesInCache.remove(file);
    otherCache._filesInCache.add(file);
    return renamedFile;
  }

  Future<void> removeFileFromCache(File file) async {
    file.delete();
    _filesInCache.remove(file);
  }

  Future<void> initialize() async {
    WidgetsFlutterBinding.ensureInitialized();
    var directoryPath = await getCacheDirectoryPath();
    Directory subDirectory = Directory(directoryPath);
    bool directoryExists = await subDirectory.exists();
    if (directoryExists) {
      await _loadFilesFromCache(subDirectory);
    } else {
      await subDirectory.create();
    }

    initialized = true;
  }

  Future<void> _loadFilesFromCache(Directory directory) async {
    var directoryStream = directory.list();
    var directoryItems = await directoryStream.toList();
    for (var item in directoryItems) {
      if (item is File) {
        _filesInCache.add(item);
      }
    }
  }
}
