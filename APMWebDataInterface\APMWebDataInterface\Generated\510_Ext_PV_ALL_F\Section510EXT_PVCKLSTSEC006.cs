//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Ext_PV_ALL_F
{
  public class Section510EXT_PVCKLSTSEC006 : DataModelItem {

    public override String DisplayName { 
      get {
        return "RIVETING";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributeWere_rivets_utilized_in_the_construction_of_the_asset;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC006Q002;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC006Q003;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC006Q004;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510EXT_PVCKLSTSEC006";

    public Section510EXT_PVCKLSTSEC006(DataModelItem parent) : base(parent)
    {
            
        attributeWere_rivets_utilized_in_the_construction_of_the_asset = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Were rivets utilized in the construction of the asset:", databaseName: "510_EXT-PV_CKLST_SEC006_Q001"); 
     
        attribute510EXT_PVCKLSTSEC006Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are the rivet heads, butt straps, plates, and caulking in acceptable condition for continued service:", databaseName: "510_EXT-PV_CKLST_SEC006_Q002"); 
     
        attribute510EXT_PVCKLSTSEC006Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Active Leakage", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Evidence of Prior Leakage", null)
        }, false, this, "Was evidence of prior or active leakage noted to be originating from the riveted joint:", databaseName: "510_EXT-PV_CKLST_SEC006_Q003"); 
     
        attribute510EXT_PVCKLSTSEC006Q004 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are the rivets and joints in acceptable condition for continued service:", databaseName: "510_EXT-PV_CKLST_SEC006_Q004"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeWere_rivets_utilized_in_the_construction_of_the_asset,
           attribute510EXT_PVCKLSTSEC006Q002,
           attribute510EXT_PVCKLSTSEC006Q003,
           attribute510EXT_PVCKLSTSEC006Q004,
        };
    }
  }
}
