//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section570_Ext_Pipe_F
{
  public class Section570EXTCKLSTSEC005 : DataModelItem {

    public override String DisplayName { 
      get {
        return "PRESSURE RELIEF (PRD/PRV)";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute570EXTCKLSTSEC005Q001;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC005Q002;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC005Q003;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC005Q004;
    public PredefinedValueAttribute attributeWas_any_mechanical_damage_noted_on_the_PRD__PRV;
    public PredefinedValueAttribute attributeIs_the_Relief_attachment_achieved_via_flanging;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC005Q007;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC005Q008;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC005Q009;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC005Q010;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC005Q011;
    public PredefinedValueAttribute attributeIs_the_Relief_valve_vent_piping_routed_to_a_safe_location;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC005Q013;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC005Q014;
    public PredefinedValueAttribute attributeIs_the_spring_tamper_car_seal_intact;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC005Q016;
    public PredefinedValueAttribute attributeIs_Relief_alignment_acceptable;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC005Q018;
    public PredefinedValueAttribute attributeWas_any_excessive_vibration_of_the_Relief_noted;
    public PredefinedValueAttribute attributeWas_the_rupture_device_orientation_correct;
    public PredefinedValueAttribute attributeAre_associated_block_valves_car_sealed_in_the_open_position;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC005Q022;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC005Q023;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section570EXTCKLSTSEC005";

    public Section570EXTCKLSTSEC005(DataModelItem parent) : base(parent)
    {
            
        attribute570EXTCKLSTSEC005Q001 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Has an inspection & test of Relief been performed within the required interval:  (i.e. 5 years for typical process services,10 years for clean (non-fouling) and noncorrosive services)", databaseName: "570_EXT_CKLST_SEC005_Q001"); 
     
        attribute570EXTCKLSTSEC005Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is Relief set at or below the max allowable operating pressure (MAOP) of the piping:  (Data plate & calibration tag information shall be recorded)", databaseName: "570_EXT_CKLST_SEC005_Q002"); 
     
        attribute570EXTCKLSTSEC005Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Erosion", null),
          new PredefinedValueOption("Yes: Pitting", null)
        }, false, this, "Were any corrosion, erosion, or pitting cells noted on the installed PRD / PRV:", databaseName: "570_EXT_CKLST_SEC005_Q003"); 
     
        attribute570EXTCKLSTSEC005Q004 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Were any crack like indications noted on the installed PRD / PRV:", databaseName: "570_EXT_CKLST_SEC005_Q004"); 
     
        attributeWas_any_mechanical_damage_noted_on_the_PRD__PRV = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was any mechanical damage noted on the PRD / PRV", databaseName: "570_EXT_CKLST_SEC005_Q005"); 
     
        attributeIs_the_Relief_attachment_achieved_via_flanging = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the Relief attachment achieved via flanging:", databaseName: "570_EXT_CKLST_SEC005_Q006"); 
     
        attribute570EXTCKLSTSEC005Q007 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Were flange fasteners fully engaged: (Any fastener is considered acceptably engaged if the lack of complete engagement is not more than one thread)", databaseName: "570_EXT_CKLST_SEC005_Q007"); 
     
        attribute570EXTCKLSTSEC005Q008 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are the flange fasteners utilized in alignment with Client specification:", databaseName: "570_EXT_CKLST_SEC005_Q008"); 
     
        attribute570EXTCKLSTSEC005Q009 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are the flange gaskets utilized in alignment with Client specification:", databaseName: "570_EXT_CKLST_SEC005_Q009"); 
     
        attribute570EXTCKLSTSEC005Q010 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the Relief attachment achieved via threaded connection(s):", databaseName: "570_EXT_CKLST_SEC005_Q010"); 
     
        attribute570EXTCKLSTSEC005Q011 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Were the Relief threaded connection(s) in acceptable condition for continued service:", databaseName: "570_EXT_CKLST_SEC005_Q011"); 
     
        attributeIs_the_Relief_valve_vent_piping_routed_to_a_safe_location = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the Relief valve vent piping routed to a safe location:", databaseName: "570_EXT_CKLST_SEC005_Q012"); 
     
        attribute570EXTCKLSTSEC005Q013 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was the Relief valve vent piping verified to be free of any foreign material that may cause plugging:", databaseName: "570_EXT_CKLST_SEC005_Q013"); 
     
        attribute570EXTCKLSTSEC005Q014 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was the bellows vent verified to be free of any foreign material that may cause plugging:", databaseName: "570_EXT_CKLST_SEC005_Q014"); 
     
        attributeIs_the_spring_tamper_car_seal_intact = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the spring tamper car seal intact:", databaseName: "570_EXT_CKLST_SEC005_Q015"); 
     
        attribute570EXTCKLSTSEC005Q016 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the manual operation leaver in acceptable condition for continued service:", databaseName: "570_EXT_CKLST_SEC005_Q016"); 
     
        attributeIs_Relief_alignment_acceptable = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is Relief alignment acceptable:", databaseName: "570_EXT_CKLST_SEC005_Q017"); 
     
        attribute570EXTCKLSTSEC005Q018 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Were the Relief supports in acceptable condition for continued service:", databaseName: "570_EXT_CKLST_SEC005_Q018"); 
     
        attributeWas_any_excessive_vibration_of_the_Relief_noted = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was any excessive vibration of the Relief noted:", databaseName: "570_EXT_CKLST_SEC005_Q019"); 
     
        attributeWas_the_rupture_device_orientation_correct = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was the rupture device orientation correct:", databaseName: "570_EXT_CKLST_SEC005_Q020"); 
     
        attributeAre_associated_block_valves_car_sealed_in_the_open_position = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are associated block valves car sealed in the open position:", databaseName: "570_EXT_CKLST_SEC005_Q021"); 
     
        attribute570EXTCKLSTSEC005Q022 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Active Leakage", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Evidence of Prior Leakage", null)
        }, false, this, "Was evidence of prior or active leakage noted originating from PRD / PRV:", databaseName: "570_EXT_CKLST_SEC005_Q022"); 
     
        attribute570EXTCKLSTSEC005Q023 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the PRD / PRV in acceptable condition for continued service:", databaseName: "570_EXT_CKLST_SEC005_Q023"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute570EXTCKLSTSEC005Q001,
           attribute570EXTCKLSTSEC005Q002,
           attribute570EXTCKLSTSEC005Q003,
           attribute570EXTCKLSTSEC005Q004,
           attributeWas_any_mechanical_damage_noted_on_the_PRD__PRV,
           attributeIs_the_Relief_attachment_achieved_via_flanging,
           attribute570EXTCKLSTSEC005Q007,
           attribute570EXTCKLSTSEC005Q008,
           attribute570EXTCKLSTSEC005Q009,
           attribute570EXTCKLSTSEC005Q010,
           attribute570EXTCKLSTSEC005Q011,
           attributeIs_the_Relief_valve_vent_piping_routed_to_a_safe_location,
           attribute570EXTCKLSTSEC005Q013,
           attribute570EXTCKLSTSEC005Q014,
           attributeIs_the_spring_tamper_car_seal_intact,
           attribute570EXTCKLSTSEC005Q016,
           attributeIs_Relief_alignment_acceptable,
           attribute570EXTCKLSTSEC005Q018,
           attributeWas_any_excessive_vibration_of_the_Relief_noted,
           attributeWas_the_rupture_device_orientation_correct,
           attributeAre_associated_block_valves_car_sealed_in_the_open_position,
           attribute570EXTCKLSTSEC005Q022,
           attribute570EXTCKLSTSEC005Q023,
        };
    }
  }
}
