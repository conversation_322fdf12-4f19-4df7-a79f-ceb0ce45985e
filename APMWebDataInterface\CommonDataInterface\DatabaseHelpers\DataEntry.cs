﻿using System;
using Newtonsoft.Json.Linq;

namespace CommonDataInterface.DatabaseHelpers
{
  public class DataEntry
  {
    public String Key;

    private object _value;

    public object Value {
      get {
        if (_value is JValue jValue) {
          return jValue.Value;
        }

        var tryToMakeDictionary = IDictionaryWrapper.Create(_value);
        if (tryToMakeDictionary != null) {
          return tryToMakeDictionary;
        }
        
        return _value;
      }
      set {
        _value = value;
      }
    }
  }
}
