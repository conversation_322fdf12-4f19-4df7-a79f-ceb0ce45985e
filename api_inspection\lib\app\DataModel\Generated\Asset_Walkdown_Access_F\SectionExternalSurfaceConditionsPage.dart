//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionExternalSurfaceConditions.dart';
import 'SectionCoatingPage.dart';
import 'SectionCorrosionPage.dart';

// ignore: camel_case_types
class SectionExternalSurfaceConditionsPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionExternalSurfaceConditions sectionExternalSurfaceConditions;

  const SectionExternalSurfaceConditionsPage(
      this.sectionExternalSurfaceConditions,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionExternalSurfaceConditionsPageState();
  }
}

class _SectionExternalSurfaceConditionsPageState
    extends State<SectionExternalSurfaceConditionsPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionExternalSurfaceConditions,
        title: "External Surface Conditions",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget
                            .sectionExternalSurfaceConditions.sectionCoating,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => SectionCoatingPage(
                                          widget
                                              .sectionExternalSurfaceConditions
                                              .sectionCoating)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget
                            .sectionExternalSurfaceConditions.sectionCorrosion,
                        onPressed: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => SectionCorrosionPage(
                                      widget.sectionExternalSurfaceConditions
                                          .sectionCorrosion))).then(
                              (value) => setState(() {}));
                        }),
                  ),
                ]),
          );
        });
  }
}
