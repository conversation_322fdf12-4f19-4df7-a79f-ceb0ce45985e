import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/UIControls/BooleanController.dart';
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';

class TeamBooleanButtons extends StatefulWidget {
  final BooleanController _controller;
  const TeamBooleanButtons(this._controller, {Key? key}) : super(key: key);

  @override
  _TeamBooleanButtonState createState() => _TeamBooleanButtonState(_controller);
}

class _TeamBooleanButtonState extends State<TeamBooleanButtons> {
  final BooleanController _controller;

  _TeamBooleanButtonState(this._controller) {
    _controller.addListener(onControllerUpdated);
  }

  @override
  void dispose() {
    _controller.removeListener(onControllerUpdated);
    super.dispose();
  }

  void onControllerUpdated() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    bool? currentValue = _controller.getValue();

    TeamToggleButton trueButton;
    TeamToggleButton falseButton;
    Color trueBorder = currentValue == true
        ? const Color.fromARGB(255, 4, 188, 242)
        : const Color.fromARGB(255, 122, 122, 122);

    Color falseBorder = currentValue == false
        ? const Color.fromARGB(255, 4, 188, 242)
        : const Color.fromARGB(255, 122, 122, 122);

    trueButton = TeamToggleButton.withText(
        "Yes", () => {_controller.setValueToggleIfSame(true)},
        borderColor: trueBorder);

    falseButton = TeamToggleButton.withText(
        "No", () => {_controller.setValueToggleIfSame(false)},
        borderColor: falseBorder);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(width: 150, child: trueButton, key: const ValueKey("yesBtn")),
        SizedBox(width: 150, child: falseButton, key: const ValueKey("noBtn")),
      ],
    );
  }
}
