$currentDir = Get-Location
Write-Host "Current directory: $currentDir"
Write-Host "Checking if pubspec.yaml exists:"
$pubspecPath = ".\pubspec.yaml"
if (Test-Path $pubspecPath) {
    Write-Host "pubspec.yaml found!"
} else {
    Write-Host "pubspec.yaml NOT found!"
}

# Run Flutter application
Write-Host "Running Flutter application..."
flutter run -d chrome --web-port=8080 -t lib/main_azure_dev.dart
