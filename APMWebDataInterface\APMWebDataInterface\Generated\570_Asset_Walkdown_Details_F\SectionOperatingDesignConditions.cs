//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section570_Asset_Walkdown_Details_F
{
  public class SectionOperatingDesignConditions : DataModelItem {

    public override String DisplayName { 
      get {
        return "Operating/Design Conditions";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public IntegerAttribute attributeOperating_Temperature;
    public DoubleAttribute attributeDesign_MAWP;
    public DoubleAttribute attributeDesign_Temperature;
    public DoubleAttribute attributeOperating_Pressure;
    public DoubleAttribute attributePRV_Set_Pressure;
    public PredefinedValueAttribute attributeOperation_Status;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionOperatingDesignConditions";

    public SectionOperatingDesignConditions(DataModelItem parent) : base(parent)
    {
            
        attributeOperating_Temperature = new IntegerAttribute(this, displayName: "Operating Temperature", databaseName: "570AW_Q205", areCommentsRequired: false, displayUnit: "F", allowNegatives: true); 
     
        attributeDesign_MAWP = new DoubleAttribute(this, displayName: "Design MAWP", databaseName: "570AW_Q206", areCommentsRequired: false, displayUnit: "psi", allowNegatives: true); 
     
        attributeDesign_Temperature = new DoubleAttribute(this, displayName: "Design Temperature", databaseName: "570AW_Q207", areCommentsRequired: false, displayUnit: "F", allowNegatives: true); 
     
        attributeOperating_Pressure = new DoubleAttribute(this, displayName: "Operating Pressure", databaseName: "570AW_Q208", areCommentsRequired: false, displayUnit: "psi", allowNegatives: true); 
     
        attributePRV_Set_Pressure = new DoubleAttribute(this, displayName: "PRV Set Pressure", databaseName: "570AW_Q209", areCommentsRequired: false, displayUnit: "psi", allowNegatives: true); 
     
        attributeOperation_Status = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("In-Service", null),
          new PredefinedValueOption("Out-Of-Service", null),
          new PredefinedValueOption("Standby", null)
        }, false, this, "Operation Status", databaseName: "570AW_Q210"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeOperating_Temperature,
           attributeDesign_MAWP,
           attributeDesign_Temperature,
           attributeOperating_Pressure,
           attributePRV_Set_Pressure,
           attributeOperation_Status,
        };
    }
  }
}
