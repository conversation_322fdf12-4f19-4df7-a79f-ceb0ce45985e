//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Ext_PV_ALL_F
{
  public class Section510EXT_PVCKLSTSEC002 : DataModelItem {

    public override String DisplayName { 
      get {
        return "LADDERS, STAIRWAYS, PLATFORMS, AND WALKWAYS";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC002Q001;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC002Q002;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC002Q003;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC002Q004;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC002Q005;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC002Q006;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC002Q007;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510EXT_PVCKLSTSEC002";

    public Section510EXT_PVCKLSTSEC002(DataModelItem parent) : base(parent)
    {
            
        attribute510EXT_PVCKLSTSEC002Q001 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are ladders, stairways, platforms or walkways associated with this asset:", databaseName: "510_EXT-PV_CKLST_SEC002_Q001"); 
     
        attribute510EXT_PVCKLSTSEC002Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Broken", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Cracking", null),
          new PredefinedValueOption("Yes: Corroded", null),
          new PredefinedValueOption("Yes: Loose", null),
          new PredefinedValueOption("Yes: Missing", null)
        }, false, this, "Is attachment hardware or bolting cracked, corroded, broken, missing or loose:", databaseName: "510_EXT-PV_CKLST_SEC002_Q002"); 
     
        attribute510EXT_PVCKLSTSEC002Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the protective coating / galvanizing material in acceptable condition for continued service:", databaseName: "510_EXT-PV_CKLST_SEC002_Q003"); 
     
        attribute510EXT_PVCKLSTSEC002Q004 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are ladder rungs or stairway treads in acceptable condition for continued service:", databaseName: "510_EXT-PV_CKLST_SEC002_Q004"); 
     
        attribute510EXT_PVCKLSTSEC002Q005 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are handrails, mid-rails & toe rails in acceptable condition for continued service:", databaseName: "510_EXT-PV_CKLST_SEC002_Q005"); 
     
        attribute510EXT_PVCKLSTSEC002Q006 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are the flooring surfaces of platforms & walkways in acceptable condition for continued service:", databaseName: "510_EXT-PV_CKLST_SEC002_Q006"); 
     
        attribute510EXT_PVCKLSTSEC002Q007 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are safety devices in acceptable condition for continued service:  (I.e. ladder cage, safety gate, etc.)", databaseName: "510_EXT-PV_CKLST_SEC002_Q007"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute510EXT_PVCKLSTSEC002Q001,
           attribute510EXT_PVCKLSTSEC002Q002,
           attribute510EXT_PVCKLSTSEC002Q003,
           attribute510EXT_PVCKLSTSEC002Q004,
           attribute510EXT_PVCKLSTSEC002Q005,
           attribute510EXT_PVCKLSTSEC002Q006,
           attribute510EXT_PVCKLSTSEC002Q007,
        };
    }
  }
}
