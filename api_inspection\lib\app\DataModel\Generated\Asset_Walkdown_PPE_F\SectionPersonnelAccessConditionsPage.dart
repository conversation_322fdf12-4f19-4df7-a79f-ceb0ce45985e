//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionPersonnelAccessConditions.dart';
import 'SectionStandingWaterPage.dart';
import 'SectionOvergrownvegetationPage.dart';

// ignore: camel_case_types
class SectionPersonnelAccessConditionsPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionPersonnelAccessConditions sectionPersonnelAccessConditions;

  const SectionPersonnelAccessConditionsPage(
      this.sectionPersonnelAccessConditions,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionPersonnelAccessConditionsPageState();
  }
}

class _SectionPersonnelAccessConditionsPageState
    extends State<SectionPersonnelAccessConditionsPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionPersonnelAccessConditions,
        title: "Personnel Access Conditions",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionPersonnelAccessConditions
                      .attributeConditions_observed_on_site
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionPersonnelAccessConditions
                            .sectionStandingWater,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          SectionStandingWaterPage(widget
                                              .sectionPersonnelAccessConditions
                                              .sectionStandingWater)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionPersonnelAccessConditions
                            .sectionOvergrownvegetation,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          SectionOvergrownvegetationPage(widget
                                              .sectionPersonnelAccessConditions
                                              .sectionOvergrownvegetation)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(widget
                      .sectionPersonnelAccessConditions.attributePower_available
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionPersonnelAccessConditions.attributeWater_available
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
