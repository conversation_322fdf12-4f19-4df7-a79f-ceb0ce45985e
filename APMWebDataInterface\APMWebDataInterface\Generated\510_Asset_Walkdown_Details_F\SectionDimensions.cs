//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Asset_Walkdown_Details_F
{
  public class SectionDimensions : DataModelItem {

    public override String DisplayName { 
      get {
        return "Dimensions";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributeDiameter_Measurement;
    public DoubleAttribute attributeDiameter;
    public PredefinedValueAttribute attributeDoes_the_shell_have_multiple_diameters;
    public DoubleAttribute attributeOverall_Length_or_Height;
    public PredefinedValueAttribute attributeAre_there_toriconical_transition_sections_in_the_shell;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionDimensions";

    public SectionDimensions(DataModelItem parent) : base(parent)
    {
            
        attributeDiameter_Measurement = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("ID", null),
          new PredefinedValueOption("OD", null)
        }, false, this, "Diameter Measurement", databaseName: "510AW_Q351"); 
     
        attributeDiameter = new DoubleAttribute(this, displayName: "Diameter", databaseName: "510AW_Q352", areCommentsRequired: false, displayUnit: "in", allowNegatives: true); 
     
        attributeDoes_the_shell_have_multiple_diameters = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Does the shell have multiple diameters?", databaseName: "510AW_Q353"); 
     
        attributeOverall_Length_or_Height = new DoubleAttribute(this, displayName: "Overall Length or Height", databaseName: "510AW_Q354", areCommentsRequired: false, displayUnit: "in", allowNegatives: true); 
     
        attributeAre_there_toriconical_transition_sections_in_the_shell = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Are there toriconical transition sections in the shell?", databaseName: "510AW_Q355"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeDiameter_Measurement,
           attributeDiameter,
           attributeDoes_the_shell_have_multiple_diameters,
           attributeOverall_Length_or_Height,
           attributeAre_there_toriconical_transition_sections_in_the_shell,
        };
    }
  }
}
