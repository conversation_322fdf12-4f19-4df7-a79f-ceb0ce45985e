import 'package:api_inspection/generic/DataModelCommon/Attributes/AttributeBase.dart';
import 'package:api_inspection/generic/MediaControls/CameraPreviewPage.dart';
import 'package:api_inspection/generic/MediaControls/MediaSynchronizer.dart';
import 'package:api_inspection/generic/MediaControls/PhotoConfirmationPage.dart';
import 'package:flutter/material.dart';

class AddPhotoButtonWidget extends StatefulWidget {
  final AttributeBase attribute;

  const AddPhotoButtonWidget(this.attribute, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _AddPhotoButtonWidgetState();
}

class _AddPhotoButtonWidgetState extends State<AddPhotoButtonWidget> {
  @override
  Widget build(BuildContext context) {
    return TextButton(
        onPressed: () async {
          Navigator.of(context, rootNavigator: true).push(MaterialPageRoute(
              builder: (context) => PhotoPreviewControl((photo) {
                    setState(() {
                      onPhotoTaken(photo);
                    });
                  }, showConfirmation: false)));
        },
        child: Container(
          width: 65,
          height: 65,
          padding: const EdgeInsets.all(20),
          decoration: const BoxDecoration(
            color: Color.fromARGB(255, 41, 44, 52),
            borderRadius: BorderRadius.all(Radius.circular(8)),
          ),
          child: const FittedBox(
              fit: BoxFit.fill,
              child: Icon(
                Icons.camera_alt,
                key: ValueKey('AddphotoButton'),
                color: Colors.grey,
              )),
        ));
  }

  Future<void> onPhotoTaken(MediaPackage photo) async {
    return await IMediaSynchronizer.getMediaSynchronizer()
        .onPhotoTaken(photo, widget.attribute);
  }
}
