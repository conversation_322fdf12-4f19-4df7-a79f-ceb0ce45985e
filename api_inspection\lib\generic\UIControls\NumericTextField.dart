import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class InputDoneView extends StatelessWidget {
  final Function? onSubmitted;

  const InputDoneView(this.onSubmitted, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      color: Colors.grey[300],
      child: <PERSON>gn(
        alignment: Alignment.topRight,
        child: Padding(
          padding: const EdgeInsets.only(top: 4.0, bottom: 4.0),
          child: CupertinoButton(
            padding: const EdgeInsets.only(right: 24.0, top: 8.0, bottom: 8.0),
            onPressed: () {
              FocusScope.of(context).requestFocus(FocusNode());
              onSubmitted?.call();
            },
            child: const Text("Done",
                style: TextStyle(
                    color: Colors.black, fontWeight: FontWeight.bold)),
          ),
        ),
      ),
    );
  }
}

class NumericTextField extends StatefulWidget {
  final Function(String?)? onSubmitted;
  final bool signed;
  final bool decimal;
  final TextEditingController controller;

  final InputDecoration? decoration;
  final TextStyle? textStyle;

  const NumericTextField(
      this.controller, this.signed, this.decimal, this.onSubmitted,
      {Key? key, this.decoration, this.textStyle})
      : super(key: key);

  @override
  _NumericTextFieldState createState() => _NumericTextFieldState();
}

class _NumericTextFieldState extends State<NumericTextField> {
  final FocusNode _focusNode = FocusNode();

  TextEditingController? _controller;

  OverlayEntry? overlayEntry;
  showOverlay(BuildContext context) {
    if (!Platform.isIOS) {
      return null;
    }
    if (widget.signed) return null;
    var currentEntry = overlayEntry;
    if (currentEntry != null) return;
    OverlayState? overlayState = Overlay.of(context);
    currentEntry = OverlayEntry(builder: (context) {
      return Positioned(
          bottom: MediaQuery.of(context).viewInsets.bottom,
          right: 0.0,
          left: 0.0,
          child: InputDoneView(() {
            widget.onSubmitted?.call(_controller?.text);
          }));
    });
    overlayEntry = currentEntry;

    if (overlayState != null) {
      overlayState.insert(currentEntry);
    }
  }

  removeOverlay() {
    if (kIsWeb) return;

    if (!Platform.isIOS) {
      return;
    }

    if (widget.signed) return;

    var currentEntry = overlayEntry;
    if (currentEntry != null) {
      currentEntry.remove();
      overlayEntry = null;
    }
  }

  @override
  void initState() {
    super.initState();

    _focusNode.addListener(() {
      bool hasFocus = _focusNode.hasFocus;
      if (hasFocus) {
        showOverlay(context);
      } else {
        removeOverlay();
      }
    });
  }

  @override
  void deactivate() {
    removeOverlay();
    super.deactivate();
  }

  @override
  Widget build(BuildContext context) {
    String regex;
    if (widget.signed) {
      regex = r"^[+-]?([0-9]*\.?[0-9]*|\.[0-9]+)";
    } else {
      regex = r"([0-9]*\.?[0-9]*|\.[0-9]+)";
    }

    return Focus(
        onFocusChange: (hasFocus) {
          widget.onSubmitted?.call(widget.controller.text);
        },
        child: TextField(
          key: const ValueKey('NumericTextField'),
          controller: widget.controller,
          focusNode: _focusNode,
          inputFormatters: [FilteringTextInputFormatter.allow(RegExp(regex))],
          keyboardType: TextInputType.numberWithOptions(
              signed: widget.signed, decimal: widget.decimal),
          decoration: widget.decoration ??
              const InputDecoration(
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.white, width: 1.0),
                ),
                border: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.white, width: 1.0),
                ),
              ),
          onSubmitted: widget.onSubmitted,
          style: widget.textStyle ?? const TextStyle(color: Colors.white),
        ));
  }
}
