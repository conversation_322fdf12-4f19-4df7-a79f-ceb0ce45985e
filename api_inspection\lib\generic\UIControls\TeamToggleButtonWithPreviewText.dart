import 'package:api_inspection/generic/AppStyle.dart';
import 'package:flutter/material.dart';

class TeamToggleButtonWithPreviewText extends StatelessWidget {
  final Function() _onPressed;
  final String _label;
  final String? _previewText;
  final Color color;
  final Color borderColor;
  final Widget? icon;
  final Widget? errorIcon;

  const TeamToggleButtonWithPreviewText(
      this._label, this._onPressed, this._previewText, this.icon,
      {Key? key,
      this.color = const Color.fromARGB(255, 41, 45, 52),
      this.borderColor = const Color.fromARGB(255, 122, 122, 122),
      this.errorIcon})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (_label.length > 40) return buildLargeLabel(context);
    return buildNormal(context);
  }

  Widget buildLargeLabel(BuildContext context) {
    String? nPreviewText = _previewText;
    String previewText = nPreviewText ?? '';

    var valueWidget = Expanded(
        child: Text(
      _label,
      textAlign: TextAlign.start,
      style: const TextStyle(color: Colors.white, fontSize: 16),
      softWrap: true,
    ));

    Widget previewWidget = SizedBox(
        width: 110,
        child: Text(
          previewText,
          textAlign: TextAlign.start,
          style: const TextStyle(color: Colors.white70, fontSize: 18),
          softWrap: true,
        ));

    Widget errorWidget = errorIcon != null ? errorIcon! : Container();

    List<Widget> rowChildren;
    if (icon == null) {
      rowChildren = [
        Container(width: 10),
        valueWidget,
        previewWidget,
        errorWidget,
        Container(width: 20)
      ];
    } else {
      var iconWidget = Container(
        height: 50,
        width: 50,
        margin: const EdgeInsets.fromLTRB(20, 0, 20, 0),
        decoration: BoxDecoration(
          color: const Color.fromARGB(255, 122, 122, 122),
          borderRadius: const BorderRadius.all(Radius.circular(8)),
          border: Border.all(
            color: const Color.fromARGB(255, 122, 122, 122),
            width: 1,
          ),
        ),
        child: icon,
      );
      rowChildren = [
        iconWidget,
        valueWidget,
        Container(width: 5),
        previewWidget,
        errorWidget,
        Container(width: 20)
      ];
    }

    TextButton button = TextButton(
      onPressed: _onPressed,
      child: Container(
          decoration: BoxDecoration(
            color: color,
            borderRadius: const BorderRadius.all(Radius.circular(8)),
            border: Border.all(
              color: borderColor,
              width: 1,
            ),
          ),
          margin: const EdgeInsets.fromLTRB(0, 0, 0, 0),
          padding: const EdgeInsets.all(10),
          alignment: Alignment.center,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: rowChildren,
          )),
    );
    return button;
  }

  Widget buildNormal(BuildContext context) {
    String? nPreviewText = _previewText;
    String previewText = nPreviewText ?? '';

    var valueWidget = Expanded(
        child: Text(
      _label,
      textAlign: TextAlign.start,
      style: const TextStyle(color: Colors.white, fontSize: 18),
      softWrap: true,
    ));

    var previewWidget = Expanded(
        child: Text(
      previewText,
      textAlign: TextAlign.start,
      style: const TextStyle(color: Colors.white70, fontSize: 18),
      softWrap: true,
    ));

    Widget errorWidget = errorIcon != null ? errorIcon! : Container();

    List<Widget> rowChildren;
    if (icon == null) {
      rowChildren = [
        Container(width: 30),
        valueWidget,
        Container(width: 5),
        previewWidget,
        errorWidget,
        Container(width: 30)
      ];
    } else {
      var iconWidget = Container(
        height: 50,
        width: 50,
        margin: const EdgeInsets.fromLTRB(20, 0, 20, 0),
        decoration: BoxDecoration(
          color: const Color.fromARGB(255, 122, 122, 122),
          borderRadius: const BorderRadius.all(Radius.circular(8)),
          border: Border.all(
            color: const Color.fromARGB(255, 122, 122, 122),
            width: 1,
          ),
        ),
        child: icon,
      );
      rowChildren = [
        iconWidget,
        valueWidget,
        Container(width: 5),
        previewWidget,
        errorWidget,
        Container(width: 20)
      ];
    }
    TextButton button = TextButton(
      onPressed: _onPressed,
      child: Container(
          constraints:
              BoxConstraints(minHeight: AppStyle.global.expandButtonSize),
          decoration: BoxDecoration(
            color: color,
            borderRadius: const BorderRadius.all(Radius.circular(8)),
            border: Border.all(
              color: borderColor,
              width: 1,
            ),
          ),
          margin: const EdgeInsets.fromLTRB(0, 0, 0, 0),
          alignment: Alignment.center,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: rowChildren,
          )),
    );
    return button;
  }
}
