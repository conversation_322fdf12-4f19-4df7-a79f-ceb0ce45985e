//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Asset_Walkdown_Details_F
{
  public class SectionManufacturer : DataModelItem {

    public override String DisplayName { 
      get {
        return "Manufacturer";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public StringAttribute attributeName;
    public DateAttribute attributeDate;
    public StringAttribute attributeSerial_Number;
    public StringAttribute attributeNational_Board_Number;
    public StringAttribute attributeRT_Number;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionManufacturer";

    public SectionManufacturer(DataModelItem parent) : base(parent)
    {
            
        attributeName = new StringAttribute(this, displayName: "Name", databaseName: "510AW_Q126"); 
     
        attributeDate = new DateAttribute(this, displayName: "Date", databaseName: "510AW_Q127", areCommentsRequired: false); 
     
        attributeSerial_Number = new StringAttribute(this, displayName: "Serial Number", databaseName: "510AW_Q128"); 
     
        attributeNational_Board_Number = new StringAttribute(this, displayName: "National Board Number", databaseName: "510AW_Q129"); 
     
        attributeRT_Number = new StringAttribute(this, displayName: "RT Number", databaseName: "510AW_Q130"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeName,
           attributeDate,
           attributeSerial_Number,
           attributeNational_Board_Number,
           attributeRT_Number,
        };
    }
  }
}
