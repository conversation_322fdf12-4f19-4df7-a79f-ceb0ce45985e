//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class Section570EXTCKLSTSEC001 extends DataModelSection {
  @override
  String getDisplayName() => "GENERAL INFORMATION";
  Section570EXTCKLSTSEC001(DataModelItem? parent)
      : super(parent: parent, sectionName: "GENERAL INFORMATION");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeIs_the_asset_appropriately_prepared_for_inspection =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Is the asset appropriately prepared for inspection:",
          databaseName: "570_EXT_CKLST_SEC001_Q001",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC001Q002 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Was design data, previous inspection / examination reporting and or relevant  MOC / information pertaining to abnormal operating conditions made available prior to inspection:",
          databaseName: "570_EXT_CKLST_SEC001_Q002",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC001Q003 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Was the visual inspection conducted within a distance of 6”-24” from the piping:",
          databaseName: "570_EXT_CKLST_SEC001_Q003",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC001Q004 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Was the visual inspection conducted within an angle of not less than 30 degrees with the piping:",
          databaseName: "570_EXT_CKLST_SEC001_Q004",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute570EXTCKLSTSEC001Q005 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "During the visual inspection was a minimum light intensity of at least 100 foot-candles present:",
          databaseName: "570_EXT_CKLST_SEC001_Q005",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeIs_the_asset_appropriately_prepared_for_inspection,
      attribute570EXTCKLSTSEC001Q002,
      attribute570EXTCKLSTSEC001Q003,
      attribute570EXTCKLSTSEC001Q004,
      attribute570EXTCKLSTSEC001Q005,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section570EXTCKLSTSEC001";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
