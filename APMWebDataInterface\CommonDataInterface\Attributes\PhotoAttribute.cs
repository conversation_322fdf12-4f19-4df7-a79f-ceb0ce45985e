﻿using System;
using System.Linq;

namespace CommonDataInterface.Attributes
{
  public class PhotoAttribute : AttributeBase {

    public override bool IsValueEqualTo(String other)
    {
      throw new Exception("IsValueEqualTo is not valid for PhotoAttribute, photo attribute does not have a direct value,  instead it only has the comment and photo properties every attribute contains");
    }

    public override void SetGenericValueTo(String other)
    {
      throw new Exception("SetGenericValueTo is not valid for PhotoAttribute, photo attribute does not have a direct value,  instead it only has the comment and photo properties every attribute contains");
    }


    public override String AttributeType => "Photo";
    
    public override String GetPreviewText() {
      var photoCount = this.Photos.Length;
      return "Photos " + photoCount;
    }
    
    public PhotoAttribute(DataModelItem parent, String displayName, bool areCommentsRequired = false, bool isQueryable = false, String databaseName = null)
      : base(parent, displayName, databaseName, areCommentsRequired)
    {

    }
    
    public override DataModelItem[] GetChildren()
    {
      return new DataModelItem[]{  GetPhotoChangeLog(), GetCommentChangeLog() };
    }
  }
}