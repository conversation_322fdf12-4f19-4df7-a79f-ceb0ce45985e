//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';

// ignore: camel_case_types
class SectionHeads extends DataModelCollectionItem {
  @override
  String getDisplayName() => "Heads";

  SectionHeads(String id, DataModelItem parent) : super(id, parent);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeNumber = StringAttribute(
      parent: this,
      displayName: "Number",
      databaseName: "510AW_Q451",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late PhotoAttribute attributePhotos = PhotoAttribute(
      parent: this,
      displayName: "Photos",
      databaseName: "510AW_Q459",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeLocation = PredefinedValueAttribute(
      parent: this,
      hasOtherOption: false,
      displayName: "Location",
      databaseName: "510AW_Q452",
      availableOptions: [
        PredefinedValueOption("Top", null, isCommentRequired: false),
        PredefinedValueOption("Bottom", null, isCommentRequired: false),
        PredefinedValueOption("N", null, isCommentRequired: false),
        PredefinedValueOption("S", null, isCommentRequired: false),
        PredefinedValueOption("E", null, isCommentRequired: false),
        PredefinedValueOption("W", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeGeometry = PredefinedValueAttribute(
      parent: this,
      hasOtherOption: false,
      displayName: "Geometry",
      databaseName: "510AW_Q453",
      availableOptions: [
        PredefinedValueOption("2:1 Ellipsoidal", null,
            isCommentRequired: false),
        PredefinedValueOption("Conical", null, isCommentRequired: false),
        PredefinedValueOption("Cylindrical", null, isCommentRequired: false),
        PredefinedValueOption("Ellipsoidal", null, isCommentRequired: false),
        PredefinedValueOption("Flat Unstayed Circular", null,
            isCommentRequired: false),
        PredefinedValueOption("Spherical", null, isCommentRequired: false),
        PredefinedValueOption("Torispherical", null, isCommentRequired: false),
        PredefinedValueOption("Torispherical (L/r=16 2/3)", null,
            isCommentRequired: false),
        PredefinedValueOption("Other", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeMaterial_Spec_and_Grade = StringAttribute(
      parent: this,
      displayName: "Material Spec and Grade",
      databaseName: "510AW_Q454",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeAllowable_Stress_at_Temperature =
      DoubleAttribute(
    parent: this,
    displayName: "Allowable Stress at Temperature",
    databaseName: "510AW_Q455",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "psi",
  );

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeNominal_Thickness = DoubleAttribute(
    parent: this,
    displayName: "Nominal Thickness",
    databaseName: "510AW_Q456",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "in",
  );

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeCorrosion_Allowance = DoubleAttribute(
    parent: this,
    displayName: "Corrosion Allowance",
    databaseName: "510AW_Q457",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "in",
  );

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeJoint_Efficiency = DoubleAttribute(
    parent: this,
    displayName: "Joint Efficiency",
    databaseName: "510AW_Q458",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "",
  );

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeNumber,
      attributePhotos,
      attributeLocation,
      attributeGeometry,
      attributeMaterial_Spec_and_Grade,
      attributeAllowable_Stress_at_Temperature,
      attributeNominal_Thickness,
      attributeCorrosion_Allowance,
      attributeJoint_Efficiency,
    ]);
    return children;
  }
}
