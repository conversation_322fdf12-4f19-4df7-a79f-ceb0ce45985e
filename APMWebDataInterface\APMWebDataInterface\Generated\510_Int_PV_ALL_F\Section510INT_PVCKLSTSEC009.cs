//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Int_PV_ALL_F
{
  public class Section510INT_PVCKLSTSEC009 : DataModelItem {

    public override String DisplayName { 
      get {
        return "INTERNAL TRAYS AND COMPONENTS";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC009Q001;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC009Q002;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC009Q003;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC009Q004;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC009Q005;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC009Q006;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC009Q007;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC009Q008;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC009Q009;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510INT_PVCKLSTSEC009";

    public Section510INT_PVCKLSTSEC009(DataModelItem parent) : base(parent)
    {
            
        attribute510INT_PVCKLSTSEC009Q001 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Does the vessel have internal components: (I.e. trays, downcomers, weirs etc.)(Note: the number and type of trays (Sieve trays, cascade trays, valve trays, etc.) shall be recorded. Tray numbering sequence will be from top of vessel down.", databaseName: "510_INT-PV_CKLST_SEC009_Q001"); 
     
        attribute510INT_PVCKLSTSEC009Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are tray decks in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC009_Q002"); 
     
        attribute510INT_PVCKLSTSEC009Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are internal manways and clips in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC009_Q003"); 
     
        attribute510INT_PVCKLSTSEC009Q004 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are tray perforations in acceptable condition for continued service: (Note: the type of tray perforations (Floating valves, fixed valves, bubble caps, extruded valves, etc. shall be recorded) (Perforation numbering sequence will correlate with tray numbering)", databaseName: "510_INT-PV_CKLST_SEC009_Q004"); 
     
        attribute510INT_PVCKLSTSEC009Q005 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are downcomers and / or weirs in acceptable condition for continued service:  (Downcomers and/or weir numbering sequence will correlate with tray numbering)", databaseName: "510_INT-PV_CKLST_SEC009_Q005"); 
     
        attribute510INT_PVCKLSTSEC009Q006 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are miscellaneous hardware associated with trays, perforations, weirs, downcomers, etc. in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC009_Q006"); 
     
        attribute510INT_PVCKLSTSEC009Q007 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are perforations contained within the internal piping / distribution system obstruction free and appropriately shaped: (I.e. round or square cut)", databaseName: "510_INT-PV_CKLST_SEC009_Q007"); 
     
        attribute510INT_PVCKLSTSEC009Q008 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are accessible tray support ring attachment welds in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC009_Q008"); 
     
        attribute510INT_PVCKLSTSEC009Q009 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the support plate clamping system in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC009_Q009"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute510INT_PVCKLSTSEC009Q001,
           attribute510INT_PVCKLSTSEC009Q002,
           attribute510INT_PVCKLSTSEC009Q003,
           attribute510INT_PVCKLSTSEC009Q004,
           attribute510INT_PVCKLSTSEC009Q005,
           attribute510INT_PVCKLSTSEC009Q006,
           attribute510INT_PVCKLSTSEC009Q007,
           attribute510INT_PVCKLSTSEC009Q008,
           attribute510INT_PVCKLSTSEC009Q009,
        };
    }
  }
}
