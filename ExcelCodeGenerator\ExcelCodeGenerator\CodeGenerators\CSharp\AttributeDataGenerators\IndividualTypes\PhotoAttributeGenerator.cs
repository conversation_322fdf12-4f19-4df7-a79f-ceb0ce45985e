﻿using CheckListGen;

namespace ExcelCodeGenerator.CodeGenerators.CSharp
{
    public class PhotoAttributeGenerator : IAttributeGenerator
    {
        public string BuildInitialization(Question question)
        {
            return
                @"
        " + question.DartVariableName + @" = new PhotoAttribute(this, displayName: """ + question.DisplayText +
                @""", databaseName: """ + question.DataName + @"""); ";
        }

        public string BuildDeclaration(Question question)
        {
            return
                @"public PhotoAttribute " + question.DartVariableName + ";";
        }
    }
}