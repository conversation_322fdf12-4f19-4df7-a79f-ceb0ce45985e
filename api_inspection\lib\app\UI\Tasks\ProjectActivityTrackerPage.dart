import 'package:api_inspection/generic/AppRoot.dart';
import 'package:api_inspection/generic/AppStyle.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/activity.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/UIControls/SideSlideoutControl.dart';
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'package:uuid/uuid.dart';

import 'ProjectActivityItemPage.dart';

class ProjectActivityTrackerPage extends StatefulWidget {
  final DataModelCollection<ProjectActivity> activities;

  const ProjectActivityTrackerPage(this.activities, {Key? key})
      : super(key: key);

  @override
  _ProjectActivityTrackerPageState createState() =>
      _ProjectActivityTrackerPageState();
}

class _ProjectActivityTrackerPageState
    extends State<ProjectActivityTrackerPage> {
  void addNewActivity() {
    var activity = ProjectActivity(widget.activities, const Uuid().v4());
    var batch = FirebaseFirestore.instance.batch();
    activity.user.setValue(AppRoot.global().currentUser?.email, batch);
    widget.activities.addItem(activity);
    widget.activities.saveItem(batch);
    batch.commit();
    Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => ProjectActivityItemPage(activity)))
        .then((value) => {setState(() {})});
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> activityButtons = [];
    for (var item in widget.activities.getEntries()) {
      var itemUser = item.user.getValue();
      if (itemUser != AppRoot.global().currentUser?.email) continue;
      Widget interior = Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            margin: const EdgeInsets.all(20),
            child: Text(
              item.date.getPreviewText(),
              style: const TextStyle(color: Colors.white, fontSize: 18),
            ),
          ),
          Container(
              margin: const EdgeInsets.fromLTRB(0, 0, 20, 0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text("WO: " + item.workOrderNumber.getPreviewText(),
                      style:
                          const TextStyle(color: Colors.white, fontSize: 18)),
                  Container(height: 5),
                  Text(item.getTotalHours().toString() + " hours",
                      style:
                          const TextStyle(color: Colors.white, fontSize: 18)),
                ],
              ))
        ],
      );

      leftSliderWidgetBuilder(context) {
        return Container(
            margin: const EdgeInsets.fromLTRB(10, 0, 0, 0),
            height: 80,
            width: 110,
            child: ElevatedButton(
                onPressed: () {
                  setState(() {
                    widget.activities.removeItem(item);
                  });
                },
                child: const Text(
                  "Remove",
                  style: TextStyle(fontSize: 18),
                ),
                style: ButtonStyle(
                  backgroundColor: MaterialStateProperty.resolveWith<Color>(
                      (Set<MaterialState> states) {
                    return Colors.red[900]!;
                  }),
                )));
      }

      activityButtons.add(SideSlideoutControl(
          leftSliderWidget: leftSliderWidgetBuilder,
          child: AttributePadding.WithStdPadding(
            TeamToggleButton(
                child: interior,
                onPressed: () {
                  Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) =>
                                  ProjectActivityItemPage(item)))
                      .then((value) => {setState(() {})});
                }),
          )));
    }

    return Scaffold(
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        appBar: AppBar(
          title: Text(
            "Project Activity Tracker",
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Expanded(child: ListView(children: activityButtons)),
              AttributePadding.WithStdPadding(Container(
                margin: const EdgeInsets.fromLTRB(0, 0, 0, 10),
                child: ElevatedButton(
                    child: const Text(
                      'Add Day',
                      style: TextStyle(fontSize: 18),
                    ),
                    onPressed: addNewActivity),
              )),
            ],
          ),
        ));
  }
}
