import 'dart:developer';

import 'package:api_inspection/generic/DataModelCommon/Attributes/ChangeLog/ChangeLogEntry.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:collection/collection.dart';
import 'package:api_inspection/generic/DataModelCommon/DatabaseInterfaces/DatabaseHelperManager.dart';
import '../../DataModelItem.dart';
import 'package:darq/darq.dart';

typedef ValueConversion<ValueType> = ValueType? Function(dynamic);

class ChangeLogItem {}

class ChangeLogBase<ValueType, ChangeLogType> extends DataModelItem {
  List<ChangeLogType> entries;
  List<ChangeLogEntry<ValueType>> _entriesForUpdate = [];
  final DataModelItem _parent;
  final String _name;
  ValueConversion<ValueType>? _conversion;

  ChangeLogBase(this._name, this._parent, this.entries) : super(_parent);

  void setConversionMethod(ValueConversion<ValueType>? conversionMethod) {
    _conversion = conversionMethod;
  }

  @override
  List<DataModelItem> getChildren() {
    return List.empty();
  }

  @override
  String getDisplayName() => _name;

  @override
  String getDBName() {
    return _name;
  }

  @override
  void saveDirectItems(WriteBatch batch) async {
    var helper = DatabaseHelperManager.global();
    var path = getDBPath();
    for (var entry in _entriesForUpdate) {
      helper.addToCollection(path, entry, batch);
    }

    _entriesForUpdate.clear();
  }

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}

class ChangeLogSingleItem<ValueType>
    extends ChangeLogBase<ValueType, ChangeLogEntry<ValueType>> {
  ChangeLogSingleItem(String name, DataModelItem parent,
      List<ChangeLogEntry<ValueType>> entries)
      : super(name, parent, entries);

  @override
  void updateFromMap(Map? map) {
    if (map == null) return;

    for (MapEntry entry in map.entries) {
      Map? subMap = entry.value as Map?;
      if (subMap == null) continue;

      if (entries.firstWhereOrNull((element) => element.key == entry.key) !=
          null) {
        continue;
      }

      String? reason;
      String? userName;
      int? timeChanged;
      ValueType? value;

      for (MapEntry entry in subMap.entries) {
        try {
          if (entry.key == "U") {
            userName = entry.value as String?;
          }
          if (entry.key == "T") {
            timeChanged = entry.value as int?;
          }
          if (entry.key == "V") {
            ValueConversion<ValueType>? conversion = _conversion;
            if (conversion != null) {
              value = conversion.call(entry.value);
            } else {
              value = entry.value as ValueType?;
            }
          }
          if (entry.key == "R") {
            reason = entry.value as String?;
          }
        } catch (ex) {
          log(ex.toString(),
              name: 'ChangeLogSingleItem:updateFromMap', time: DateTime.now());
        }
      }

      if (userName == null || timeChanged == null) continue;
      String key = entry.key;

      ChangeLogEntry<ValueType> entry1 = ChangeLogEntry<ValueType>(key, value,
          DateTime.fromMillisecondsSinceEpoch(timeChanged), userName,
          reason: reason);
      entries.add(entry1);
    }
    entries.sortBy((element) => element.timeChanged);

    _parent.notifyListeners();
  }

  @override
  bool updateFromOther(DataModelItem other, Map<String, dynamic> updates) {
    if (other is! ChangeLogSingleItem<ValueType>) {
      throw "Unexpected type encountered in updateFromOther";
    }
    bool updated = false;
    for (var entry in other.entries) {
      if (!entries.any((element) => element.key == entry.key)) {
        entries.add(entry);
        updates[entry.key] = {
          "T": entry.timeChanged.millisecondsSinceEpoch,
          "V": entry.value,
          "U": entry.userName,
          "R": entry.reason
        };
        updated = true;
      }
    }
    return updated || super.updateFromOther(other, updates);
  }

  Future<void> addNewItem(ChangeLogEntry<ValueType> entry) async {
    entries.add(entry);
    _entriesForUpdate.add(entry);
    onChildrenChanged(this);
  }
}

class ListChangeLog<ValueType>
    extends ChangeLogBase<ValueType, ListChangeLogEntry<ValueType>> {
  @override
  bool updateFromOther(DataModelItem other, Map<String, dynamic> updates) {
    if (other is! ListChangeLog<ValueType>) {
      throw "Unexpected type encountered in updateFromOther";
    }
    bool updated = false;
    for (var entry in other.entries) {
      if (!entries.any((element) => element.key == entry.key)) {
        entries.add(entry);
        updates[entry.key] = {
          "T": entry.timeChanged.millisecondsSinceEpoch,
          "V": entry.value,
          "U": entry.userName,
          "A": entry.action
        };
        updated = true;
      }
    }
    return updated || super.updateFromOther(other, updates);
  }

  List<ValueType> getCurrentEntries() {
    var sorted = entries.orderBy((element) => element.timeChanged).toList();
    var allEntries = sorted.select((a, i) => a.value).distinct().toList();
    var allCopy = allEntries.toList();
    for (var entry in allCopy) {
      var lastMatch = sorted.lastWhere((element) => element.value == entry);
      if (lastMatch.action == "Removed") {
        allEntries.remove(entry);
      }
    }

    return allEntries.nonNull().toList();
  }

  ListChangeLog(String name, DataModelItem parent,
      List<ListChangeLogEntry<ValueType>> entries)
      : super(name, parent, entries);

  @override
  void updateFromMap(Map? map) {
    entries = [];

    if (map == null) return;

    for (MapEntry entry in map.entries) {
      Map? subMap = entry.value as Map?;
      if (subMap == null) continue;

      String? userName;
      int? timeChanged;
      ValueType? value;
      String? action;

      for (MapEntry entry in subMap.entries) {
        try {
          if (entry.key == "U") {
            userName = entry.value as String?;
          }
          if (entry.key == "T") {
            timeChanged = entry.value as int?;
          }
          if (entry.key == "V") {
            ValueConversion<ValueType>? conversion = _conversion;
            if (conversion != null) {
              value = conversion.call(entry.value);
            } else {
              value = entry.value as ValueType?;
            }
          }
          if (entry.key == "A") {
            action = entry.value as String?;
          }
        } catch (ex) {
          log(ex.toString(), name: 'updateFromMap');
        }
      }

      if (userName == null || timeChanged == null || action == null) continue;
      String key = entry.key;

      ListChangeLogEntry<ValueType> entry1 = ListChangeLogEntry<ValueType>(
          key,
          value,
          DateTime.fromMillisecondsSinceEpoch(timeChanged),
          userName,
          action);
      entries.add(entry1);
    }
    entries.sortBy((element) => element.timeChanged);

    _parent.notifyListeners();
  }

  Future<void> addNewItem(ListChangeLogEntry<ValueType> entry) async {
    entries.add(entry);
    _entriesForUpdate.add(entry);
    onChildrenChanged(this);

    _parent.notifyListeners();
  }

  @override
  void saveDirectItems(WriteBatch batch) async {
    var helper = DatabaseHelperManager.global();
    var path = getDBPath();
    for (var entry in _entriesForUpdate) {
      helper.addToListCollection(path, entry as ListChangeLogEntry, batch);
    }

    _entriesForUpdate.clear();
  }
}
