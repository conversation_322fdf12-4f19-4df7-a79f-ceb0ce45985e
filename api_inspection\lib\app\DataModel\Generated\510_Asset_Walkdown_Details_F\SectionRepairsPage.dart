//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/UI/CollectionControl.dart';
import 'package:uuid/uuid.dart';
import 'SectionRepairs.dart';

// ignore: camel_case_types
// ignore: camel_case_types
class SectionRepairsPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final DataModelCollection<SectionRepairs> sectionRepairs;
  const SectionRepairsPage(this.sectionRepairs, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionRepairsPageState();
  }
}

class _SectionRepairsPageState extends State<SectionRepairsPage> {
  Widget _cardBuilder(BuildContext context, int number, SectionRepairs item) {
    return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(number.toString(),
              style: const TextStyle(color: Colors.white, fontSize: 18)),
        ]);
  }

  Widget _editPageBuilder(BuildContext context, SectionRepairs item) {
    return SectionScaffold(
        section: item,
        title: "Repairs",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(item
                      .attributeDate_Repaired_or_Altered
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item
                      .attributeRepairAlteration_organization
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item
                      .attributePurpose_of_repairalteration
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item
                      .attributeIs_NB_Form_R_1_Available
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item
                      .attributeIs_NB_Form_R_2_Available
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item
                      .attributeNB_R_Certificate_Number
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }

  SectionRepairs _createNewItem() {
    String id = const Uuid().v4();
    var item = SectionRepairs(id, widget.sectionRepairs);

    return item;
  }

  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionRepairs,
        title: "Repairs",
        childBuilder: (editableController) {
          return Center(
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: <Widget>[
                Expanded(
                    child: CollectionControl<SectionRepairs>(
                  cardTitle: "Repairs",
                  collection: widget.sectionRepairs,
                  cardBuilder: _cardBuilder,
                  createNewItem: _createNewItem,
                  editPageBuilder: _editPageBuilder,
                ))
              ]));
        });
  }
}
