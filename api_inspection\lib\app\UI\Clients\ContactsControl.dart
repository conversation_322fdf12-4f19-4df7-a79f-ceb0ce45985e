import 'package:api_inspection/app/batch_helper.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/project.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'ContactEditPage.dart';
import 'ContactViewPage.dart';
import 'package:api_inspection/generic/UIControls/SideSlideoutControl.dart';
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
import 'package:uuid/uuid.dart';

class ContactsControl extends StatefulWidget {
  final DataModelCollection<Contact> contacts;
  const ContactsControl(this.contacts, {Key? key}) : super(key: key);

  @override
  _ContactsControlState createState() => _ContactsControlState();
}

class _ContactsControlState extends State<ContactsControl> {
  late SlideOutController controller = SlideOutController();

  @override
  Widget build(BuildContext context) {
    List<Widget> widgets = [];
    for (var contact in widget.contacts.getEntries()) {
      leftSliderWidgetBuilder(context) {
        return SizedBox(
            height: 110,
            width: 110,
            child: ElevatedButton(
                onPressed: () {
                  widget.contacts.removeItem(contact);
                },
                child: const Text(
                  "Remove",
                  style: TextStyle(fontSize: 18),
                ),
                style: ButtonStyle(
                  backgroundColor: MaterialStateProperty.resolveWith<Color>(
                      (Set<MaterialState> states) {
                    return Colors.red[900]!;
                  }),
                )));
      }

      rightSliderWidgetBuilder(context) {
        return SizedBox(
            height: 110,
            width: 110,
            child: ElevatedButton(
                onPressed: () {
                  controller.closeSlideOuts();
                  Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => ContactEditPage(contact)))
                      .then((value) => setState(() {}));
                },
                child: const Text(
                  "Edit",
                  style: TextStyle(fontSize: 18),
                ),
                style: ButtonStyle(
                  backgroundColor: MaterialStateProperty.resolveWith<Color>(
                      (Set<MaterialState> states) {
                    return Colors.green[800]!;
                  }),
                )));
      }

      widgets.add(AttributePadding.WithStdPadding(SideSlideoutControl(
          controller: controller,
          leftSliderWidget: leftSliderWidgetBuilder,
          rightSliderWidget: rightSliderWidgetBuilder,
          child: TeamToggleButton(
              borderColor: Colors.lime[900]!,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                      margin: const EdgeInsets.fromLTRB(20, 0, 10, 0),
                      child: const Text("Contact",
                          style: TextStyle(color: Colors.white, fontSize: 18))),
                  Expanded(
                      child: Center(
                          child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                        Text(contact.name.getValue() ?? "Name not set",
                            style: const TextStyle(
                                color: Colors.white, fontSize: 18)),
                        Text(contact.title.getValue() ?? "Title not set",
                            style: const TextStyle(
                                color: Colors.white70, fontSize: 16)),
                        const SizedBox(width: 180, height: 10),
                        Text(
                            contact.phoneNumber.getValue() ??
                                "Phone number not set",
                            style: const TextStyle(
                                color: Colors.white70, fontSize: 16)),
                        Text(contact.email.getValue() ?? "Email not set",
                            style: const TextStyle(
                                color: Colors.white70, fontSize: 16)),
                      ])))
                ],
              ),
              onPressed: () {
                Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => ContactViewPage(contact)))
                    .then((value) => setState(() {}));
              },
              height: 110))));
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(child: ListView(children: widgets)),
        Align(
            alignment: Alignment.centerRight,
            child: Container(
                margin: const EdgeInsets.all(10),
                height: 50,
                child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        String id = const Uuid().v4();
                        var contact = Contact(id, widget.contacts);
                        var batch = FirebaseFirestore.instance.batch();
                        contact.name.setValue("New Contact", batch);
                        widget.contacts.addItem(contact);
                        widget.contacts.saveItem(batch);
                        batch.commit();
                        Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) =>
                                        ContactEditPage(contact)))
                            .then((value) => setState(() {}));
                      });
                    },
                    child: const Text("Create New Contact"))))
      ],
    );
  }
}
