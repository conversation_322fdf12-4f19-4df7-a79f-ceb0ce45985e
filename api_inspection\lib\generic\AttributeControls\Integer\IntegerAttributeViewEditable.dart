import 'package:api_inspection/app/batch_helper.dart';
import 'package:api_inspection/generic/UIControls/listener.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
import 'package:api_inspection/generic/UIControls/NumericTextField.dart';

class IntegerAttributeViewEditable extends StatefulWidget {
  final IntegerAttribute _attribute;
  final ListenerWrapper updateListener;

  const IntegerAttributeViewEditable(this._attribute, this.updateListener,
      {Key? key})
      : super(key: key);

  @override
  _IntegerAttributeViewEditableState createState() =>
      _IntegerAttributeViewEditableState();
}

class _IntegerAttributeViewEditableState
    extends State<IntegerAttributeViewEditable> {
  void updateAttributeValue() {
    TextEditingController? controller = _controller;

    if (controller != null) {
      int? parsedValue = int.tryParse(controller.text);
      widget._attribute.setValue(parsedValue);
      BatchHelper.saveAndCommit(widget._attribute);
    }
  }

  TextEditingController? _controller;

  bool initialized = false;
  void initialize() {
    if (initialized) return;
    initialized = true;
    IntegerAttribute attribute = widget._attribute;
    attribute.addListener(onAttributeChanged);
  }

  bool areValuesEqual(String? str, int? value) {
    if (str == null && value == null) return true;
    if (str != null && str.isEmpty && value == null) return true;
    if (str == null || value == null) return false;

    int? strAsValue = int.tryParse(str);
    return strAsValue == value;
  }

  String intToString(int? value) {
    if (value == null) {
      return "";
    }
    return value.toString();
  }

  void onAttributeChanged() {
    setState(() {
      TextEditingController? controller = _controller;
      int? attributeValue = widget._attribute.getValue();
      if (controller != null &&
          !areValuesEqual(controller.text, attributeValue)) {
        controller.text =
            attributeValue == null ? "" : intToString(attributeValue);
      }
    });
  }

  @override
  void dispose() {
    widget.updateListener.removeListener(updateAttributeValue);
    IntegerAttribute attribute = widget._attribute;
    attribute.removeListener(onAttributeChanged);
    _controller?.dispose();
    super.dispose();
  }

  @override
  void initState() {
    widget.updateListener.addListener(updateAttributeValue);
    super.initState();
  }

  @override
  void didUpdateWidget(covariant IntegerAttributeViewEditable oldWidget) {
    oldWidget.updateListener.removeListener(updateAttributeValue);
    widget.updateListener.addListener(updateAttributeValue);
    super.didUpdateWidget(oldWidget);
  }

  Widget buildAttributeField() {
    var textField = NumericTextField(
        _controller!, widget._attribute.allowNegatives, false, (String? value) {
      updateAttributeValue();
    });
    if (widget._attribute.displayUnit != null) {
      return Row(children: [
        Expanded(child: textField),
        Container(
            margin: const EdgeInsets.fromLTRB(10, 0, 0, 0),
            child: Text(
              widget._attribute.displayUnit.toString(),
              style: const TextStyle(color: Colors.white, fontSize: 18),
              textAlign: TextAlign.start,
            ))
      ]);
    } else {
      return textField;
    }
  }

  @override
  Widget build(BuildContext context) {
    initialize();

    _controller ??=
        TextEditingController(text: intToString(widget._attribute.getValue()));

    return Container(
      margin: const EdgeInsets.fromLTRB(20, 10, 20, 10),
      child: Focus(
          onFocusChange: (hasFocus) {
            updateAttributeValue();
          },
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.stretch, children: [
            Text(
              widget._attribute.displayName,
              style: const TextStyle(color: Colors.white, fontSize: 16),
              textAlign: TextAlign.start,
            ),
            buildAttributeField()
          ])),
    );
  }
}
