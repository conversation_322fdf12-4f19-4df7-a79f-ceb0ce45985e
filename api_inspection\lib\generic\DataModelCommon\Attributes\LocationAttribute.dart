import 'package:api_inspection/generic/AttributeControls/AttributeStructure.dart';
import 'package:api_inspection/generic/AttributeControls/Location/LocationAttributeView.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/AttributeBase.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/ChangeLog/ChangeLogEntry.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';

class LocationAttribute extends SingleAttributeBase<String> {
  LocationAttribute(
      {required DataModelItem parent,
      required String displayName,
      Widget? iconWidget,
      bool areCommentsRequired = false,
      String? databaseName})
      : super(
            parent, displayName, iconWidget, areCommentsRequired, databaseName);

  @override
  String getPreviewText() {
    var value = getDisplayValue();
    return value ?? "";
  }

  bool areCurrentCoordinatesValid() {
    var lat = getLatValue();
    var long = getLongValue();
    if ((lat == null) ^ (long == null)) return false;
    if (lat != null && (lat < -90 || lat > 90)) return false;
    if (long != null && (long < -180 || long > 180)) return false;

    return true;
  }

  @override
  bool getIsInErrorState() {
    return super.getIsInErrorState() || !areCurrentCoordinatesValid();
  }

  void setValue(double? lat, double? long) {
    var currentLat = getLatValue();
    var currentLong = getLongValue();
    if (currentLat == lat && currentLong == long) {
      return;
    }

    ChangeLogEntry<String> entry;
    if (lat == null && long == null) {
      entry = ChangeLogEntry<String>.newlyCreated(null);
    } else {
      String latText = lat == null ? "" : lat.toString();
      String longText = long == null ? "" : long.toString();

      entry = ChangeLogEntry<String>.newlyCreated(latText + "," + longText);
    }

    valueChangeLog.addNewItem(entry);
    changedBySelfListeners.notifyListeners();
    notifyListeners();
  }

  @override
  Widget buildWidget(
      {IsEditableController? editingController,
      bool showPhotos = true,
      bool showComments = true}) {
    return LocationAttributeView(
      this,
      editingController: editingController,
      showPhotos: showPhotos,
      showComments: showComments,
    );
  }

  double? getLatValue() {
    try {
      if (valueChangeLog.entries.isEmpty) return null;
      String? currentValue = valueChangeLog.entries.last.value;
      if (currentValue != null) {
        final split = currentValue.split(",");
        return double.parse(split[0]);
      }
    } catch (e) {
      return null;
    }

    return null;
  }

  double? getLongValue() {
    try {
      if (valueChangeLog.entries.isEmpty) return null;
      String? currentValue = valueChangeLog.entries.last.value;
      if (currentValue != null && currentValue.contains(',')) {
        final split = currentValue.split(",");
        return double.parse(split[1]);
      }
    } catch (e) {
      return null;
    }

    return null;
  }

  @override
  bool hasData() {
    var lat = getLatValue();
    var long = getLongValue();
    if (lat == null || long == null) {
      return false;
    }
    return areCurrentCoordinatesValid();
  }

  String? getDisplayValue() {
    var lat = getLatValue();
    var long = getLongValue();
    if (valueChangeLog.entries.isEmpty || (lat == null && long == null)) {
      return "Not set";
    }

    var latStr = lat == null ? "" : lat.toStringAsFixed(4);
    var longStr = long == null ? "" : long.toStringAsFixed(4);
    if (!areCurrentCoordinatesValid()) {
      return "Invalid Coordinates";
    }
    return latStr + ", " + longStr;
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
