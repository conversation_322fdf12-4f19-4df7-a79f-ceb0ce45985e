//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class SectionDimensions extends DataModelSection {
  @override
  String getDisplayName() => "Dimensions";
  SectionDimensions(DataModelItem? parent)
      : super(parent: parent, sectionName: "Dimensions");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeDiameter_Measurement =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Diameter Measurement",
          databaseName: "510AW_Q351",
          availableOptions: [
        PredefinedValueOption("ID", null, isCommentRequired: false),
        PredefinedValueOption("OD", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeDiameter = DoubleAttribute(
    parent: this,
    displayName: "Diameter",
    databaseName: "510AW_Q352",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "in",
  );

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute
      attributeDoes_the_shell_have_multiple_diameters =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Does the shell have multiple diameters?",
          databaseName: "510AW_Q353",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: true),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeOverall_Length_or_Height = DoubleAttribute(
    parent: this,
    displayName: "Overall Length or Height",
    databaseName: "510AW_Q354",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "in",
  );

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute
      attributeAre_there_toriconical_transition_sections_in_the_shell =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Are there toriconical transition sections in the shell?",
          databaseName: "510AW_Q355",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeDiameter_Measurement,
      attributeDiameter,
      attributeDoes_the_shell_have_multiple_diameters,
      attributeOverall_Length_or_Height,
      attributeAre_there_toriconical_transition_sections_in_the_shell,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionDimensions";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
