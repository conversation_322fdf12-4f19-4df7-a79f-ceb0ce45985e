import 'package:flutter/material.dart';

class ListenerWrapper {
  final List<VoidCallback> _listeners =
      List<VoidCallback>.empty(growable: true);

  void dispose() {
    _listeners.clear();
  }

  void addListener(VoidCallback listener) {
    _listeners.add(listener);
  }

  void removeListener(VoidCallback listener) {
    _listeners.remove(listener);
  }

  void notifyListeners() {
    for (var element in _listeners) {
      element.call();
    }
  }
}
