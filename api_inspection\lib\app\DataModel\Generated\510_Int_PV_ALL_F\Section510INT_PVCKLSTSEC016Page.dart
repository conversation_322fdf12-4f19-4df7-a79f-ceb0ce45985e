//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'Section510INT_PVCKLSTSEC016.dart';

// ignore: camel_case_types
class Section510INT_PVCKLSTSEC016Page extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final Section510INT_PVCKLSTSEC016 section510INT_PVCKLSTSEC016;

  const Section510INT_PVCKLSTSEC016Page(this.section510INT_PVCKLSTSEC016,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _Section510INT_PVCKLSTSEC016PageState();
  }
}

class _Section510INT_PVCKLSTSEC016PageState
    extends State<Section510INT_PVCKLSTSEC016Page> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.section510INT_PVCKLSTSEC016,
        title: "MISCELLANEOUS",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC016
                      .attribute510INT_PVCKLSTSEC016Q001
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC016
                      .attribute510INT_PVCKLSTSEC016Q002
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC016
                      .attribute510INT_PVCKLSTSEC016Q003
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC016
                      .attribute510INT_PVCKLSTSEC016Q004
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC016
                      .attributeAre_threaded_connections_acceptably_engaged_and_leak_free
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC016
                      .attribute510INT_PVCKLSTSEC016Q006
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC016
                      .attributeAre_threaded_connections_acceptable_for_continued_service
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC016
                      .attribute510INT_PVCKLSTSEC016Q008
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
