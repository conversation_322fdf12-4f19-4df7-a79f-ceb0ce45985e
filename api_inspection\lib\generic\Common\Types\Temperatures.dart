import 'package:flutter/material.dart';

enum TemperatureUnits { Ke<PERSON>, Celsius, Fahrenheit }

extension TemperatureUnitsExt on TemperatureUnits {
  String abbreviation() {
    switch (this){
      case TemperatureUnits.Kelvin:
        return "K";
      case TemperatureUnits.Celsius:
        return "C";
      case TemperatureUnits.Fahrenheit:
        return "F";
    }
  }
}

@immutable
class Temperature {
  late final double _temperatureInKelvin;

  Temperature.fromUnit(double temp, TemperatureUnits unit) {
    switch (unit){
      case TemperatureUnits.Celsius:
        _temperatureInKelvin = _convertCelsiusToKelvin(temp);
        break;
      case TemperatureUnits.Kelvin:
        _temperatureInKelvin = temp;
        break;
      case TemperatureUnits.Fahrenheit:
        _temperatureInKelvin = _convertFahrenheitToKelvin(temp);
        break;
    }
  }

  Temperature.fromKelvin(double temp){
    _temperatureInKelvin = temp;
  }

  Temperature.fromCelsius(double temp){
    _temperatureInKelvin = _convertCelsiusToKelvin(temp);
  }

  Temperature.fromFahrenheit(double temp){
    _temperatureInKelvin = _convertFahrenheitToKelvin(temp);
  }


  double? getInUnit(TemperatureUnits unit){
    switch (unit){
      case TemperatureUnits.Celsius:
        return inCelsius;
      case TemperatureUnits.Kelvin:
        return inKelvin;
      case TemperatureUnits.Fahrenheit:
        return inFahrenheit;
    }
  }

  double? get inKelvin{
    return _temperatureInKelvin;
  }
  
  double? get inCelsius{
    return _convertKelvinToCelsius(_temperatureInKelvin);
  }

  double? get inFahrenheit{
    return _convertKelvinToFahrenheit(_temperatureInKelvin);
  }


  @override
  bool operator ==(Object other) =>
      other is Temperature &&
      other.runtimeType == runtimeType &&
      other._temperatureInKelvin == _temperatureInKelvin;

  @override
  int get hashCode => _temperatureInKelvin.hashCode;

  
  double _convertCelsiusToKelvin(double temp){
    return temp + 273.15;
  }
  double _convertFahrenheitToKelvin(double temp){
    return ((temp - 32) / 1.8) + 273.15;
  }
  double _convertKelvinToFahrenheit(double temp){
    return (temp - 273.15) * 1.8 + 32;
  }
  double _convertKelvinToCelsius(double temp){
    return temp - 273.15;
  }

}
