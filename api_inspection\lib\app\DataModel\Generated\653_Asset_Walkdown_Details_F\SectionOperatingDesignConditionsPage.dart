//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionOperatingDesignConditions.dart';
import 'SectionTankOutOfServiceRequirementsPage.dart';
import 'SectionRegulatoryRequirementsPage.dart';

// ignore: camel_case_types
class SectionOperatingDesignConditionsPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionOperatingDesignConditions sectionOperatingDesignConditions;

  const SectionOperatingDesignConditionsPage(
      this.sectionOperatingDesignConditions,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionOperatingDesignConditionsPageState();
  }
}

class _SectionOperatingDesignConditionsPageState
    extends State<SectionOperatingDesignConditionsPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionOperatingDesignConditions,
        title: "Operating/Design Conditions",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionOperatingDesignConditions.attributeCurrent_service
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionOperatingDesignConditions.attributeDesign_Temp
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionOperatingDesignConditions
                      .attributeCurrent_Operating_Temperature
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionOperatingDesignConditions
                      .attributeCurrent_Fill_level_if_available
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionOperatingDesignConditions
                      .attributeOperation_Status
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionOperatingDesignConditions
                      .attributeIs_the_tank_equipped_with_VRU
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionOperatingDesignConditions
                      .attributeTank_equipped_with_Leak_Detection
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionOperatingDesignConditions
                            .sectionTankOutOfServiceRequirements,
                        onPressed: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      SectionTankOutOfServiceRequirementsPage(widget
                                          .sectionOperatingDesignConditions
                                          .sectionTankOutOfServiceRequirements))).then(
                              (value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionOperatingDesignConditions
                            .sectionRegulatoryRequirements,
                        onPressed: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      SectionRegulatoryRequirementsPage(widget
                                          .sectionOperatingDesignConditions
                                          .sectionRegulatoryRequirements))).then(
                              (value) => setState(() {}));
                        }),
                  ),
                ]),
          );
        });
  }
}
