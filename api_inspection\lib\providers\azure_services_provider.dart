import 'package:flutter/material.dart';
import '../auth/azure_auth_service.dart';
import '../data/cosmos_db_service.dart';
import '../monitoring/azure_monitoring.dart';
import '../storage/azure_blob_service.dart';

/// A provider for all Azure services used in the app
/// This makes it easy to access services from any part of the app
class AzureServicesProvider extends InheritedWidget {
  final AzureAuthService authService;
  final CosmosDbService databaseService;
  final AzureBlobService storageService;
  final AzureMonitoring monitoringService;
  
  const AzureServicesProvider({
    Key? key,
    required Widget child,
    required this.authService,
    required this.databaseService,
    required this.storageService,
    required this.monitoringService,
  }) : super(key: key, child: child);
  
  static AzureServicesProvider of(BuildContext context) {
    final AzureServicesProvider? result = context.dependOnInheritedWidgetOfExactType<AzureServicesProvider>();
    assert(result != null, 'No AzureServicesProvider found in context');
    return result!;
  }
  
  @override
  bool updateShouldNotify(AzureServicesProvider oldWidget) {
    return authService != oldWidget.authService ||
           databaseService != oldWidget.databaseService ||
           storageService != oldWidget.storageService ||
           monitoringService != oldWidget.monitoringService;
  }
}
