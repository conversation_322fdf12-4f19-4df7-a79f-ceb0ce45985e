//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionLeak_Report_F
{
  public class SectionLeakReportPhotos : DataModelCollectionItem {

    public override String DisplayName { 
      get {
        return "Leak Report Photos";
      }
    }
    
    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public StringAttribute attributeDescription;
    public StringAttribute attributeComment;
    public LocationAttribute attributeArea_of_interest_GIS_position;
    public LocationAttribute attributeUpstream_tie_in_GIS_location;
    public LocationAttribute attributeDownstream_tie_in_GIS_location;
    public DoubleAttribute attributeUT_High_Measurement;
    public DoubleAttribute attributeUT_Low_measurement;

    #endregion [-- Attributes --]

    public SectionLeakReportPhotos(String id, DataModelItem parent) : base(id, parent)
    {
            
        attributeDescription = new StringAttribute(this, displayName: "Description", databaseName: "LR_Q210"); 
     
        attributeComment = new StringAttribute(this, displayName: "Comment", databaseName: "LR_Q211"); 
     
        attributeArea_of_interest_GIS_position = new LocationAttribute(this, displayName: "Area of interest GIS position", databaseName: "LR_Q212"); 
     
        attributeUpstream_tie_in_GIS_location = new LocationAttribute(this, displayName: "Upstream tie-in GIS location", databaseName: "LR_Q213"); 
     
        attributeDownstream_tie_in_GIS_location = new LocationAttribute(this, displayName: "Downstream tie-in GIS location", databaseName: "LR_Q214"); 
     
        attributeUT_High_Measurement = new DoubleAttribute(this, displayName: "UT High Measurement", databaseName: "LR_Q215", areCommentsRequired: false, displayUnit: "in", allowNegatives: true); 
     
        attributeUT_Low_measurement = new DoubleAttribute(this, displayName: "UT Low measurement", databaseName: "LR_Q216", areCommentsRequired: false, displayUnit: "in", allowNegatives: true); 
    }   
    
    public override DataModelItem[] GetChildren() {
      var parentChildren = base.GetChildren();

      return parentChildren.Concat(new DataModelItem[] { 
        attributeDescription,
           attributeComment,
           attributeArea_of_interest_GIS_position,
           attributeUpstream_tie_in_GIS_location,
           attributeDownstream_tie_in_GIS_location,
           attributeUT_High_Measurement,
           attributeUT_Low_measurement,
      }).ToArray();
    }
  }
}
