import 'dart:developer';

import 'package:shared_preferences/shared_preferences.dart';

class PendingUploads {
  static const String _key = 'pending-files';

  static Future<List<String>> getPendingFiles() async {
    var pref = await SharedPreferences.getInstance();
    var list = Set<String>.from(pref.getStringList(_key) ?? []).toList();
    log('Got ${list.length} pending files from local storage',
        name: 'getPendingFiles');
    return list;
  }

  static Future<bool> addPendingFile(String file) async {
    var pref = await SharedPreferences.getInstance();
    List<String> value;
    var existingList = pref.getStringList(_key);
    if (existingList != null) {
      var set = Set<String>.from(existingList);
      set.add(file);
      value = set.toList();
    } else {
      value = [file];
    }
    return await pref.setStringList(_key, value);
  }

  static Future<bool> removePendingFile(String file) async {
    List<String> value;
    var pref = await SharedPreferences.getInstance();
    var existingList = pref.getStringList(_key);
    if (existingList != null) {
      var set = Set<String>.from(existingList);
      set.remove(file);
      value = set.toList();
    } else {
      value = [];
    }
    return await pref.setStringList(_key, value);
  }
}
