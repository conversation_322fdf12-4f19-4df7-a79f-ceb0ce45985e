//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class Section510EXT_PVCKLSTSEC013 extends DataModelSection {
  @override
  String getDisplayName() => "SHELL AND HEADS";
  Section510EXT_PVCKLSTSEC013(DataModelItem? parent)
      : super(parent: parent, sectionName: "SHELL AND HEADS");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q001 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Were any corrosion, erosion, or pitting cells noted on the shell or head surfaces:  (The dimensions and locations of any damage shall be recorded)",
          databaseName: "510_EXT-PV_CKLST_SEC013_Q001",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Corrosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Erosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Pitting", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q002 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Was any mechanical damage or cracking noted on the shell or head surfaces:  (The dimensions and location of any damage shall be recorded)",
          databaseName: "510_EXT-PV_CKLST_SEC013_Q002",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Cracking", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Mechanical Damage", null,
            isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q003 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Does the shell or head have any deformations or hot spots:(Bulges, Blisters, Dimpling)",
          databaseName: "510_EXT-PV_CKLST_SEC013_Q003",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Deformation", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Hot Spots", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q004 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Were there indications of weld corrosion or cracking in the welds or Heat Affected Zones of the pressure retaining welds:",
          databaseName: "510_EXT-PV_CKLST_SEC013_Q004",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Corrosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Cracking", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q005 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Were any corrosion, erosion, or pitting cells noted on the channel surfaces:  (The dimensions and locations of any damage shall be recorded)",
          databaseName: "510_EXT-PV_CKLST_SEC013_Q005",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Corrosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Erosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Pitting", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q006 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Was any mechanical damage or cracking noted on the channel surfaces:  (The dimensions and location of any damage shall be recorded)",
          databaseName: "510_EXT-PV_CKLST_SEC013_Q006",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Cracking", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Mechanical Damage", null,
            isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q007 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Does the channel surfaces have any deformations or hot spots: (Bulges , Blisters , Dimpling)",
          databaseName: "510_EXT-PV_CKLST_SEC013_Q007",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Deformation", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Hot Spots", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q008 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Were there indications of weld corrosion or cracking in the welds or Heat Affected Zones of the channel pressure retaining welds:",
          databaseName: "510_EXT-PV_CKLST_SEC013_Q008",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Corrosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Cracking", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q009 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Do the head surfaces have any deformations or hot spots: (Bulges , Blisters , Dimpling)",
          databaseName: "510_EXT-PV_CKLST_SEC013_Q009",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Deformation", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Hot Spots", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q010 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Were there indications of weld corrosion or cracking in the welds or Heat Affected Zones of the head pressure retaining welds:",
          databaseName: "510_EXT-PV_CKLST_SEC013_Q010",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Corrosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Cracking", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q011 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Were any corrosion, erosion, or pitting cells noted on the head surfaces:  (The dimensions and locations of any damage shall be recorded)",
          databaseName: "510_EXT-PV_CKLST_SEC013_Q011",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Corrosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Erosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Pitting", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q012 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Was any mechanical damage, or cracking noted on the head surfaces:  (The dimensions and location of any damage shall be recorded)",
          databaseName: "510_EXT-PV_CKLST_SEC013_Q012",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Cracking", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Mechanical Damage", null,
            isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q013 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Were there indications of weld corrosion or cracking in the welds or Heat Affected Zones of the pressure retaining welds:",
          databaseName: "510_EXT-PV_CKLST_SEC013_Q013",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Corrosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Cracking", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC013Q014 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Was any excessive vibration noted that could propagate failure of weld(s) or supports:",
          databaseName: "510_EXT-PV_CKLST_SEC013_Q014",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC013Q015 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Were any temperature variances or pressure limitations noted at time of inspection:",
          databaseName: "510_EXT-PV_CKLST_SEC013_Q015",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC013Q016 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Was any environmental deterioration or impacts from objects noted on the heads or shell:",
          databaseName: "510_EXT-PV_CKLST_SEC013_Q016",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeIs_the_head_to_shell_attachment_achieved_via_welding =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Is the head to shell attachment achieved via welding:",
          databaseName: "510_EXT-PV_CKLST_SEC013_Q017",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC013Q018 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the head to shell weld in acceptable condition for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC013_Q018",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeIs_the_head_to_shell_attachment_achieved_via_bolting =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Is the head to shell attachment achieved via bolting:",
          databaseName: "510_EXT-PV_CKLST_SEC013_Q019",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC013Q020 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the head to shell bolting in acceptable condition for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC013_Q020",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC013Q021 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the channel to shell bolting in acceptable condition for continued service? (Not all channel to shells are bolted)",
          databaseName: "510_EXT-PV_CKLST_SEC013_Q021",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC013Q022 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the channel to head bolting in acceptable condition for continued service? (Not all channel to heads are bolted)",
          databaseName: "510_EXT-PV_CKLST_SEC013_Q022",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510EXT_PVCKLSTSEC013Q023 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Was evidence of prior or active leakage noted to be originating from the shell or heads:",
          databaseName: "510_EXT-PV_CKLST_SEC013_Q023",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Active Leakage", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Evidence of Prior Leakage", null,
            isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC013Q024 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the shell of the vessel in acceptable condition for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC013_Q024",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC013Q025 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are the heads of the vessel in acceptable condition for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC013_Q025",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attribute510EXT_PVCKLSTSEC013Q001,
      attribute510EXT_PVCKLSTSEC013Q002,
      attribute510EXT_PVCKLSTSEC013Q003,
      attribute510EXT_PVCKLSTSEC013Q004,
      attribute510EXT_PVCKLSTSEC013Q005,
      attribute510EXT_PVCKLSTSEC013Q006,
      attribute510EXT_PVCKLSTSEC013Q007,
      attribute510EXT_PVCKLSTSEC013Q008,
      attribute510EXT_PVCKLSTSEC013Q009,
      attribute510EXT_PVCKLSTSEC013Q010,
      attribute510EXT_PVCKLSTSEC013Q011,
      attribute510EXT_PVCKLSTSEC013Q012,
      attribute510EXT_PVCKLSTSEC013Q013,
      attribute510EXT_PVCKLSTSEC013Q014,
      attribute510EXT_PVCKLSTSEC013Q015,
      attribute510EXT_PVCKLSTSEC013Q016,
      attributeIs_the_head_to_shell_attachment_achieved_via_welding,
      attribute510EXT_PVCKLSTSEC013Q018,
      attributeIs_the_head_to_shell_attachment_achieved_via_bolting,
      attribute510EXT_PVCKLSTSEC013Q020,
      attribute510EXT_PVCKLSTSEC013Q021,
      attribute510EXT_PVCKLSTSEC013Q022,
      attribute510EXT_PVCKLSTSEC013Q023,
      attribute510EXT_PVCKLSTSEC013Q024,
      attribute510EXT_PVCKLSTSEC013Q025,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section510EXT_PVCKLSTSEC013";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
