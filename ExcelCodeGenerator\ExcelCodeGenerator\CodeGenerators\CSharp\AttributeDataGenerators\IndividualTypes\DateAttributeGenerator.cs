﻿using CheckListGen;

namespace ExcelCodeGenerator.CodeGenerators.CSharp
{
    public class DateAttributeGenerator : IAttributeGenerator
    {
        public string BuildInitialization(Question question)
        {
            return
                @"
        " + question.DartVariableName + @" = new DateAttribute(this, displayName: """ + question.DisplayText +
                @""", databaseName: """ + question.DataName + @""", areCommentsRequired: " +
                (question.ForceComment ? "true" : "false") + @"); ";
        }

        public string BuildDeclaration(Question question)
        {
            return
                @"public DateAttribute " + question.DartVariableName + ";";
        }
    }
}