//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'Section510INT_PVCKLSTSEC014.dart';

// ignore: camel_case_types
class Section510INT_PVCKLSTSEC014Page extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final Section510INT_PVCKLSTSEC014 section510INT_PVCKLSTSEC014;

  const Section510INT_PVCKLSTSEC014Page(this.section510INT_PVCKLSTSEC014,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _Section510INT_PVCKLSTSEC014PageState();
  }
}

class _Section510INT_PVCKLSTSEC014PageState
    extends State<Section510INT_PVCKLSTSEC014Page> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.section510INT_PVCKLSTSEC014,
        title: "TUBE BUNDLE - HEX ONLY",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC014
                      .attribute510INT_PVCKLSTSEC014Q001
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC014
                      .attribute510INT_PVCKLSTSEC014Q002
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC014
                      .attribute510INT_PVCKLSTSEC014Q003
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC014
                      .attribute510INT_PVCKLSTSEC014Q004
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC014
                      .attribute510INT_PVCKLSTSEC014Q005
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC014
                      .attribute510INT_PVCKLSTSEC014Q006
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC014
                      .attribute510INT_PVCKLSTSEC014Q007
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC014
                      .attribute510INT_PVCKLSTSEC014Q008
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC014
                      .attribute510INT_PVCKLSTSEC014Q009
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC014
                      .attribute510INT_PVCKLSTSEC014Q010
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC014
                      .attribute510INT_PVCKLSTSEC014Q011
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC014
                      .attributeAre_tierods_double_nutted
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC014
                      .attribute510INT_PVCKLSTSEC014Q013
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC014
                      .attribute510INT_PVCKLSTSEC014Q014
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510INT_PVCKLSTSEC014
                      .attribute510INT_PVCKLSTSEC014Q015
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
