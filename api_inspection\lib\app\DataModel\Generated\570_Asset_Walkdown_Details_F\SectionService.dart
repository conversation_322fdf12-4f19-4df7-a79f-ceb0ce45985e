//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class SectionService extends DataModelSection {
  @override
  String getDisplayName() => "Service";
  SectionService(DataModelItem? parent)
      : super(parent: parent, sectionName: "Service");

  // ignore: non_constant_identifier_names
  late StringAttribute attributeServiceProductContents = StringAttribute(
      parent: this,
      displayName: "Service/Product/Contents",
      databaseName: "570AW_Q161",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeSpecific_Gravity = DoubleAttribute(
    parent: this,
    displayName: "Specific Gravity",
    databaseName: "570AW_Q162",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "",
  );

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeServiceProductContents,
      attributeSpecific_Gravity,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionService";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
