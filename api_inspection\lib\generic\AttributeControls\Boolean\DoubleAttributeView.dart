import 'package:api_inspection/generic/AttributeControls/Boolean/BooleanAttributeViewEditable.dart';
import 'package:api_inspection/generic/AttributeControls/Boolean/BooleanAttributeViewNonEditable.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:flutter/material.dart';

import '../AttributeStructure.dart';

class BooleanAttributeView extends StatefulWidget {
  final BooleanAttribute _attribute;
  final IsEditableController? editingController;
  final bool showPhotos;
  final bool showComments;

  const BooleanAttributeView(this._attribute,
      {Key? key,
      this.editingController,
      this.showPhotos = true,
      this.showComments = true})
      : super(key: key);

  @override
  _BooleanAttributeViewState createState() => _BooleanAttributeViewState();
}

class _BooleanAttributeViewState extends State<BooleanAttributeView> {
  @override
  Widget build(BuildContext context) {
    return AttributeStructure(widget._attribute,
        showComments: widget.showComments,
        showPhotos: widget.showPhotos,
        editingController: widget.editingController,
        editingBuilder: (context, listener) {
      return BooleanAttributeViewEditable(widget._attribute);
    }, nonEditingBuilder: (context) {
      return BooleanAttributeViewNonEditable(widget._attribute);
    });
  }
}
