﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CommonDataInterface.Attributes;
using Newtonsoft.Json;

namespace CommonDataInterface
{
  
  public class FlatPhotoHolder
  {
    public string BindingDisplayPath { get; set; } // "General Information.Head.Is Something XY
    public string BindingNamePath { get; set; } // "GeneralInformation.Head.Attribute5842
    public MediaEntry MediaEntry { get; set; }

    public AttributeBase RelatedAttribute { get; set; }
  }

  public abstract class DataModelItem
  {
    
    public abstract String DisplayName { get; }

    public virtual DateTime? GetLastChangedTime()
    {
      var children = GetChildren();
      var childrenTimes = children
                          .Select(a => a.GetLastChangedTime())
                          .Where(a => a != null)
                          .OfType<DateTime>()
                          .OrderBy(a => a).ToArray();
      if (childrenTimes.Length == 0) {
        return null;
      }

      return childrenTimes.LastOrDefault();
    }

    
    internal virtual bool updateFromOther(DataModelItem other){
      bool updated =false;
      foreach (var child in GetChildren()) {
        var matchingChild = other.GetChildren().FirstOrDefault((element) => element.GetDBName() == child.GetDBName());
        if (matchingChild == null){
          throw new Exception("Unexpected null");
        }
        updated |= child.updateFromOther(matchingChild);
      }
      return updated;
    }


    [Newtonsoft.Json.JsonIgnore]
    public DataModelItem Parent { get; set; }

    public DataModelItem(DataModelItem parent)
    {
      Parent = parent;
    }
    
    public T FindParentOfType<T>() where T : class {
      var localParent = Parent;

      if (localParent == null)
        return null;
      if (localParent is T) {
        return localParent as T;
      }

      return localParent.FindParentOfType<T>();
    }
    
    internal virtual String GetDBPath() {
      if (Parent == null) {
        return GetDBName();
      }
      return Parent.GetDBPath() + "." + GetDBName();
    }

    internal abstract String GetDBName();

    public virtual DataModelItem[] GetChildren()
    {
      return new DataModelItem[0];
    }

    public virtual void UpdateFromMap(Dictionary<string, object> map)
    {
      bool updatedEntries = false;
      if (map == null){

        foreach (var child in GetChildren()) {
          child.UpdateFromMap(null);
        }
        UpdateDirectPropertiesFromMapEntry(null);
        return;
      }

      var children = GetChildren().ToList();
      foreach (var entry in map) {
        DataModelItem? match = children.FirstOrDefault((element) => String.Equals(element.GetDBName(), entry.Key, StringComparison.InvariantCultureIgnoreCase));
        if (match != null){
          match.UpdateFromMap(entry.Value as Dictionary<string, object>);
          children.Remove(match);
          continue;
        }

        if (UpdateDirectPropertiesFromMapEntry(entry)){
          updatedEntries = true;
          continue;
        }
      }
    
      if (!updatedEntries){
        UpdateDirectPropertiesFromMapEntry(null);
      }
      foreach (var child in children){
        child.UpdateFromMap(null);
      }
    }

    public virtual bool UpdateDirectPropertiesFromMapEntry(KeyValuePair<string, object>? entry)
    {
      return false;
    }


    internal async Task AddPendingChangesToDictionary(Dictionary<string, Object> updates, String user)
    {
      await DoAddPendingChangesToDictionary(updates, user);

      foreach (var child in GetChildren()) {
        await child.AddPendingChangesToDictionary(updates, user);
      }

    }

    protected virtual async Task DoAddPendingChangesToDictionary(Dictionary<string, Object> updates, String user) 
    {
    }
    
    protected void AddOneTimeChangesToDictionary(Dictionary<string, Object> updates)
    {
      foreach (var child in GetChildren()) {
        child.AddOneTimeChangesToDictionary(updates);
      }

      DoAddOneTimeChangesToDictionary(updates);
    }
    
    public virtual void DoAddOneTimeChangesToDictionary(Dictionary<string, Object> updates)
    {

    }

    public virtual bool GetHasDatabaseChangesPending()
    {
      return GetChildren().Any(a => a.GetHasDatabaseChangesPending());
    }

    public DataModelItem[] GetChildrenRecursive()
    {
      var children = GetChildren();

      var returnList = children.ToList();
      foreach (var child in children) {
        returnList.AddRange(child.GetChildrenRecursive());
      }

      return returnList.ToArray();
    }

    public String GetPropertyPath()
    {
      if (Parent == null) {
        return null;
      }

      var currentObj = this;

      var objTree = new List<DataModelItem>();
      objTree.Add(currentObj);
      while (currentObj.Parent != null) {
        objTree.Add(currentObj.Parent);
        currentObj = currentObj.Parent;
      }


      String path = "";
      for (int i = 0; i < objTree.Count - 1; i++) {
        var obj = objTree[i];
        var parent = objTree[i + 1];

        foreach (var property in parent.GetType().GetProperties())
        {
          if (property.GetValue(parent) == obj) {

            if (path.Length == 0) {
              path = property.Name;
            }
            else {
              path = property.Name + '.' + path;
            }
          }
        }
      }

      return path;
    }
    
    public String GetDisplayPath()
    {
      if (Parent == null) {
        return null;
      }

      var currentObj = this;

      var objTree = new List<DataModelItem>();
      objTree.Add(currentObj);
      while (currentObj.Parent != null) {
        objTree.Add(currentObj.Parent);
        currentObj = currentObj.Parent;
      }


      String path = "";
      for (int i = 0; i < objTree.Count - 1; i++) {
        var obj = objTree[i];
        String currentName = obj.DisplayName;

        if (path.Length == 0) {
          path = currentName;
        }
        else {
          path = currentName + '.' + path;
        }

      }

      return path;
    }

    public List<FlatPhotoHolder> GetAllPhotos(bool shareMedia)
    {
      var allChildren = GetChildrenRecursive();
      var allAttributes = allChildren.OfType<AttributeBase>();
      var attributesWithPhotos = allAttributes.Where(a => a.Photos != null && a.Photos.Length > 0);

      List<FlatPhotoHolder> photos = new List<FlatPhotoHolder>();
      foreach (var attr in attributesWithPhotos) {
        foreach (var mediaEntry in attr.Photos) {
          photos.Add(new FlatPhotoHolder {
            BindingDisplayPath = attr.GetDisplayPath(),
            BindingNamePath = attr.GetPropertyPath(),
            MediaEntry = shareMedia ? mediaEntry : mediaEntry.Duplicate(),
            RelatedAttribute = attr
          });
        }
      }


      return photos;
    }

  }


  public abstract class DataModelRoot : DataModelItem
  {

    
    public virtual async Task SavePendingChanges(String userEmail)
    {
      if (!GetHasDatabaseChangesPending())
        return;

      await DoStartSavingChange(userEmail);

      var currentPath = GetDBPath().Split('.');
      var current = await DataManager.ContextManager.ContextForNonListenerAction(db => db.Collection(currentPath[0]).Document(currentPath[1]));

      var changes = new Dictionary<string, object>();
      await AddPendingChangesToDictionary(changes, userEmail);
      AddOneTimeChangesToDictionary(changes);

      try {
        var result = await current.UpdateAsync(changes);
      }
      catch (Exception ex) {
        Console.WriteLine(ex);
      }
    }
    
    protected virtual async Task DoStartSavingChange(String user)
    {

    }



    protected DataModelRoot(DataModelItem parent) : base(parent) { }
  }
}