//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionAsset_Walkdown_Access_F
{
  public class SectionInternalCoatingLiner : DataModelItem {

    public override String DisplayName { 
      get {
        return "Internal Coating Liner";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public MultiPredefinedValueAttribute attributeCoatingLiner_Type;
    public MultiPredefinedValueAttribute attributeCoatingLiner_Condition;
    public MultiPredefinedValueAttribute attributeCoatingLiner_Conditions_Observed;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionInternalCoatingLiner";

    public SectionInternalCoatingLiner(DataModelItem parent) : base(parent)
    {
            
        attributeCoatingLiner_Type = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Concrete", null),
          new PredefinedValueOption("Tile", null),
          new PredefinedValueOption("Epoxy", null),
          new PredefinedValueOption("Resin", null),
          new PredefinedValueOption("None", null)
        }, true, this, "Coating/Liner Type", databaseName: "AWA_Q341"); 
     
        attributeCoatingLiner_Condition = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Acceptable", null),
          new PredefinedValueOption("Concern", null),
          new PredefinedValueOption("N/A", null)
        }, true, this, "Coating/Liner Condition", databaseName: "AWA_Q342"); 
     
        attributeCoatingLiner_Conditions_Observed = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Smooth", null),
          new PredefinedValueOption("Peeling", null),
          new PredefinedValueOption("Blistering", null),
          new PredefinedValueOption("Holiday", null)
        }, true, this, "Coating/Liner Conditions Observed", databaseName: "AWA_Q343"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeCoatingLiner_Type,
           attributeCoatingLiner_Condition,
           attributeCoatingLiner_Conditions_Observed,
        };
    }
  }
}
