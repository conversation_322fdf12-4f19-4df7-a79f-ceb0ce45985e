import 'package:api_inspection/app/DataModel/Generated/Leak_Report_F/SectionLeakReport.dart';
import 'package:api_inspection/app/DataModel/Generated/Leak_Report_F/SectionWorkDetail.dart';
import 'package:api_inspection/app/DataModel/LeakReport/leakReportPhoto.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/ConcretePhotoRoot.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/DataModelCommon/DatabaseInterfaces/FirebaseDatabaseHelper.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class LeakReport extends ConcretePhotoRoot {
  @override
  String getDisplayName() => "Leak Report";

  @override
  String getDBPath() => "leakreports." + getDBName();

  String createdBy;
  String status;
  late SectionLeakReport report = SectionLeakReport(this);
  late SectionWorkDetail workDetail = SectionWorkDetail(this);
  late StringAttribute apmNumber =
      StringAttribute(parent: this, displayName: "APM Number");

  late StringAttribute businessUnitId = StringAttribute(
      parent: this, displayName: "BusinessUnitId", isQueryable: true);

  late DataModelCollection<LeakReportPhoto> leakReportPhotos =
      DataModelCollection<LeakReportPhoto>("LeakReportPhotos", (parent, entry) {
    return LeakReportPhoto(leakReportPhotos, entry.key);
  }, this);

  void setStatus(String status) {
    this.status = status.toLowerCase();
    var batch = FirebaseFirestore.instance.batch();
    saveItem(batch);
    batch.commit();
  }

  LeakReport(String id, this.createdBy, this.status) : super(id);

  @override
  void saveDirectItems(WriteBatch batch) async {
    FirebaseDatabaseHelper helper = FirebaseDatabaseHelper.global();
    helper.updateProperties(
        getDBPath(),
        {
          "createdBy": createdBy,
          "status": status,
        },
        batch);
  }

  @override
  List<DataModelItem> getChildren() {
    var parentList = super.getChildren().toList();
    parentList.addAll(
        [report, leakReportPhotos, workDetail, apmNumber, businessUnitId]);
    return parentList;
  }
}
