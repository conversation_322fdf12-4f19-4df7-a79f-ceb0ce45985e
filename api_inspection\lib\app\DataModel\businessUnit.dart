import 'package:api_inspection/generic/DataModelCommon/ConcretePhotoRoot.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DatabaseInterfaces/FirebaseDatabaseHelper.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class BusinessUnitCache {
  static final Map _map = {};

  static BusinessUnit findEntry(String id) {
    if (_map.containsKey(id)) {
      return _map[id] as BusinessUnit;
    }
    var newBusinessUnit = BusinessUnit(id);
    _map[id] = newBusinessUnit;
    return newBusinessUnit;
  }
}

class BusinessUnit extends ConcretePhotoRoot {
  BusinessUnit(String id) : super(id);

  @override
  String getDBName() {
    return id;
  }

  @override
  String getDisplayName() => name.getValue() ?? "Unknown";

  @override
  String getDBPath() => "businessUnits." + getDBName();

  late StringAttribute name =
      StringAttribute(parent: this, displayName: "Name");

  late String ClientId;

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([name]);
    return children;
  }

  @override
  void saveDirectItems(WriteBatch batch) async {
    FirebaseDatabaseHelper helper = FirebaseDatabaseHelper.global();
    var path = getDBPath();
    helper.updateItem(path + ".ClientId", ClientId, batch);
  }

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    if (entry.key == "ClientId") {
      ClientId = entry.value;
      return true;
    }
    return false;
  }
}
