//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section570_Asset_Walkdown_Details_F
{
  public class SectionManufacturerFabricator : DataModelItem {

    public override String DisplayName { 
      get {
        return "Manufacturer (Fabricator)";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public StringAttribute attributeMFG_Name;
    public DateAttribute attributeMFG_Date;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionManufacturerFabricator";

    public SectionManufacturerFabricator(DataModelItem parent) : base(parent)
    {
            
        attributeMFG_Name = new StringAttribute(this, displayName: "MFG Name", databaseName: "570AW_Q141"); 
     
        attributeMFG_Date = new DateAttribute(this, displayName: "MFG Date", databaseName: "570AW_Q142", areCommentsRequired: false); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeMFG_Name,
           attributeMFG_Date,
        };
    }
  }
}
