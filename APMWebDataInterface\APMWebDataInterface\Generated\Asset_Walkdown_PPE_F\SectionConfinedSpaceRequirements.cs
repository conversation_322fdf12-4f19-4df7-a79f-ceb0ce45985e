//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionAsset_Walkdown_PPE_F
{
  public class SectionConfinedSpaceRequirements : DataModelItem {

    public override String DisplayName { 
      get {
        return "Confined Space Requirements";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributePermit_required;
    public PredefinedValueAttribute attributeHole_watch_needed;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionConfinedSpaceRequirements";

    public SectionConfinedSpaceRequirements(DataModelItem parent) : base(parent)
    {
            
        attributePermit_required = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Permit required", databaseName: "PPEAW_Q081"); 
     
        attributeHole_watch_needed = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Hole watch needed", databaseName: "PPEAW_Q082"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributePermit_required,
           attributeHole_watch_needed,
        };
    }
  }
}
