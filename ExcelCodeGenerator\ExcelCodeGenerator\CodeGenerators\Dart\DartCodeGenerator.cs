﻿using System.IO;
using System.Linq;
using CheckListGen;

namespace ExcelCodeGenerator.CodeGenerators.Dart
{
    public class DartCodeGenerator
    {
        private DartCodeGenerator()
        {
        }

        public static void GenerateCodeForForm(string outputPath, Section section)
        {
            var generator = new DartCodeGenerator();
            generator.DoGenerateCodeForForm(outputPath, section);
        }

        private void DoGenerateCodeForForm(string outputPath, Section section)
        {
            var outputDirectory = outputPath + "/" + Helpers.CleanupVariableName(section.Name);
            Directory.CreateDirectory(outputDirectory);

            BuildFilesForSection(section, outputDirectory);
        }

        private void BuildFilesForSection(Section section, string outputDirectory)
        {
            foreach (var child in section.Children.OfType<Section>()) BuildFilesForSection(child, outputDirectory);

            DartDataFileGenerator.BuildSectionDataFile(section, outputDirectory);
            DartUIGenerator.BuildSectionUIFile(section, outputDirectory);
        }
    }
}