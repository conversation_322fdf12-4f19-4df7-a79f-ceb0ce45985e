//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class SectionAerialLiftRequirements extends DataModelSection {
  @override
  String getDisplayName() => "Aerial Lift Requirements";
  SectionAerialLiftRequirements(DataModelItem? parent)
      : super(parent: parent, sectionName: "Aerial Lift Requirements");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeAerial_Lift_Needed =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Aerial Lift Needed",
          databaseName: "AWA_Q221",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: true),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeAWAQ222 = PredefinedValueAttribute(
      parent: this,
      hasOtherOption: false,
      displayName:
          "Does the site have access for Aerial Lift for ALL locations at heights?",
      databaseName: "AWA_Q222",
      availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeGas_Powered_Permitted =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Gas Powered Permitted",
          databaseName: "AWA_Q223",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeBattery_Powered_Permitted =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Battery Powered Permitted",
          databaseName: "AWA_Q224",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeClient_required_proof_of_training =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Client required proof of training",
          databaseName: "AWA_Q225",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeClient_provided_operator =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Client provided operator",
          databaseName: "AWA_Q226",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late IntegerAttribute
      attributeEstimated_distance_to_any_live_electrical_overhead_lines =
      IntegerAttribute(
    parent: this,
    displayName: "Estimated distance to any live electrical overhead lines",
    databaseName: "AWA_Q227",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "ft",
  );

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeAerial_Lift_Needed,
      attributeAWAQ222,
      attributeGas_Powered_Permitted,
      attributeBattery_Powered_Permitted,
      attributeClient_required_proof_of_training,
      attributeClient_provided_operator,
      attributeEstimated_distance_to_any_live_electrical_overhead_lines,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionAerialLiftRequirements";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
