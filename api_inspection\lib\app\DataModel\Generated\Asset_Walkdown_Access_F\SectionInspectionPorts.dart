//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class SectionInspectionPorts extends DataModelSection {
  @override
  String getDisplayName() => "Inspection Ports";
  SectionInspectionPorts(DataModelItem? parent)
      : super(parent: parent, sectionName: "Inspection Ports");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeExisting_inspection_ports =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Existing inspection ports?",
          databaseName: "AWA_Q021",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeInsulation_plugs_missing =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Insulation plugs missing?",
          databaseName: "AWA_Q022",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: true),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeAdditional_ports_needed =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Additional ports needed?",
          databaseName: "AWA_Q023",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: true),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeExisting_inspection_ports,
      attributeInsulation_plugs_missing,
      attributeAdditional_ports_needed,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionInspectionPorts";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
