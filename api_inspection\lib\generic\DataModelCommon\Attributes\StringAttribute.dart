import 'package:api_inspection/generic/AttributeControls/AttributeStructure.dart';
import 'package:api_inspection/generic/AttributeControls/String/StringAttributeView.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/DataModelCommon/DatabaseInterfaces/DatabaseHelperManager.dart';

import 'AttributeBase.dart';
import 'ChangeLog/ChangeLogEntry.dart';

class StringAttribute extends SingleAttributeBase<String> {
  bool isQueryable;

  @override
  bool hasData() {
    var current = getValue();
    return current != null && current.isNotEmpty;
  }

  String? getValue() {
    if (valueChangeLog.entries.isEmpty) return null;
    return valueChangeLog.entries.last.value;
  }

  String? getReason() {
    if (valueChangeLog.entries.isEmpty) return null;
    return valueChangeLog.entries.last.reason;
  }

  @override
  String getPreviewText() {
    var value = getValue();
    return value ?? "";
  }

  void setValue(String? value, WriteBatch batch) {
    if (areStringsEqual(getValue(), value)) return;

    var entry = ChangeLogEntry<String>.newlyCreated(value ?? "");
    valueChangeLog.addNewItem(entry);
    if (isQueryable) {
      var helper = DatabaseHelperManager.global();
      helper.updateItem(getDBPath() + ".Value", value?.toLowerCase(), batch);
    }

    changedBySelfListeners.notifyListeners();
    notifyListeners();
  }

  void setValueWithReason(String? value, String? reason, WriteBatch batch) {
    if (areStringsEqual(getValue(), value)) return;

    var entry =
        ChangeLogEntry<String>.newlyCreated(value ?? "", reason: reason);
    valueChangeLog.addNewItem(entry);
    if (isQueryable) {
      var helper = DatabaseHelperManager.global();
      helper.updateItem(getDBPath() + ".Value", value, batch);
    }

    changedBySelfListeners.notifyListeners();
    notifyListeners();
  }

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  StringAttribute(
      {required DataModelItem parent,
      required String displayName,
      Widget? iconWidget,
      bool areCommentsRequired = false,
      String? databaseName,
      this.isQueryable = false})
      : super(parent, displayName, iconWidget, areCommentsRequired,
            databaseName) {
    valueChangeLog.setConversionMethod((item) => item.toString());
  }

  @override
  Widget buildWidget(
      {IsEditableController? editingController,
      bool showPhotos = true,
      bool showComments = true}) {
    if (displayName == "Comment") {
      showComments = false;
    }
    return StringAttributeView(
      this,
      editingController: editingController,
      showPhotos: showPhotos,
      showComments: showComments,
    );
  }

  Widget buildWidgetNonEditable() {
    return buildWidget(
        editingController: IsEditableController(isEditable: false));
  }
}
