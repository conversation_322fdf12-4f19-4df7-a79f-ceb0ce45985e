import 'package:flutter/material.dart';

enum AppSizes { Small, Regular }

class AppStyle {
  static AppStyle? _global;
  static AppStyle get global {
    _global ??= AppStyle();
    return _global!;
  }

  late AppSizes currentAppSize;

  late final double iconSizeMediumSmall;
  late final double iconSizeMedium;
  late final double toolBarFontSize;
  late final double toolBarHeight;
  late final double buttonSizeRegular;
  late final double textboxSizeLarge;
  late final double pixels200;
  late final double pixels100;
  late final double pixels80;
  late final double pixels65;
  late final double pixels50;
  late final double pixels40;
  late final double pixels35;
  late final double pixels20;
  late final double expandButtonSize;

  void init(double appHeight, double appWidth) {
    if (appHeight < 650) {
      currentAppSize = AppSizes.Small;
      iconSizeMediumSmall = 18;
      iconSizeMedium = 24;
      toolBarFontSize = 18;
      toolBarHeight = 40;
      buttonSizeRegular = 20;
      textboxSizeLarge = 42;
      pixels200 = 160;
      pixels100 = 80;
      pixels80 = 64;
      pixels65 = 52;
      pixels50 = 40;
      pixels40 = 32;
      pixels35 = 28;
      pixels20 = 16;
      expandButtonSize = 65;
    } else {
      currentAppSize = AppSizes.Regular;
      iconSizeMediumSmall = 24;
      iconSizeMedium = 30;
      toolBarFontSize = 20;
      toolBarHeight = 55;
      buttonSizeRegular = 24;
      textboxSizeLarge = 50;
      pixels200 = 200;
      pixels100 = 100;
      pixels80 = 80;
      pixels65 = 65;
      pixels50 = 50;
      pixels40 = 40;
      pixels35 = 35;
      pixels20 = 20;
      expandButtonSize = 80;
    }
  }

  InputDecoration get textFieldDecoration {
    return const InputDecoration(
      enabledBorder: OutlineInputBorder(
        borderSide: BorderSide(color: Colors.white, width: 1.0),
      ),
      border: OutlineInputBorder(
        borderSide: BorderSide(color: Colors.white, width: 1.0),
      ),
    );
  }
}
