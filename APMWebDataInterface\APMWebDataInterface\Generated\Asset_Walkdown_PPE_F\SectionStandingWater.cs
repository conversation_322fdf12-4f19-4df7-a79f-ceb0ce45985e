//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionAsset_Walkdown_PPE_F
{
  public class SectionStandingWater : DataModelItem {

    public override String DisplayName { 
      get {
        return "Standing Water";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributeIs_there_standing_water;
    public PredefinedValueAttribute attributeDrainage_needed;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionStandingWater";

    public SectionStandingWater(DataModelItem parent) : base(parent)
    {
            
        attributeIs_there_standing_water = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Is there standing water?", databaseName: "PPEAW_Q106"); 
     
        attributeDrainage_needed = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Drainage needed?", databaseName: "PPEAW_Q107"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeIs_there_standing_water,
           attributeDrainage_needed,
        };
    }
  }
}
