//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionGeneralInformation.dart';
import 'SectionPhotosPage.dart';
import 'SectionDesignPage.dart';
import 'SectionServicePage.dart';

// ignore: camel_case_types
class SectionGeneralInformationPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionGeneralInformation sectionGeneralInformation;

  const SectionGeneralInformationPage(this.sectionGeneralInformation,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionGeneralInformationPageState();
  }
}

class _SectionGeneralInformationPageState
    extends State<SectionGeneralInformationPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionGeneralInformation,
        title: "General Information",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionGeneralInformation.sectionPhotos,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => SectionPhotosPage(
                                          widget.sectionGeneralInformation
                                              .sectionPhotos)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionGeneralInformation.sectionDesign,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => SectionDesignPage(
                                          widget.sectionGeneralInformation
                                              .sectionDesign)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section:
                            widget.sectionGeneralInformation.sectionService,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => SectionServicePage(
                                          widget.sectionGeneralInformation
                                              .sectionService)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                ]),
          );
        });
  }
}
