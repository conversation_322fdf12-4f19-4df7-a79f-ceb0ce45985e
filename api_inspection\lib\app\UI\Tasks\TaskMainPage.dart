import 'dart:async';

import 'package:api_inspection/app/APMRoot.dart';
import 'package:api_inspection/app/UI/BusinessUnits/BusinessUnitSelector.dart';
import 'package:api_inspection/generic/AppRoot.dart';
import 'package:api_inspection/generic/MediaControls/download_progress_indicator.dart';
import 'package:darq/darq.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/Generated/510_Asset_Walkdown_Details_F/Section510_Asset_Walkdown_Details_F.dart';
import 'package:api_inspection/app/DataModel/Generated/570_Asset_Walkdown_Details_F/Section570_Asset_Walkdown_Details_F.dart';
import 'package:api_inspection/app/DataModel/Generated/653_Asset_Walkdown_Details_F/Section653_Asset_Walkdown_Details_F.dart';
import 'package:api_inspection/app/DataModel/project.dart';
import 'package:api_inspection/app/DataModel/task.dart';
import 'package:api_inspection/app/DataModel/workorder.dart';
import 'package:api_inspection/app/UI/Tasks/ProjectActivityTrackerPage.dart';
import 'package:api_inspection/app/UI/Tasks/TasksCardView.dart';
import 'package:api_inspection/generic/MediaControls/MediaDownloadIndicator.dart';
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'package:api_inspection/generic/UIControls/selector.dart';
import 'package:api_inspection/generic/AppStyle.dart';

class TasksMainPage extends StatefulWidget {
  final String title = "Tasks";
  const TasksMainPage({Key? key}) : super(key: key);

  @override
  _TasksMainPageState createState() => _TasksMainPageState();
}

class _TasksMainPageState extends State<TasksMainPage> {
  String filter = "My Tasks";
  String progressFilter = "Active";
  TextEditingController searchTextController = TextEditingController();

  @override
  void initState() {
    searchTextController.addListener(onSearchTextChanged);
    APMRoot.global.queries.businessUnitQueries.selectedBusinessUnitIdListener
        .addListener(onSelectedBusinessUnitIdChanged);
    APMRoot.global.queries.userQuery.userListener.addListener(onUserChanged);
    APMRoot.global.queries.businessUnitQueries.businessUnitsChangedListener
        .addListener(onBusinessUnitsChanged);
    super.initState();
  }

  void onSearchTextChanged() {
    setState(() {});
  }

  void onSelectedBusinessUnitIdChanged() {
    setState(() {});
  }

  @override
  void deactivate() {
    super.deactivate();
  }

  @override
  void dispose() {
    APMRoot.global.newQueries.workOrderListener
        .removeListener(workOrdersChanged);

    searchTextController.removeListener(onSearchTextChanged);
    APMRoot.global.queries.businessUnitQueries.selectedBusinessUnitIdListener
        .removeListener(onSelectedBusinessUnitIdChanged);
    APMRoot.global.queries.businessUnitQueries.businessUnitsChangedListener
        .removeListener(onBusinessUnitsChanged);

    APMRoot.global.queries.userQuery.userListener.removeListener(onUserChanged);
    super.dispose();
  }

  bool isInit = false;
  void ensureInit() {
    if (isInit) {
      return;
    }
    isInit = true;

    APMRoot.global.newQueries.workOrderListener.addListener(workOrdersChanged);
    workOrdersChanged();
  }

  List<WorkOrder> currentListeningWorkOrders = [];

  void workOrdersChanged() {
    setState(() {});
    var missingWorkOrders = APMRoot.global.newQueries.workOrders
        .where((workOrder) => !currentListeningWorkOrders.contains(workOrder));

    var workOrdersToRemove = currentListeningWorkOrders
        .where((workOrder) =>
            APMRoot.global.newQueries.workOrders.contains(workOrder))
        .toList();

    for (var workOrder in missingWorkOrders) {
      workOrder.tasksListener.addListener(onTasksChanged);
    }
    for (var workOrder in workOrdersToRemove) {
      workOrder.tasksListener.removeListener(onTasksChanged);
      currentListeningWorkOrders.remove(workOrder);
    }
  }

  void onTasksChanged() {
    setState(() {});
  }

  void onUserChanged() {
    setState(() {});
  }

  void onBusinessUnitsChanged() {
    setState(() {});
  }

  void showProjectActivityTracker() async {
    var project = await selectProject();
    if (project == null) return;
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) =>
                ProjectActivityTrackerPage(project.activities)));
  }

  InputDecoration textFieldDecoration = const InputDecoration(
    enabledBorder: OutlineInputBorder(
      borderSide: BorderSide(color: Colors.white, width: 1.0),
    ),
    border: OutlineInputBorder(
      borderSide: BorderSide(color: Colors.white, width: 1.0),
    ),
  );

  bool doesTaskMatchFilters(Task task) {
    var user = AppRoot.global().currentUser!.email;

    var buId = task.businessUnitId.getValue();
    if (buId == null || buId != APMRoot.global.selectedBusinessUnitId) {
      return false;
    }

    if (filter == "My Tasks") {
      if (!task.assignedUsers.contains(user)) {
        return false;
      }
    }

    var status = task.status.getValue();
    bool isActive = (status == null) ||
        (status == "Not Started") ||
        (status == "In Progress");

    bool isClosed = status == "Completed" || status == "Published";

    if (progressFilter == "Active" && !isActive) {
      return false;
    } else if (progressFilter == "Closed" && !isClosed) {
      return false;
    }
    if (progressFilter == "Other" && (isActive || isClosed)) {
      return false;
    }

    var searchText = searchTextController.text.toLowerCase();
    if (searchText.isEmpty) return true;

    List<String> filteredStrings = [];

    String assetId;
    filteredStrings.add(task.asset.assetCategory);

    var walkDown = task.asset.walkDown;
    if (walkDown is Section510_Asset_Walkdown_Details_F) {
      assetId =
          walkDown.sectionIdentification.attributeNumber_or_ID.getValue() ??
              "No Asset Id";
    } else if (walkDown is Section570_Asset_Walkdown_Details_F) {
      assetId = walkDown.sectionIdentification.attributeNumber_or_Circuit_ID
              .getValue() ??
          "No Asset Id";
    } else if (walkDown is Section653_Asset_Walkdown_Details_F) {
      assetId =
          walkDown.sectionIdentification.attributeNumber_or_ID.getValue() ??
              "No Asset Id";
    } else {
      assetId = "Unimplemented asset category";
    }
    filteredStrings.add(assetId);
    filteredStrings.addAll(task.assignedUsers);
    filteredStrings.add(task.getAssignedToPreviewText());
    var workOrderDescription = task.clientWorkOrderDescription.getValue();
    if (workOrderDescription != null) {
      filteredStrings.add(workOrderDescription);
    }

    if (status != null) {
      filteredStrings.add(status);
    }

    for (var item in filteredStrings) {
      if (item.toLowerCase().contains(searchText)) return true;
    }

    return false;
  }

  Widget buildLeftSelected(String text) {
    return Row(children: [
      Text(text, style: TextStyle(color: Colors.blue[400], fontSize: 14)),
      const SizedBox(
          width: 20,
          child: Icon(
            Icons.arrow_back,
            size: 16,
          )),
    ]);
  }

  Widget buildLeftUnselected(String text) {
    return Row(children: [
      Text(text, style: const TextStyle(color: Colors.white, fontSize: 14)),
      Container(width: 20),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    ensureInit();

    List<Widget> tasksList = [];

    var user = AppRoot.global().currentUser!;
    var businessUnits = APMRoot.global.queries.businessUnitQueries.businessUnits
        .where((element) => user.effectiveBusinessUnitIds.contains(element.id));

    for (var task in APMRoot.global.newQueries.workOrders
        .selectMany((workOrder, index) => workOrder.tasks)) {
      if (doesTaskMatchFilters(task)) {
        tasksList.add(TaskCardView(task));
      }
    }

    Widget activeWidget;
    Widget closedWidget;
    Widget otherWidget;
    if (progressFilter == "Active") {
      activeWidget = buildLeftSelected("Active");
    } else {
      activeWidget = buildLeftUnselected("Active");
    }

    if (progressFilter == "Closed") {
      closedWidget = buildLeftSelected("Closed");
    } else {
      closedWidget = buildLeftUnselected("Closed");
    }

    if (progressFilter == "Other") {
      otherWidget = buildLeftSelected("Other");
    } else {
      otherWidget = buildLeftUnselected("Other");
    }

    Widget allTasksWidget;
    Widget myTasksWidget;

    if (filter == "All Tasks") {
      allTasksWidget = Row(children: [
        const SizedBox(
            width: 20,
            child: Icon(
              Icons.arrow_forward,
              size: 16,
            )),
        Text("All Tasks",
            style: TextStyle(color: Colors.blue[400], fontSize: 14))
      ]);
      myTasksWidget = Row(children: [
        Container(width: 20),
        const Text("My Tasks",
            style: TextStyle(color: Colors.white, fontSize: 14))
      ]);
    } else {
      allTasksWidget = Row(children: [
        Container(width: 20),
        const Text("All Tasks",
            style: TextStyle(color: Colors.white, fontSize: 14))
      ]);
      myTasksWidget = Row(children: [
        const SizedBox(
          width: 20,
          child: Icon(
            Icons.arrow_forward,
            size: 16,
          ),
        ),
        Text("My Tasks",
            style: TextStyle(color: Colors.blue[400], fontSize: 14))
      ]);
    }

    return Column(
      children: [
        AppBar(
          title: Text(
            "Dashboard",
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
            overflow: TextOverflow.visible,
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
          actions: [
            BusinessUnitSelector(businessUnits: businessUnits.toList()),
            const MediaDownloadIndicator(),
            AppRoot.global().buildMenuButtonWidget(context)
          ],
        ),
        const DownloadProgressIndicatorWidget(),
        Expanded(
            child: GestureDetector(
                onTap: () {
                  FocusScope.of(context).unfocus();
                },
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: <Widget>[
                      Container(
                        height: 10,
                      ),
                      Row(
                        children: [
                          TextButton(
                              onPressed: () {
                                setState(() {
                                  if (progressFilter == "Active") {
                                    progressFilter = "Closed";
                                  } else if (progressFilter == "Closed") {
                                    progressFilter = "Other";
                                  } else {
                                    progressFilter = "Active";
                                  }
                                });
                              },
                              child: Container(
                                  margin:
                                      const EdgeInsets.fromLTRB(0, 0, 10, 0),
                                  child: Column(children: [
                                    activeWidget,
                                    Container(height: 2),
                                    closedWidget,
                                    Container(height: 2),
                                    otherWidget
                                  ]))),
                          Expanded(
                              child: SizedBox(
                                  height: AppStyle.global.pixels50,
                                  child: TextField(
                                    key: const ValueKey("TasksSearchField"),
                                    autocorrect: false,
                                    enableSuggestions: false,
                                    keyboardType: TextInputType.text,
                                    decoration: InputDecoration(
                                      hintText: "Search",
                                      hintStyle: TextStyle(
                                          color: Colors.grey[400],
                                          fontSize: 16),
                                      contentPadding: const EdgeInsets.fromLTRB(
                                          10, 0, 10, 0),
                                      enabledBorder: const OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color: Colors.white, width: 1.0),
                                      ),
                                      border: const OutlineInputBorder(
                                        borderSide: BorderSide(
                                            color: Colors.white, width: 1.0),
                                      ),
                                    ),
                                    controller: searchTextController,
                                    style: const TextStyle(
                                        color: Colors.white, fontSize: 16),
                                  ))),
                          TextButton(
                              onPressed: () {
                                setState(() {
                                  if (filter == "All Tasks") {
                                    filter = "My Tasks";
                                  } else {
                                    filter = "All Tasks";
                                  }
                                });
                              },
                              child: Container(
                                  margin:
                                      const EdgeInsets.fromLTRB(0, 0, 10, 0),
                                  child: Column(children: [
                                    myTasksWidget,
                                    Container(height: 5),
                                    allTasksWidget,
                                  ])))
                        ],
                      ),
                      Divider(
                          color: Colors.grey[100], indent: 10, endIndent: 10),
                      Expanded(
                          child: ListView(
                        children: tasksList,
                      )),
                      Divider(
                          color: Colors.grey[100], indent: 10, endIndent: 10),
                      AttributePadding.WithStdSidePadding(Container(
                        margin: const EdgeInsets.fromLTRB(0, 0, 0, 10),
                        child: ElevatedButton(
                            child: const Text(
                              'Project Activity Tracker',
                              style: TextStyle(fontSize: 16),
                            ),
                            onPressed: showProjectActivityTracker),
                      )),
                    ],
                  ),
                )))
      ],
    );
  }

  Color selectedColor = const Color.fromARGB(255, 4, 188, 242);
  Color unselectedColor = const Color.fromARGB(255, 122, 122, 122);

  Future<Project?> selectProject() async {
    var selectedProjects =
        APMRoot.global.queries.selectedProjects.getSelectedProjects();
    if (selectedProjects.length == 1) return selectedProjects.first;

    Project? selectedProject;

    await showDialog(
        context: context,
        builder: (BuildContext context) {
          List<Widget> options = [
            const Text("Please select a project for new task",
                style: TextStyle(color: Colors.white)),
            SingleChildScrollView(
                child: Selector<Project>(selectedProjects, (project) {
              return project.name.getValue() ?? "Project with no name";
            }, (Project? selectedItem) {
              selectedProject = selectedItem;
            }, buttonColor: Colors.blueGrey[800]!))
          ];

          return AlertDialog(
            backgroundColor: const Color.fromARGB(255, 41, 45, 52),
            title: const Text('Select Project',
                style: TextStyle(color: Colors.white)),
            content: SizedBox(
                height: 300,
                child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: options)),
            actions: [
              ElevatedButton(
                child:
                    const Text('Cancel', style: TextStyle(color: Colors.white)),
                onPressed: () {
                  selectedProject = null;
                  Navigator.of(context).pop();
                },
              ),
              ElevatedButton(
                child:
                    const Text('Select', style: TextStyle(color: Colors.white)),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        });

    return selectedProject;
  }
}
