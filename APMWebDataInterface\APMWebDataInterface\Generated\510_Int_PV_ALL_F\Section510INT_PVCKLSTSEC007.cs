//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Int_PV_ALL_F
{
  public class Section510INT_PVCKLSTSEC007 : DataModelItem {

    public override String DisplayName { 
      get {
        return "BONNET - HEX ONLY";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC007Q001;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC007Q002;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC007Q003;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC007Q004;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC007Q005;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC007Q006;
    public PredefinedValueAttribute attributeIs_the_bonnet_to_shell_attachment_achieved_via_welding;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC007Q008;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC007Q009;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC007Q010;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC007Q011;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510INT_PVCKLSTSEC007";

    public Section510INT_PVCKLSTSEC007(DataModelItem parent) : base(parent)
    {
            
        attribute510INT_PVCKLSTSEC007Q001 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Impingement", null),
          new PredefinedValueOption("Yes: Pitting", null)
        }, false, this, "Were any corrosion cells, impingement or pitting noted on the bonnet surfaces: (The dimensions and locations of any damage shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC007_Q001"); 
     
        attribute510INT_PVCKLSTSEC007Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues ( for Dimensions)", null),
          new PredefinedValueOption("Yes: Cracking ( for Dimensions)", null),
          new PredefinedValueOption("Yes: Mechanical Damage ( for Dimensions)", null)
        }, false, this, "Was any mechanical damage or cracking noted on the bonnet surfaces: (The dimensions and location of any damage shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC007_Q002"); 
     
        attribute510INT_PVCKLSTSEC007Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues ( for Dimensions)", null),
          new PredefinedValueOption("Yes: Weld Corrosion ( for Dimensions)", null),
          new PredefinedValueOption("Yes: Weld Cracking ( for Dimensions)", null)
        }, false, this, "Were there indications of weld corrosion or cracking in the welds or Heat Affected Zones of the bonnet pressure retaining welds:", databaseName: "510_INT-PV_CKLST_SEC007_Q003"); 
     
        attribute510INT_PVCKLSTSEC007Q004 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Deformation", null),
          new PredefinedValueOption("Yes: Hot Spots", null)
        }, false, this, "Does the bonnet have any deformations or hot spots: (Bulges, Blisters, Dimpling)", databaseName: "510_INT-PV_CKLST_SEC007_Q004"); 
     
        attribute510INT_PVCKLSTSEC007Q005 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are bonnet penetrations and adjacent areas in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC007_Q005"); 
     
        attribute510INT_PVCKLSTSEC007Q006 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was any mechanical damage or impacts from objects noted on bonnet:", databaseName: "510_INT-PV_CKLST_SEC007_Q006"); 
     
        attributeIs_the_bonnet_to_shell_attachment_achieved_via_welding = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the bonnet to shell attachment achieved via welding:", databaseName: "510_INT-PV_CKLST_SEC007_Q007"); 
     
        attribute510INT_PVCKLSTSEC007Q008 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the bonnet to shell weld in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC007_Q008"); 
     
        attribute510INT_PVCKLSTSEC007Q009 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the bonnet to shell attachment achieved via flanged connection(s):", databaseName: "510_INT-PV_CKLST_SEC007_Q009"); 
     
        attribute510INT_PVCKLSTSEC007Q010 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the bonnet to shell  flanged connection(s) in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC007_Q010"); 
     
        attribute510INT_PVCKLSTSEC007Q011 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the bonnet of the asset in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC007_Q011"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute510INT_PVCKLSTSEC007Q001,
           attribute510INT_PVCKLSTSEC007Q002,
           attribute510INT_PVCKLSTSEC007Q003,
           attribute510INT_PVCKLSTSEC007Q004,
           attribute510INT_PVCKLSTSEC007Q005,
           attribute510INT_PVCKLSTSEC007Q006,
           attributeIs_the_bonnet_to_shell_attachment_achieved_via_welding,
           attribute510INT_PVCKLSTSEC007Q008,
           attribute510INT_PVCKLSTSEC007Q009,
           attribute510INT_PVCKLSTSEC007Q010,
           attribute510INT_PVCKLSTSEC007Q011,
        };
    }
  }
}
