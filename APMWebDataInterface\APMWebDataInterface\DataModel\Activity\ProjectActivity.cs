﻿using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.DataModel.Activity
{
    
  public class ProjectActivity : DataModelCollectionItem {
    
    public override string DisplayName => "Project Activity";

    public ProjectActivity(DataModelItem parent, String id) : base(id, parent)
    {
      workOrderNumber = new StringAttribute(parent: this, displayName: "Client WO Number");
      date = new DateAttribute(parent: this, displayName: "Date");
      user = new StringAttribute(displayName: "User", isQueryable:true, parent:this);

      activities = new List<ProjectActivityItem>{
        new ProjectActivityItem(parentActivity: this, name: "Permitting"),
        new ProjectActivityItem(parentActivity: this, name: "Job Setup"),
        new ProjectActivityItem(parentActivity: this, name: "Lunch"),
        new ProjectActivityItem(parentActivity: this, name: "Post CleanUp"),
        new ProjectActivityItem(parentActivity: this, name: "FW-RT"),
        new ProjectActivityItem(parentActivity: this, name: "FW-MT"),
        new ProjectActivityItem(parentActivity: this, name: "FW-PT"),
        new ProjectActivityItem(parentActivity: this, name: "FW-UT"),
        new ProjectActivityItem(parentActivity: this, name: "FW-VT"),
        new ProjectActivityItem(parentActivity: this, name: "FW-ML"),
        new ProjectActivityItem(parentActivity: this, name: "FW-GW"),

        new ProjectActivityItem(parentActivity: this, name: "FW-ET"),
        new ProjectActivityItem(parentActivity: this, name: "FW-LS"),
        new ProjectActivityItem(parentActivity: this, name: "FW-GPR"),
        new ProjectActivityItem(parentActivity: this, name: "FW-LT"),
        new ProjectActivityItem(parentActivity: this, name: "FW-IR"),
        new ProjectActivityItem(parentActivity: this, name: "FW-PMI"),
        new ProjectActivityItem(parentActivity: this, name: "FW-AE"),
        new ProjectActivityItem(parentActivity: this, name: "FW-VA"),
        new ProjectActivityItem(parentActivity: this, name: "FW-API 510"),
        new ProjectActivityItem(parentActivity: this, name: "FW-API 570"),
        new ProjectActivityItem(parentActivity: this, name: "FW-API 653"),

        new ProjectActivityItem(parentActivity: this, name: "FW-AWS CWI"),
        new ProjectActivityItem(parentActivity: this, name: "FW-NACE"),
        new ProjectActivityItem(parentActivity: this, name: "FW-Other"),
      };
    }

    public StringAttribute workOrderNumber;
    public DateAttribute date;
    public StringAttribute user;

    public List<ProjectActivityItem> activities;

    public override DataModelItem[] GetChildren()
    {
      return base.GetChildren().Concat(new DataModelItem[] {
        workOrderNumber, date, user
      }).Concat(activities).ToArray();

    }
    
  }
}
