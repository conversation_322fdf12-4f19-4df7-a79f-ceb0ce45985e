//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Ext_PV_ALL_F
{
  public class Section510EXT_PVCKLSTSEC005 : DataModelItem {

    public override String DisplayName { 
      get {
        return "STEEL SUPPORTS";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC005Q001;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC005Q002;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC005Q003;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC005Q004;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC005Q005;
    public PredefinedValueAttribute attributeDoes_the_asset_have_a_support_skirt;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC005Q007;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC005Q008;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC005Q009;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC005Q010;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC005Q011;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC005Q012;
    public PredefinedValueAttribute attributeIs_the_asset_skirt_insulated;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC005Q014;
    public PredefinedValueAttribute attributeIs_fireproofing_applied_to_asset_skirt_or_support_beams;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC005Q016;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC005Q017;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC005Q018;
    public PredefinedValueAttribute attributeAre_supports_in_acceptable_condition_for_continued_service;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510EXT_PVCKLSTSEC005";

    public Section510EXT_PVCKLSTSEC005(DataModelItem parent) : base(parent)
    {
            
        attribute510EXT_PVCKLSTSEC005Q001 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Cracking", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Distortion", null)
        }, false, this, "Is there any evidence of corrosion, distortion, and or cracking of the steel supports:", databaseName: "510_EXT-PV_CKLST_SEC005_Q001"); 
     
        attribute510EXT_PVCKLSTSEC005Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Has the remaining thickness of corroded supporting elements been determined:  (Skirts, columns, and bracing)", databaseName: "510_EXT-PV_CKLST_SEC005_Q002"); 
     
        attribute510EXT_PVCKLSTSEC005Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Buckling", null),
          new PredefinedValueOption("Yes: Deflection", null)
        }, false, this, "Is there any evidence of buckling or excessive deflection of the columns and or load-carrying beams:", databaseName: "510_EXT-PV_CKLST_SEC005_Q003"); 
     
        attribute510EXT_PVCKLSTSEC005Q004 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "HEX Only - Is there adequate area to allow free expansion of the exchanger:", databaseName: "510_EXT-PV_CKLST_SEC005_Q004"); 
     
        attribute510EXT_PVCKLSTSEC005Q005 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "HEX Only - Is the expansion allowance system employed in acceptable condition for continued service:", databaseName: "510_EXT-PV_CKLST_SEC005_Q005"); 
     
        attributeDoes_the_asset_have_a_support_skirt = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Does the asset have a support skirt:", databaseName: "510_EXT-PV_CKLST_SEC005_Q006"); 
     
        attribute510EXT_PVCKLSTSEC005Q007 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the asset support skirt accessible for internal inspection:", databaseName: "510_EXT-PV_CKLST_SEC005_Q007"); 
     
        attribute510EXT_PVCKLSTSEC005Q008 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Does the asset skirt have a protective coating system applied to the external surfaces:", databaseName: "510_EXT-PV_CKLST_SEC005_Q008"); 
     
        attribute510EXT_PVCKLSTSEC005Q009 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the skirt protective coating system in acceptable condition for continued service:", databaseName: "510_EXT-PV_CKLST_SEC005_Q009"); 
     
        attribute510EXT_PVCKLSTSEC005Q010 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Pitting", null),
          new PredefinedValueOption("Yes: Thinning", null)
        }, false, this, "Were any corrosion cells, pitting, or thinning noted on the skirt surfaces:", databaseName: "510_EXT-PV_CKLST_SEC005_Q010"); 
     
        attribute510EXT_PVCKLSTSEC005Q011 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Cracking", null),
          new PredefinedValueOption("Yes: Deformation", null),
          new PredefinedValueOption("Yes: Mechanical Damage", null)
        }, false, this, "Was any deformation, cracking, or mechanical damage noted on the skirt surfaces:", databaseName: "510_EXT-PV_CKLST_SEC005_Q011"); 
     
        attribute510EXT_PVCKLSTSEC005Q012 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are support lugs and connection hardware in acceptable condition for continued service:", databaseName: "510_EXT-PV_CKLST_SEC005_Q012"); 
     
        attributeIs_the_asset_skirt_insulated = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the asset skirt insulated:", databaseName: "510_EXT-PV_CKLST_SEC005_Q013"); 
     
        attribute510EXT_PVCKLSTSEC005Q014 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the skirt insulation in acceptable condition for continued service:", databaseName: "510_EXT-PV_CKLST_SEC005_Q014"); 
     
        attributeIs_fireproofing_applied_to_asset_skirt_or_support_beams = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is fireproofing applied to asset skirt or support beams:", databaseName: "510_EXT-PV_CKLST_SEC005_Q015"); 
     
        attribute510EXT_PVCKLSTSEC005Q016 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the skirt fireproofing in acceptable condition for continued service:  (Any crack over 0.250” in width, and any crack which has displacement or bulging of the concrete fireproofing material should be investigated for corrosion under fireproofing)(CUF)", databaseName: "510_EXT-PV_CKLST_SEC005_Q016"); 
     
        attribute510EXT_PVCKLSTSEC005Q017 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the asset skirt in acceptable condition for continued service:", databaseName: "510_EXT-PV_CKLST_SEC005_Q017"); 
     
        attribute510EXT_PVCKLSTSEC005Q018 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is there evidence of piping attachment distortion due to pipe movement:  (I.e. supports and guides attached to asset)", databaseName: "510_EXT-PV_CKLST_SEC005_Q018"); 
     
        attributeAre_supports_in_acceptable_condition_for_continued_service = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are supports in acceptable condition for continued service:", databaseName: "510_EXT-PV_CKLST_SEC005_Q019"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute510EXT_PVCKLSTSEC005Q001,
           attribute510EXT_PVCKLSTSEC005Q002,
           attribute510EXT_PVCKLSTSEC005Q003,
           attribute510EXT_PVCKLSTSEC005Q004,
           attribute510EXT_PVCKLSTSEC005Q005,
           attributeDoes_the_asset_have_a_support_skirt,
           attribute510EXT_PVCKLSTSEC005Q007,
           attribute510EXT_PVCKLSTSEC005Q008,
           attribute510EXT_PVCKLSTSEC005Q009,
           attribute510EXT_PVCKLSTSEC005Q010,
           attribute510EXT_PVCKLSTSEC005Q011,
           attribute510EXT_PVCKLSTSEC005Q012,
           attributeIs_the_asset_skirt_insulated,
           attribute510EXT_PVCKLSTSEC005Q014,
           attributeIs_fireproofing_applied_to_asset_skirt_or_support_beams,
           attribute510EXT_PVCKLSTSEC005Q016,
           attribute510EXT_PVCKLSTSEC005Q017,
           attribute510EXT_PVCKLSTSEC005Q018,
           attributeAre_supports_in_acceptable_condition_for_continued_service,
        };
    }
  }
}
