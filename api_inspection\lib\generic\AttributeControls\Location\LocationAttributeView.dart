import 'package:api_inspection/generic/AttributeControls/Location/LocationAttributeViewEditable.dart';
import 'package:api_inspection/generic/AttributeControls/Location/LocationAttributeViewNonEditable.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
import 'package:flutter/material.dart';

import '../AttributeStructure.dart';

class LocationAttributeView extends StatefulWidget {
  final LocationAttribute _attribute;
  final IsEditableController? editingController;
  final bool showPhotos;
  final bool showComments;

  const LocationAttributeView(this._attribute,
      {Key? key,
      this.editingController,
      this.showPhotos = true,
      this.showComments = true})
      : super(key: key);

  @override
  _LocationAttributeViewState createState() => _LocationAttributeViewState();
}

class _LocationAttributeViewState extends State<LocationAttributeView> {
  @override
  Widget build(BuildContext context) {
    return AttributeStructure(widget._attribute,
        showComments: widget.showComments,
        showPhotos: widget.showPhotos,
        editingController: widget.editingController,
        editingBuilder: (context, updateListener) {
      return LocationAttributeViewEditable(widget._attribute, updateListener);
    }, nonEditingBuilder: (context) {
      return LocationAttributeViewNonEditable(widget._attribute);
    });
  }
}
