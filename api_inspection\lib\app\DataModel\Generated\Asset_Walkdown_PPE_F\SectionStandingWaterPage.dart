//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionStandingWater.dart';

// ignore: camel_case_types
class SectionStandingWaterPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionStandingWater sectionStandingWater;

  const SectionStandingWaterPage(this.sectionStandingWater, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionStandingWaterPageState();
  }
}

class _SectionStandingWaterPageState extends State<SectionStandingWaterPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionStandingWater,
        title: "Standing Water",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionStandingWater.attributeIs_there_standing_water
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionStandingWater.attributeDrainage_needed
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
