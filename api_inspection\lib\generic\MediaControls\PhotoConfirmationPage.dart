import 'dart:io';
import 'package:flutter/material.dart';

import 'package:camera/camera.dart';

import 'package:path_provider/path_provider.dart' as pathProvider;
import 'package:uuid/uuid.dart';
import 'package:image/image.dart' as img;

class MediaPackage {
  XFile mediaFile;
  String? description;
  MediaPackage(this.mediaFile, this.description);
}

typedef OnPhotoCompleted = void Function(MediaPackage? photo);

class PhotoConfirmationPage extends StatefulWidget {
  final XFile _photo;

  final OnPhotoCompleted _onCompleted;

  const PhotoConfirmationPage(this._photo, this._onCompleted, {Key? key})
      : super(key: key);

  @override
  _PhotoConfirmationPageState createState() => _PhotoConfirmationPageState();
}

class _PhotoConfirmationPageState extends State<PhotoConfirmationPage> {
  late File photo;

  Future<bool> _onWillPop() async {
    widget._onCompleted(null);
    Navigator.pop(context);
    return false;
  }

  @override
  void initState() {
    super.initState();

    photo = File(widget._photo.path);
  }

  void rotatePhoto(int degrees) async {
    var directory = await pathProvider.getTemporaryDirectory();
    String path = directory.path;

    String key = const Uuid().v4().toString();

    final img.Image decodedImage = img.decodeImage(await photo.readAsBytes())!;
    final img.Image rotatedImage = img.copyRotate(decodedImage, degrees);
    final rotatedFile = await File(path + "/" + key + ".jpg")
        .writeAsBytes(img.encodeJpg(rotatedImage));

    var oldPhoto = photo;
    setState(() {
      photo = rotatedFile;
    });
    await oldPhoto.delete();
  }

  void rotatePhotoLeft() async {
    rotatePhoto(-90);
  }

  void rotatePhotoRight() async {
    rotatePhoto(90);
  }

  TextEditingController? _controller;

  @override
  Widget build(BuildContext context) {
    _controller ??= TextEditingController();

    Widget imgControl;

    imgControl = Expanded(child: Image.file(photo));

    var rotateImgControls = Visibility(
        visible: true,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Container(
                margin: const EdgeInsets.fromLTRB(15, 5, 15, 5),
                child: Ink(
                    decoration: const ShapeDecoration(
                      color: Colors.blueGrey,
                      shape: CircleBorder(),
                    ),
                    child: IconButton(
                        splashRadius: 25,
                        iconSize: 40,
                        onPressed: rotatePhotoLeft,
                        icon: const Icon(
                          Icons.rotate_left,
                          color: Colors.white,
                        )))),
            Container(
                margin: const EdgeInsets.fromLTRB(15, 5, 15, 5),
                child: Ink(
                    decoration: const ShapeDecoration(
                      color: Colors.blueGrey,
                      shape: CircleBorder(),
                    ),
                    child: IconButton(
                        splashRadius: 25,
                        iconSize: 40,
                        onPressed: rotatePhotoRight,
                        icon: const Icon(Icons.rotate_right,
                            color: Colors.white)))),
          ],
        ));

    var descriptionLabel = Container(
        margin: const EdgeInsets.fromLTRB(25, 5, 25, 0),
        alignment: Alignment.centerLeft,
        child: const Text(
          "Description",
          textAlign: TextAlign.start,
          style: TextStyle(color: Colors.white, fontSize: 22),
        ));

    var descriptionTextField = Container(
        margin: const EdgeInsets.fromLTRB(25, 5, 25, 25),
        child: TextField(
          controller: _controller,
          decoration: const InputDecoration(
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: Colors.white, width: 1.0),
            ),
            border: OutlineInputBorder(
              borderSide: BorderSide(color: Colors.white, width: 1.0),
            ),
          ),
          style: const TextStyle(color: Colors.white),
        ));

    var confirmationButtons = Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Container(
            margin: const EdgeInsets.fromLTRB(10, 0, 10, 10),
            child: ElevatedButton(
                style: ButtonStyle(
                  backgroundColor: MaterialStateProperty.resolveWith<Color?>(
                    (Set<MaterialState> states) {
                      return Colors
                          .blueGrey[600]; // Use the component's default.
                    },
                  ),
                ),
                onPressed: () {
                  widget._onCompleted(null);
                  Navigator.pop(context);
                },
                child: Container(
                    margin: const EdgeInsets.all(10),
                    child:
                        const Text("Cancel", style: TextStyle(fontSize: 18))))),
        Container(
            margin: const EdgeInsets.fromLTRB(10, 0, 10, 10),
            child: ElevatedButton(
                onPressed: () {
                  widget._onCompleted(
                      MediaPackage(XFile(photo.path), _controller!.text));
                  Navigator.pop(context);
                },
                child: Container(
                    margin: const EdgeInsets.all(10),
                    child:
                        const Text("Accept", style: TextStyle(fontSize: 18))))),
      ],
    );

    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
          backgroundColor: const Color.fromARGB(255, 24, 28, 32),
          body: Column(children: [
            imgControl,
            rotateImgControls,
            descriptionLabel,
            descriptionTextField,
            confirmationButtons
          ])),
    );
  }
}
