//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionDesign.dart';

// ignore: camel_case_types
class SectionDesignPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionDesign sectionDesign;

  const SectionDesignPage(this.sectionDesign, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionDesignPageState();
  }
}

class _SectionDesignPageState extends State<SectionDesignPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionDesign,
        title: "Design",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionDesign.attributeDesign_Code
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionDesign.attributeCode_Year
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionDesign.attributeAddendum
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
