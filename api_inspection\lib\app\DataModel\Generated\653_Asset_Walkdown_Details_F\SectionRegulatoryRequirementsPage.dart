//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/UI/CollectionControl.dart';
import 'package:uuid/uuid.dart';
import 'SectionRegulatoryRequirements.dart';

// ignore: camel_case_types
// ignore: camel_case_types
class SectionRegulatoryRequirementsPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final DataModelCollection<SectionRegulatoryRequirements>
      sectionRegulatoryRequirements;
  const SectionRegulatoryRequirementsPage(this.sectionRegulatoryRequirements,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionRegulatoryRequirementsPageState();
  }
}

class _SectionRegulatoryRequirementsPageState
    extends State<SectionRegulatoryRequirementsPage> {
  Widget _cardBuilder(
      BuildContext context, int number, SectionRegulatoryRequirements item) {
    return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
              "Jurisdiction-Regulatory agency: " +
                  (item.attributeJurisdiction_Regulatory_agency
                              .getPreviewText() ==
                          ""
                      ? "Not set"
                      : item.attributeJurisdiction_Regulatory_agency
                          .getPreviewText()),
              style: const TextStyle(color: Colors.white, fontSize: 18)),
        ]);
  }

  Widget _editPageBuilder(
      BuildContext context, SectionRegulatoryRequirements item) {
    return SectionScaffold(
        section: item,
        title: "Regulatory Requirements",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(item
                      .attributeJurisdiction_Regulatory_agency
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }

  SectionRegulatoryRequirements _createNewItem() {
    String id = const Uuid().v4();
    var item =
        SectionRegulatoryRequirements(id, widget.sectionRegulatoryRequirements);

    return item;
  }

  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionRegulatoryRequirements,
        title: "Regulatory Requirements",
        childBuilder: (editableController) {
          return Center(
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: <Widget>[
                Expanded(
                    child: CollectionControl<SectionRegulatoryRequirements>(
                  cardTitle: "Regulatory Requirements",
                  collection: widget.sectionRegulatoryRequirements,
                  cardBuilder: _cardBuilder,
                  createNewItem: _createNewItem,
                  editPageBuilder: _editPageBuilder,
                ))
              ]));
        });
  }
}
