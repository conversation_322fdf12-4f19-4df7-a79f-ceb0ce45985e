import 'package:api_inspection/app/DataModel/Generated/Leak_Report_F/SectionLeakReportPage.dart';
import 'package:api_inspection/app/DataModel/Generated/Leak_Report_F/SectionWorkDetailPage.dart';
import 'package:api_inspection/app/DataModel/LeakReport/leakReport.dart';
import 'package:api_inspection/app/UI/LeakReport/LeakReportPhotosPage.dart';
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'package:api_inspection/generic/AppStyle.dart';
import 'package:flutter/material.dart';

class LeakReportEditPage extends StatefulWidget {
  final LeakReport leakReport;
  const LeakReportEditPage(this.leakReport, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return LeakReportEditPageState();
  }
}

class LeakReportEditPageState extends State<LeakReportEditPage> {
  void goToPhotosPage() {
    Navigator.of(context).push(MaterialPageRoute(
        builder: (context) => LeakReportPhotosPage(widget.leakReport)));
  }

  Widget revertToActiveButton() {
    return ElevatedButton(
        onPressed: () async {
          await showDialog(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                backgroundColor: const Color.fromARGB(255, 41, 45, 52),
                title: const Text(
                  'Mark as active?',
                  style: TextStyle(color: Colors.white),
                ),
                content: const Text(
                    "Do you want to mark this leak detection as active?",
                    style: TextStyle(color: Colors.white)),
                actions: [
                  ElevatedButton(
                    child: const Text('Yes',
                        style: TextStyle(color: Colors.white)),
                    onPressed: () {
                      setState(() {
                        Navigator.of(context).pop();
                        widget.leakReport.setStatus("active");
                        Navigator.of(this.context).pop();
                      });
                    },
                  ),
                  Container(width: 10),
                  ElevatedButton(
                    child:
                        const Text('No', style: TextStyle(color: Colors.white)),
                    onPressed: () {
                      Navigator.of(context).pop();
                      return;
                    },
                  ),
                ],
              );
            },
          );
        },
        child: const Text(
          "Revert to in progress",
          style: TextStyle(fontSize: 16),
        ));
  }

  Widget completionButton() {
    return ElevatedButton(
        onPressed: () async {
          await showDialog(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                backgroundColor: const Color.fromARGB(255, 41, 45, 52),
                title: const Text(
                  'Mark as completed?',
                  style: TextStyle(color: Colors.white),
                ),
                content: const Text(
                    "Do you want to mark this leak detection as completed?",
                    style: TextStyle(color: Colors.white)),
                actions: [
                  ElevatedButton(
                    child: const Text('Yes',
                        style: TextStyle(color: Colors.white)),
                    onPressed: () {
                      setState(() {
                        Navigator.of(context).pop();
                        widget.leakReport.setStatus("closed");
                        Navigator.of(this.context).pop();
                      });
                    },
                  ),
                  Container(width: 10),
                  ElevatedButton(
                    child:
                        const Text('No', style: TextStyle(color: Colors.white)),
                    onPressed: () {
                      Navigator.of(context).pop();
                      return;
                    },
                  ),
                ],
              );
            },
          );
        },
        child: const Text(
          "Complete Leak Detection",
          style: TextStyle(fontSize: 16),
        ));
  }

  @override
  Widget build(BuildContext context) {
    var leakReport = widget.leakReport;

    return Scaffold(
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        appBar: AppBar(
          title: Text(
            "Leak Report",
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        body: Column(children: [
          Expanded(
              child: SingleChildScrollView(
                  child: Column(children: [
            AttributePadding.WithStdPadding(
                TeamToggleButton.withText("Photos", goToPhotosPage)),
            AttributePadding.WithStdPadding(
              TeamToggleButton.withText('Work Detail', () {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) =>
                            SectionWorkDetailPage(leakReport.workDetail)));
              }),
            ),
            AttributePadding.WithStdPadding(
              TeamToggleButton.withText('Leak Report', () {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) =>
                            SectionLeakReportPage(leakReport.report)));
              }),
            ),
          ]))),
          Container(height: 10),
          leakReport.status.toLowerCase() == "closed"
              ? revertToActiveButton()
              : completionButton(),
          Container(height: 10)
        ]));
  }
}
