//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Asset_Walkdown_Details_F
{
  public class SectionRepairRecord : DataModelItem {

    public override String DisplayName { 
      get {
        return "Repair Record";
      }
    }

    #region [-- Sub Sections --]

    #region [-- SectionRepairs --]
    private DataModelCollection<SectionRepairs> _sectionRepairs;
    public DataModelCollection<SectionRepairs> sectionRepairs {
        get {
            if (_sectionRepairs == null) {
              _sectionRepairs = new DataModelCollection<SectionRepairs>("Repairs", (parent, entry) => {
                 return new SectionRepairs(entry.Key, _sectionRepairs);
              }, (parent, id) => {
                return new SectionRepairs(id, _sectionRepairs);
              }, this);
            }

            return _sectionRepairs;
        }
    }
    #endregion [-- SectionRepairs --]
    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributeDoes_the_asset_have_a_repair_or_alteration_plate;
    public BooleanAttribute attributeRepairAlteration_Plates_Legible;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionRepairRecord";

    public SectionRepairRecord(DataModelItem parent) : base(parent)
    {
            
        attributeDoes_the_asset_have_a_repair_or_alteration_plate = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Does the asset have a repair or alteration plate?", databaseName: "510AW_Q181"); 
     
        attributeRepairAlteration_Plates_Legible = new BooleanAttribute(this, displayName: "Repair/Alteration Plate(s) Legible?", databaseName: "510AW_Q182"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           sectionRepairs,
           attributeDoes_the_asset_have_a_repair_or_alteration_plate,
           attributeRepairAlteration_Plates_Legible,
        };
    }
  }
}
