//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'SectionConfinedSpaceRequirements.dart';

// ignore: camel_case_types
class SectionPermittingRequired extends DataModelSection {
  @override
  String getDisplayName() => "Permitting Required";
  SectionPermittingRequired(DataModelItem? parent)
      : super(parent: parent, sectionName: "Permitting Required");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeGeneral_Work =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "General Work",
          databaseName: "PPEAW_Q071",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeGeneral_Hot_Work =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "General Hot Work",
          databaseName: "PPEAW_Q072",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeOpen_Flame_Hot_Work =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Open Flame Hot Work",
          databaseName: "PPEAW_Q073",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeControl_Area_of_Permit =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Control Area of Permit",
          databaseName: "PPEAW_Q090",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeHazardous_Area_Permit =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Hazardous Area Permit",
          databaseName: "PPEAW_Q091",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeHazardous_Material_Permit =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Hazardous Material Permit",
          databaseName: "PPEAW_Q092",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

// ignore: non_constant_identifier_names
  late SectionConfinedSpaceRequirements sectionConfinedSpaceRequirements =
      SectionConfinedSpaceRequirements(this);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      sectionConfinedSpaceRequirements,
      attributeGeneral_Work,
      attributeGeneral_Hot_Work,
      attributeOpen_Flame_Hot_Work,
      attributeControl_Area_of_Permit,
      attributeHazardous_Area_Permit,
      attributeHazardous_Material_Permit,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionPermittingRequired";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
