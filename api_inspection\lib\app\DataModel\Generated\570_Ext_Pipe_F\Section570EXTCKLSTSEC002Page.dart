//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'Section570EXTCKLSTSEC002.dart';

// ignore: camel_case_types
class Section570EXTCKLSTSEC002Page extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final Section570EXTCKLSTSEC002 section570EXTCKLSTSEC002;

  const Section570EXTCKLSTSEC002Page(this.section570EXTCKLSTSEC002, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _Section570EXTCKLSTSEC002PageState();
  }
}

class _Section570EXTCKLSTSEC002PageState
    extends State<Section570EXTCKLSTSEC002Page> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.section570EXTCKLSTSEC002,
        title: "PROTECTIVE COATING AND INSULATION",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC002
                      .attributeDoes_the_asset_have_a_protective_coating_system_applied
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC002
                      .attributeWas_condensation_noted_on_the_exterior_of_the_asset
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC002.attribute570EXTCKLSTSEC002Q003
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC002.attribute570EXTCKLSTSEC002Q004
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC002.attribute570EXTCKLSTSEC002Q005
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC002.attribute570EXTCKLSTSEC002Q006
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC002.attribute570EXTCKLSTSEC002Q007
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC002
                      .attributeIs_the_insulation_jacketing_seams_properly_oriented
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC002
                      .attributeAre_there_indications_of_insulation_jacketing_seal_failure
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC002
                      .attributeAre_there_locations_of_missing_or_damaged_insulation
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC002.attributeAre_CML_ports_installed
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC002
                      .attributeAre_all_CML_port_hole_covers_present_and_properly_sealed
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC002.attribute570EXTCKLSTSEC002Q013
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC002
                      .attributeAre_there_areas_of_wet_or_damp_insulation
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC002
                      .attributeWas_any_insulation_removed_as_part_of_this_inspection
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC002.attribute570EXTCKLSTSEC002Q016
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC002.attribute570EXTCKLSTSEC002Q017
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
