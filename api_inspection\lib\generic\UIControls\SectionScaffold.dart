import 'package:api_inspection/generic/AppStyle.dart';
import 'package:api_inspection/generic/AttributeControls/AttributeStructure.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/UI/SearchPage.dart';
import 'package:api_inspection/generic/UIControls/SectionPage.dart';
import 'package:flutter/material.dart';

class SectionScaffold extends StatefulWidget {
  final String title;
  final DataModelSection section;
  final Widget Function(IsEditableController) childBuilder;

  const SectionScaffold(
      {Key? key,
      required this.title,
      required this.section,
      required this.childBuilder})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionScaffoldState();
  }
}

class _SectionScaffoldState extends State<SectionScaffold> {
  void searchClicked() {
    var route = MaterialPageRoute(
        builder: (context) =>
            SearchPage(rootToSearch: widget.section, title: widget.title));
    Navigator.push(context, route).then((value) => setState(() {}));
  }

  Future<bool> willPop() async {
    SectionUtils.shouldWarn = false;
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: willPop,
        child: Scaffold(
            backgroundColor: const Color.fromARGB(255, 24, 28, 32),
            appBar: AppBar(
              title: Row(
                children: [
                  Expanded(
                    child: Text(
                      widget.title,
                      style:
                          TextStyle(fontSize: AppStyle.global.toolBarFontSize),
                    ),
                  ),
                  IconButton(
                    onPressed: searchClicked,
                    icon: const Icon(Icons.search),
                    key: const ValueKey("searchIcon"),
                  )
                ],
              ),
              toolbarHeight: AppStyle.global.toolBarHeight,
            ),
            body: SectionPage(
                section: widget.section, childBuilder: widget.childBuilder)));
  }
}
