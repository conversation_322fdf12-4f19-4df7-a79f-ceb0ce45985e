import 'package:api_inspection/generic/DataModelCommon/DatabaseInterfaces/FirebaseDatabaseHelper.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class DatabaseCounts {
  int? currentMaxWorkOrderId;
  int? currentMaxProjectId;
  int? currentMaxTaskId;
  int? currentMaxLeakReportId;

  int getNewLeakReportId() {
    var currentId = currentMaxLeakReportId;
    if (currentId == null) {
      currentId = 100;
    } else {
      currentId++;
    }

    currentMaxLeakReportId = currentId;

    FirebaseDatabaseHelper helper = FirebaseDatabaseHelper.global();
    var batch = FirebaseFirestore.instance.batch();
    helper.updateItem(
        "utility.Counts.currentMaxLeakReportId", currentId, batch);
    batch.commit();

    return currentId;
  }

  void updateFromMap(Map map) {
    for (MapEntry entry in map.entries) {
      updateDirectPropertiesFromMapEntry(entry);
    }
  }

  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    if (entry.key == "currentMaxWorkOrderId") {
      currentMaxWorkOrderId = int.tryParse(entry.value.toString());
      return true;
    }
    if (entry.key == "currentMaxProjectId") {
      currentMaxProjectId = int.tryParse(entry.value.toString());
      return true;
    }
    if (entry.key == "currentMaxTaskId") {
      currentMaxTaskId = int.tryParse(entry.value.toString());
      return true;
    }
    if (entry.key == "currentMaxLeakReportId") {
      currentMaxLeakReportId = int.tryParse(entry.value.toString());
      return true;
    }
    return false;
  }
}
