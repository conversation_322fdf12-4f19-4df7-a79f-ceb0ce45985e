﻿using System;
using CheckListGen;

namespace ExcelCodeGenerator.CodeGenerators.CSharp
{
    public static class AttributeGeneratorFactory
    {
        public static IAttributeGenerator BuildGenerator(this Question question)
        {
            switch (question.AttributeType)
            {
                case Question.AttributeTypes.PhoneNumber:
                    return new PhoneNumberAttributeGenerator();
                case Question.AttributeTypes.Coordinate:
                    return new CoordinateAttributeGenerator();
                case Question.AttributeTypes.PhotoCollection:
                    return new PhotoAttributeGenerator();
                case Question.AttributeTypes.Date:
                    return new DateAttributeGenerator();
                case Question.AttributeTypes.String:
                    return new StringAttributeGenerator();
                case Question.AttributeTypes.Decimal:
                    return new DecimalAttributeGenerator();
                case Question.AttributeTypes.Integer:
                    return new IntegerAttributeGenerator();
                case Question.AttributeTypes.Boolean:
                    return new BooleanAttributeGenerator();
                case Question.AttributeTypes.PredefinedValue:
                case Question.AttributeTypes.PredefinedValueWithOther:
                    return new PredefinedValueAttributeGenerator();
                case Question.AttributeTypes.MultiPredefinedValue:
                case Question.AttributeTypes.MultiPredefinedValueWithOther:
                    return new MultiPredefinedValueAttributeGenerator();
                default:
                    throw new NotSupportedException("Unexpected attribute type encountered");
            }
        }
    }
}