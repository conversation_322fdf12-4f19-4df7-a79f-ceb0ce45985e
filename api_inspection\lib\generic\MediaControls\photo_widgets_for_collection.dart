import 'package:api_inspection/generic/MediaControls/MediaEntry.dart';
import 'package:api_inspection/generic/MediaControls/PhotoReviewPage.dart';
import 'package:api_inspection/generic/MediaControls/PhotoReviewPageNonEditable.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

class MediaEntryImage extends StatefulWidget {
  final MediaEntry mediaEntry;

  const MediaEntryImage({Key? key, required this.mediaEntry}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _MediaEntryImageState();
  }
}

class _MediaEntryImageState extends State<MediaEntryImage> {
  @override
  void initState() {
    super.initState();
    widget.mediaEntry.addListener(_onMediaEntry);
  }

  @override
  void dispose() {
    widget.mediaEntry.removeListener(_onMediaEntry);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.mediaEntry.isUploading) return const Icon(Icons.upload);

    return CachedNetworkImage(
        memCacheWidth: 200,
        maxWidthDiskCache: 200,
        imageUrl: widget.mediaEntry.downloadUrl,
        progressIndicatorBuilder: (context, url, downloadProgress) => Padding(
            padding: const EdgeInsets.all(5),
            child: CircularProgressIndicator(value: downloadProgress.progress)),
        errorWidget: (context, url, error) {
          CachedNetworkImage.evictFromCache(widget.mediaEntry.downloadUrl);
          return IconButton(
            icon: const Icon(Icons.error),
            onPressed: () => _refresh(),
          );
        });
  }

  void _onMediaEntry() {
    _refresh();
  }

  void _refresh() {
    if (mounted) {
      setState(() {});
    }
  }
}

TextButton resolvedPhotoForCollection(BuildContext context, MediaEntry entry,
    List<MediaEntry> mediaEntries, bool editable) {
  return TextButton(
      onPressed: () async {
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => editable
                    ? PhotoReviewPage(entry, mediaEntries)
                    : PhotoReviewPageNonEditable(entry, mediaEntries)));
      },
      child: Stack(children: <Widget>[
        Container(
          width: 65,
          height: 65,
          margin: const EdgeInsets.all(0),
          child: ClipRRect(
              borderRadius: const BorderRadius.all(Radius.circular(8)),
              child: FittedBox(
                child: MediaEntryImage(mediaEntry: entry),
                fit: BoxFit.fill,
              )),
        ),
      ]));
}
