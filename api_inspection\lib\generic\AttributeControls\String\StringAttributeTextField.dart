import 'package:api_inspection/app/batch_helper.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';

class StringAttributeTextField extends StatefulWidget {
  final StringAttribute _attribute;
  final InputDecoration? decoration;
  final TextStyle? textStyle;
  final double? height;
  const StringAttributeTextField(this._attribute,
      {Key? key, this.decoration, this.textStyle, this.height})
      : super(key: key);

  @override
  _StringAttributeTextFieldState createState() =>
      _StringAttributeTextFieldState();
}

class _StringAttributeTextFieldState extends State<StringAttributeTextField> {
  TextEditingController? _controller;

  @override
  void initState() {
    StringAttribute attribute = widget._attribute;
    attribute.addListener(onAttributeChanged);
    super.initState();
  }

  @override
  void didUpdateWidget(covariant StringAttributeTextField oldWidget) {
    super.didUpdateWidget(oldWidget);

    StringAttribute attribute = oldWidget._attribute;

    attribute.removeListener(onAttributeChanged);
    var controller = _controller;
    if (controller != null) {
      String? attributeValue = widget._attribute.getValue();
      controller.text = attributeValue ?? "";
    }

    widget._attribute.addListener(onAttributeChanged);
  }

  @override
  void dispose() {
    StringAttribute attribute = widget._attribute;

    attribute.removeListener(onAttributeChanged);
    _controller?.dispose();
    super.dispose();
  }

  void onAttributeChanged() {
    setState(() {
      TextEditingController? controller = _controller;
      var attribute = widget._attribute;
      if (controller != null && controller.text != attribute.getValue()) {
        String? attributeValue = attribute.getValue();
        controller.text = attributeValue ?? "";
      }
    });
  }

  void updateAttributeValue() {
    TextEditingController? controller = _controller;
    if (controller != null) {
      var attribute = widget._attribute;
      BatchHelper.saveAndCommitStringAttribute(attribute, controller.text);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_controller == null) {
      var attribute = widget._attribute;
      _controller = TextEditingController(text: attribute.getValue());
    }

    return SizedBox(
      height: widget.height,
      child: Focus(
          onFocusChange: (hasFocus) {
            if (!hasFocus) {
              updateAttributeValue();
            }
          },
          child: TextField(
            controller: _controller,
            decoration: widget.decoration ??
                const InputDecoration(
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.white, width: 1.0),
                  ),
                  border: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.white, width: 1.0),
                  ),
                ),
            onSubmitted: (String value) {
              updateAttributeValue();
            },
            style: widget.textStyle ?? const TextStyle(color: Colors.white),
          )),
    );
  }
}
