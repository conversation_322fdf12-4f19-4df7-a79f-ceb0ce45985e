import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';

class DateAttributeViewNonEditable extends StatefulWidget {
  final DateAttribute _attribute;
  const DateAttributeViewNonEditable(this._attribute, {Key? key})
      : super(key: key);

  @override
  _DateAttributeViewNonEditableState createState() =>
      _DateAttributeViewNonEditableState();
}

class _DateAttributeViewNonEditableState
    extends State<DateAttributeViewNonEditable> {
  void onAttributeChanged() {
    setState(() {});
  }

  @override
  void dispose() {
    DateAttribute attribute = widget._attribute;
    attribute.removeListener(onAttributeChanged);
    super.dispose();
  }

  @override
  void initState() {
    DateAttribute attribute = widget._attribute;
    attribute.addListener(onAttributeChanged);

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: const EdgeInsets.fromLTRB(40, 10, 40, 10),
        child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              widget._attribute.getValue()?.toString().substring(0, 10) ?? "",
              style: const TextStyle(color: Colors.white, fontSize: 22),
            )));
  }
}
