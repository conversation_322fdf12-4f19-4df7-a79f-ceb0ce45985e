import 'package:flutter/material.dart';
import 'MediaSynchronizer.dart';

class MediaDownloadIndicator extends StatefulWidget {
  const MediaDownloadIndicator({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return MediaDownloadIndicatorState();
  }
}

class MediaDownloadIndicatorState extends State<MediaDownloadIndicator> {
  @override
  void initState() {
    IMediaSynchronizer.getMediaSynchronizer()
        .listener
        .addListener(onMediaSyncrhonizerUpdated);

    super.initState();
  }

  @override
  void dispose() {
    IMediaSynchronizer.getMediaSynchronizer()
        .listener
        .removeListener(onMediaSyncrhonizerUpdated);
    super.dispose();
  }

  void onMediaSyncrhonizerUpdated() {
    updateCount();
  }

  int pendingUploads = 0;

  void updateCount() async {
    var isOnTopOfNavigation = ModalRoute.of(context)?.isCurrent ?? false;

    if (isOnTopOfNavigation) {
      int newPending =
          await IMediaSynchronizer.getMediaSynchronizer().getPendingUploads();

      setState(() {
        pendingUploads = newPending;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (pendingUploads == 0) {
      return Container();
    }

    return Container(
      margin: const EdgeInsets.fromLTRB(0, 0, 20, 0),
      child: Row(children: [
        const Icon(Icons.upload),
        Text("Images " + pendingUploads.toString())
      ]),
    );
  }
}
