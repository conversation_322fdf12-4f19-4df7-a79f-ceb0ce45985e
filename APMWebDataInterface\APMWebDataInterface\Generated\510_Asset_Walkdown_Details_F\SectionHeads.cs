//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Asset_Walkdown_Details_F
{
  public class SectionHeads : DataModelCollectionItem {

    public override String DisplayName { 
      get {
        return "Heads";
      }
    }
    
    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public StringAttribute attributeNumber;
    public PhotoAttribute attributePhotos;
    public PredefinedValueAttribute attributeLocation;
    public PredefinedValueAttribute attributeGeometry;
    public StringAttribute attributeMaterial_Spec_and_Grade;
    public DoubleAttribute attributeAllowable_Stress_at_Temperature;
    public DoubleAttribute attributeNominal_Thickness;
    public DoubleAttribute attributeCorrosion_Allowance;
    public DoubleAttribute attributeJoint_Efficiency;

    #endregion [-- Attributes --]

    public SectionHeads(String id, DataModelItem parent) : base(id, parent)
    {
            
        attributeNumber = new StringAttribute(this, displayName: "Number", databaseName: "510AW_Q451"); 
     
        attributePhotos = new PhotoAttribute(this, displayName: "Photos", databaseName: "510AW_Q459"); 
     
        attributeLocation = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Top", null),
          new PredefinedValueOption("Bottom", null),
          new PredefinedValueOption("N", null),
          new PredefinedValueOption("S", null),
          new PredefinedValueOption("E", null),
          new PredefinedValueOption("W", null)
        }, false, this, "Location", databaseName: "510AW_Q452"); 
     
        attributeGeometry = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("2:1 Ellipsoidal", null),
          new PredefinedValueOption("Conical", null),
          new PredefinedValueOption("Cylindrical", null),
          new PredefinedValueOption("Ellipsoidal", null),
          new PredefinedValueOption("Flat Unstayed Circular", null),
          new PredefinedValueOption("Spherical", null),
          new PredefinedValueOption("Torispherical", null),
          new PredefinedValueOption("Torispherical (L/r=16 2/3)", null),
          new PredefinedValueOption("Other", null)
        }, false, this, "Geometry", databaseName: "510AW_Q453"); 
     
        attributeMaterial_Spec_and_Grade = new StringAttribute(this, displayName: "Material Spec and Grade", databaseName: "510AW_Q454"); 
     
        attributeAllowable_Stress_at_Temperature = new DoubleAttribute(this, displayName: "Allowable Stress at Temperature", databaseName: "510AW_Q455", areCommentsRequired: false, displayUnit: "psi", allowNegatives: true); 
     
        attributeNominal_Thickness = new DoubleAttribute(this, displayName: "Nominal Thickness", databaseName: "510AW_Q456", areCommentsRequired: false, displayUnit: "in", allowNegatives: true); 
     
        attributeCorrosion_Allowance = new DoubleAttribute(this, displayName: "Corrosion Allowance", databaseName: "510AW_Q457", areCommentsRequired: false, displayUnit: "in", allowNegatives: true); 
     
        attributeJoint_Efficiency = new DoubleAttribute(this, displayName: "Joint Efficiency", databaseName: "510AW_Q458", areCommentsRequired: false, displayUnit: null, allowNegatives: true); 
    }   
    
    public override DataModelItem[] GetChildren() {
      var parentChildren = base.GetChildren();

      return parentChildren.Concat(new DataModelItem[] { 
        attributeNumber,
           attributePhotos,
           attributeLocation,
           attributeGeometry,
           attributeMaterial_Spec_and_Grade,
           attributeAllowable_Stress_at_Temperature,
           attributeNominal_Thickness,
           attributeCorrosion_Allowance,
           attributeJoint_Efficiency,
      }).ToArray();
    }
  }
}
