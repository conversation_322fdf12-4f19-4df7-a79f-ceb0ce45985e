//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class SectionIdentification extends DataModelSection {
  @override
  String getDisplayName() => "Identification";
  SectionIdentification(DataModelItem? parent)
      : super(parent: parent, sectionName: "Identification");

  // ignore: non_constant_identifier_names
  late StringAttribute attributeName = StringAttribute(
      parent: this,
      displayName: "Name",
      databaseName: "570AW_Q005",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeNumber_or_Circuit_ID = StringAttribute(
      parent: this,
      displayName: "Number or Circuit ID",
      databaseName: "570AW_Q006",
      areCommentsRequired: false,
      isQueryable: true);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeAsset_Type = StringAttribute(
      parent: this,
      displayName: "Asset Type",
      databaseName: "570AW_Q007",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeEquipment_Description = StringAttribute(
      parent: this,
      displayName: "Equipment Description",
      databaseName: "570AW_Q016",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeProduct_Handled = StringAttribute(
      parent: this,
      displayName: "Product Handled",
      databaseName: "570AW_Q008",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late DateAttribute attributeLast_known_inspection_date = DateAttribute(
      parent: this,
      displayName: "Last known inspection date",
      databaseName: "570AW_Q009",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeLocation = PredefinedValueAttribute(
      parent: this,
      hasOtherOption: false,
      displayName: "Location",
      databaseName: "570AW_Q010",
      availableOptions: [
        PredefinedValueOption("On-Plot (Facility)", null,
            isCommentRequired: false),
        PredefinedValueOption("Off-Plot (Field)", null,
            isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeLine_from_what_equipment_ID = StringAttribute(
      parent: this,
      displayName: "Line from what equipment ID?",
      databaseName: "570AW_Q011",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late LocationAttribute attributeStart_GIS_Location = LocationAttribute(
      parent: this,
      displayName: "Start GIS Location",
      databaseName: "570AW_Q012",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeLine_to_what_eqiupment_ID = StringAttribute(
      parent: this,
      displayName: "Line to what eqiupment ID?",
      databaseName: "570AW_Q014",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late LocationAttribute attributeEnd_GIS_Location = LocationAttribute(
      parent: this,
      displayName: "End GIS Location",
      databaseName: "570AW_Q015",
      areCommentsRequired: false);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeName,
      attributeNumber_or_Circuit_ID,
      attributeAsset_Type,
      attributeEquipment_Description,
      attributeProduct_Handled,
      attributeLast_known_inspection_date,
      attributeLocation,
      attributeLine_from_what_equipment_ID,
      attributeStart_GIS_Location,
      attributeLine_to_what_eqiupment_ID,
      attributeEnd_GIS_Location,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionIdentification";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
