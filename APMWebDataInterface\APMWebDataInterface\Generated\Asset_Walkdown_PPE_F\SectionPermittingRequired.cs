//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionAsset_Walkdown_PPE_F
{
  public class SectionPermittingRequired : DataModelItem {

    public override String DisplayName { 
      get {
        return "Permitting Required";
      }
    }

    #region [-- Sub Sections --]

    #region [-- SectionConfinedSpaceRequirements --]
    private SectionConfinedSpaceRequirements _sectionConfinedSpaceRequirements;
    public SectionConfinedSpaceRequirements sectionConfinedSpaceRequirements {
        get {
            if (_sectionConfinedSpaceRequirements == null) {
               _sectionConfinedSpaceRequirements = new SectionConfinedSpaceRequirements(this);
            }

            return _sectionConfinedSpaceRequirements;
        }
    }
    #endregion [-- SectionConfinedSpaceRequirements --]
    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributeGeneral_Work;
    public PredefinedValueAttribute attributeGeneral_Hot_Work;
    public PredefinedValueAttribute attributeOpen_Flame_Hot_Work;
    public PredefinedValueAttribute attributeControl_Area_of_Permit;
    public PredefinedValueAttribute attributeHazardous_Area_Permit;
    public PredefinedValueAttribute attributeHazardous_Material_Permit;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionPermittingRequired";

    public SectionPermittingRequired(DataModelItem parent) : base(parent)
    {
            
        attributeGeneral_Work = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "General Work", databaseName: "PPEAW_Q071"); 
     
        attributeGeneral_Hot_Work = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "General Hot Work", databaseName: "PPEAW_Q072"); 
     
        attributeOpen_Flame_Hot_Work = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Open Flame Hot Work", databaseName: "PPEAW_Q073"); 
     
        attributeControl_Area_of_Permit = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Control Area of Permit", databaseName: "PPEAW_Q090"); 
     
        attributeHazardous_Area_Permit = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Hazardous Area Permit", databaseName: "PPEAW_Q091"); 
     
        attributeHazardous_Material_Permit = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Hazardous Material Permit", databaseName: "PPEAW_Q092"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           sectionConfinedSpaceRequirements,
           attributeGeneral_Work,
           attributeGeneral_Hot_Work,
           attributeOpen_Flame_Hot_Work,
           attributeControl_Area_of_Permit,
           attributeHazardous_Area_Permit,
           attributeHazardous_Material_Permit,
        };
    }
  }
}
