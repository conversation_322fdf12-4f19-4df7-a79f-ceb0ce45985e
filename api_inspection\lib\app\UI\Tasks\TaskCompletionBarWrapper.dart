import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
import 'package:api_inspection/generic/UIControls/SelectorWithOther.dart';
import 'package:api_inspection/generic/UIControls/selector.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/task.dart';
import 'package:collection/collection.dart';

class TaskCompletionBarWrapper extends StatefulWidget {
  final Widget child;
  final Task task;

  const TaskCompletionBarWrapper(
      {Key? key, required this.task, required this.child})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _TaskCompletionBarWrapperState();
  }
}

class _TaskCompletionBarWrapperState extends State<TaskCompletionBarWrapper> {
  void pickDeferReason(String status) async {
    List<String> reasons = [
      "Asset area inaccessible",
      "Asset data incorrect or incomplete",
      "Cannot find Asset"
    ];
    await showDialog(
        context: context,
        builder: (BuildContext context) {
          String? selectedOption;
          return AlertDialog(
            backgroundColor: const Color.fromARGB(255, 41, 45, 52),
            title: const Text(
                "Please give a reason for placing this task on hold",
                style: TextStyle(color: Colors.white)),
            content: SizedBox(
                height: 325,
                child: SingleChildScrollView(
                    child: SelectorWithOther(
                  reasons,
                  (option) {
                    return option;
                  },
                  (option) {
                    selectedOption = option;
                  },
                ))),
            actions: [
              ElevatedButton(
                child:
                    const Text('Accept', style: TextStyle(color: Colors.white)),
                onPressed: () {
                  setState(() {
                    Navigator.of(context).pop();
                    if (selectedOption != null) {
                      widget.task.setStatus(status, reason: selectedOption);
                    }
                  });
                },
              ),
              Container(width: 10),
              ElevatedButton(
                child:
                    const Text('Cancel', style: TextStyle(color: Colors.white)),
                onPressed: () {
                  Navigator.of(context).pop();
                  return;
                },
              ),
            ],
          );
        });
  }

  void showChangeStateAdditional() async {
    await showDialog(
        context: context,
        builder: (BuildContext context) {
          PredefinedValueOption? selectedOption = widget.task.status
              .getOptions()
              .firstWhereOrNull(
                  (element) => element.value == widget.task.status.getValue());
          return AlertDialog(
            backgroundColor: const Color.fromARGB(255, 41, 45, 52),
            title: const Text("Change status of task?",
                style: TextStyle(color: Colors.white)),
            content: SizedBox(
                height: 325,
                child: Selector<PredefinedValueOption>(
                  widget.task.status.getOptions(),
                  (option) {
                    return option.description ?? option.value;
                  },
                  (option) {
                    selectedOption = option;
                  },
                  initialSelectedOption: selectedOption,
                )),
            actions: [
              ElevatedButton(
                child:
                    const Text('Accept', style: TextStyle(color: Colors.white)),
                onPressed: () {
                  setState(() {
                    Navigator.of(context).pop();
                    var option = selectedOption?.value;
                    if (option != null) {
                      if (selectedOption!.value == "On Hold") {
                        pickDeferReason(option);
                      } else {
                        widget.task.setStatus(option);
                      }
                    }
                  });
                },
              ),
              Container(width: 10),
              ElevatedButton(
                child:
                    const Text('Cancel', style: TextStyle(color: Colors.white)),
                onPressed: () {
                  Navigator.of(context).pop();
                  return;
                },
              ),
            ],
          );
        });
  }

  @override
  Widget build(BuildContext context) {

    var status = widget.task.status.getValue()!.toLowerCase();

    switch (status) {
      case Task.STATUS_NOT_STARTED: 
      return buildMarkAsCompletedButton();

      case Task.STATUS_SCHEDULED:
      return buildRevertToInProgressButton();
      
      case Task.STATUS_IN_PROGRESS: 
      return buildMarkAsCompletedButton();

      case Task.STATUS_ON_HOLD:
      return areOtherTasksInProgress() ? buildOtherTasksLabel() : buildRevertToInProgressButton();
      
      case Task.STATUS_COMPLETED: 
      return areOtherTasksInProgress() ? buildOtherTasksLabel() : buildRevertToInProgressButton();

      case Task.STATUS_PUBLISHED:
      return buildPublishedTaskLabel();

      case Task.STATUS_CANCELED: 
      return buildCanceledTaskLabel();

      default: 
      FirebaseCrashlytics.instance.log("Task with status $status is not implemented.");
      return Row();
    }
  }

  Widget buildRevertToInProgressButton() {
    var revertButton = Row(children: [
          Container(width: 50),
          Expanded(
            child: ElevatedButton(
                onPressed: () async {
                  await showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          backgroundColor:
                              const Color.fromARGB(255, 41, 45, 52),
                          title: const Text("Revert Task to In Progress?",
                              style: TextStyle(color: Colors.white)),
                          content: const Text(
                              "Do you want to revert this task to in progress?",
                              style: TextStyle(color: Colors.white)),
                          actions: [
                            ElevatedButton(
                              child: const Text('Yes',
                                  style: TextStyle(color: Colors.white)),
                              onPressed: () {
                                setState(() {
                                  Navigator.of(context).pop();
                                  widget.task.clearCompleted();
                                });
                              },
                            ),
                            Container(width: 10),
                            ElevatedButton(
                              child: const Text('No',
                                  style: TextStyle(color: Colors.white)),
                              onPressed: () {
                                Navigator.of(context).pop();
                                return;
                              },
                            ),
                          ],
                        );
                      });
                },
                child: const Text(
                  "Revert to In Progress",
                  style: TextStyle(fontSize: 16),
                )),
          ),
          SizedBox(
              width: 50,
              key: const ValueKey('StatusOption'),
              child: IconButton(
                icon: const Icon(Icons.keyboard_control, color: Colors.white),
                onPressed: showChangeStateAdditional,
              ))
        ]);
    return buildLayout(revertButton);
  }

  Widget buildMarkAsCompletedButton() {
    var completionButton = Row(children: [
        Container(width: 50),
        Expanded(
          child: ElevatedButton(
              onPressed: () async {
                await showDialog(
                  context: context,
                  builder: (BuildContext context) {
                    return AlertDialog(
                      backgroundColor: const Color.fromARGB(255, 41, 45, 52),
                      title: const Text(
                        'Mark as completed?',
                        style: TextStyle(color: Colors.white),
                      ),
                      content: const Text(
                          "Do you want to mark this task as completed?",
                          style: TextStyle(color: Colors.white)),
                      actions: [
                        ElevatedButton(
                          child: const Text('Yes',
                              style: TextStyle(color: Colors.white)),
                          onPressed: () {
                            setState(() {
                              Navigator.of(context).pop();
                              widget.task.markCompleted();
                            });
                          },
                        ),
                        Container(width: 10),
                        ElevatedButton(
                          child: const Text('No',
                              style: TextStyle(color: Colors.white)),
                          onPressed: () {
                            Navigator.of(context).pop();
                            return;
                          },
                        ),
                      ],
                    );
                  },
                );
              },
              child: const Text(
                "Complete Task",
                style: TextStyle(fontSize: 16),
              )),
        ),
        SizedBox(
            width: 50,
            key: const ValueKey('StatusOption'),
            child: IconButton(
              icon: const Icon(Icons.keyboard_control, color: Colors.white),
              onPressed: showChangeStateAdditional,
            ))
      ]);
      return buildLayout(completionButton);
  }

  Widget buildOtherTasksLabel() {
    String message = "This task can't be reverted to in-progress as another task " 
          "of the same type is either active or scheduled.";
    return buildLabel(message);
  }

  Widget buildCanceledTaskLabel() {
    String message = "This task can't be modified because it has been canceled.";
    return buildLabel(message);
  }

  Widget buildPublishedTaskLabel() {
    String message = "This task can't be modified because it has been published.";
    return buildLabel(message);
  }

  bool areOtherTasksInProgress() {
    var currentTask = widget.task;
    var activeWorkOrderTasks = currentTask.workOrder.tasks.where((task) => task.isInProgress());

    var sameTypeTasks = activeWorkOrderTasks.where((otherTask) => 
      otherTask.taskType.toLowerCase() == currentTask.taskType.toLowerCase() && otherTask != currentTask
    );

    var currentFullInspectionInProgress = activeWorkOrderTasks.where((otherTask) => 
      currentTask.taskType.toLowerCase() == Task.TYPE_FULL && 
      (
        otherTask.taskType.toLowerCase() == Task.TYPE_EXTERNAL_VISUAL ||
        otherTask.taskType.toLowerCase() == Task.TYPE_INTERNAL_VISUAL
      )
    );

    var otherFullInspectionInProgress = activeWorkOrderTasks.where((otherTask) => 
      otherTask.taskType.toLowerCase() == Task.TYPE_FULL &&
      (
        currentTask.taskType.toLowerCase() == Task.TYPE_EXTERNAL_VISUAL ||
        currentTask.taskType.toLowerCase() == Task.TYPE_INTERNAL_VISUAL
      )
    );

    return sameTypeTasks.isNotEmpty || 
    currentFullInspectionInProgress.isNotEmpty || 
    otherFullInspectionInProgress.isNotEmpty;
  }

  Widget buildLabel(String message) {
    var label = Container(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
      child: Text(message, textAlign: TextAlign.center, style: TextStyle(color: Colors.grey)),
    );
    return buildLayout(label);
  }

  Widget buildLayout(Widget content) {
     return Column(crossAxisAlignment: CrossAxisAlignment.stretch, children: [
      Expanded(child: this.widget.child),
      Divider(color: Colors.grey[100], indent: 10, endIndent: 10),
      Container(
          margin: EdgeInsets.fromLTRB(10, 0, 10, 10), child: content)
    ]);
  }
}
