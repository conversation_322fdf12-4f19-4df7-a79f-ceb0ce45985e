import 'package:flutter/material.dart';
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';

typedef GetDisplayValue<T> = String Function(T item);

class Selector<T> extends StatefulWidget {
  final List<T> options;
  final GetDisplayValue<T> getDisplayValueFunc;
  final Function(T?) onItemSelected;
  final T? initialSelectedOption;

  final Color buttonColor;
  const Selector(this.options, this.getDisplayValueFunc, this.onItemSelected,
      {Key? key,
      this.buttonColor = const Color.fromARGB(255, 41, 45, 52),
      this.initialSelectedOption})
      : super(key: key);

  @override
  _SelectorState<T> createState() => _SelectorState<T>();
}

Color selectedColor = const Color.fromARGB(255, 4, 188, 242);
Color unselectedColor = const Color.fromARGB(255, 122, 122, 122);

class _SelectorState<T> extends State<Selector<T>> {
  T? selectedOption;

  @override
  void initState() {
    selectedOption = widget.initialSelectedOption;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> optionWidgets = [];
    for (var option in widget.options) {
      Color borderColor =
          selectedOption == option ? selectedColor : unselectedColor;
      optionWidgets.add(SizedBox(
          width: 250,
          height: 65,
          child: TeamToggleButton.withText(
            widget.getDisplayValueFunc(option),
            () => {
              setState(() {
                if (selectedOption == option) {
                  selectedOption = null;
                } else {
                  selectedOption = option;
                }
                widget.onItemSelected.call(selectedOption);
              })
            },
            borderColor: borderColor,
            color: widget.buttonColor,
          )));
    }

    return Column(children: optionWidgets);
  }
}
