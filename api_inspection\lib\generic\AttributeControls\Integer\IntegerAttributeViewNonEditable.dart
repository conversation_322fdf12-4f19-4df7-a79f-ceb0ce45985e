import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
import 'package:flutter/material.dart';

class IntegerAttributeViewNonEditable extends StatefulWidget {
  final IntegerAttribute _attribute;

  const IntegerAttributeViewNonEditable(this._attribute, {Key? key})
      : super(key: key);

  @override
  _IntegerAttributeViewNonEditableState createState() =>
      _IntegerAttributeViewNonEditableState();
}

class _IntegerAttributeViewNonEditableState
    extends State<IntegerAttributeViewNonEditable> {
  void onAttributeChanged() {
    setState(() {});
  }

  @override
  void dispose() {
    IntegerAttribute attribute = widget._attribute;
    attribute.removeListener(onAttributeChanged);
    super.dispose();
  }

  @override
  void initState() {
    IntegerAttribute attribute = widget._attribute;
    attribute.addListener(onAttributeChanged);

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: const EdgeInsets.fromLTRB(40, 10, 40, 10),
        child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              widget._attribute.getPreviewText(),
              style: const TextStyle(color: Colors.white, fontSize: 22),
            )));
  }
}
