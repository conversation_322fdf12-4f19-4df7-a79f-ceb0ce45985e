import 'package:api_inspection/generic/AttributeControls/AttributeStructure.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/AttributeControls/Photo/PhotoAttributeView.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';

import 'AttributeBase.dart';

class PhotoAttribute extends AttributeBase {
  PhotoAttribute(
      {required DataModelItem parent,
      required String displayName,
      Widget? iconWidget,
      bool areCommentsRequired = false,
      String? databaseName})
      : super(
            parent, displayName, iconWidget, areCommentsRequired, databaseName);

  @override
  Widget buildWidget(
      {IsEditableController? editingController,
      bool showPhotos = true,
      bool showComments = true}) {
    return PhotoAttributeView(
      this,
      editingController: editingController,
      showComments: showComments,
    );
  }

  @override
  bool hasData() {
    return getPhotos().isNotEmpty;
  }

  Widget buildWidgetNonEditable() {
    return PhotoAttributeView(this);
  }

  @override
  String getPreviewText() {
    return getPhotos().length.toString() + " Photos";
  }
}
