﻿<wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=32b28d13_002Df695_002D41af_002D8808_002Dfc594f19b1aa/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" Name="testQuery" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;TestAncestor&gt;&#xD;
    &lt;TestId&gt;NUnit3x::F14FB119-CE8E-4E2F-B113-94FECF671D6D::.NETCoreApp,Version=v3.1::WebAPICore.Class1.testQuery&lt;/TestId&gt;&#xD;
  &lt;/TestAncestor&gt;&#xD;
&lt;/SessionState&gt;</s:String>
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=41c61022_002D6b92_002D480c_002Db298_002D0f26fe2ace11/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" IsActive="True" Name="testQuery #2" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;Or&gt;&#xD;
    &lt;Not&gt;&#xD;
      &lt;TestAncestor&gt;&#xD;
        &lt;TestId&gt;NUnit3x::F14FB119-CE8E-4E2F-B113-94FECF671D6D::.NETCoreApp,Version=v3.1::WebAPICore.Class1.testQuery&lt;/TestId&gt;&#xD;
      &lt;/TestAncestor&gt;&#xD;
    &lt;/Not&gt;&#xD;
    &lt;TestAncestor&gt;&#xD;
      &lt;TestId&gt;NUnit3x::F14FB119-CE8E-4E2F-B113-94FECF671D6D::.NETCoreApp,Version=v3.1::WebAPICore.Class1.testQuery&lt;/TestId&gt;&#xD;
    &lt;/TestAncestor&gt;&#xD;
  &lt;/Or&gt;&#xD;
&lt;/SessionState&gt;</s:String>
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=76a07618_002D18a6_002D4edd_002Dbdb4_002D5dee09ecbd54/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" Name="testQuery #4" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;TestAncestor&gt;&#xD;
    &lt;TestId&gt;NUnit3x::F14FB119-CE8E-4E2F-B113-94FECF671D6D::.NETCoreApp,Version=v3.1::WebAPICore.Class1.testQuery&lt;/TestId&gt;&#xD;
  &lt;/TestAncestor&gt;&#xD;
&lt;/SessionState&gt;</s:String>
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=7be3a5b7_002D0e0c_002D44a0_002D97d2_002D4ab272feb150/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" Name="testQuery #6" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;TestAncestor&gt;&#xD;
    &lt;TestId&gt;NUnit3x::F14FB119-CE8E-4E2F-B113-94FECF671D6D::.NETCoreApp,Version=v3.1::WebAPICore.Class1.testQuery&lt;/TestId&gt;&#xD;
  &lt;/TestAncestor&gt;&#xD;
&lt;/SessionState&gt;</s:String>
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=8707d640_002Dbf9f_002D4958_002Da596_002D5d1d74714cd2/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" Name="testQuery #5" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;TestAncestor&gt;&#xD;
    &lt;TestId&gt;NUnit3x::F14FB119-CE8E-4E2F-B113-94FECF671D6D::.NETCoreApp,Version=v3.1::WebAPICore.Class1.testQuery&lt;/TestId&gt;&#xD;
  &lt;/TestAncestor&gt;&#xD;
&lt;/SessionState&gt;</s:String>
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=b6356e9e_002D261b_002D4eff_002D887a_002D4abae3624f23/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" Name="testQuery #3" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;TestAncestor&gt;&#xD;
    &lt;TestId&gt;NUnit3x::F14FB119-CE8E-4E2F-B113-94FECF671D6D::.NETCoreApp,Version=v3.1::WebAPICore.Class1.testQuery&lt;/TestId&gt;&#xD;
  &lt;/TestAncestor&gt;&#xD;
&lt;/SessionState&gt;</s:String>
	<s:Boolean x:Key="/Default/UserDictionary/Words/=Firebase/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/UserDictionary/Words/=Firestore/@EntryIndexedValue">True</s:Boolean></wpf:ResourceDictionary>