import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:api_inspection/generic/MediaControls/ListOfMediaItems.dart';
import 'package:api_inspection/generic/MediaControls/MediaCache.dart';
import 'package:api_inspection/generic/MediaControls/MediaEntry.dart';

import '../DataModelCommon/DataModelItem.dart';
import 'PhotoRoot.dart';

abstract class ConcretePhotoRoot extends PhotoRoot {
  late ListOfMediaItems mediaItems = ListOfMediaItems("Photos", this);

  String id;

  ConcretePhotoRoot(this.id) : super(null);

  @override
  bool getShouldDownloadPhotos() {
    return mediaItems.getShouldDownloadPhotos();
  }

  @override
  void setShouldDownloadPhotos(bool newValue) {
    mediaItems.setShouldDownloadPhotos(newValue);
  }

  @override
  MediaEntry addPhoto(String filename, String extension) {
    var mediaEntry = MediaCache.getEntry(filename, extension, mediaItems);
    mediaEntry.version = 1;
    mediaItems.addItem(mediaEntry);

    return mediaEntry;
  }

  @override
  MediaEntry getPhoto(String filename, String extension) {
    return MediaCache.getEntry(filename, extension, mediaItems);
  }

  @override
  void removePhoto(MediaEntry entry) {
    //mediaItems.removePhoto(entry);
  }

  @override
  String getDBName() {
    return id;
  }

  @override
  @mustCallSuper
  List<DataModelItem> getChildren() {
    return [mediaItems];
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
