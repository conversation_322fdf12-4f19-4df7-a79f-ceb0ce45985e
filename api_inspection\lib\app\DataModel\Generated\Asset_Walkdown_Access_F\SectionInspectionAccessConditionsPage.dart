//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionInspectionAccessConditions.dart';
import 'SectionInsulationPage.dart';
import 'SectionInspectionPortsPage.dart';

// ignore: camel_case_types
class SectionInspectionAccessConditionsPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionInspectionAccessConditions sectionInspectionAccessConditions;

  const SectionInspectionAccessConditionsPage(
      this.sectionInspectionAccessConditions,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionInspectionAccessConditionsPageState();
  }
}

class _SectionInspectionAccessConditionsPageState
    extends State<SectionInspectionAccessConditionsPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionInspectionAccessConditions,
        title: "Inspection Access Conditions",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionInspectionAccessConditions
                            .sectionInsulation,
                        onPressed: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => SectionInsulationPage(
                                      widget.sectionInspectionAccessConditions
                                          .sectionInsulation))).then(
                              (value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionInspectionAccessConditions
                            .sectionInspectionPorts,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          SectionInspectionPortsPage(widget
                                              .sectionInspectionAccessConditions
                                              .sectionInspectionPorts)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                ]),
          );
        });
  }
}
