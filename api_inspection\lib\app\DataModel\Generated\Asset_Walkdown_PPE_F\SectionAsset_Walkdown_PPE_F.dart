//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'SectionPPERequirements.dart';
import 'SectionGeneralSiteConditions.dart';

// ignore: camel_case_types
class SectionAsset_Walkdown_PPE_F extends DataModelSection {
  @override
  String getDisplayName() => "Asset Walkdown-PPE";
  SectionAsset_Walkdown_PPE_F(DataModelItem? parent)
      : super(parent: parent, sectionName: "Asset Walkdown-PPE");

// ignore: non_constant_identifier_names
  late SectionPPERequirements sectionPPERequirements =
      SectionPPERequirements(this);
  // ignore: non_constant_identifier_names
  late SectionGeneralSiteConditions sectionGeneralSiteConditions =
      SectionGeneralSiteConditions(this);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      sectionPPERequirements,
      sectionGeneralSiteConditions,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionAsset_Walkdown_PPE_F";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
