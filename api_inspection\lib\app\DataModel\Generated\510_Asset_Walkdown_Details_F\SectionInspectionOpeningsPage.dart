//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/UI/CollectionControl.dart';
import 'package:uuid/uuid.dart';
import 'SectionInspectionOpenings.dart';

// ignore: camel_case_types
// ignore: camel_case_types
class SectionInspectionOpeningsPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final DataModelCollection<SectionInspectionOpenings>
      sectionInspectionOpenings;
  const SectionInspectionOpeningsPage(this.sectionInspectionOpenings,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionInspectionOpeningsPageState();
  }
}

class _SectionInspectionOpeningsPageState
    extends State<SectionInspectionOpeningsPage> {
  Widget _cardBuilder(
      BuildContext context, int number, SectionInspectionOpenings item) {
    return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(number.toString(),
              style: const TextStyle(color: Colors.white, fontSize: 18)),
        ]);
  }

  Widget _editPageBuilder(
      BuildContext context, SectionInspectionOpenings item) {
    return SectionScaffold(
        section: item,
        title: "Inspection Openings",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(item.attributeOpening_Type
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item.attributeOpening_Number
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item.attributeOpening_Size
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }

  SectionInspectionOpenings _createNewItem() {
    String id = const Uuid().v4();
    var item = SectionInspectionOpenings(id, widget.sectionInspectionOpenings);

    return item;
  }

  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionInspectionOpenings,
        title: "Inspection Openings",
        childBuilder: (editableController) {
          return Center(
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: <Widget>[
                Expanded(
                    child: CollectionControl<SectionInspectionOpenings>(
                  cardTitle: "Inspection Openings",
                  collection: widget.sectionInspectionOpenings,
                  cardBuilder: _cardBuilder,
                  createNewItem: _createNewItem,
                  editPageBuilder: _editPageBuilder,
                ))
              ]));
        });
  }
}
