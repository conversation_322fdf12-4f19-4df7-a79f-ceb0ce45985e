import 'dart:developer';

import 'package:api_inspection/generic/DataModelCommon/DataModel/user.dart';
import 'package:api_inspection/generic/DataModelCommon/DatabaseInterfaces/FirebaseDatabaseHelper.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:api_inspection/generic/UI/Login/LoginPage.dart';
import 'package:api_inspection/generic/AppRoot.dart';
import 'package:api_inspection/generic/AppStyle.dart';

class EmailRegistrationPage extends StatefulWidget {
  final String title = "APM Login";

  final Future Function(BuildContext) onLoginSuccessful;
  const EmailRegistrationPage({required this.onLoginSuccessful, Key? key})
      : super(key: key);

  @override
  _EmailRegistrationPageState createState() => _EmailRegistrationPageState();
}

class _EmailRegistrationPageState extends State<EmailRegistrationPage> {
  TextEditingController emailController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController passwordConfirmationController =
      TextEditingController();

  void showErrorMessage(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color.fromARGB(255, 41, 45, 52),
          title: const Text(
            'Error',
            style: TextStyle(color: Colors.white),
          ),
          content: Text(message, style: const TextStyle(color: Colors.white)),
          actions: [
            ElevatedButton(
              child: const Text('Ok', style: TextStyle(color: Colors.white)),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  Future<void> showCompletionMessage(String email) async {
    var databaseReference = FirebaseDatabaseHelper.global().databaseReference;

    var userProfile = await databaseReference
        .collection('users')
        .doc(email.replaceAll(".", "|"))
        .get();

    var profile = UserProfile(email);
    profile.updateFromMap(userProfile.data());

    if (profile.role == null) {
      var document = FirebaseFirestore.instance
          .collection("nonverifiedusers")
          .doc(profile.email.replaceAll(".", "|"));
      await document.set({"exists": true});

      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: const Color.fromARGB(255, 41, 45, 52),
            title: const Text(
              'Registration Complete',
              style: TextStyle(color: Colors.white),
            ),
            content: const Text(
                "Registration has been successful!  However, your account needs to be allocated the required permissions before you are able to access APM and login.",
                style: TextStyle(color: Colors.white)),
            actions: [
              ElevatedButton(
                child: const Text('Ok', style: TextStyle(color: Colors.white)),
                onPressed: () {
                  Navigator.pushAndRemoveUntil(
                      context,
                      MaterialPageRoute(
                          builder: (context) => LoginPage(
                                onLoginSuccessful: widget.onLoginSuccessful,
                              )),
                      (_) => false);
                },
              ),
            ],
          );
        },
      );
    } else {
      AppRoot.global().currentUser = profile;
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: const Color.fromARGB(255, 41, 45, 52),
            title: const Text(
              'Registration Complete',
              style: TextStyle(color: Colors.white),
            ),
            content: const Text(
                "Registration has been successful!  Your account has been pre authorized to use APM.",
                style: TextStyle(color: Colors.white)),
            actions: [
              ElevatedButton(
                child: const Text('Ok', style: TextStyle(color: Colors.white)),
                onPressed: () async {
                  await widget.onLoginSuccessful(context);
                },
              ),
            ],
          );
        },
      );
    }
  }

  void register() async {
    setState(() {
      loading = true;
    });
    var password1Text = passwordController.text;
    var password2Text = passwordConfirmationController.text;
    var email = emailController.text.trim().toLowerCase();

    String? errorMessage;
    if (password1Text != password2Text) {
      errorMessage = "Passwords do not match";
    } else if (password1Text.isEmpty || password2Text.isEmpty) {
      errorMessage = "Password cannot be empty";
    } else if (password1Text.isEmpty) {
      errorMessage = "Password must be atleast 8 characters long";
    } else if (email.isEmpty) {
      errorMessage = "Email cannot be empty";
    } else if (!email.endsWith("@teaminc.com")) {
      errorMessage = "Your Team email must be provided";
    }

    if (errorMessage != null) {
      showErrorMessage(errorMessage);
      setState(() {
        loading = false;
      });
      return;
    }

    try {
      await Firebase.initializeApp();

      FirebaseAuth auth = FirebaseAuth.instance;

      try {
        await auth.createUserWithEmailAndPassword(
            email: email, password: password1Text);

        await showCompletionMessage(email);
        return;
      } on FirebaseAuthException catch (e) {
        errorMessage = e.message ?? "Error encountered registering email";
      }
    } catch (e) {
      log(e.toString(), name: 'EmailRegistrationPage');
    }

    if (errorMessage != null) {
      showErrorMessage(errorMessage);
    }

    setState(() {
      loading = false;
    });
  }

  bool loading = false;

  @override
  Widget build(BuildContext context) {
    if (loading) {
      return Scaffold(
          appBar: AppBar(
            title: Text(
              "Email Registration",
              style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
            ),
            toolbarHeight: AppStyle.global.toolBarHeight,
          ),
          backgroundColor: const Color.fromARGB(255, 24, 28, 32),
          body: SingleChildScrollView(
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                SizedBox(
                  height: 150,
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      FittedBox(
                          clipBehavior: Clip.hardEdge,
                          fit: BoxFit.fitWidth,
                          child: Image.asset("assets/home_background.png")),
                      Align(
                          alignment: Alignment.topLeft,
                          child: Container(
                              margin: const EdgeInsets.all(10),
                              height: 50,
                              child: Image.asset("assets/logo.png")))
                    ],
                  ),
                ),
                Container(
                  alignment: Alignment.center,
                  margin: const EdgeInsets.all(25),
                  child: const Text("New Account Registration",
                      style: TextStyle(
                          fontSize: 18,
                          color: Colors.white,
                          fontWeight: FontWeight.w500)),
                ),
                Container(
                  alignment: Alignment.center,
                  margin: const EdgeInsets.all(25),
                  child: const Text("Registering..",
                      style: TextStyle(
                          fontSize: 18,
                          color: Colors.white,
                          fontWeight: FontWeight.w500)),
                )
              ])));
    }

    var textFieldDecoration = const InputDecoration(
      enabledBorder: OutlineInputBorder(
        borderSide: BorderSide(color: Colors.white, width: 1.0),
      ),
      border: OutlineInputBorder(
        borderSide: BorderSide(color: Colors.white, width: 1.0),
      ),
    );
    return Scaffold(
        appBar: AppBar(
          title: Text(
            "Email Registration",
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        body: SingleChildScrollView(
            child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            SizedBox(
              height: 150,
              child: Stack(
                fit: StackFit.expand,
                children: [
                  FittedBox(
                      clipBehavior: Clip.hardEdge,
                      fit: BoxFit.fitWidth,
                      child: Image.asset("assets/home_background.png")),
                  Align(
                      alignment: Alignment.topLeft,
                      child: Container(
                          margin: const EdgeInsets.all(10),
                          height: 50,
                          child: Image.asset("assets/logo.png")))
                ],
              ),
            ),
            Container(
              alignment: Alignment.center,
              child: Container(
                alignment: Alignment.topLeft,
                width: 300,
                margin: const EdgeInsets.all(5),
                child: const Text("Email",
                    style: TextStyle(
                        fontSize: 18,
                        color: Colors.white,
                        fontWeight: FontWeight.w500)),
              ),
            ),
            Center(
              child: SizedBox(
                height: 50,
                width: 300,
                child: TextField(
                    decoration: textFieldDecoration,
                    style: const TextStyle(color: Colors.white),
                    controller: emailController),
              ),
            ),
            Container(
              margin: const EdgeInsets.fromLTRB(0, 20, 0, 0),
              alignment: Alignment.center,
              child: Container(
                alignment: Alignment.topLeft,
                width: 300,
                margin: const EdgeInsets.all(5),
                child: const Text("Password",
                    style: TextStyle(
                        fontSize: 18,
                        color: Colors.white,
                        fontWeight: FontWeight.w500)),
              ),
            ),
            Center(
              child: SizedBox(
                height: 50,
                width: 300,
                child: TextField(
                    autocorrect: false,
                    decoration: textFieldDecoration,
                    style: const TextStyle(color: Colors.white),
                    obscureText: true,
                    controller: passwordController),
              ),
            ),
            Container(
              alignment: Alignment.center,
              child: Container(
                alignment: Alignment.topLeft,
                width: 300,
                margin: const EdgeInsets.all(5),
                child: const Text("Confirm Password",
                    style: TextStyle(
                        fontSize: 18,
                        color: Colors.white,
                        fontWeight: FontWeight.w500)),
              ),
            ),
            Center(
              child: SizedBox(
                height: 50,
                width: 300,
                child: TextField(
                    autocorrect: false,
                    decoration: textFieldDecoration,
                    style: const TextStyle(color: Colors.white),
                    obscureText: true,
                    controller: passwordConfirmationController),
              ),
            ),
            Container(
              alignment: Alignment.center,
              margin: const EdgeInsets.all(25),
              child: ElevatedButton(
                  onPressed: register,
                  child: Container(
                      width: 100,
                      height: 24,
                      margin: const EdgeInsets.all(10),
                      alignment: Alignment.center,
                      child: const Text(
                        "Register",
                        style: TextStyle(
                            fontSize: 16, fontWeight: FontWeight.bold),
                      ))),
            ),
          ],
        )));
  }
}
