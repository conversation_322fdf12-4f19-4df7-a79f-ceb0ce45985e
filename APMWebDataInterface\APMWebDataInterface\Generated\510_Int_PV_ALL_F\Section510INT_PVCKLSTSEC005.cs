//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Int_PV_ALL_F
{
  public class Section510INT_PVCKLSTSEC005 : DataModelItem {

    public override String DisplayName { 
      get {
        return "FLOATING HEAD COVER - HEX ONLY";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC005Q001;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC005Q002;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC005Q003;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC005Q004;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC005Q005;
    public PredefinedValueAttribute attributeWas_any_damage_to_the_split_rings_noted;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC005Q007;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC005Q008;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510INT_PVCKLSTSEC005";

    public Section510INT_PVCKLSTSEC005(DataModelItem parent) : base(parent)
    {
            
        attribute510INT_PVCKLSTSEC005Q001 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Impingement", null),
          new PredefinedValueOption("Yes: Pitting", null)
        }, false, this, "Were any corrosion cells, impingement or pitting noted on the floating head cover surfaces: (The dimensions and locations of any damage shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC005_Q001"); 
     
        attribute510INT_PVCKLSTSEC005Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Cracking", null),
          new PredefinedValueOption("Yes: Mechanical Damage", null)
        }, false, this, "Was any mechanical damage or cracking noted on the floating head cover surfaces: (The dimensions and location of any damage shall be recorded)", databaseName: "510_INT-PV_CKLST_SEC005_Q002"); 
     
        attribute510INT_PVCKLSTSEC005Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues ( for Dimensions)", null),
          new PredefinedValueOption("Yes: Weld Corrosion ( for Dimensions)", null),
          new PredefinedValueOption("Yes: Weld Cracking ( for Dimensions)", null)
        }, false, this, "Were there indications of weld corrosion or cracking in the welds or Heat Affected Zones of the floating head cover pressure retaining welds:", databaseName: "510_INT-PV_CKLST_SEC005_Q003"); 
     
        attribute510INT_PVCKLSTSEC005Q004 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Deformation", null),
          new PredefinedValueOption("Yes: Hot Spots", null)
        }, false, this, "Does the floating head cover have any deformations or hot spots: (Bulges, Blisters, Dimpling)", databaseName: "510_INT-PV_CKLST_SEC005_Q004"); 
     
        attribute510INT_PVCKLSTSEC005Q005 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was any mechanical damage or impacts from objects noted on the floating head cover:", databaseName: "510_INT-PV_CKLST_SEC005_Q005"); 
     
        attributeWas_any_damage_to_the_split_rings_noted = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was any damage to the split rings noted:", databaseName: "510_INT-PV_CKLST_SEC005_Q006"); 
     
        attribute510INT_PVCKLSTSEC005Q007 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is split ring hardware in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC005_Q007"); 
     
        attribute510INT_PVCKLSTSEC005Q008 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the floating head cover of the asset in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC005_Q008"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute510INT_PVCKLSTSEC005Q001,
           attribute510INT_PVCKLSTSEC005Q002,
           attribute510INT_PVCKLSTSEC005Q003,
           attribute510INT_PVCKLSTSEC005Q004,
           attribute510INT_PVCKLSTSEC005Q005,
           attributeWas_any_damage_to_the_split_rings_noted,
           attribute510INT_PVCKLSTSEC005Q007,
           attribute510INT_PVCKLSTSEC005Q008,
        };
    }
  }
}
