import 'package:api_inspection/app/batch_helper.dart';
import 'package:api_inspection/generic/UIControls/listener.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';

class PredefinedValueAttributeViewWithOtherEditable extends StatefulWidget {
  final PredefinedValueAttribute _attribute;
  final bool showPhotoControl;
  final bool showCommentsControl;

  final ListenerWrapper updateListener;

  const PredefinedValueAttributeViewWithOtherEditable(
      this._attribute, this.updateListener,
      {Key? key, this.showPhotoControl = true, this.showCommentsControl = true})
      : super(key: key);

  @override
  _PredefinedValueAttributeViewWithOtherEditableState createState() =>
      _PredefinedValueAttributeViewWithOtherEditableState();
}

class _PredefinedValueAttributeViewWithOtherEditableState
    extends State<PredefinedValueAttributeViewWithOtherEditable> {
  bool otherButtonSelected = false;

  TextEditingController? _controller;

  @override
  void initState() {
    var attribute = widget._attribute;
    attribute.addListener(onAttributeChanged);
    widget.updateListener.addListener(updateAttributeValue);
    super.initState();
  }

  @override
  void didUpdateWidget(
      covariant PredefinedValueAttributeViewWithOtherEditable oldWidget) {
    oldWidget.updateListener.removeListener(updateAttributeValue);
    widget.updateListener.addListener(updateAttributeValue);

    super.didUpdateWidget(oldWidget);

    var attribute = oldWidget._attribute;

    attribute.removeListener(onAttributeChanged);
    updateControllerBasedOnValue();

    widget._attribute.addListener(onAttributeChanged);
  }

  @override
  void dispose() {
    var attribute = widget._attribute;
    widget.updateListener.removeListener(updateAttributeValue);
    attribute.removeListener(onAttributeChanged);
    _controller?.dispose();
    super.dispose();
  }

  void updateControllerBasedOnValue() {
    TextEditingController? controller = _controller;
    if (controller == null) {
      return;
    }

    var attribute = widget._attribute;
    var currentValue = attribute.getValue();

    var possibleValues = widget._attribute.getOptions();
    if (possibleValues.any((element) => element.value == currentValue)) {
      controller.text = "";
    } else {
      controller.text = currentValue ?? "";
    }
  }

  void onAttributeChanged() {
    setState(() {
      updateControllerBasedOnValue();
    });
  }

  void updateAttributeValue() {
    TextEditingController? controller = _controller;
    if (controller != null) {
      var attribute = widget._attribute;
      if (controller.text != "") {
        BatchHelper.saveAndCommitStringAttribute(attribute, controller.text);
        return;
      }
      var currentValue = attribute.getValue();
      if (!attribute.getOptions().any((a) => a.value == currentValue)) {
        BatchHelper.saveAndCommitStringAttribute(attribute, null);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;

    if (_controller == null) {
      var controllerValue = widget._attribute.getValue();
      var attribute = widget._attribute;
      if (attribute.getOptions().any((a) => a.value == controllerValue)) {
        controllerValue = "";
      }
      if (controllerValue != "" && controllerValue != null) {
        otherButtonSelected = true;
      }
      _controller = TextEditingController(text: controllerValue);
    }

    Color selectedColor = const Color.fromARGB(255, 4, 188, 242);
    Color unselectedColor = const Color.fromARGB(255, 122, 122, 122);

    List<Widget> buttons = [];
    var attrValue = widget._attribute.getValue();
    for (var item in widget._attribute.getOptions()) {
      Color buttonColor;
      if (item.value == attrValue) {
        buttonColor = selectedColor;
      } else {
        buttonColor = unselectedColor;
      }

      buttons.add(SizedBox(
          width: (width - 55) / 2,
          child: TeamToggleButton.withText(
              item.value + " " + (widget._attribute.unit ?? ""),
              () => {
                    setState(() {
                      var currentValue = widget._attribute.getValue();
                      if (currentValue == item.value) {
                        BatchHelper.saveAndCommitStringAttribute(
                            widget._attribute, null);
                      } else {
                        BatchHelper.saveAndCommitStringAttribute(
                            widget._attribute, item.value);
                      }

                      otherButtonSelected = false;
                    })
                  },
              borderColor: buttonColor)));
    }

    buttons.add(SizedBox(
        width: (width - 55) / 2,
        child: TeamToggleButton.withText(
            "Other",
            () => {
                  this.setState(() {
                    BatchHelper.saveAndCommitStringAttribute(
                        widget._attribute, null);
                    _controller!.text = "";
                    otherButtonSelected = !otherButtonSelected;
                  })
                },
            borderColor:
                otherButtonSelected ? selectedColor : unselectedColor)));

    if (otherButtonSelected) {
      return Container(
          margin: const EdgeInsets.fromLTRB(0, 10, 0, 10),
          child: Column(
            children: [
              Wrap(children: buttons),
              Focus(
                  onFocusChange: (hasFocus) {
                    updateAttributeValue();
                  },
                  child: TextField(
                    controller: _controller,
                    decoration: const InputDecoration(
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.white, width: 1.0),
                      ),
                      border: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.white, width: 1.0),
                      ),
                    ),
                    onSubmitted: (String value) {
                      updateAttributeValue();
                    },
                    style: const TextStyle(color: Colors.white),
                  ))
            ],
          ));
    } else {
      return Container(
          margin: const EdgeInsets.fromLTRB(0, 10, 0, 10),
          child: Column(
            children: [Wrap(children: buttons)],
          ));
    }
  }
}
