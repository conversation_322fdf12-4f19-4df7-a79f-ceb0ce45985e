//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'Section570EXTCKLSTSEC007.dart';

// ignore: camel_case_types
class Section570EXTCKLSTSEC007Page extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final Section570EXTCKLSTSEC007 section570EXTCKLSTSEC007;

  const Section570EXTCKLSTSEC007Page(this.section570EXTCKLSTSEC007, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _Section570EXTCKLSTSEC007PageState();
  }
}

class _Section570EXTCKLSTSEC007PageState
    extends State<Section570EXTCKLSTSEC007Page> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.section570EXTCKLSTSEC007,
        title: "SOIL TO AIR INTERFACE",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC007.attribute570EXTCKLSTSEC007Q001
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC007.attribute570EXTCKLSTSEC007Q002
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC007.attribute570EXTCKLSTSEC007Q003
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC007.attribute570EXTCKLSTSEC007Q004
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC007.attribute570EXTCKLSTSEC007Q005
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC007.attribute570EXTCKLSTSEC007Q006
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC007.attribute570EXTCKLSTSEC007Q007
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC007.attribute570EXTCKLSTSEC007Q008
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC007.attribute570EXTCKLSTSEC007Q009
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC007.attribute570EXTCKLSTSEC007Q010
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC007.attribute570EXTCKLSTSEC007Q011
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC007.attribute570EXTCKLSTSEC007Q012
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC007.attribute570EXTCKLSTSEC007Q013
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC007.attribute570EXTCKLSTSEC007Q014
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
