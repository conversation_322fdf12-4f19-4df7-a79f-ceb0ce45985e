//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionTankBottomFloor.dart';

// ignore: camel_case_types
class SectionTankBottomFloorPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionTankBottomFloor sectionTankBottomFloor;

  const SectionTankBottomFloorPage(this.sectionTankBottomFloor, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionTankBottomFloorPageState();
  }
}

class _SectionTankBottomFloorPageState
    extends State<SectionTankBottomFloorPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionTankBottomFloor,
        title: "Tank Bottom (Floor)",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionTankBottomFloor.attributeType
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionTankBottomFloor.attributeMaterial_Spec_and_Grade
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget.sectionTankBottomFloor
                      .attributeNominal_thickness_annular_ring
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget.sectionTankBottomFloor
                      .attributeNominal_thickness_sketch_plates
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget.sectionTankBottomFloor
                      .attributeNominal_thickness_inner_plates
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionTankBottomFloor.attributeCorrosion_Allowance
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
