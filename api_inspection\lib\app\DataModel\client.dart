import 'package:api_inspection/generic/DataModelCommon/ConcretePhotoRoot.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';

class ClientCache {
  static final Map _map = {};

  static Client findEntry(String id) {
    if (_map.containsKey(id)) {
      return _map[id] as Client;
    }
    var newClient = Client(id);
    _map[id] = newClient;
    return newClient;
  }
}

class Client extends ConcretePhotoRoot {
  Client(String id) : super(id);

  @override
  String getDBName() {
    return id;
  }

  @override
  String getDisplayName() => name.getValue() ?? "Unknown";

  @override
  String getDBPath() => "clients." + getDBName();

  late StringAttribute name =
      StringAttribute(parent: this, displayName: "Name");

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([name]);
    return children;
  }
}
