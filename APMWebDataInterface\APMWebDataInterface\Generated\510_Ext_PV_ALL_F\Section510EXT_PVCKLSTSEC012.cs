//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Ext_PV_ALL_F
{
  public class Section510EXT_PVCKLSTSEC012 : DataModelItem {

    public override String DisplayName { 
      get {
        return "PROTECTIVE COATING AND INSULATION";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC012Q001;
    public PredefinedValueAttribute attributeDoes_the_asset_have_a_protective_coating_system_applied;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC012Q003;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC012Q004;
    public PredefinedValueAttribute attributeIs_the_insulation_jacketing_properly_sealed__oriented;
    public PredefinedValueAttribute attributeWas_condensation_noted_on_the_exterior_of_the_asset;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC012Q007;
    public PredefinedValueAttribute attributeWas_any_evidence_of_CUI_noted;
    public PredefinedValueAttribute attributeAre_CML_ports_installed;
    public PredefinedValueAttribute attributeAre_all_CML_port_hole_covers_present_and_properly_sealed;
    public PredefinedValueAttribute attributeDoes_the_vessel_have_fireproofing_applied;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC012Q012;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC012Q013;
    public PredefinedValueAttribute attribute510EXT_PVCKLSTSEC012Q014;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510EXT_PVCKLSTSEC012";

    public Section510EXT_PVCKLSTSEC012(DataModelItem parent) : base(parent)
    {
            
        attribute510EXT_PVCKLSTSEC012Q001 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Do the asset supports have a protective coating system applied:", databaseName: "510_EXT-PV_CKLST_SEC012_Q001"); 
     
        attributeDoes_the_asset_have_a_protective_coating_system_applied = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Does the asset have a protective coating system applied:", databaseName: "510_EXT-PV_CKLST_SEC012_Q002"); 
     
        attribute510EXT_PVCKLSTSEC012Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the asset insulated: (The type of insulation system, metal jacket with underlying insulation type, blanket, etc., shall be recorded)", databaseName: "510_EXT-PV_CKLST_SEC012_Q003"); 
     
        attribute510EXT_PVCKLSTSEC012Q004 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the asset heat traced and in acceptable condition for continued service:  (The type of heat tracing, electrical or steam, shall be recorded)", databaseName: "510_EXT-PV_CKLST_SEC012_Q004"); 
     
        attributeIs_the_insulation_jacketing_properly_sealed__oriented = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the insulation jacketing properly sealed & oriented:", databaseName: "510_EXT-PV_CKLST_SEC012_Q005"); 
     
        attributeWas_condensation_noted_on_the_exterior_of_the_asset = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was condensation noted on the exterior of the asset:", databaseName: "510_EXT-PV_CKLST_SEC012_Q006"); 
     
        attribute510EXT_PVCKLSTSEC012Q007 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Does the asset operate within the CUI range: (10 °F (–12 °C) and 350 °F (177 °C) for carbon and low alloy steels, 140 °F (60 °C) and 350 °F (177 °C) for austenitic stainless steels, and 280 °F (138 °C) and  350 °F (177 °C) for duplex stainless steels or in intermittent service", databaseName: "510_EXT-PV_CKLST_SEC012_Q007"); 
     
        attributeWas_any_evidence_of_CUI_noted = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was any evidence of CUI noted:", databaseName: "510_EXT-PV_CKLST_SEC012_Q008"); 
     
        attributeAre_CML_ports_installed = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are CML ports installed:", databaseName: "510_EXT-PV_CKLST_SEC012_Q009"); 
     
        attributeAre_all_CML_port_hole_covers_present_and_properly_sealed = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are all CML port hole covers present and properly sealed:", databaseName: "510_EXT-PV_CKLST_SEC012_Q010"); 
     
        attributeDoes_the_vessel_have_fireproofing_applied = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Does the vessel have fireproofing applied:", databaseName: "510_EXT-PV_CKLST_SEC012_Q011"); 
     
        attribute510EXT_PVCKLSTSEC012Q012 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the fireproofing applied to asset in acceptable condition for continued service:  (Any crack over 0.250” in width, and any crack which has displacement or bulging of the concrete fireproofing material should be investigated for corrosion under fireproofing)(CUF)", databaseName: "510_EXT-PV_CKLST_SEC012_Q012"); 
     
        attribute510EXT_PVCKLSTSEC012Q013 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the insulation system in acceptable condition for continued service:", databaseName: "510_EXT-PV_CKLST_SEC012_Q013"); 
     
        attribute510EXT_PVCKLSTSEC012Q014 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the protective coating system in acceptable condition for continued service:", databaseName: "510_EXT-PV_CKLST_SEC012_Q014"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute510EXT_PVCKLSTSEC012Q001,
           attributeDoes_the_asset_have_a_protective_coating_system_applied,
           attribute510EXT_PVCKLSTSEC012Q003,
           attribute510EXT_PVCKLSTSEC012Q004,
           attributeIs_the_insulation_jacketing_properly_sealed__oriented,
           attributeWas_condensation_noted_on_the_exterior_of_the_asset,
           attribute510EXT_PVCKLSTSEC012Q007,
           attributeWas_any_evidence_of_CUI_noted,
           attributeAre_CML_ports_installed,
           attributeAre_all_CML_port_hole_covers_present_and_properly_sealed,
           attributeDoes_the_vessel_have_fireproofing_applied,
           attribute510EXT_PVCKLSTSEC012Q012,
           attribute510EXT_PVCKLSTSEC012Q013,
           attribute510EXT_PVCKLSTSEC012Q014,
        };
    }
  }
}
