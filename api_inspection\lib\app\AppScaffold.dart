import 'package:api_inspection/app/APMRoot.dart';
import 'package:api_inspection/app/UI/InfoPages/InfoPage.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/CustomNavigator/CustomNavigator.dart';
import 'package:api_inspection/app/UI/Tasks/TaskMainPage.dart';
import 'package:api_inspection/generic/UI/Users/<USER>';
import 'package:api_inspection/generic/AppStyle.dart';
import 'package:api_inspection/generic/AppRoot.dart';

class TeamAppScaffold extends StatefulWidget {
  const TeamAppScaffold({Key? key}) : super(key: key);

  @override
  _TeamAppScaffoldState createState() => _TeamAppScaffoldState();
}

class _TeamAppScaffoldState extends State<TeamAppScaffold> {
  CustomNavigator? projectsNavigator;
  final GlobalKey<NavigatorState> projectsNavKey = GlobalKey<NavigatorState>();
  CustomNavigator? tasksNavigator;
  final GlobalKey<NavigatorState> tasksNavKey = GlobalKey<NavigatorState>();

  CustomNavigator? assetsNavigator;
  final GlobalKey<NavigatorState> assetsNavKey = GlobalKey<NavigatorState>();

  CustomNavigator? clientsNavigator;
  final GlobalKey<NavigatorState> clientsNavKey = GlobalKey<NavigatorState>();

  CustomNavigator? usersNavigator;
  final GlobalKey<NavigatorState> usersNavKey = GlobalKey<NavigatorState>();

  CustomNavigator? infoNavigator;
  final GlobalKey<NavigatorState> infoNavKey = GlobalKey<NavigatorState>();

  int _selectedIndex = 0;

  _TeamAppScaffoldState();

  @override
  void dispose() {
    var root = APMRoot.global;
    root.queries.selectedProjects.selectedProjectsListener
        .addListener(onProjectsUpdated);

    super.dispose();
  }

  void onProjectsUpdated() {
    setState(() {});
  }

  bool _isinit = false;
  void init(BuildContext context) {
    if (_isinit) return;
    _isinit = true;

    var root = APMRoot.global;
    root.queries.selectedProjects.selectedProjectsListener
        .addListener(onProjectsUpdated);
  }

  @override
  Widget build(BuildContext context) {
    init(context);

    GlobalKey<NavigatorState> key;

    if (_selectedIndex == 0) {
      if (tasksNavigator == null) {
        var homePage = const TasksMainPage();
        tasksNavigator = CustomNavigator(
          navigatorKey: tasksNavKey,
          key: const Key("TasksNavigator"),
          home: homePage,
          pageRoute: PageRoutes.cupertinoPageRoute,
        );
      }
      key = tasksNavKey;
    } else if (_selectedIndex == 1) {
      if (infoNavigator == null) {
        var info = const InfoPage();
        infoNavigator = CustomNavigator(
          navigatorKey: infoNavKey,
          key: const Key("InfoNavigator"),
          home: info,
          pageRoute: PageRoutes.cupertinoPageRoute,
        );
      }
      key = infoNavKey;
    } else if (_selectedIndex == 2) {
      if (usersNavigator == null) {
        var usersPage = const UsersMainPage();
        usersNavigator = CustomNavigator(
          navigatorKey: usersNavKey,
          key: const Key("UsersNavigator"),
          home: usersPage,
          pageRoute: PageRoutes.cupertinoPageRoute,
        );
      }
      key = usersNavKey;
    } else {
      throw "Unexpected index encountered";
    }

    Widget taskIcon = const Icon(Icons.pending_actions);

    List<Widget> stackWidgets = [
      tasksNavigator ?? Row(),
      infoNavigator ?? Row(),
    ];

    List<BottomNavigationBarItem> bottomBarWidgets = [
      BottomNavigationBarItem(
        label: "Dashboard",
        icon: taskIcon,
      ),
      const BottomNavigationBarItem(label: "Other", icon: Icon(Icons.dehaze)),
    ];

    if (AppRoot.global().currentUser?.role == "admin") {
      stackWidgets.add(usersNavigator ?? Row());

      bottomBarWidgets.add(const BottomNavigationBarItem(
        label: "Accounts",
        icon: Icon(Icons.people),
      ));
    }

    return WillPopScope(
        onWillPop: () async => await onWillPop(key),
        child: Scaffold(
          backgroundColor: const Color.fromARGB(255, 24, 28, 32),
          body: IndexedStack(
            index: _selectedIndex,
            children: stackWidgets,
          ),
          bottomNavigationBar: BottomNavigationBar(
            backgroundColor: const Color.fromARGB(255, 41, 45, 52),
            type: BottomNavigationBarType.fixed,
            currentIndex: _selectedIndex,
            selectedItemColor: Colors.blue,
            unselectedItemColor: Colors.white70,
            selectedFontSize: 13,
            unselectedFontSize: 13,
            iconSize: AppStyle.global.iconSizeMedium,
            onTap: (indexTapped) {
              _navigationBarTapped(indexTapped);
            },
            items: bottomBarWidgets,
          ),
        ));
  }

  Future<bool> onWillPop(GlobalKey<NavigatorState> key) async {
    var state = key.currentState;
    if (state == null) {
      return false;
    }
    await state.maybePop();

    return false;
  }

  void showErrorPrompt(String message) {
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: const Color.fromARGB(255, 41, 45, 52),
            title: Row(
              children: [
                Container(
                    margin: const EdgeInsets.all(5),
                    child:
                        const Icon(Icons.error, color: Colors.red, size: 32)),
                const Text(
                  'Error',
                  style: TextStyle(color: Colors.white),
                )
              ],
            ),
            content: Text(message, style: const TextStyle(color: Colors.white)),
            actions: [
              ElevatedButton(
                child: const Text('Ok', style: TextStyle(color: Colors.white)),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        });
  }

  void _navigationBarTapped(int index) {
    setState(() {
      if (_selectedIndex == index) {
        if (index == 0) {
          var tasksState = tasksNavKey.currentState;
          if (tasksState != null) {
            tasksState.popUntil((r) => r.isFirst);
          }
        } else if (index == 1) {
          var infoState = infoNavKey.currentState;
          if (infoState != null) {
            infoState.popUntil((r) => r.isFirst);
          }
        } else if (index == 2) {
          var usersState = usersNavKey.currentState;
          if (usersState != null) {
            usersState.popUntil((r) => r.isFirst);
          }
        }
      }
      _selectedIndex = index;
    });
  }
}
