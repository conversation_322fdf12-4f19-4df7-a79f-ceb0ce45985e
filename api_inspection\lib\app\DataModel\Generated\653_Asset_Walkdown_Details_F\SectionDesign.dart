//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'SectionInspectionOpenings.dart';
import 'SectionInspection.dart';
import 'SectionDataPlate.dart';
import 'SectionManufacturerFabricator.dart';

// ignore: camel_case_types
class SectionDesign extends DataModelSection {
  @override
  String getDisplayName() => "Design";
  SectionDesign(DataModelItem? parent)
      : super(parent: parent, sectionName: "Design");

  // ignore: non_constant_identifier_names
  late StringAttribute attributeDesign_Code = StringAttribute(
      parent: this,
      displayName: "Design Code",
      databaseName: "653AW_Q106",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeCode_Year = StringAttribute(
      parent: this,
      displayName: "Code Year",
      databaseName: "653AW_Q107",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeAddendum = StringAttribute(
      parent: this,
      displayName: "Addendum",
      databaseName: "653AW_Q108",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeMaximum_Fill_Height = DoubleAttribute(
    parent: this,
    displayName: "Maximum Fill Height",
    databaseName: "653AW_Q109",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "ft",
  );

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeDiameter = DoubleAttribute(
    parent: this,
    displayName: "Diameter",
    databaseName: "653AW_Q110",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "ft",
  );

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeHeight = DoubleAttribute(
    parent: this,
    displayName: "Height",
    databaseName: "653AW_Q111",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "ft",
  );

  // ignore: non_constant_identifier_names
  late DoubleAttribute attributeTank_Volume_in_BBL = DoubleAttribute(
    parent: this,
    displayName: "Tank Volume in BBL",
    databaseName: "653AW_Q112",
    areCommentsRequired: false,
    allowNegatives: true,
    displayUnit: "BBL",
  );

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeConstruction_Method =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: true,
          displayName: "Construction Method",
          databaseName: "653AW_Q113",
          availableOptions: [
        PredefinedValueOption("Welded", null, isCommentRequired: false),
        PredefinedValueOption("Bolted", null, isCommentRequired: false),
        PredefinedValueOption("Riveted", null, isCommentRequired: false),
        PredefinedValueOption("Fiberglass", null, isCommentRequired: false),
        PredefinedValueOption("Plastic", null, isCommentRequired: false),
        PredefinedValueOption("Wood", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeOrientation = PredefinedValueAttribute(
      parent: this,
      hasOtherOption: false,
      displayName: "Orientation",
      databaseName: "653AW_Q114",
      availableOptions: [
        PredefinedValueOption("Horizontal", null, isCommentRequired: false),
        PredefinedValueOption("Vertical", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeRT = PredefinedValueAttribute(
      parent: this,
      hasOtherOption: false,
      displayName: "RT",
      databaseName: "653AW_Q115",
      availableOptions: [
        PredefinedValueOption("Spot", null, isCommentRequired: false),
        PredefinedValueOption("Full", null, isCommentRequired: false),
        PredefinedValueOption("None", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late DateAttribute attributeInstallation_Date = DateAttribute(
      parent: this,
      displayName: "Installation Date",
      databaseName: "653AW_Q116",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late DateAttribute attributeIn_service_Date = DateAttribute(
      parent: this,
      displayName: "In-service Date",
      databaseName: "653AW_Q117",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributePID_Number = StringAttribute(
      parent: this,
      displayName: "P&ID Number",
      databaseName: "653AW_Q118",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeConstructionDesign_Drawing_Number =
      StringAttribute(
          parent: this,
          displayName: "Construction/Design Drawing Number",
          databaseName: "653AW_Q119",
          areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeLowest_Flange_Rating =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Lowest Flange Rating",
          databaseName: "653AW_Q120",
          availableOptions: [
        PredefinedValueOption("150", null, isCommentRequired: false),
        PredefinedValueOption("300", null, isCommentRequired: false),
        PredefinedValueOption("400", null, isCommentRequired: false),
        PredefinedValueOption("600", null, isCommentRequired: false),
        PredefinedValueOption("900", null, isCommentRequired: false),
        PredefinedValueOption("1500", null, isCommentRequired: false),
        PredefinedValueOption("2500", null, isCommentRequired: false),
      ]);

// ignore: non_constant_identifier_names
  late SectionInspection sectionInspection = SectionInspection(this);
  // ignore: non_constant_identifier_names
  late SectionDataPlate sectionDataPlate = SectionDataPlate(this);
  // ignore: non_constant_identifier_names
  late SectionManufacturerFabricator sectionManufacturerFabricator =
      SectionManufacturerFabricator(this);

// ignore: non_constant_identifier_names
  late DataModelCollection<SectionInspectionOpenings>
      sectionInspectionOpenings =
      DataModelCollection<SectionInspectionOpenings>("Inspection Openings",
          (parent, entry) {
    return SectionInspectionOpenings(entry.key, sectionInspectionOpenings);
  }, this);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      sectionInspectionOpenings,
      sectionInspection,
      sectionDataPlate,
      sectionManufacturerFabricator,
      attributeDesign_Code,
      attributeCode_Year,
      attributeAddendum,
      attributeMaximum_Fill_Height,
      attributeDiameter,
      attributeHeight,
      attributeTank_Volume_in_BBL,
      attributeConstruction_Method,
      attributeOrientation,
      attributeRT,
      attributeInstallation_Date,
      attributeIn_service_Date,
      attributePID_Number,
      attributeConstructionDesign_Drawing_Number,
      attributeLowest_Flange_Rating,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionDesign";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
