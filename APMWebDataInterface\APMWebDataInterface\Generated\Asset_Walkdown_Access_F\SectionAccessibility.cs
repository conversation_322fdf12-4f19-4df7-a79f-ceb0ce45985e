//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionAsset_Walkdown_Access_F
{
  public class SectionAccessibility : DataModelItem {

    public override String DisplayName { 
      get {
        return "Accessibility";
      }
    }

    #region [-- Sub Sections --]

    #region [-- SectionAerialLiftRequirements --]
    private SectionAerialLiftRequirements _sectionAerialLiftRequirements;
    public SectionAerialLiftRequirements sectionAerialLiftRequirements {
        get {
            if (_sectionAerialLiftRequirements == null) {
               _sectionAerialLiftRequirements = new SectionAerialLiftRequirements(this);
            }

            return _sectionAerialLiftRequirements;
        }
    }
    #endregion [-- SectionAerialLiftRequirements --]
    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributeFixed_Equipment_LaddersStairwaysPlatforms_Installed;
    public PredefinedValueAttribute attributeAll_components_under_4_ft_in_height;
    public MultiPredefinedValueAttribute attributeLadder_Requirements;
    public PredefinedValueAttribute attributeScaffolding_required;
    public PredefinedValueAttribute attributeRope_access_required;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionAccessibility";

    public SectionAccessibility(DataModelItem parent) : base(parent)
    {
            
        attributeFixed_Equipment_LaddersStairwaysPlatforms_Installed = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Fixed Equipment Ladders/Stairways/Platforms Installed?", databaseName: "AWA_Q205"); 
     
        attributeAll_components_under_4_ft_in_height = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "All components under 4 ft in height?", databaseName: "AWA_Q206"); 
     
        attributeLadder_Requirements = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Step Ladder", null),
          new PredefinedValueOption("Extension Ladder", null)
        }, true, this, "Ladder Requirements", databaseName: "AWA_Q207"); 
     
        attributeScaffolding_required = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Scaffolding required?", databaseName: "AWA_Q240"); 
     
        attributeRope_access_required = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Rope access required?", databaseName: "AWA_Q241"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           sectionAerialLiftRequirements,
           attributeFixed_Equipment_LaddersStairwaysPlatforms_Installed,
           attributeAll_components_under_4_ft_in_height,
           attributeLadder_Requirements,
           attributeScaffolding_required,
           attributeRope_access_required,
        };
    }
  }
}
