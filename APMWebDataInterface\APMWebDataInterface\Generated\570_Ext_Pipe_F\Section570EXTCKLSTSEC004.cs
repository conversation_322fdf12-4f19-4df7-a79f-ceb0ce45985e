//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section570_Ext_Pipe_F
{
  public class Section570EXTCKLSTSEC004 : DataModelItem {

    public override String DisplayName { 
      get {
        return "PIPING AND COMPONENTS";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributeWas_the_piping_in_operation_during_this_inspection;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC004Q002;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC004Q003;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC004Q004;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC004Q005;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC004Q006;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC004Q007;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC004Q008;
    public PredefinedValueAttribute attributeWas_there_evidence_of_piping_components_swaying;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC004Q010;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC004Q011;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC004Q012;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC004Q013;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC004Q014;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC004Q015;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC004Q016;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC004Q017;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC004Q018;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC004Q019;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC004Q020;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC004Q021;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC004Q022;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC004Q023;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section570EXTCKLSTSEC004";

    public Section570EXTCKLSTSEC004(DataModelItem parent) : base(parent)
    {
            
        attributeWas_the_piping_in_operation_during_this_inspection = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was the piping in operation during this inspection:", databaseName: "570_EXT_CKLST_SEC004_Q001"); 
     
        attribute570EXTCKLSTSEC004Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was the piping operating within its design parameters:  (i.e. temperature and pressure)", databaseName: "570_EXT_CKLST_SEC004_Q002"); 
     
        attribute570EXTCKLSTSEC004Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is installed piping in accordance with owner / user design specification:", databaseName: "570_EXT_CKLST_SEC004_Q003"); 
     
        attribute570EXTCKLSTSEC004Q004 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are installed flanges in accordance with owners / users piping design specifications:", databaseName: "570_EXT_CKLST_SEC004_Q004"); 
     
        attribute570EXTCKLSTSEC004Q005 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are installed fasteners (bolting) in accordance with owners / users piping design specifications:", databaseName: "570_EXT_CKLST_SEC004_Q005"); 
     
        attribute570EXTCKLSTSEC004Q006 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is a lack of engagement of more than ~1 thread present on any bolting fastener:", databaseName: "570_EXT_CKLST_SEC004_Q006"); 
     
        attribute570EXTCKLSTSEC004Q007 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are threaded  connections in accordance with owner/ users piping design specifications:", databaseName: "570_EXT_CKLST_SEC004_Q007"); 
     
        attribute570EXTCKLSTSEC004Q008 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was evidence of vibration present within the inspected piping:", databaseName: "570_EXT_CKLST_SEC004_Q008"); 
     
        attributeWas_there_evidence_of_piping_components_swaying = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was there evidence of piping components swaying:", databaseName: "570_EXT_CKLST_SEC004_Q009"); 
     
        attribute570EXTCKLSTSEC004Q010 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are piping joints misaligned:  (welded, flanged, or threaded joints)", databaseName: "570_EXT_CKLST_SEC004_Q010"); 
     
        attribute570EXTCKLSTSEC004Q011 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Erosion", null),
          new PredefinedValueOption("Yes: Pitting", null)
        }, false, this, "Were any corrosion, erosion, or pitting cells noted on the piping or welds:", databaseName: "570_EXT_CKLST_SEC004_Q011"); 
     
        attribute570EXTCKLSTSEC004Q012 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Were any crack like indications noted on the piping or welds:", databaseName: "570_EXT_CKLST_SEC004_Q012"); 
     
        attribute570EXTCKLSTSEC004Q013 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Active Leakage", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Evidence of Prior Leakage", null)
        }, false, this, "Was evidence of prior or active leakage noted to be originating from the piping or welded connections:", databaseName: "570_EXT_CKLST_SEC004_Q013"); 
     
        attribute570EXTCKLSTSEC004Q014 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Active Leakage", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Evidence of Prior Leakage", null)
        }, false, this, "Was evidence of prior or active leakage noted to be originating from flanged joint connections:", databaseName: "570_EXT_CKLST_SEC004_Q014"); 
     
        attribute570EXTCKLSTSEC004Q015 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are installed flange gaskets in accordance with owner/ users piping design specifications:", databaseName: "570_EXT_CKLST_SEC004_Q015"); 
     
        attribute570EXTCKLSTSEC004Q016 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are flange gaskets installed properly and in acceptable condition for continued service:", databaseName: "570_EXT_CKLST_SEC004_Q016"); 
     
        attribute570EXTCKLSTSEC004Q017 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Active Leakage", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Evidence of Prior Leakage", null)
        }, false, this, "Was evidence of prior or active leakage noted to be originating from threaded connections:", databaseName: "570_EXT_CKLST_SEC004_Q017"); 
     
        attribute570EXTCKLSTSEC004Q018 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was corrosion found to be present at flanged joint connections:", databaseName: "570_EXT_CKLST_SEC004_Q018"); 
     
        attribute570EXTCKLSTSEC004Q019 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Stretching", null)
        }, false, this, "Was corrosion or stretching found to be present of flange bolting fasteners:", databaseName: "570_EXT_CKLST_SEC004_Q019"); 
     
        attribute570EXTCKLSTSEC004Q020 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Dripping Liquids", null),
          new PredefinedValueOption("Yes: Impingement", null),
          new PredefinedValueOption("Yes: Spilled Liquids", null)
        }, false, this, "Are there locations of steam impingement, dripping fluid, or spilled liquids:", databaseName: "570_EXT_CKLST_SEC004_Q020"); 
     
        attribute570EXTCKLSTSEC004Q021 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Buckling", null),
          new PredefinedValueOption("Yes: Bulging", null),
          new PredefinedValueOption("Yes: Hot Spots", null),
          new PredefinedValueOption("Yes: Metallurgical Changes", null),
          new PredefinedValueOption("Yes: Spalling", null)
        }, false, this, "Are there indications of bulging, scaling, buckling, hot spots, or metallurgical changes:", databaseName: "570_EXT_CKLST_SEC004_Q021"); 
     
        attribute570EXTCKLSTSEC004Q022 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Does the piping currently have any field modifications or temporary repairs that were not previously recorded:", databaseName: "570_EXT_CKLST_SEC004_Q022"); 
     
        attribute570EXTCKLSTSEC004Q023 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the piping and associated components in acceptable condition for continued service:", databaseName: "570_EXT_CKLST_SEC004_Q023"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeWas_the_piping_in_operation_during_this_inspection,
           attribute570EXTCKLSTSEC004Q002,
           attribute570EXTCKLSTSEC004Q003,
           attribute570EXTCKLSTSEC004Q004,
           attribute570EXTCKLSTSEC004Q005,
           attribute570EXTCKLSTSEC004Q006,
           attribute570EXTCKLSTSEC004Q007,
           attribute570EXTCKLSTSEC004Q008,
           attributeWas_there_evidence_of_piping_components_swaying,
           attribute570EXTCKLSTSEC004Q010,
           attribute570EXTCKLSTSEC004Q011,
           attribute570EXTCKLSTSEC004Q012,
           attribute570EXTCKLSTSEC004Q013,
           attribute570EXTCKLSTSEC004Q014,
           attribute570EXTCKLSTSEC004Q015,
           attribute570EXTCKLSTSEC004Q016,
           attribute570EXTCKLSTSEC004Q017,
           attribute570EXTCKLSTSEC004Q018,
           attribute570EXTCKLSTSEC004Q019,
           attribute570EXTCKLSTSEC004Q020,
           attribute570EXTCKLSTSEC004Q021,
           attribute570EXTCKLSTSEC004Q022,
           attribute570EXTCKLSTSEC004Q023,
        };
    }
  }
}
