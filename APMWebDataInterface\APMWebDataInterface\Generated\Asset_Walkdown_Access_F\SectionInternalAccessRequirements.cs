//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionAsset_Walkdown_Access_F
{
  public class SectionInternalAccessRequirements : DataModelItem {

    public override String DisplayName { 
      get {
        return "Internal Access Requirements";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributeAre_there_inspection_openings;
    public MultiPredefinedValueAttribute attributeInspection_opening_Types;
    public StringAttribute attributeSize_of_all_accessible_openings;
    public MultiPredefinedValueAttribute attributeVentilation_requirements;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionInternalAccessRequirements";

    public SectionInternalAccessRequirements(DataModelItem parent) : base(parent)
    {
            
        attributeAre_there_inspection_openings = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Yes", null),
          new PredefinedValueOption("No", null)
        }, false, this, "Are there inspection openings?", databaseName: "AWA_Q311"); 
     
        attributeInspection_opening_Types = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Manway", null),
          new PredefinedValueOption("Handhole", null)
        }, true, this, "Inspection opening Types", databaseName: "AWA_Q312"); 
     
        attributeSize_of_all_accessible_openings = new StringAttribute(this, displayName: "Size of all accessible openings", databaseName: "AWA_Q313"); 
     
        attributeVentilation_requirements = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Open to Atmosphere", null),
          new PredefinedValueOption("Air Mover", null)
        }, true, this, "Ventilation requirements", databaseName: "AWA_Q314"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeAre_there_inspection_openings,
           attributeInspection_opening_Types,
           attributeSize_of_all_accessible_openings,
           attributeVentilation_requirements,
        };
    }
  }
}
