import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class LeakReportPhoto extends DataModelCollectionItem {
  @override
  String getDisplayName() => "Leak Report Photo";

  LeakReportPhoto(DataModelItem parent, String id) : super(id, parent);

  late PhotoAttribute photos =
      PhotoAttribute(parent: this, displayName: "Photos");

  late StringAttribute description =
      StringAttribute(parent: this, displayName: "Description");
  late StringAttribute comment =
      StringAttribute(parent: this, displayName: "Comment");
  late LocationAttribute areaOfInterestCoordinate = LocationAttribute(
      parent: this, displayName: "Area of Interest GIS position");
  late LocationAttribute upstreamTieInCoordinate = LocationAttribute(
      parent: this, displayName: "Upstream tie-in GIS location");
  late LocationAttribute downstreamTieInCoordinate = LocationAttribute(
      parent: this, displayName: "Downstream tie-in GIS location");
  late DoubleAttribute utHighMeasurment = DoubleAttribute(
      parent: this, displayName: "UT High Measurement", allowNegatives: true);
  late DoubleAttribute utLowMeasurment = DoubleAttribute(
      parent: this, displayName: "UT Low Measurement", allowNegatives: true);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      photos,
      description,
      comment,
      areaOfInterestCoordinate,
      upstreamTieInCoordinate,
      downstreamTieInCoordinate,
      utHighMeasurment,
      utLowMeasurment
    ]);
    return children;
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
