//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionRepairsandAlterations.dart';
import 'SectionRepairsPage.dart';

// ignore: camel_case_types
class SectionRepairsandAlterationsPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionRepairsandAlterations sectionRepairsandAlterations;

  const SectionRepairsandAlterationsPage(this.sectionRepairsandAlterations,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionRepairsandAlterationsPageState();
  }
}

class _SectionRepairsandAlterationsPageState
    extends State<SectionRepairsandAlterationsPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionRepairsandAlterations,
        title: "Repairs and Alterations",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionRepairsandAlterations
                      .attributeDoes_the_asset_have_a_repair_or_alteration_plate
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionRepairsandAlterations
                      .attributeRepairAlteration_Plates_Legible
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section:
                            widget.sectionRepairsandAlterations.sectionRepairs,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => SectionRepairsPage(
                                          widget.sectionRepairsandAlterations
                                              .sectionRepairs)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                ]),
          );
        });
  }
}
