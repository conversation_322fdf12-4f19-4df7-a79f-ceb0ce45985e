import 'dart:async';

import 'package:api_inspection/app/APMRoot.dart';
import 'package:api_inspection/environment.dart';
import 'package:api_inspection/generic/CustomNavigator/CustomNavigator.dart';
import 'package:api_inspection/generic/MediaControls/MediaSynchronizer.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/UI/Login/LoginPage.dart';
import 'package:api_inspection/generic/AppStyle.dart';
import 'package:api_inspection/generic/AppRoot.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

class MyApp extends StatelessWidget {
  final Environment environment;

  final Widget Function(BuildContext) buildMainWidget;

  const MyApp(
      {Key? key, required this.buildMainWidget, required this.environment})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'APM',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home:
          MyAppRoot(environment: environment, child: MainPage(buildMainWidget)),
    );
  }
}

class MyAppRoot extends StatefulWidget {
  final Widget child;
  final Environment environment;

  MyAppRoot({Key? key, required this.child, required this.environment})
      : super(key: key) {
    if (!kIsWeb) {
      IMediaSynchronizer.getMediaSynchronizer().ensureInitialization();
    }
    AppRoot.global().environment = environment;
  }

  @override
  State<StatefulWidget> createState() {
    return MyAppRootState();
  }
}

class MyAppRootState extends State<MyAppRoot> {
  CustomNavigator? navigator;
  final GlobalKey<NavigatorState> navKey = GlobalKey<NavigatorState>();

  @override
  initState() {
    AppRoot.global().mainNavKey = navKey;
    AppRoot.global().mainBC = context;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return CustomNavigator(
        navigatorKey: navKey,
        key: const Key("MainNavigator"),
        home: widget.child,
        pageRoute: PageRoutes.materialPageRoute);
  }
}

class MainPage extends StatefulWidget {
  final Widget Function(BuildContext) buildMainWidget;

  const MainPage(this.buildMainWidget, {Key? key}) : super(key: key);

  @override
  _MainPageState createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  @override
  void dispose() {
    super.dispose();
  }

  @override
  void initState() {
    AppRoot.global().loginPageRootBC = context;
    AppRoot.global().loginPagePopCondition = pushUnitlCondition;
    init();
    super.initState();
  }

  bool pushUnitlCondition(dynamic obj) {
    return obj is MyAppRoot;
  }

  Future<void> init() async {
    Timer.periodic(const Duration(milliseconds: 5), (timer) {
      var appHeight = MediaQuery.of(context).size.height;
      var appWidth = MediaQuery.of(context).size.width;
      if (appHeight > 0 && appWidth > 0) {
        timer.cancel();
        finishInit();
      }
    });
  }

  Future<void> finishInit() async {
    var appHeight = MediaQuery.of(context).size.height;
    var appWidth = MediaQuery.of(context).size.width;

    AppStyle.global.init(appHeight, appWidth);
    await AppRoot.global().initPreferences();

    initLogin(buildContext) async {
      var email = FirebaseAuth.instance.currentUser?.email;
      if (email != null) {
        FirebaseCrashlytics.instance.setUserIdentifier(email);
        FirebaseAnalytics.instance.setUserId(id: email);
        FirebaseAnalytics.instance.setUserProperty(name: "email", value: email);
      }
      APMRoot.global.initQueries();
      Navigator.pushAndRemoveUntil(buildContext,
          MaterialPageRoute(builder: widget.buildMainWidget), (_) => false);
    }

    AppRoot.global().onInitialLoginSuccessful = initLogin;
    AppRoot.global().onLogOut = () async {
      await APMRoot.global.resetState();
    };

    Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(
            builder: (context) => LoginPage(onLoginSuccessful: initLogin)),
        pushUnitlCondition);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const Text(
            "Asset Performance Mangament",
            style: TextStyle(fontSize: 20),
          ),
          toolbarHeight: 55,
        ),
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        body: Container(
            alignment: Alignment.center,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: const [
                Text(
                  "Loading",
                  style: TextStyle(color: Colors.white, fontSize: 26),
                ),
                Text(
                  "please wait",
                  style: TextStyle(color: Colors.white, fontSize: 18),
                )
              ],
            )));
  }
}
