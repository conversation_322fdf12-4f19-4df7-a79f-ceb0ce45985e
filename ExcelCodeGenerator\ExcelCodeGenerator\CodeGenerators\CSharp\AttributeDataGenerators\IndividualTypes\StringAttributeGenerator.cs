﻿using CheckListGen;

namespace ExcelCodeGenerator.CodeGenerators.CSharp
{
    public class StringAttributeGenerator : IAttributeGenerator
    {
        public string BuildInitialization(Question question)
        {
            return
                @"
        " + question.DartVariableName + @" = new StringAttribute(this, displayName: """ + question.DisplayText +
                @""", databaseName: """ + question.DataName + @"""); ";
        }

        public string BuildDeclaration(Question question)
        {
            return
                @"public StringAttribute " + question.DartVariableName + ";";
        }
    }
}