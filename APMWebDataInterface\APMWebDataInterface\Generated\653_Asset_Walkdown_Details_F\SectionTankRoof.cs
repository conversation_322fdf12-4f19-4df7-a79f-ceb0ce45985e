//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section653_Asset_Walkdown_Details_F
{
  public class SectionTankRoof : DataModelCollectionItem {

    public override String DisplayName { 
      get {
        return "Tank Roof";
      }
    }
    
    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public MultiPredefinedValueAttribute attributeType;
    public StringAttribute attributeMaterial_Spec_and_Grade;
    public DoubleAttribute attributeNominal_thickness_roof;
    public DoubleAttribute attributeCorrosion_Allowance;

    #endregion [-- Attributes --]

    public SectionTankRoof(String id, DataModelItem parent) : base(id, parent)
    {
            
        attributeType = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Fixed", null),
          new PredefinedValueOption("Internal Floating", null),
          new PredefinedValueOption("External Floating", null)
        }, false, this, "Type", databaseName: "653AW_Q441"); 
     
        attributeMaterial_Spec_and_Grade = new StringAttribute(this, displayName: "Material Spec and Grade", databaseName: "653AW_Q442"); 
     
        attributeNominal_thickness_roof = new DoubleAttribute(this, displayName: "Nominal thickness (roof)", databaseName: "653AW_Q443", areCommentsRequired: false, displayUnit: "in", allowNegatives: true); 
     
        attributeCorrosion_Allowance = new DoubleAttribute(this, displayName: "Corrosion Allowance", databaseName: "653AW_Q444", areCommentsRequired: false, displayUnit: "in", allowNegatives: true); 
    }   
    
    public override DataModelItem[] GetChildren() {
      var parentChildren = base.GetChildren();

      return parentChildren.Concat(new DataModelItem[] { 
        attributeType,
           attributeMaterial_Spec_and_Grade,
           attributeNominal_thickness_roof,
           attributeCorrosion_Allowance,
      }).ToArray();
    }
  }
}
