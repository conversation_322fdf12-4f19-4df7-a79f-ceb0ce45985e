//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionFlexible_Task_Data_F
{
  public class SectionFlexible_Task_Data_F : DataModelItem {

    public override String DisplayName { 
      get {
        return "Flexible-Task-Data-F";
      }
    }

    #region [-- Sub Sections --]

    #region [-- SectionTaskDetails --]
    private SectionTaskDetails _sectionTaskDetails;
    public SectionTaskDetails sectionTaskDetails {
        get {
            if (_sectionTaskDetails == null) {
               _sectionTaskDetails = new SectionTaskDetails(this);
            }

            return _sectionTaskDetails;
        }
    }
    #endregion [-- SectionTaskDetails --]
    
    #region [-- SectionTaskPhotos --]
    private DataModelCollection<SectionTaskPhotos> _sectionTaskPhotos;
    public DataModelCollection<SectionTaskPhotos> sectionTaskPhotos {
        get {
            if (_sectionTaskPhotos == null) {
              _sectionTaskPhotos = new DataModelCollection<SectionTaskPhotos>("TaskPhotos", (parent, entry) => {
                 return new SectionTaskPhotos(entry.Key, _sectionTaskPhotos);
              }, (parent, id) => {
                return new SectionTaskPhotos(id, _sectionTaskPhotos);
              }, this);
            }

            return _sectionTaskPhotos;
        }
    }
    #endregion [-- SectionTaskPhotos --]
    
    #region [-- SectionAttachments --]
    private SectionAttachments _sectionAttachments;
    public SectionAttachments sectionAttachments {
        get {
            if (_sectionAttachments == null) {
               _sectionAttachments = new SectionAttachments(this);
            }

            return _sectionAttachments;
        }
    }
    #endregion [-- SectionAttachments --]
    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionFlexible_Task_Data_F";

    public SectionFlexible_Task_Data_F(DataModelItem parent) : base(parent)
    {
            
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           sectionTaskDetails,
           sectionTaskPhotos,
           sectionAttachments,
        };
    }
  }
}
