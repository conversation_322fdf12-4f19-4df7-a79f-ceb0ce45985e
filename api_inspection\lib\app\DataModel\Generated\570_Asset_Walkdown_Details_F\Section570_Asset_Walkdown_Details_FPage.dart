//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'Section570_Asset_Walkdown_Details_F.dart';
import 'SectionIdentificationPage.dart';
import 'SectionGeneralInformationPage.dart';
import 'SectionOperatingDesignConditionsPage.dart';
import 'SectionComponentsPage.dart';

// ignore: camel_case_types
class Section570_Asset_Walkdown_Details_FPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final Section570_Asset_Walkdown_Details_F section570_Asset_Walkdown_Details_F;

  const Section570_Asset_Walkdown_Details_FPage(
      this.section570_Asset_Walkdown_Details_F,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _Section570_Asset_Walkdown_Details_FPageState();
  }
}

class _Section570_Asset_Walkdown_Details_FPageState
    extends State<Section570_Asset_Walkdown_Details_FPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.section570_Asset_Walkdown_Details_F,
        title: "570 Asset Walkdown-Details",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section570_Asset_Walkdown_Details_F
                            .sectionIdentification,
                        onPressed: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      SectionIdentificationPage(widget
                                          .section570_Asset_Walkdown_Details_F
                                          .sectionIdentification))).then(
                              (value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section570_Asset_Walkdown_Details_F
                            .sectionGeneralInformation,
                        onPressed: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      SectionGeneralInformationPage(widget
                                          .section570_Asset_Walkdown_Details_F
                                          .sectionGeneralInformation))).then(
                              (value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section570_Asset_Walkdown_Details_F
                            .sectionOperatingDesignConditions,
                        onPressed: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      SectionOperatingDesignConditionsPage(widget
                                          .section570_Asset_Walkdown_Details_F
                                          .sectionOperatingDesignConditions))).then(
                              (value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.section570_Asset_Walkdown_Details_F
                            .sectionComponents,
                        onPressed: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => SectionComponentsPage(
                                      widget.section570_Asset_Walkdown_Details_F
                                          .sectionComponents))).then(
                              (value) => setState(() {}));
                        }),
                  ),
                ]),
          );
        });
  }
}
