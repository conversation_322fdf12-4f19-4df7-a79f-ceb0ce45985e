//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class Section510INT_PVCKLSTSEC007 extends DataModelSection {
  @override
  String getDisplayName() => "BONNET - HEX ONLY";
  Section510INT_PVCKLSTSEC007(DataModelItem? parent)
      : super(parent: parent, sectionName: "BONNET - HEX ONLY");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510INT_PVCKLSTSEC007Q001 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Were any corrosion cells, impingement or pitting noted on the bonnet surfaces: (The dimensions and locations of any damage shall be recorded)",
          databaseName: "510_INT-PV_CKLST_SEC007_Q001",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Corrosion", null, isCommentRequired: true),
        PredefinedValueOption("Yes: Impingement", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Pitting", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510INT_PVCKLSTSEC007Q002 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Was any mechanical damage or cracking noted on the bonnet surfaces: (The dimensions and location of any damage shall be recorded)",
          databaseName: "510_INT-PV_CKLST_SEC007_Q002",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption(
            "Yes: Combination of Issues ( for Dimensions)", null,
            isCommentRequired: false),
        PredefinedValueOption("Yes: Cracking ( for Dimensions)", null,
            isCommentRequired: false),
        PredefinedValueOption("Yes: Mechanical Damage ( for Dimensions)", null,
            isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510INT_PVCKLSTSEC007Q003 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Were there indications of weld corrosion or cracking in the welds or Heat Affected Zones of the bonnet pressure retaining welds:",
          databaseName: "510_INT-PV_CKLST_SEC007_Q003",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption(
            "Yes: Combination of Issues ( for Dimensions)", null,
            isCommentRequired: false),
        PredefinedValueOption("Yes: Weld Corrosion ( for Dimensions)", null,
            isCommentRequired: false),
        PredefinedValueOption("Yes: Weld Cracking ( for Dimensions)", null,
            isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attribute510INT_PVCKLSTSEC007Q004 =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName:
              "Does the bonnet have any deformations or hot spots: (Bulges, Blisters, Dimpling)",
          databaseName: "510_INT-PV_CKLST_SEC007_Q004",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes: Combination of Issues", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Deformation", null,
            isCommentRequired: true),
        PredefinedValueOption("Yes: Hot Spots", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC007Q005 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are bonnet penetrations and adjacent areas in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC007_Q005",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC007Q006 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Was any mechanical damage or impacts from objects noted on bonnet:",
          databaseName: "510_INT-PV_CKLST_SEC007_Q006",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeIs_the_bonnet_to_shell_attachment_achieved_via_welding =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the bonnet to shell attachment achieved via welding:",
          databaseName: "510_INT-PV_CKLST_SEC007_Q007",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC007Q008 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the bonnet to shell weld in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC007_Q008",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC007Q009 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the bonnet to shell attachment achieved via flanged connection(s):",
          databaseName: "510_INT-PV_CKLST_SEC007_Q009",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC007Q010 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the bonnet to shell  flanged connection(s) in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC007_Q010",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC007Q011 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the bonnet of the asset in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC007_Q011",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attribute510INT_PVCKLSTSEC007Q001,
      attribute510INT_PVCKLSTSEC007Q002,
      attribute510INT_PVCKLSTSEC007Q003,
      attribute510INT_PVCKLSTSEC007Q004,
      attribute510INT_PVCKLSTSEC007Q005,
      attribute510INT_PVCKLSTSEC007Q006,
      attributeIs_the_bonnet_to_shell_attachment_achieved_via_welding,
      attribute510INT_PVCKLSTSEC007Q008,
      attribute510INT_PVCKLSTSEC007Q009,
      attribute510INT_PVCKLSTSEC007Q010,
      attribute510INT_PVCKLSTSEC007Q011,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section510INT_PVCKLSTSEC007";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
