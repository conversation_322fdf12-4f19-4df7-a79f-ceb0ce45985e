import 'package:flutter/foundation.dart';
import 'package:api_inspection/environment.dart';
import 'package:shared_preferences/shared_preferences.dart';

class EnvironmentConfig {
  static final EnvironmentConfig _instance = EnvironmentConfig._internal();
  
  factory EnvironmentConfig() {
    return _instance;
  }
  
  EnvironmentConfig._internal();
  
  // Environment
  Environment _environment = EnvironmentValue.development;
  
  // Azure Configuration
  String _azureStorageConnectionString = '';
  String _azureStorageAccountName = '';
  String _azureStorageContainer = '';
  String _azureCosmosDbEndpoint = '';
  String _azureAdB2cAuthority = '';
  String _azureAdB2cClientId = '';
  String _azureAdB2cScope = '';
  String _azureAppInsightsKey = '';
  
  // Getters
  Environment get environment => _environment;
  String get azureStorageConnectionString => _azureStorageConnectionString;
  String get azureStorageAccountName => _azureStorageAccountName;
  String get azureStorageContainer => _azureStorageContainer;
  String get azureCosmosDbEndpoint => _azureCosmosDbEndpoint;
  String get azureAdB2cAuthority => _azureAdB2cAuthority;
  String get azureAdB2cClientId => _azureAdB2cClientId;
  String get azureAdB2cScope => _azureAdB2cScope;
  String get azureAppInsightsKey => _azureAppInsightsKey;
  
  // Initialize with environment-specific values
  Future<void> initialize(Environment environment) async {
    _environment = environment;
    
    // Load config based on environment
    switch (environment) {
      case EnvironmentValue.development:
        await _loadDevelopmentConfig();
        break;
      case EnvironmentValue.production:
        await _loadProductionConfig();
        break;
      default:
        await _loadDevelopmentConfig();
    }
    
    debugPrint('Environment config initialized: $environment');
  }
  
  // Load development configuration
  Future<void> _loadDevelopmentConfig() async {
    _azureStorageAccountName = 'apmstorageDev';
    _azureStorageContainer = 'inspections';
    _azureCosmosDbEndpoint = 'https://apm-cosmosdb-dev.documents.azure.com:443/';
    _azureAdB2cAuthority = 'https://apmdevb2c.b2clogin.com/apmdevb2c.onmicrosoft.com/B2C_1_signupsignin1';
    _azureAdB2cClientId = 'YOUR_DEV_CLIENT_ID';
    _azureAdB2cScope = 'https://apmdevb2c.onmicrosoft.com/api/user_impersonation';
    _azureAppInsightsKey = 'YOUR_DEV_APP_INSIGHTS_KEY';
    
    // Try to load from secure storage or shared preferences if available
    await _loadStoredConfig();
  }
  
  // Load production configuration
  Future<void> _loadProductionConfig() async {
    _azureStorageAccountName = 'apmStorageProd';
    _azureStorageContainer = 'inspections';
    _azureCosmosDbEndpoint = 'https://apm-cosmosdb-prod.documents.azure.com:443/';
    _azureAdB2cAuthority = 'https://apmprod.b2clogin.com/apmprod.onmicrosoft.com/B2C_1_signupsignin1';
    _azureAdB2cClientId = 'YOUR_PROD_CLIENT_ID';
    _azureAdB2cScope = 'https://apmprod.onmicrosoft.com/api/user_impersonation';
    _azureAppInsightsKey = 'YOUR_PROD_APP_INSIGHTS_KEY';
    
    // Try to load from secure storage or shared preferences if available
    await _loadStoredConfig();
  }
  
  // Load config from stored values (e.g., for overrides)
  Future<void> _loadStoredConfig() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Only load if values exist
      _azureStorageAccountName = prefs.getString('azure_storage_account') ?? _azureStorageAccountName;
      _azureStorageContainer = prefs.getString('azure_storage_container') ?? _azureStorageContainer;
      _azureCosmosDbEndpoint = prefs.getString('azure_cosmos_endpoint') ?? _azureCosmosDbEndpoint;
      
      // Build connection string
      _azureStorageConnectionString = 'DefaultEndpointsProtocol=https;AccountName=$_azureStorageAccountName;AccountKey=YOUR_ACCOUNT_KEY;EndpointSuffix=core.windows.net';
      
    } catch (e) {
      debugPrint('Error loading stored configuration: $e');
    }
  }
  
  // Save config overrides
  Future<void> saveConfigOverrides({
    String? azureStorageAccountName,
    String? azureStorageContainer,
    String? azureCosmosDbEndpoint,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      if (azureStorageAccountName != null) {
        await prefs.setString('azure_storage_account', azureStorageAccountName);
        _azureStorageAccountName = azureStorageAccountName;
      }
      
      if (azureStorageContainer != null) {
        await prefs.setString('azure_storage_container', azureStorageContainer);
        _azureStorageContainer = azureStorageContainer;
      }
      
      if (azureCosmosDbEndpoint != null) {
        await prefs.setString('azure_cosmos_endpoint', azureCosmosDbEndpoint);
        _azureCosmosDbEndpoint = azureCosmosDbEndpoint;
      }
      
      // Rebuild connection string
      _azureStorageConnectionString = 'DefaultEndpointsProtocol=https;AccountName=$_azureStorageAccountName;AccountKey=YOUR_ACCOUNT_KEY;EndpointSuffix=core.windows.net';
      
    } catch (e) {
      debugPrint('Error saving configuration overrides: $e');
    }
  }
}
