import 'package:api_inspection/generic/UIControls/listener.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';

class StringAttributeViewEditable extends StatefulWidget {
  final StringAttribute _attribute;
  final ListenerWrapper updateListener;
  final bool showPhotoControl;
  final bool showCommentsControl;

  const StringAttributeViewEditable(this._attribute, this.updateListener,
      {Key? key, this.showPhotoControl = true, this.showCommentsControl = true})
      : super(key: key);

  @override
  _StringAttributeViewEditableState createState() =>
      _StringAttributeViewEditableState();
}

class _StringAttributeViewEditableState
    extends State<StringAttributeViewEditable> {
  TextEditingController? _controller;
  bool initialized = false;

  void updateAttributeValue() {
    TextEditingController? controller = _controller;
    if (controller != null) {
      var batch = FirebaseFirestore.instance.batch();
      widget._attribute.setValue(controller.text, batch);
      widget._attribute.saveItem(batch);
      batch.commit();
    }
  }

  void initialize() {
    if (initialized) return;
    initialized = true;
    StringAttribute attribute = widget._attribute;
    attribute.addListener(onAttributeChanged);
  }

  void onAttributeChanged() {
    setState(() {
      TextEditingController? controller = _controller;
      if (controller != null &&
          controller.text != widget._attribute.getValue()) {
        String? attributeValue = widget._attribute.getValue();
        controller.text = attributeValue ?? "";
      }
    });
  }

  @override
  void dispose() {
    widget.updateListener.removeListener(updateAttributeValue);
    StringAttribute attribute = widget._attribute;
    attribute.removeListener(onAttributeChanged);
    _controller?.dispose();
    super.dispose();
  }

  @override
  void initState() {
    widget.updateListener.addListener(updateAttributeValue);
    super.initState();
  }

  @override
  void didUpdateWidget(covariant StringAttributeViewEditable oldWidget) {
    oldWidget.updateListener.removeListener(updateAttributeValue);
    widget.updateListener.addListener(updateAttributeValue);
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    initialize();

    _controller ??= TextEditingController(text: widget._attribute.getValue());
    return Container(
      margin: const EdgeInsets.fromLTRB(20, 10, 20, 10),
      child: Focus(
          onFocusChange: (hasFocus) {
            updateAttributeValue();
          },
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.stretch, children: [
            Text(
              widget._attribute.displayName,
              style: const TextStyle(color: Colors.white, fontSize: 16),
              textAlign: TextAlign.start,
            ),
            TextField(
              key: const ValueKey('StringTextboxField'),
              controller: _controller,
              decoration: const InputDecoration(
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.white, width: 1.0),
                ),
                border: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.white, width: 1.0),
                ),
              ),
              onSubmitted: (String value) {
                updateAttributeValue();
              },
              style: const TextStyle(color: Colors.white, fontSize: 16),
            )
          ])),
    );
  }
}
