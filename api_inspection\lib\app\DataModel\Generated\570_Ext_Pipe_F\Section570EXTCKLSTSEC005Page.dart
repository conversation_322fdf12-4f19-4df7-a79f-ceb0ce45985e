//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'Section570EXTCKLSTSEC005.dart';

// ignore: camel_case_types
class Section570EXTCKLSTSEC005Page extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final Section570EXTCKLSTSEC005 section570EXTCKLSTSEC005;

  const Section570EXTCKLSTSEC005Page(this.section570EXTCKLSTSEC005, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _Section570EXTCKLSTSEC005PageState();
  }
}

class _Section570EXTCKLSTSEC005PageState
    extends State<Section570EXTCKLSTSEC005Page> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.section570EXTCKLSTSEC005,
        title: "PRESSURE RELIEF (PRD/PRV)",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC005.attribute570EXTCKLSTSEC005Q001
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC005.attribute570EXTCKLSTSEC005Q002
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC005.attribute570EXTCKLSTSEC005Q003
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC005.attribute570EXTCKLSTSEC005Q004
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC005
                      .attributeWas_any_mechanical_damage_noted_on_the_PRD__PRV
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC005
                      .attributeIs_the_Relief_attachment_achieved_via_flanging
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC005.attribute570EXTCKLSTSEC005Q007
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC005.attribute570EXTCKLSTSEC005Q008
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC005.attribute570EXTCKLSTSEC005Q009
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC005.attribute570EXTCKLSTSEC005Q010
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC005.attribute570EXTCKLSTSEC005Q011
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC005
                      .attributeIs_the_Relief_valve_vent_piping_routed_to_a_safe_location
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC005.attribute570EXTCKLSTSEC005Q013
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC005.attribute570EXTCKLSTSEC005Q014
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC005
                      .attributeIs_the_spring_tamper_car_seal_intact
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC005.attribute570EXTCKLSTSEC005Q016
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC005
                      .attributeIs_Relief_alignment_acceptable
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC005.attribute570EXTCKLSTSEC005Q018
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC005
                      .attributeWas_any_excessive_vibration_of_the_Relief_noted
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC005
                      .attributeWas_the_rupture_device_orientation_correct
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC005
                      .attributeAre_associated_block_valves_car_sealed_in_the_open_position
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC005.attribute570EXTCKLSTSEC005Q022
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section570EXTCKLSTSEC005.attribute570EXTCKLSTSEC005Q023
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
