//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section570_Ext_Pipe_F
{
  public class Section570EXTCKLSTSEC003 : DataModelItem {

    public override String DisplayName { 
      get {
        return "STEEL SUPPORTS";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attributeIs_the_piping_properly_supported;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC003Q002;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC003Q003;
    public PredefinedValueAttribute attributeAre_pipe_rollers_shoes_or_slide_plates_restricted;
    public PredefinedValueAttribute attributeIs_there_evidence_of_piping_distortion_due_to_pipe_movement;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC003Q006;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC003Q007;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC003Q008;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC003Q009;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC003Q010;
    public PredefinedValueAttribute attributeAre_foundation_anchor_bolts_loose_or_corroded;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC003Q012;
    public PredefinedValueAttribute attributeAre_dummy_legs_retaining_moisture_or_water;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC003Q014;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC003Q015;
    public PredefinedValueAttribute attributeAre_supports_in_acceptable_condition_for_continued_service;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section570EXTCKLSTSEC003";

    public Section570EXTCKLSTSEC003(DataModelItem parent) : base(parent)
    {
            
        attributeIs_the_piping_properly_supported = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is the piping properly supported:", databaseName: "570_EXT_CKLST_SEC003_Q001"); 
     
        attribute570EXTCKLSTSEC003Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are there broken, missing or insecure pipe anchorage components:  (Brackets, U-bolts, or clamps)", databaseName: "570_EXT_CKLST_SEC003_Q002"); 
     
        attribute570EXTCKLSTSEC003Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is piping misalignment present due to an issue with piping supports:", databaseName: "570_EXT_CKLST_SEC003_Q003"); 
     
        attributeAre_pipe_rollers_shoes_or_slide_plates_restricted = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are pipe rollers, shoes, or slide plates restricted:", databaseName: "570_EXT_CKLST_SEC003_Q004"); 
     
        attributeIs_there_evidence_of_piping_distortion_due_to_pipe_movement = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is there evidence of piping distortion due to pipe movement:", databaseName: "570_EXT_CKLST_SEC003_Q005"); 
     
        attribute570EXTCKLSTSEC003Q006 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are springs cans and or pipe hangers properly adjusted: (Spring can values obtained from the index plate shall be recorded)", databaseName: "570_EXT_CKLST_SEC003_Q006"); 
     
        attribute570EXTCKLSTSEC003Q007 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are there indications of movement or deterioration of concrete footings:", databaseName: "570_EXT_CKLST_SEC003_Q007"); 
     
        attribute570EXTCKLSTSEC003Q008 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Cracking", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Distortion", null)
        }, false, this, "Is there any evidence of corrosion, distortion, and or cracking of the steel supports:", databaseName: "570_EXT_CKLST_SEC003_Q008"); 
     
        attribute570EXTCKLSTSEC003Q009 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Has the remaining thickness of corroded supporting elements been determined:", databaseName: "570_EXT_CKLST_SEC003_Q009"); 
     
        attribute570EXTCKLSTSEC003Q010 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Buckling", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Deflection", null)
        }, false, this, "Is there any evidence of corrosion, buckling or excessive deflection of the columns and or load-carrying beams:", databaseName: "570_EXT_CKLST_SEC003_Q010"); 
     
        attributeAre_foundation_anchor_bolts_loose_or_corroded = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are foundation anchor bolts loose or corroded:", databaseName: "570_EXT_CKLST_SEC003_Q011"); 
     
        attribute570EXTCKLSTSEC003Q012 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are small branch connections contacting pipe supports as a result of movement of a larger line:", databaseName: "570_EXT_CKLST_SEC003_Q012"); 
     
        attributeAre_dummy_legs_retaining_moisture_or_water = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are dummy legs retaining moisture or water:", databaseName: "570_EXT_CKLST_SEC003_Q013"); 
     
        attribute570EXTCKLSTSEC003Q014 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is contact point / touch point corrosion present at piping to support interfaces:", databaseName: "570_EXT_CKLST_SEC003_Q014"); 
     
        attribute570EXTCKLSTSEC003Q015 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are there indications of corrosion of pipe walls inside open ended Trunnion supports:", databaseName: "570_EXT_CKLST_SEC003_Q015"); 
     
        attributeAre_supports_in_acceptable_condition_for_continued_service = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are supports in acceptable condition for continued service:", databaseName: "570_EXT_CKLST_SEC003_Q016"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeIs_the_piping_properly_supported,
           attribute570EXTCKLSTSEC003Q002,
           attribute570EXTCKLSTSEC003Q003,
           attributeAre_pipe_rollers_shoes_or_slide_plates_restricted,
           attributeIs_there_evidence_of_piping_distortion_due_to_pipe_movement,
           attribute570EXTCKLSTSEC003Q006,
           attribute570EXTCKLSTSEC003Q007,
           attribute570EXTCKLSTSEC003Q008,
           attribute570EXTCKLSTSEC003Q009,
           attribute570EXTCKLSTSEC003Q010,
           attributeAre_foundation_anchor_bolts_loose_or_corroded,
           attribute570EXTCKLSTSEC003Q012,
           attributeAre_dummy_legs_retaining_moisture_or_water,
           attribute570EXTCKLSTSEC003Q014,
           attribute570EXTCKLSTSEC003Q015,
           attributeAre_supports_in_acceptable_condition_for_continued_service,
        };
    }
  }
}
