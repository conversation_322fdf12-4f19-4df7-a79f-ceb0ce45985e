﻿using System;
using APMWebDataInterface.Generated.section510_Int_PV_ALL_F;
using CommonDataInterface;

namespace APMWebDataInterface.Generated.section510_Ext_PV_ALL_F
{
  public class Section510_FullInspection : DataModelItem {

    public override String DisplayName { 
      get {
        return "510 Full Inspection";
      }
    }

    #region [-- Sub Sections --]

    #region [-- InternalSection --]
    private Section510_Int_PV_ALL_F _internalSection;
    public Section510_Int_PV_ALL_F InternalSection {
        get {
            if (_internalSection == null) {
              _internalSection = new Section510_Int_PV_ALL_F(this);
            }

            return _internalSection;
        }
    }
    #endregion [-- InternalSection --]
    
    #region [-- ExternalSection --]
    private Section510_Ext_PV_ALL_F _externalSection;
    public Section510_Ext_PV_ALL_F ExternalSection {
      get {
        if (_externalSection == null) {
          _externalSection = new Section510_Ext_PV_ALL_F(this);
        }

        return _externalSection;
      }
    }
    #endregion [-- ExternalSection --]

    #endregion [-- Sub Sections --]

    internal override String GetDBName() => "Section510_FullInspection";

    public Section510_FullInspection(DataModelItem parent) : base(parent)
    {
            
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
          InternalSection,
          ExternalSection,
        };
    }
  }
}
