import 'package:api_inspection/generic/AppRoot.dart';
import 'package:uuid/uuid.dart';

class ChangeLogEntry<ValueType> {
  late String key;

  ValueType? value;

  late DateTime timeChanged;

  late String userName;

  String? reason;

  ChangeLogEntry(this.key, this.value, this.timeChanged, this.userName,
      {this.reason});

  ChangeLogEntry.newlyCreated(this.value, {this.reason}) {
    key = const Uuid().v4().toString();
    timeChanged = DateTime.now();
    userName = AppRoot.global().currentUser?.email ?? "Unknown";
  }
}

class ListChangeLogEntry<ValueType> extends ChangeLogEntry<ValueType> {
  String action;

  ListChangeLogEntry(String key, ValueType? value, DateTime timeChanged,
      String userName, this.action)
      : super(key, value, timeChanged, userName);

  ListChangeLogEntry.newlyCreated(ValueType? value, this.action)
      : super.newlyCreated(value);
}
