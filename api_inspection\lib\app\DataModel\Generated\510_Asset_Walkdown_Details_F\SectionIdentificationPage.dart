//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionIdentification.dart';

// ignore: camel_case_types
class SectionIdentificationPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionIdentification sectionIdentification;

  const SectionIdentificationPage(this.sectionIdentification, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionIdentificationPageState();
  }
}

class _SectionIdentificationPageState extends State<SectionIdentificationPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionIdentification,
        title: "Identification",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionIdentification.attributeName
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionIdentification.attributeNumber_or_ID
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionIdentification.attributeAsset_Type
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionIdentification.attributeEquipment_Description
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionIdentification.attributeLast_known_inspection_date
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionIdentification.attributeLocation
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionIdentification.attributeGIS_Location
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
