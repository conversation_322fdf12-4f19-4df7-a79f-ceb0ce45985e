//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionPhotos.dart';

// ignore: camel_case_types
class SectionPhotosPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionPhotos sectionPhotos;

  const SectionPhotosPage(this.sectionPhotos, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionPhotosPageState();
  }
}

class _SectionPhotosPageState extends State<SectionPhotosPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionPhotos,
        title: "Photos",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionPhotos.attributeFront
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionPhotos.attributeBack
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionPhotos.attributeLeft
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionPhotos.attributeRight
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
