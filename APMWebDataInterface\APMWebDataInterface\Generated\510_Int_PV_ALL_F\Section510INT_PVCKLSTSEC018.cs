//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section510_Int_PV_ALL_F
{
  public class Section510INT_PVCKLSTSEC018 : DataModelItem {

    public override String DisplayName { 
      get {
        return "AUXILIARY EQUIPMENT";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC018Q001;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC018Q002;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC018Q003;
    public PredefinedValueAttribute attributeAre_threaded_connections_acceptably_engaged_and_leak_free;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC018Q005;
    public PredefinedValueAttribute attributeAre_threaded_connections_acceptable_for_continued_service;
    public PredefinedValueAttribute attribute510INT_PVCKLSTSEC018Q007;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section510INT_PVCKLSTSEC018";

    public Section510INT_PVCKLSTSEC018(DataModelItem parent) : base(parent)
    {
            
        attribute510INT_PVCKLSTSEC018Q001 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are auxiliary equipment items in acceptable condition for continued service:  (Gauge connections, sight glasses, float wells, etc.)", databaseName: "510_INT-PV_CKLST_SEC018_Q001"); 
     
        attribute510INT_PVCKLSTSEC018Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are impingement plates and adjacent shell areas in acceptable condition for continued service:", databaseName: "510_INT-PV_CKLST_SEC018_Q002"); 
     
        attribute510INT_PVCKLSTSEC018Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are there any threaded connections associated with the vessel:", databaseName: "510_INT-PV_CKLST_SEC018_Q003"); 
     
        attributeAre_threaded_connections_acceptably_engaged_and_leak_free = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are threaded connections acceptably engaged and leak free:", databaseName: "510_INT-PV_CKLST_SEC018_Q004"); 
     
        attribute510INT_PVCKLSTSEC018Q005 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is threaded connection piping constructed from schedule 80 or greater piping:", databaseName: "510_INT-PV_CKLST_SEC018_Q005"); 
     
        attributeAre_threaded_connections_acceptable_for_continued_service = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are threaded connections acceptable for continued service:", databaseName: "510_INT-PV_CKLST_SEC018_Q006"); 
     
        attribute510INT_PVCKLSTSEC018Q007 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Were drain connections verified to be free of any foreign material that may cause plugging:", databaseName: "510_INT-PV_CKLST_SEC018_Q007"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute510INT_PVCKLSTSEC018Q001,
           attribute510INT_PVCKLSTSEC018Q002,
           attribute510INT_PVCKLSTSEC018Q003,
           attributeAre_threaded_connections_acceptably_engaged_and_leak_free,
           attribute510INT_PVCKLSTSEC018Q005,
           attributeAre_threaded_connections_acceptable_for_continued_service,
           attribute510INT_PVCKLSTSEC018Q007,
        };
    }
  }
}
