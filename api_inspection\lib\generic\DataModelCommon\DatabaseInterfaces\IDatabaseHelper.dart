import 'package:api_inspection/generic/DataModelCommon/Attributes/ChangeLog/ChangeLogEntry.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

abstract class IDatabaseHelper {
  void updateItem(String path, Object? value, WriteBatch batch);
  void updateProperties(
      String path, Map<String, dynamic> updates, WriteBatch batch);
  void addToCollection(String path, ChangeLogEntry value, WriteBatch batch);
  void addToListCollection(
      String path, ListChangeLogEntry value, WriteBatch batch);
}
