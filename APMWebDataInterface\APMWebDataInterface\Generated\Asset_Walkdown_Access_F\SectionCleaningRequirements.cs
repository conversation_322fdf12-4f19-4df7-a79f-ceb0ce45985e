//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionAsset_Walkdown_Access_F
{
  public class SectionCleaningRequirements : DataModelItem {

    public override String DisplayName { 
      get {
        return "Cleaning Requirements";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public MultiPredefinedValueAttribute attributeCleaning_recommendations;
    public PredefinedValueAttribute attributeCleaning_service_review;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionCleaningRequirements";

    public SectionCleaningRequirements(DataModelItem parent) : base(parent)
    {
            
        attributeCleaning_recommendations = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Water Spray", null),
          new PredefinedValueOption("Pressure Wash", null),
          new PredefinedValueOption("Sandblasted", null),
          new PredefinedValueOption("Acid Wash", null),
          new PredefinedValueOption("Steam", null),
          new PredefinedValueOption("None", null)
        }, true, this, "Cleaning recommendations", databaseName: "AWA_Q321"); 
     
        attributeCleaning_service_review = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Acceptable", null),
          new PredefinedValueOption("Concern", null),
          new PredefinedValueOption("N/A", null)
        }, false, this, "Cleaning service review", databaseName: "AWA_Q322"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeCleaning_recommendations,
           attributeCleaning_service_review,
        };
    }
  }
}
