import 'package:api_inspection/generic/DataModelCommon/Attributes/PercentageAttribute.dart';
import 'package:flutter/material.dart';

class PercentageAttributeViewNonEditable extends StatefulWidget {
  final PercentageAttribute _attribute;

  const PercentageAttributeViewNonEditable(this._attribute, {Key? key})
      : super(key: key);

  @override
  _PercentageAttributeViewNonEditableState createState() =>
      _PercentageAttributeViewNonEditableState();
}

class _PercentageAttributeViewNonEditableState
    extends State<PercentageAttributeViewNonEditable> {
  void onAttributeChanged() {
    setState(() {});
  }

  @override
  void dispose() {
    PercentageAttribute attribute = widget._attribute;
    attribute.removeListener(onAttributeChanged);
    super.dispose();
  }

  @override
  void initState() {
    PercentageAttribute attribute = widget._attribute;
    attribute.addListener(onAttributeChanged);

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: const EdgeInsets.fromLTRB(40, 10, 40, 10),
        child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              widget._attribute.getValue().toString() + "%",
              style: const TextStyle(color: Colors.white, fontSize: 22),
            )));
  }
}
