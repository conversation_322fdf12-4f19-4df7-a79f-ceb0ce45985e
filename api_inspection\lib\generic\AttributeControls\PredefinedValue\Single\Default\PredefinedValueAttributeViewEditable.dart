import 'package:api_inspection/app/batch_helper.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';

class PredefinedValueAttributeViewEditable extends StatefulWidget {
  final PredefinedValueAttribute _attribute;

  const PredefinedValueAttributeViewEditable(this._attribute, {Key? key})
      : super(key: key);

  @override
  _PredefinedValueAttributeViewEditableState createState() =>
      _PredefinedValueAttributeViewEditableState();
}

class _PredefinedValueAttributeViewEditableState
    extends State<PredefinedValueAttributeViewEditable> {
  bool initialized = false;

  void initialize() {
    if (initialized) return;
    initialized = true;
    var attribute = widget._attribute;
    attribute.addListener(onAttributeChanged);
  }

  void onAttributeChanged() {
    setState(() {});
  }

  @override
  void dispose() {
    var attribute = widget._attribute;
    attribute.removeListener(onAttributeChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    initialize();

    double width = MediaQuery.of(context).size.width;

    Color selectedColor = const Color.fromARGB(255, 4, 188, 242);
    Color unselectedColor = const Color.fromARGB(255, 122, 122, 122);

    List<Widget> buttons = [];
    var attrValue = widget._attribute.getValue();
    for (var item in widget._attribute.getOptions()) {
      Color buttonColor;
      if (item.value == attrValue) {
        buttonColor = selectedColor;
      } else {
        buttonColor = unselectedColor;
      }

      buttons.add(SizedBox(
          width: (width - 55) / 2,
          child: TeamToggleButton.withText(
              item.value + " " + (widget._attribute.unit ?? ""),
              () => {
                    setState(() {
                      var currentValue = widget._attribute.getValue();
                      if (currentValue == item.value) {
                        BatchHelper.saveAndCommitStringAttribute(
                            widget._attribute, null);
                      } else {
                        BatchHelper.saveAndCommitStringAttribute(
                            widget._attribute, item.value);
                      }
                    })
                  },
              borderColor: buttonColor)));
    }
    return Container(
        margin: const EdgeInsets.fromLTRB(0, 10, 0, 10),
        child: Wrap(children: buttons));
  }
}
