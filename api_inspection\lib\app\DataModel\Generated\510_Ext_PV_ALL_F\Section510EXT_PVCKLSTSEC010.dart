//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class Section510EXT_PVCKLSTSEC010 extends DataModelSection {
  @override
  String getDisplayName() => "GROUNDING";
  Section510EXT_PVCKLSTSEC010(DataModelItem? parent)
      : super(parent: parent, sectionName: "GROUNDING");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeIs_the_asset_grounded_to_the_earth =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Is the asset grounded to the earth:",
          databaseName: "510_EXT-PV_CKLST_SEC010_Q001",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC010Q002 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the bonding clip, attachment weld and surrounding base metal in acceptable condition for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC010_Q002",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC010Q003 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the grounding system in acceptable condition for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC010_Q003",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeIs_the_asset_grounded_to_the_earth,
      attribute510EXT_PVCKLSTSEC010Q002,
      attribute510EXT_PVCKLSTSEC010Q003,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section510EXT_PVCKLSTSEC010";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
