﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace CheckListGen
{
    public static class Helpers
    {
        public static string CleanupVariableName(string str)
        {
            char[] underScoreItems = {'-', '.', ' '};
            var name = new StringBuilder();
            var trimmed = str.Trim();
            foreach (var item in trimmed)
            {
                if (item == ':') Console.Write("A");
                if (char.IsLetterOrDigit(item))
                    name.Append(item);
                else if (underScoreItems.Contains(item))
                    name.Append("_");
            }

            return name.ToString();
        }


        public static bool ParseBooleanEXT(string str)
        {
            switch (str.ToLower().Trim())
            {
                case "y":
                case "yes":
                case "true":
                case "t":
                case "n":
                case "no":
                case "false":
                case "f":
                    return true;
                default:
                    return bool.Parse(str);
            }
        }

        public static string AggregateEXT(
            this IEnumerable<string> source,
            Func<string, string, string> func)
        {
            if (source == null || !source.Any())
                return "";

            return source.Aggregate(func);
        }
    }
}