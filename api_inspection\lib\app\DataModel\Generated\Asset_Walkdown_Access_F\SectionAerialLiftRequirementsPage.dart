//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionAerialLiftRequirements.dart';

// ignore: camel_case_types
class SectionAerialLiftRequirementsPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionAerialLiftRequirements sectionAerialLiftRequirements;

  const SectionAerialLiftRequirementsPage(this.sectionAerialLiftRequirements,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionAerialLiftRequirementsPageState();
  }
}

class _SectionAerialLiftRequirementsPageState
    extends State<SectionAerialLiftRequirementsPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionAerialLiftRequirements,
        title: "Aerial Lift Requirements",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionAerialLiftRequirements.attributeAerial_Lift_Needed
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionAerialLiftRequirements.attributeAWAQ222
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionAerialLiftRequirements
                      .attributeGas_Powered_Permitted
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionAerialLiftRequirements
                      .attributeBattery_Powered_Permitted
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionAerialLiftRequirements
                      .attributeClient_required_proof_of_training
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionAerialLiftRequirements
                      .attributeClient_provided_operator
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionAerialLiftRequirements
                      .attributeEstimated_distance_to_any_live_electrical_overhead_lines
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
