import 'package:api_inspection/generic/DataModelCommon/Attributes/LengthAttribute.dart';
import 'package:flutter/material.dart';

class LengthAttributeViewNonEditable extends StatefulWidget {
  final LengthAttribute _attribute;

  const LengthAttributeViewNonEditable(this._attribute, {Key? key})
      : super(key: key);

  @override
  _LengthAttributeViewNonEditableState createState() =>
      _LengthAttributeViewNonEditableState();
}

class _LengthAttributeViewNonEditableState
    extends State<LengthAttributeViewNonEditable> {
  void onAttributeChanged() {
    setState(() {});
  }

  @override
  void dispose() {
    LengthAttribute attribute = widget._attribute;
    attribute.removeListener(onAttributeChanged);
    super.dispose();
  }

  @override
  void initState() {
    LengthAttribute attribute = widget._attribute;
    attribute.addListener(onAttributeChanged);

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: const EdgeInsets.fromLTRB(40, 10, 40, 10),
        child: Row(
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                widget._attribute.getPreviewText(),
                style: const TextStyle(color: Colors.white, fontSize: 22),
              ),
            ),
          ],
        ));
  }
}
