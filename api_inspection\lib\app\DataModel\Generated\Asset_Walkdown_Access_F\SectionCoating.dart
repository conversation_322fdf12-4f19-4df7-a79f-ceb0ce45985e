//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class SectionCoating extends DataModelSection {
  @override
  String getDisplayName() => "Coating";
  SectionCoating(DataModelItem? parent)
      : super(parent: parent, sectionName: "Coating");

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeCoating_Type =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: true,
          displayName: "Coating Type",
          databaseName: "AWA_Q106",
          availableOptions: [
        PredefinedValueOption("Paint", null, isCommentRequired: false),
        PredefinedValueOption("FBE", null, isCommentRequired: false),
        PredefinedValueOption("Bitumen", null, isCommentRequired: false),
        PredefinedValueOption("Wrap", null, isCommentRequired: false),
        PredefinedValueOption("None", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeCoating_Condition =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: true,
          displayName: "Coating Condition",
          databaseName: "AWA_Q107",
          availableOptions: [
        PredefinedValueOption("Acceptable", null, isCommentRequired: false),
        PredefinedValueOption("Concern", null, isCommentRequired: false),
        PredefinedValueOption("N/A", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late MultiPredefinedValueAttribute attributeCoating_Conditions_Observed =
      MultiPredefinedValueAttribute(
          parent: this,
          hasOtherOption: true,
          displayName: "Coating Conditions Observed",
          databaseName: "AWA_Q108",
          availableOptions: [
        PredefinedValueOption("Smooth", null, isCommentRequired: false),
        PredefinedValueOption("Chalking", null, isCommentRequired: false),
        PredefinedValueOption("Chipping", null, isCommentRequired: false),
        PredefinedValueOption("Peeling", null, isCommentRequired: false),
        PredefinedValueOption("Blistering", null, isCommentRequired: false),
        PredefinedValueOption("Holiday", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeCoating_Removal_Required =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Coating Removal Required?",
          databaseName: "AWA_Q109",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeCoating_Type,
      attributeCoating_Condition,
      attributeCoating_Conditions_Observed,
      attributeCoating_Removal_Required,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionCoating";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
