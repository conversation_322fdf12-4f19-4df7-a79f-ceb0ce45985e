﻿using System;
using System.Linq;

namespace CommonDataInterface.DatabaseHelpers
{
  public class DictionaryWrapper_JObject : IDictionaryWrapper
  {
    private Newtonsoft.Json.Linq.JObject _jObj;
    
    public DataEntry this[String key] {
      get {
        var item =  _jObj[key];
        return new DataEntry{Key = key, Value = item};
      }
    }

    public bool ContainsKey(String key)
    {
      return _jObj.ContainsKey(key);
    }


    public DictionaryWrapper_JObject( Newtonsoft.Json.Linq.JObject jObj)
    {
      _jObj = jObj;
    }
    
    public DataEntry[] GetEntries()
    {
      return _jObj.Properties().Select(a => new DataEntry {Key = a.Name, Value = a.Value}).ToArray();
    }

  }
}