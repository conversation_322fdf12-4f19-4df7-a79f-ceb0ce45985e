//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';

// ignore: camel_case_types
class SectionRepairs extends DataModelCollectionItem {
  @override
  String getDisplayName() => "Repairs";

  SectionRepairs(String id, DataModelItem parent) : super(id, parent);

  // ignore: non_constant_identifier_names
  late DateAttribute attributeDate_Repaired_or_Altered = DateAttribute(
      parent: this,
      displayName: "Date Repaired or Altered",
      databaseName: "510AW_Q186",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeRepairAlteration_organization = StringAttribute(
      parent: this,
      displayName: "Repair/Alteration organization",
      databaseName: "510AW_Q187",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late StringAttribute attributePurpose_of_repairalteration = StringAttribute(
      parent: this,
      displayName: "Purpose of repair/alteration",
      databaseName: "510AW_Q188",
      areCommentsRequired: false);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeIs_NB_Form_R_1_Available =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Is NB Form R-1 Available",
          databaseName: "510AW_Q189",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeIs_NB_Form_R_2_Available =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Is NB Form R-2 Available",
          databaseName: "510AW_Q190",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late StringAttribute attributeNB_R_Certificate_Number = StringAttribute(
      parent: this,
      displayName: "NB 'R' Certificate Number",
      databaseName: "510AW_Q191",
      areCommentsRequired: false);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attributeDate_Repaired_or_Altered,
      attributeRepairAlteration_organization,
      attributePurpose_of_repairalteration,
      attributeIs_NB_Form_R_1_Available,
      attributeIs_NB_Form_R_2_Available,
      attributeNB_R_Certificate_Number,
    ]);
    return children;
  }
}
