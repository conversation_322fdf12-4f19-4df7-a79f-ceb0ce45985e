import 'package:api_inspection/generic/AttributeControls/AttributeStructure.dart';
import 'package:api_inspection/generic/AttributeControls/PredefinedValue/Single/WithOther/PredefinedValueAttributeViewWithOtherEditable.dart';
import 'package:api_inspection/generic/AttributeControls/String/StringAttributeViewNonEditable.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
import 'package:flutter/material.dart';

class PredefinedValueAttributeViewWithOther extends StatefulWidget {
  final PredefinedValueAttribute _attribute;
  final IsEditableController? editingController;
  final bool showPhotos;
  final bool showComments;

  const PredefinedValueAttributeViewWithOther(this._attribute,
      {Key? key,
      this.editingController,
      this.showPhotos = true,
      this.showComments = true})
      : super(key: key);

  @override
  _PredefinedValueAttributeViewWithOtherState createState() =>
      _PredefinedValueAttributeViewWithOtherState();
}

class _PredefinedValueAttributeViewWithOtherState
    extends State<PredefinedValueAttributeViewWithOther> {
  @override
  Widget build(BuildContext context) {
    return AttributeStructure(widget._attribute,
        showComments: widget.showComments,
        showPhotos: widget.showPhotos,
        editingController: widget.editingController,
        editingBuilder: (context, updateListener) {
      return PredefinedValueAttributeViewWithOtherEditable(
          widget._attribute, updateListener);
    }, nonEditingBuilder: (context) {
      return StringAttributeViewNonEditable(widget._attribute);
    });
  }
}
