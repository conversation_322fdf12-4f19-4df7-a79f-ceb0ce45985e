//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionGeneralSiteConditions.dart';
import 'SectionPermittingRequiredPage.dart';
import 'SectionPersonnelAccessConditionsPage.dart';

// ignore: camel_case_types
class SectionGeneralSiteConditionsPage extends StatefulWidget {
  final SectionGeneralSiteConditions sectionGeneralSiteConditions;

  const SectionGeneralSiteConditionsPage(this.sectionGeneralSiteConditions,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionGeneralSiteConditionsPageState();
  }
}

class _SectionGeneralSiteConditionsPageState
    extends State<SectionGeneralSiteConditionsPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionGeneralSiteConditions,
        title: "General Site Conditions",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionGeneralSiteConditions
                      .attributeAre_there_any_on_site_leaks
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionGeneralSiteConditions
                      .attributeVehicle_Accessibility
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionGeneralSiteConditions
                            .sectionPermittingRequired,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          SectionPermittingRequiredPage(widget
                                              .sectionGeneralSiteConditions
                                              .sectionPermittingRequired)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget.sectionGeneralSiteConditions
                            .sectionPersonnelAccessConditions,
                        onPressed: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      SectionPersonnelAccessConditionsPage(widget
                                          .sectionGeneralSiteConditions
                                          .sectionPersonnelAccessConditions))).then(
                              (value) => setState(() {}));
                        }),
                  ),
                ]),
          );
        });
  }
}
