//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.section570_Ext_Pipe_F
{
  public class Section570EXTCKLSTSEC007 : DataModelItem {

    public override String DisplayName { 
      get {
        return "SOIL TO AIR INTERFACE";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public PredefinedValueAttribute attribute570EXTCKLSTSEC007Q001;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC007Q002;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC007Q003;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC007Q004;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC007Q005;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC007Q006;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC007Q007;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC007Q008;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC007Q009;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC007Q010;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC007Q011;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC007Q012;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC007Q013;
    public PredefinedValueAttribute attribute570EXTCKLSTSEC007Q014;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "Section570EXTCKLSTSEC007";

    public Section570EXTCKLSTSEC007(DataModelItem parent) : base(parent)
    {
            
        attribute570EXTCKLSTSEC007Q001 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are any sections of the piping susceptible to soil to air interface damage mechanisms:", databaseName: "570_EXT_CKLST_SEC007_Q001"); 
     
        attribute570EXTCKLSTSEC007Q002 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Were any of the following indicators for leakage noted, change in the surface contour of the ground, discoloration of the soil, softening of paving asphalt, pool formation, bubbling water puddles, or noticeable odor:", databaseName: "570_EXT_CKLST_SEC007_Q002"); 
     
        attribute570EXTCKLSTSEC007Q003 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is piping protected by an impressed current cathodic protection system:  (if yes, record rectifier reading)", databaseName: "570_EXT_CKLST_SEC007_Q003"); 
     
        attribute570EXTCKLSTSEC007Q004 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "For impressed systems are the exposed lead wires in acceptable condition for continued service:", databaseName: "570_EXT_CKLST_SEC007_Q004"); 
     
        attribute570EXTCKLSTSEC007Q005 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is piping protected by a galvanic anode cathodic protection system:", databaseName: "570_EXT_CKLST_SEC007_Q005"); 
     
        attribute570EXTCKLSTSEC007Q006 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "For galvanic anode systems, are the exposed anodes in acceptable condition for continued service.", databaseName: "570_EXT_CKLST_SEC007_Q006"); 
     
        attribute570EXTCKLSTSEC007Q007 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "For galvanic anode systems is the bonding method acceptable for continued service:", databaseName: "570_EXT_CKLST_SEC007_Q007"); 
     
        attribute570EXTCKLSTSEC007Q008 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Was a Soil Resistivity Test conducted: (if yes record the Soil Resistivity (ohm cm) value obtained)).", databaseName: "570_EXT_CKLST_SEC007_Q008"); 
     
        attribute570EXTCKLSTSEC007Q009 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are the concrete to air / asphalt to air interface acceptable sealed to prevent water ingress:", databaseName: "570_EXT_CKLST_SEC007_Q009"); 
     
        attribute570EXTCKLSTSEC007Q010 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Erosion", null),
          new PredefinedValueOption("Yes: Pitting", null)
        }, false, this, "Were any corrosion, erosion, or pitting cells noted on the upper limit of the soil to air interface: (~6” above interface).", databaseName: "570_EXT_CKLST_SEC007_Q010"); 
     
        attribute570EXTCKLSTSEC007Q011 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Corrosion", null),
          new PredefinedValueOption("Yes: Erosion", null),
          new PredefinedValueOption("Yes: Pitting", null)
        }, false, this, "Were any corrosion, erosion, or pitting cells noted on the lower limit of the soil to air interface: (~12” below interface).", databaseName: "570_EXT_CKLST_SEC007_Q011"); 
     
        attribute570EXTCKLSTSEC007Q012 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Is bituminous coating and or wrapping in acceptable condition for continued service:", databaseName: "570_EXT_CKLST_SEC007_Q012"); 
     
        attribute570EXTCKLSTSEC007Q013 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes: Active Leakage", null),
          new PredefinedValueOption("Yes: Combination of Issues", null),
          new PredefinedValueOption("Yes: Evidence of Prior Leakage", null)
        }, false, this, "Was evidence of past or present leakage noted to be originating from the piping or welded connections:", databaseName: "570_EXT_CKLST_SEC007_Q013"); 
     
        attribute570EXTCKLSTSEC007Q014 = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("N/A", null),
          new PredefinedValueOption("No", null),
          new PredefinedValueOption("Yes", null)
        }, false, this, "Are the areas encompassed by the soil to air interface in acceptable condition for continued service:", databaseName: "570_EXT_CKLST_SEC007_Q014"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attribute570EXTCKLSTSEC007Q001,
           attribute570EXTCKLSTSEC007Q002,
           attribute570EXTCKLSTSEC007Q003,
           attribute570EXTCKLSTSEC007Q004,
           attribute570EXTCKLSTSEC007Q005,
           attribute570EXTCKLSTSEC007Q006,
           attribute570EXTCKLSTSEC007Q007,
           attribute570EXTCKLSTSEC007Q008,
           attribute570EXTCKLSTSEC007Q009,
           attribute570EXTCKLSTSEC007Q010,
           attribute570EXTCKLSTSEC007Q011,
           attribute570EXTCKLSTSEC007Q012,
           attribute570EXTCKLSTSEC007Q013,
           attribute570EXTCKLSTSEC007Q014,
        };
    }
  }
}
