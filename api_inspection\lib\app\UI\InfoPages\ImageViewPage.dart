import 'package:api_inspection/generic/AppStyle.dart';
import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';

class ImageViewPage extends StatefulWidget {
  final String imageName;

  final String imagePath;

  const ImageViewPage(this.imageName, this.imagePath, {Key? key})
      : super(key: key);

  @override
  _ImageViewPageState createState() => _ImageViewPageState();
}

class _ImageViewPageState extends State<ImageViewPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        appBar: AppBar(
          title: Text(
            widget.imageName,
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        body: PhotoView(
          imageProvider: AssetImage(widget.imagePath),
        ));
  }
}
