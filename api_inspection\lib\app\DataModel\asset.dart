import 'dart:developer';

import 'package:api_inspection/app/DataModel/Generated/570_Asset_Walkdown_Details_F/Section570_Asset_Walkdown_Details_FPage.dart';
import 'package:api_inspection/app/DataModel/Generated/653_Asset_Walkdown_Details_F/Section653_Asset_Walkdown_Details_FPage.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/Generated/510_Asset_Walkdown_Details_F/Section510_Asset_Walkdown_Details_F.dart';
import 'package:api_inspection/app/DataModel/Generated/510_Asset_Walkdown_Details_F/Section510_Asset_Walkdown_Details_FPage.dart';
import 'package:api_inspection/app/DataModel/Generated/570_Asset_Walkdown_Details_F/Section570_Asset_Walkdown_Details_F.dart';
import 'package:api_inspection/app/DataModel/Generated/653_Asset_Walkdown_Details_F/Section653_Asset_Walkdown_Details_F.dart';
import 'package:api_inspection/app/DataModel/Generated/Asset_Walkdown_Access_F/SectionAsset_Walkdown_Access_F.dart';
import 'package:api_inspection/app/DataModel/Generated/Asset_Walkdown_PPE_F/SectionAsset_Walkdown_PPE_F.dart';
import 'package:api_inspection/app/DataModel/assetCard.dart';
import 'package:api_inspection/app/DataModel/location.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/ConcretePhotoRoot.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/DataModelCommon/DatabaseInterfaces/DatabaseHelperManager.dart';

class Asset extends ConcretePhotoRoot {
  @override
  String getDBPath() => "assets." + getDBName();

  List<String> listeningUsers = [];

  List<String> workOrdersContainedIn = [];
  List<String> projectsContainedIn = [];

  late StringAttribute businessUnitId = StringAttribute(
      parent: this, displayName: "BusinessUnitId", isQueryable: true);

  @override
  String getDisplayName() => "Asset ID: " + (getAssetId() ?? "Unknown");

  String? getAssetName() {
    var currentWalkDown = walkDown;
    if (currentWalkDown is Section510_Asset_Walkdown_Details_F) {
      return currentWalkDown.sectionIdentification.attributeName.getValue();
    }
    if (currentWalkDown is Section570_Asset_Walkdown_Details_F) {
      return currentWalkDown.sectionIdentification.attributeName.getValue();
    }
    if (currentWalkDown is Section653_Asset_Walkdown_Details_F) {
      return currentWalkDown.sectionIdentification.attributeName.getValue();
    }
    return null;
  }

  String? getAssetId() {
    var currentWalkDown = walkDown;
    if (currentWalkDown is Section510_Asset_Walkdown_Details_F) {
      return currentWalkDown.sectionIdentification.attributeNumber_or_ID
          .getValue();
    }
    if (currentWalkDown is Section570_Asset_Walkdown_Details_F) {
      return currentWalkDown.sectionIdentification.attributeNumber_or_Circuit_ID
          .getValue();
    }
    if (currentWalkDown is Section653_Asset_Walkdown_Details_F) {
      return currentWalkDown.sectionIdentification.attributeNumber_or_ID
          .getValue();
    }
    return null;
  }

  /// 'Vessel', 'Piping' or 'Tank'
  String assetCategory;

  AssetCard buildCard() {
    var currentWalkDown = walkDown;

    String? name;
    String? assetId;
    String? assetType;
    if (currentWalkDown is Section510_Asset_Walkdown_Details_F) {
      name = currentWalkDown.sectionIdentification.attributeName.getValue();
      assetId = currentWalkDown.sectionIdentification.attributeNumber_or_ID
          .getValue();
      assetType =
          currentWalkDown.sectionIdentification.attributeAsset_Type.getValue();
    }
    if (currentWalkDown is Section570_Asset_Walkdown_Details_F) {
      name = currentWalkDown.sectionIdentification.attributeName.getValue();
      assetId = currentWalkDown
          .sectionIdentification.attributeNumber_or_Circuit_ID
          .getValue();
      assetType =
          currentWalkDown.sectionIdentification.attributeAsset_Type.getValue();
    }
    if (currentWalkDown is Section653_Asset_Walkdown_Details_F) {
      name = currentWalkDown.sectionIdentification.attributeName.getValue();
      assetId = currentWalkDown.sectionIdentification.attributeNumber_or_ID
          .getValue();
      assetType =
          currentWalkDown.sectionIdentification.attributeAsset_Type.getValue();
    }

    return AssetCard(
        assetDBId: id,
        assetName: name,
        assetId: assetId,
        assetType: assetType,
        assetCategory: assetCategory,
        businessUnitId: businessUnitId.getValue());
  }

  void updateDBCard(WriteBatch batch) async {
    location.assetCards.updateItem(buildCard(), batch);
  }

  late DataModelItem walkDown;

  late SectionAsset_Walkdown_PPE_F assetPPE = SectionAsset_Walkdown_PPE_F(this);

  late SectionAsset_Walkdown_Access_F assetAccess =
      SectionAsset_Walkdown_Access_F(this);

  late StringAttribute area =
      StringAttribute(displayName: "Area", parent: this);

  late StringAttribute unit =
      StringAttribute(displayName: "Unit", parent: this);

  late Location location;

  Widget buildWalkdownPage() {
    var currentWalkDown = walkDown;
    if (currentWalkDown is Section510_Asset_Walkdown_Details_F) {
      return Section510_Asset_Walkdown_Details_FPage(currentWalkDown);
    }
    if (currentWalkDown is Section570_Asset_Walkdown_Details_F) {
      return Section570_Asset_Walkdown_Details_FPage(currentWalkDown);
    }
    if (currentWalkDown is Section653_Asset_Walkdown_Details_F) {
      return Section653_Asset_Walkdown_Details_FPage(currentWalkDown);
    }
    throw Exception("Not Implemented");
  }

  void onCardAttributeChangedBySelf() {
    var batch = FirebaseFirestore.instance.batch();
    updateDBCard(batch);
    batch.commit();
  }

  Asset(
      {required this.assetCategory,
      required String id,
      required String locationId})
      : super(id) {
    if (assetCategory == "Vessel") {
      var createdWalkdown = Section510_Asset_Walkdown_Details_F(this);
      createdWalkdown
          .sectionIdentification.attributeAsset_Type.changedBySelfListeners
          .addListener(onCardAttributeChangedBySelf);
      createdWalkdown.sectionIdentification.attributeName.changedBySelfListeners
          .addListener(onCardAttributeChangedBySelf);
      createdWalkdown
          .sectionIdentification.attributeNumber_or_ID.changedBySelfListeners
          .addListener(onCardAttributeChangedBySelf);
      walkDown = createdWalkdown;
    } else if (assetCategory == "Piping") {
      var createdWalkdown = Section570_Asset_Walkdown_Details_F(this);
      createdWalkdown
          .sectionIdentification.attributeAsset_Type.changedBySelfListeners
          .addListener(onCardAttributeChangedBySelf);
      createdWalkdown.sectionIdentification.attributeName.changedBySelfListeners
          .addListener(onCardAttributeChangedBySelf);
      createdWalkdown.sectionIdentification.attributeNumber_or_Circuit_ID
          .changedBySelfListeners
          .addListener(onCardAttributeChangedBySelf);
      walkDown = createdWalkdown;
    } else if (assetCategory == "Tank") {
      var createdWalkdown = Section653_Asset_Walkdown_Details_F(this);
      createdWalkdown
          .sectionIdentification.attributeAsset_Type.changedBySelfListeners
          .addListener(onCardAttributeChangedBySelf);
      createdWalkdown.sectionIdentification.attributeName.changedBySelfListeners
          .addListener(onCardAttributeChangedBySelf);
      createdWalkdown
          .sectionIdentification.attributeNumber_or_ID.changedBySelfListeners
          .addListener(onCardAttributeChangedBySelf);
      walkDown = createdWalkdown;
    } else {
      throw "Invalid asset category";
    }

    location = LocationCache.findEntry(locationId);
  }

  @override
  List<DataModelItem> getChildren() {
    var parentChildren = super.getChildren().toList();
    parentChildren
        .addAll([walkDown, assetPPE, assetAccess, area, unit, businessUnitId]);
    return parentChildren;
  }

  @override
  void saveDirectItems(WriteBatch batch) async {
    var helper = DatabaseHelperManager.global();
    var path = getDBPath();

    helper.updateProperties(
        path,
        {
          "Id": id,
          "ListeningUsers": listeningUsers,
          "LocationId": location.id,
          "AssetCategory": assetCategory
        },
        batch);
  }

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    if (entry.key == "WorkOrderIds") {
      if (entry.value is List<dynamic>) {
        List<String> workOrderIds = [];
        for (var item in entry.value as List<dynamic>) {
          workOrderIds.add(item.toString());
        }
        workOrdersContainedIn = workOrderIds;
        return true;
      } else {
        log("Unexpected data: WorkOrderIds were null: AssetId " + id);
      }
    }
    if (entry.key == "ProjectIds") {
      if (entry.value is List<dynamic>) {
        List<String> projectIds = [];
        for (var item in entry.value as List<dynamic>) {
          projectIds.add(item.toString());
        }
        projectsContainedIn = projectIds;
        return true;
      } else {
        log("Unexpected data: ProjectIds were null: AssetId " + id);
      }
    }

    if (entry.key == 'ListeningUsers') {
      if (entry.value is List<dynamic>) {
        List<String> emails = [];
        for (var item in entry.value as List<dynamic>) {
          emails.add(item.toString());
        }
        listeningUsers = emails;
        return true;
      } else {
        log('Unexpected data: ListeningUsers were not what we expected.  AssetId: ' +
            id);
      }
    }
    return false;
  }
}
