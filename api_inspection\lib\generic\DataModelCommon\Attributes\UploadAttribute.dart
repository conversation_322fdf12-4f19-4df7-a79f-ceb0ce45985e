// import 'package:api_inspection/generic/AttributeControls/AttributeStructure.dart';
// import 'package:api_inspection/generic/DataModelCommon/Attributes/AttributeBase.dart';
// import 'package:api_inspection/generic/DataModelCommon/Attributes/ChangeLog/ChangeLog.dart';
// import 'package:api_inspection/generic/DataModelCommon/Attributes/ChangeLog/ChangeLogEntry.dart';
// import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
// import 'package:api_inspection/generic/DataModelCommon/PhotoRoot.dart';
// import 'package:api_inspection/generic/MediaControls/MediaEntry.dart';
// import 'package:api_inspection/generic/DataModelCommon/AttributeControls/Upload/UploadAttributeView.dart';

// import 'package:flutter/cupertino.dart';

// class UploadAttribute extends AttributeBase {
//   UploadAttribute(
//       {required DataModelItem parent,
//       required String displayName,
//       Widget? iconWidget,
//       bool areCommentsRequired = false,
//       String? databaseName})
//       : super(
//             parent, displayName, iconWidget, areCommentsRequired, databaseName);

//   late ListChangeLog<String> fileChangeLog = ListChangeLog<String>(
//       "FileChangeLog", this, <ListChangeLogEntry<String>>[]);

//   List<MediaEntry> getFiles() {
//     var photoRoot = findParentOfType<PhotoRoot>();
//     if (photoRoot == null) return List.empty();
//     var currentEntries = fileChangeLog.getCurrentEntries();
//     var currentEntries1 = photoChangeLog.getCurrentEntries();

//     List<MediaEntry> mediaEntries = [];
//     for (var entry in currentEntries) {
//       var parts = entry.split('.');
//       mediaEntries.add(photoRoot.getPhoto(parts[0], parts[1]));
//     }
//     for (var ent in currentEntries1) {
//       var parts = ent.split('.');
//       mediaEntries.add(photoRoot.getPhoto(parts[0], parts[1]));
//     }

//     return mediaEntries;
//   }

//   void addFile(MediaEntry entry) {
//     var newEntry =
//         ListChangeLogEntry<String>.newlyCreated(entry.fullFileName, "Added");
//     fileChangeLog.addNewItem(newEntry);

//     notifyListeners();
//   }

//   void removeFile(MediaEntry entry) {
//     var photoRoot = findParentOfType<PhotoRoot>();
//     if (photoRoot == null) throw "No photo root control found in stack";

//     entry.removeReferencePath(getDBPath());

//     var changeEntry =
//         ListChangeLogEntry<String>.newlyCreated(entry.fullFileName, "Removed");
//     if (entry.extension == "png") {
//       photoChangeLog.addNewItem(changeEntry);
//     } else {
//       fileChangeLog.addNewItem(changeEntry);
//     }

//     notifyListeners();
//   }

//   Widget buildWidget(
//       {IsEditableController? editingController = null,
//       bool showPhotos = true,
//       bool showComments = true}) {
//     return UploadAttributeView(
//       this,
//       editingController: editingController,
//       showPhotos: showPhotos,
//       showComments: showComments,
//     );
//   }

//   Widget buildWidgetNonEditable() {
//     return UploadAttributeView(this);
//   }

//   @override
//   String getPreviewText() {
//     var len = getFiles().length + getPhotos().length;
//     return len.toString() + " Files";
//   }

//   @override
//   List<DataModelItem> getChildren() {
//     var baseChildren = super.getChildren().toList();
//     baseChildren.addAll([photoChangeLog, fileChangeLog]);
//     return baseChildren;
//   }

//   @override
//   bool hasData() {
//     // TODO: implement hasData
//     throw UnimplementedError();
//   }
// }
