//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionLeak_Report_F
{
  public class SectionPhotos : DataModelItem {

    public override String DisplayName { 
      get {
        return "Photos";
      }
    }

    #region [-- Sub Sections --]

    #region [-- SectionLeakReportPhotos --]
    private DataModelCollection<SectionLeakReportPhotos> _sectionLeakReportPhotos;
    public DataModelCollection<SectionLeakReportPhotos> sectionLeakReportPhotos {
        get {
            if (_sectionLeakReportPhotos == null) {
              _sectionLeakReportPhotos = new DataModelCollection<SectionLeakReportPhotos>("Leak Report Photos", (parent, entry) => {
                 return new SectionLeakReportPhotos(entry.Key, _sectionLeakReportPhotos);
              }, (parent, id) => {
                return new SectionLeakReportPhotos(id, _sectionLeakReportPhotos);
              }, this);
            }

            return _sectionLeakReportPhotos;
        }
    }
    #endregion [-- SectionLeakReportPhotos --]
    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionPhotos";

    public SectionPhotos(DataModelItem parent) : base(parent)
    {
            
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           sectionLeakReportPhotos,
        };
    }
  }
}
