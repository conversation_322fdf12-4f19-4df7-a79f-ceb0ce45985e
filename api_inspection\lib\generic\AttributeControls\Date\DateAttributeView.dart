import 'package:api_inspection/generic/AttributeControls/Date/DateAttributeViewEditable.dart';
import 'package:api_inspection/generic/AttributeControls/Date/DateAttributeViewNonEditable.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
import 'package:flutter/material.dart';

import '../AttributeStructure.dart';

class DateAttributeView extends StatefulWidget {
  final DateAttribute _attribute;
  final IsEditableController? editingController;
  final bool showPhotos;
  final bool showComments;

  const DateAttributeView(this._attribute,
      {Key? key,
      this.editingController,
      this.showPhotos = true,
      this.showComments = true})
      : super(key: key);

  @override
  _DateAttributeViewState createState() => _DateAttributeViewState();
}

class _DateAttributeViewState extends State<DateAttributeView> {
  @override
  Widget build(BuildContext context) {
    return AttributeStructure(widget._attribute,
        showComments: widget.showComments,
        showPhotos: widget.showPhotos,
        editingController: widget.editingController,
        editingBuilder: (context, listener) {
      return DateAttributeViewEditable(widget._attribute);
    }, nonEditingBuilder: (context) {
      return DateAttributeViewNonEditable(widget._attribute);
    });
  }
}
