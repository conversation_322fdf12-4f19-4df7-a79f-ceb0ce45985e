import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:api_inspection/generic/DataModelCommon/Attributes/ChangeLog/ChangeLog.dart';

class CollectionHistoryControl extends StatefulWidget {
  final ListChangeLog _changeLog;
  const CollectionHistoryControl(this._changeLog, {Key? key}) : super(key: key);

  @override
  _CollectionHistoryControlState createState() =>
      _CollectionHistoryControlState();
}

class _CollectionHistoryControlState extends State<CollectionHistoryControl> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    if (widget._changeLog.entries.isEmpty) return Row();

    Widget expansionButton = Container(
        alignment: Alignment.center,
        margin: const EdgeInsets.fromLTRB(0, 10, 0, 10),
        child: ElevatedButton(
          onPressed: () {
            setState(() {
              _isExpanded = !_isExpanded;
            });
          },
          child: Container(
              margin: const EdgeInsets.fromLTRB(50, 15, 50, 15),
              child: const Text("History",
                  style: TextStyle(color: Colors.white, fontSize: 18))),
          style: ButtonStyle(
            backgroundColor: MaterialStateProperty.resolveWith<Color?>(
              (Set<MaterialState> states) {
                return Colors.blueGrey[600]; // Use the component's default.
              },
            ),
          ),
        ));

    if (!_isExpanded) {
      return expansionButton;
    }

    List<Widget> widgets = [expansionButton];

    widgets.add(Row(children: const [
      Expanded(
          child: Text("Date",
              textAlign: TextAlign.start,
              style: TextStyle(color: Colors.white, fontSize: 18))),
      Expanded(
          child: Text("Value",
              textAlign: TextAlign.start,
              style: TextStyle(color: Colors.white, fontSize: 18))),
      Expanded(
          child: Text("Action",
              textAlign: TextAlign.start,
              style: TextStyle(color: Colors.white, fontSize: 18))),
      Expanded(
          child: Text("User",
              textAlign: TextAlign.start,
              style: TextStyle(color: Colors.white, fontSize: 18))),
    ], mainAxisAlignment: MainAxisAlignment.spaceEvenly));

    for (var entry in widget._changeLog.entries) {
      var formatter = DateFormat.jm();
      String date = entry.timeChanged.month.toString() +
          "/" +
          entry.timeChanged.day.toString() +
          " " +
          formatter.format(entry.timeChanged);

      widgets.add(Row(children: [
        Expanded(
            child: Text(date,
                textAlign: TextAlign.start,
                style: const TextStyle(color: Colors.white, fontSize: 16))),
        Expanded(
            child: Text(entry.value.toString(),
                textAlign: TextAlign.start,
                style: const TextStyle(color: Colors.white, fontSize: 16))),
        Expanded(
            child: Text(entry.action,
                textAlign: TextAlign.start,
                style: const TextStyle(color: Colors.white, fontSize: 16))),
        Expanded(
            child: Text(entry.userName,
                textAlign: TextAlign.start,
                style: const TextStyle(color: Colors.white, fontSize: 16))),
      ], mainAxisAlignment: MainAxisAlignment.spaceEvenly));
    }

    return Container(
        margin: const EdgeInsets.fromLTRB(15, 0, 15, 0),
        child: Column(
            children: widgets, crossAxisAlignment: CrossAxisAlignment.stretch));
  }
}
