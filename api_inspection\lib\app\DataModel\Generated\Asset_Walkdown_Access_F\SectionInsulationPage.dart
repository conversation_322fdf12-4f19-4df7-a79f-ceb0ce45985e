//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionInsulation.dart';

// ignore: camel_case_types
class SectionInsulationPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionInsulation sectionInsulation;

  const SectionInsulationPage(this.sectionInsulation, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionInsulationPageState();
  }
}

class _SectionInsulationPageState extends State<SectionInsulationPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionInsulation,
        title: "Insulation",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionInsulation.attributeDoes_the_asset_have_insulation
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionInsulation.attributePossible_asbestos
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionInsulation.attributeJacketing_type
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionInsulation.attributeInsulation_type
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionInsulation.attributeInsulation_removal_required
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionInsulation.attributeHeat_tracing
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
