//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionDimensions.dart';

// ignore: camel_case_types
class SectionDimensionsPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionDimensions sectionDimensions;

  const SectionDimensionsPage(this.sectionDimensions, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionDimensionsPageState();
  }
}

class _SectionDimensionsPageState extends State<SectionDimensionsPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionDimensions,
        title: "Dimensions",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionDimensions.attributeDiameter_Measurement
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionDimensions.attributeDiameter
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget.sectionDimensions
                      .attributeDoes_the_shell_have_multiple_diameters
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionDimensions.attributeOverall_Length_or_Height
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget.sectionDimensions
                      .attributeAre_there_toriconical_transition_sections_in_the_shell
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
