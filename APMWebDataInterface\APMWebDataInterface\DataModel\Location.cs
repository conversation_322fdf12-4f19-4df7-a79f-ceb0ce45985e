﻿using System;
using System.Linq;
using System.Threading.Tasks;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.ExampleDataModel
{
  
  public class Location : ConcretePhotoRoot {
    
    public override string DisplayName => "Location";
    public Location() : base(null)
    {
      InitializeAttributes();
    }


    internal Location(String id) : base(id, null)
    {
      InitializeAttributes();
    }


    private void InitializeAttributes()
    {
      name = new StringAttribute(parent: this, displayName: "Name");
      businessUnitId = new StringAttribute(this, "BusinessUnitId", isQueryable: true);
      description = new StringAttribute(parent: this, displayName: "Description");
      street1 = new StringAttribute(parent: this, displayName: "Address Line 1");
      street2 = new StringAttribute(parent: this, displayName: "Address Line 2");
      city = new StringAttribute(parent: this, displayName: "City");
      region = new StringAttribute(parent: this, displayName: "State / Region");
      postalCode = new StringAttribute(parent: this, displayName: "Zip code");
      country = new StringAttribute(parent: this, displayName: "Country");
      latitude = new DoubleAttribute(this, "Latitude", true);
      longitude = new DoubleAttribute(this, "Longitude", true);
      elevation = new DoubleAttribute(this, "Elevation", true);

      comment = new StringAttribute(parent: this, displayName: "Comment");
    }

    public StringAttribute name;
    public StringAttribute businessUnitId;
    public StringAttribute description;
    public StringAttribute street1;
    public StringAttribute street2;
    public StringAttribute city;
    public StringAttribute region;
    public StringAttribute postalCode;
    public StringAttribute country;
    public DoubleAttribute latitude;
    public DoubleAttribute longitude;
    public DoubleAttribute elevation;

    public StringAttribute comment;

    
    public override DataModelItem[] GetChildren(){
      var parentChildren = base.GetChildren();

      return parentChildren.Concat(new DataModelItem[] { name,
        description,
        street1,
        street2,
        city,
        region,
        postalCode,
        country,
        latitude,
        longitude,
        elevation,
        comment,
        businessUnitId
      }).ToArray();
    }
    
    internal override String GetDBPath() => "locations." + GetDBName();
    internal override String GetDBName() => id;


    public override Task SavePendingChanges(string user)
    {
        APM_WebDataInterface.Global.AuthorizeWriteToRootObject(this.businessUnitId, user);
        return base.SavePendingChanges(user);
    }
  }
}
