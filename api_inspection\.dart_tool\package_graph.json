{"roots": ["api_inspection"], "packages": [{"name": "api_inspection", "version": "1.2.0+37", "dependencies": ["azblob", "cached_network_image", "camera", "cloud_firestore", "connectivity_plus", "cupertino_icons", "darq", "encrypt", "file_picker", "file_saver", "firebase_analytics", "firebase_auth", "firebase_core", "firebase_crashlytics", "flutter", "flutter_appauth", "flutter_uploader", "gallery_saver", "geolocator", "image", "intl", "map_launcher", "open_file", "package_info_plus", "path_provider", "photo_view", "shared_preferences", "synchronized", "url_launcher", "uuid"], "devDependencies": ["flutter_launcher_icons", "flutter_lints", "flutter_test"]}, {"name": "flutter_launcher_icons", "version": "0.10.0", "dependencies": ["args", "checked_yaml", "cli_util", "image", "json_annotation", "path", "yaml"]}, {"name": "flutter_lints", "version": "2.0.1", "dependencies": ["lints"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "cached_network_image", "version": "3.2.2", "dependencies": ["cached_network_image_platform_interface", "cached_network_image_web", "flutter", "flutter_cache_manager", "octo_image"]}, {"name": "flutter_uploader", "version": "3.0.0-beta.4", "dependencies": ["equatable", "flutter"]}, {"name": "firebase_crashlytics", "version": "2.8.11", "dependencies": ["firebase_core", "firebase_core_platform_interface", "firebase_crashlytics_platform_interface", "flutter", "stack_trace"]}, {"name": "gallery_saver", "version": "2.3.2", "dependencies": ["flutter", "http", "path", "path_provider"]}, {"name": "flutter_appauth", "version": "4.2.0", "dependencies": ["flutter", "flutter_appauth_platform_interface"]}, {"name": "photo_view", "version": "0.14.0", "dependencies": ["flutter"]}, {"name": "connectivity_plus", "version": "2.3.7", "dependencies": ["connectivity_plus_linux", "connectivity_plus_macos", "connectivity_plus_platform_interface", "connectivity_plus_web", "connectivity_plus_windows", "flutter"]}, {"name": "map_launcher", "version": "2.4.0", "dependencies": ["flutter"]}, {"name": "shared_preferences", "version": "2.0.15", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_ios", "shared_preferences_linux", "shared_preferences_macos", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "package_info_plus", "version": "1.4.2", "dependencies": ["flutter", "package_info_plus_linux", "package_info_plus_macos", "package_info_plus_platform_interface", "package_info_plus_web", "package_info_plus_windows"]}, {"name": "encrypt", "version": "5.0.1", "dependencies": ["args", "asn1lib", "clock", "collection", "crypto", "pointycastle"]}, {"name": "firebase_auth", "version": "3.10.0", "dependencies": ["firebase_auth_platform_interface", "firebase_auth_web", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "firebase_analytics", "version": "9.3.6", "dependencies": ["firebase_analytics_platform_interface", "firebase_analytics_web", "firebase_core", "firebase_core_platform_interface", "flutter"]}, {"name": "firebase_core", "version": "1.23.0", "dependencies": ["firebase_core_platform_interface", "firebase_core_web", "flutter", "meta"]}, {"name": "synchronized", "version": "3.0.0+3", "dependencies": []}, {"name": "file_saver", "version": "0.1.1", "dependencies": ["flutter", "flutter_web_plugins", "path_provider", "path_provider_linux", "path_provider_windows"]}, {"name": "azblob", "version": "2.3.0", "dependencies": ["crypto", "http"]}, {"name": "cloud_firestore", "version": "3.4.9", "dependencies": ["cloud_firestore_platform_interface", "cloud_firestore_web", "collection", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "path_provider", "version": "2.0.11", "dependencies": ["flutter", "path_provider_android", "path_provider_ios", "path_provider_linux", "path_provider_macos", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "open_file", "version": "3.2.1", "dependencies": ["ffi", "flutter"]}, {"name": "file_picker", "version": "4.6.1", "dependencies": ["ffi", "flutter", "flutter_plugin_android_lifecycle", "flutter_web_plugins", "path", "plugin_platform_interface", "win32"]}, {"name": "image", "version": "3.2.0", "dependencies": ["archive", "meta", "xml"]}, {"name": "camera", "version": "0.10.0+1", "dependencies": ["camera_android", "camera_avfoundation", "camera_platform_interface", "camera_web", "flutter", "flutter_plugin_android_lifecycle", "quiver"]}, {"name": "url_launcher", "version": "6.1.5", "dependencies": ["flutter", "url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows"]}, {"name": "intl", "version": "0.17.0", "dependencies": ["clock", "path"]}, {"name": "geolocator", "version": "9.0.2", "dependencies": ["flutter", "geolocator_android", "geolocator_apple", "geolocator_platform_interface", "geolocator_web", "geolocator_windows"]}, {"name": "uuid", "version": "3.0.6", "dependencies": ["crypto"]}, {"name": "darq", "version": "1.2.1", "dependencies": []}, {"name": "cupertino_icons", "version": "1.0.5", "dependencies": []}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "yaml", "version": "3.1.1", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "json_annotation", "version": "4.7.0", "dependencies": ["meta"]}, {"name": "cli_util", "version": "0.3.5", "dependencies": ["meta", "path"]}, {"name": "checked_yaml", "version": "2.0.1", "dependencies": ["json_annotation", "source_span", "yaml"]}, {"name": "args", "version": "2.3.1", "dependencies": []}, {"name": "lints", "version": "2.0.0", "dependencies": []}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "cached_network_image_web", "version": "1.0.2", "dependencies": ["cached_network_image_platform_interface", "flutter", "flutter_cache_manager"]}, {"name": "cached_network_image_platform_interface", "version": "2.0.0", "dependencies": ["flutter", "flutter_cache_manager"]}, {"name": "octo_image", "version": "1.0.2", "dependencies": ["flutter", "flutter_blurhash"]}, {"name": "flutter_cache_manager", "version": "3.3.0", "dependencies": ["clock", "collection", "file", "flutter", "http", "path", "path_provider", "pedantic", "rxdart", "sqflite", "uuid"]}, {"name": "equatable", "version": "2.0.5", "dependencies": ["collection", "meta"]}, {"name": "firebase_crashlytics_platform_interface", "version": "3.2.17", "dependencies": ["collection", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "firebase_core_platform_interface", "version": "4.5.1", "dependencies": ["collection", "flutter", "flutter_test", "meta", "plugin_platform_interface"]}, {"name": "http", "version": "0.13.5", "dependencies": ["async", "http_parser", "meta", "path"]}, {"name": "flutter_appauth_platform_interface", "version": "5.2.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "connectivity_plus_windows", "version": "1.2.2", "dependencies": ["connectivity_plus_platform_interface", "flutter"]}, {"name": "connectivity_plus_web", "version": "1.2.3", "dependencies": ["connectivity_plus_platform_interface", "flutter", "flutter_web_plugins"]}, {"name": "connectivity_plus_macos", "version": "1.2.4", "dependencies": ["connectivity_plus_platform_interface", "flutter"]}, {"name": "connectivity_plus_linux", "version": "1.3.1", "dependencies": ["connectivity_plus_platform_interface", "flutter", "meta", "nm"]}, {"name": "connectivity_plus_platform_interface", "version": "1.2.1", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "shared_preferences_windows", "version": "2.1.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.0.4", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_platform_interface", "version": "2.1.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_macos", "version": "2.0.4", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.1.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_ios", "version": "2.1.1", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_android", "version": "2.0.13", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "package_info_plus_web", "version": "1.0.5", "dependencies": ["flutter", "flutter_web_plugins", "http", "meta", "package_info_plus_platform_interface"]}, {"name": "package_info_plus_windows", "version": "1.0.5", "dependencies": ["ffi", "flutter", "package_info_plus_platform_interface", "win32"]}, {"name": "package_info_plus_macos", "version": "1.3.0", "dependencies": ["flutter"]}, {"name": "package_info_plus_linux", "version": "1.0.5", "dependencies": ["flutter", "package_info_plus_platform_interface", "path"]}, {"name": "package_info_plus_platform_interface", "version": "1.0.2", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "pointycastle", "version": "3.6.2", "dependencies": ["collection", "convert", "js"]}, {"name": "crypto", "version": "3.0.2", "dependencies": ["typed_data"]}, {"name": "asn1lib", "version": "1.1.1", "dependencies": []}, {"name": "firebase_auth_web", "version": "4.5.0", "dependencies": ["firebase_auth_platform_interface", "firebase_core", "firebase_core_web", "flutter", "flutter_web_plugins", "http_parser", "intl", "js", "meta"]}, {"name": "firebase_auth_platform_interface", "version": "6.9.0", "dependencies": ["collection", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "firebase_analytics_web", "version": "0.4.2+5", "dependencies": ["firebase_analytics_platform_interface", "firebase_core", "firebase_core_web", "flutter", "flutter_web_plugins", "js"]}, {"name": "firebase_analytics_platform_interface", "version": "3.3.5", "dependencies": ["firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "firebase_core_web", "version": "1.7.2", "dependencies": ["firebase_core_platform_interface", "flutter", "flutter_web_plugins", "js", "meta"]}, {"name": "path_provider_linux", "version": "2.1.7", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_windows", "version": "2.0.7", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "win32"]}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "cloud_firestore_web", "version": "2.8.8", "dependencies": ["cloud_firestore_platform_interface", "collection", "firebase_core", "firebase_core_web", "flutter", "flutter_web_plugins", "js"]}, {"name": "cloud_firestore_platform_interface", "version": "5.7.5", "dependencies": ["collection", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.0.4", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_macos", "version": "2.0.6", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "path_provider_ios", "version": "2.0.11", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "path_provider_android", "version": "2.0.20", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "ffi", "version": "1.2.1", "dependencies": []}, {"name": "win32", "version": "2.6.1", "dependencies": ["ffi"]}, {"name": "plugin_platform_interface", "version": "2.1.3", "dependencies": ["meta"]}, {"name": "flutter_plugin_android_lifecycle", "version": "2.0.7", "dependencies": ["flutter"]}, {"name": "xml", "version": "6.1.0", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "archive", "version": "3.3.1", "dependencies": ["crypto", "path"]}, {"name": "quiver", "version": "3.1.0", "dependencies": ["matcher"]}, {"name": "camera_web", "version": "0.3.0", "dependencies": ["camera_platform_interface", "flutter", "flutter_web_plugins", "stream_transform"]}, {"name": "camera_platform_interface", "version": "2.2.0", "dependencies": ["cross_file", "flutter", "plugin_platform_interface", "stream_transform"]}, {"name": "camera_avfoundation", "version": "0.9.8+5", "dependencies": ["camera_platform_interface", "flutter", "stream_transform"]}, {"name": "camera_android", "version": "0.10.0+2", "dependencies": ["camera_platform_interface", "flutter", "flutter_plugin_android_lifecycle", "stream_transform"]}, {"name": "url_launcher_windows", "version": "3.0.1", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_web", "version": "2.0.13", "dependencies": ["flutter", "flutter_web_plugins", "url_launcher_platform_interface"]}, {"name": "url_launcher_platform_interface", "version": "2.1.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "url_launcher_macos", "version": "3.0.1", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_linux", "version": "3.0.1", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_ios", "version": "6.0.17", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_android", "version": "6.0.19", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "geolocator_windows", "version": "0.1.1", "dependencies": ["flutter", "geolocator_platform_interface"]}, {"name": "geolocator_web", "version": "2.1.6", "dependencies": ["flutter", "flutter_web_plugins", "geolocator_platform_interface"]}, {"name": "geolocator_apple", "version": "2.2.2", "dependencies": ["flutter", "geolocator_platform_interface"]}, {"name": "geolocator_android", "version": "4.1.3", "dependencies": ["flutter", "geolocator_platform_interface"]}, {"name": "geolocator_platform_interface", "version": "4.0.6", "dependencies": ["flutter", "meta", "plugin_platform_interface", "vector_math"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "flutter_blurhash", "version": "0.7.0", "dependencies": ["flutter"]}, {"name": "sqflite", "version": "2.1.0", "dependencies": ["flutter", "path", "sqflite_common"]}, {"name": "rxdart", "version": "0.27.5", "dependencies": []}, {"name": "pedantic", "version": "1.11.1", "dependencies": []}, {"name": "file", "version": "6.1.4", "dependencies": ["meta", "path"]}, {"name": "http_parser", "version": "4.0.1", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "nm", "version": "0.5.0", "dependencies": ["dbus"]}, {"name": "js", "version": "0.6.4", "dependencies": []}, {"name": "convert", "version": "3.0.2", "dependencies": ["typed_data"]}, {"name": "typed_data", "version": "1.3.1", "dependencies": ["collection"]}, {"name": "xdg_directories", "version": "0.2.0+2", "dependencies": ["meta", "path", "process"]}, {"name": "platform", "version": "3.1.0", "dependencies": []}, {"name": "petitparser", "version": "5.0.0", "dependencies": ["meta"]}, {"name": "stream_transform", "version": "2.0.0", "dependencies": []}, {"name": "cross_file", "version": "0.3.3+2", "dependencies": ["js", "meta"]}, {"name": "sqflite_common", "version": "2.3.0", "dependencies": ["meta", "path", "synchronized"]}, {"name": "dbus", "version": "0.7.4", "dependencies": ["args", "ffi", "meta", "xml"]}, {"name": "process", "version": "4.2.4", "dependencies": ["file", "path", "platform"]}], "configVersion": 1}