//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionFlexible_Task_Data_F.dart';
import 'SectionTaskDetailsPage.dart';
import 'SectionTaskPhotosPage.dart';

// ignore: camel_case_types
class SectionFlexible_Task_Data_FPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionFlexible_Task_Data_F sectionFlexible_Task_Data_F;

  const SectionFlexible_Task_Data_FPage(this.sectionFlexible_Task_Data_F,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionFlexible_Task_Data_FPageState();
  }
}

class _SectionFlexible_Task_Data_FPageState
    extends State<SectionFlexible_Task_Data_FPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionFlexible_Task_Data_F,
        title: "Flexible-Task-Data",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget
                            .sectionFlexible_Task_Data_F.sectionTaskDetails,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          SectionTaskDetailsPage(
                                              widget.sectionFlexible_Task_Data_F
                                                  .sectionTaskDetails)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget
                            .sectionFlexible_Task_Data_F.sectionTaskPhotos,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          SectionTaskPhotosPage(
                                              widget.sectionFlexible_Task_Data_F
                                                  .sectionTaskPhotos)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                ]),
          );
        });
  }
}
