# Firebase to Azure Migration Guide

This guide outlines the steps needed to migrate the API Inspection app from Firebase/GCP to Azure.

## Migration Overview

The API Inspection app is being migrated from Firebase/GCP to Azure. This involves:

1. Replacing Firebase Authentication with Azure AD B2C
2. Replacing Firebase Crashlytics with Azure Application Insights
3. Replacing Cloud Firestore with Azure Cosmos DB
4. Replacing Firebase Storage with Azure Blob Storage

## Prerequisites

Before starting the migration, you'll need:

1. An Azure subscription
2. Azure AD B2C tenant set up
3. Azure Cosmos DB account
4. Azure Storage account
5. Azure Application Insights instance
6. Azure Functions app (for image processing)

## Azure Resource Setup

### 1. Azure AD B2C

1. Create a new Azure AD B2C tenant or use an existing one
2. Create user flows for sign-up/sign-in, password reset, and profile editing
3. Register your app in Azure AD B2C
4. Configure identity providers (email, social logins, etc.)
5. Note down your client ID, tenant name, and policy names

### 2. Azure Cosmos DB

1. Create a Cosmos DB account with SQL API
2. Create a new database
3. Create collections matching your Firestore collections (e.g., inspections, assets, users)
4. Configure throughput and partition keys based on your data access patterns
5. Note down your connection strings and keys

### 3. Azure Blob Storage

1. Create a Storage Account
2. Create a Blob container for your app's files
3. Configure CORS settings for web/mobile access
4. Note down your connection strings and keys

### 4. Azure Application Insights

1. Create an Application Insights resource
2. Note down the instrumentation key

### 5. Azure Functions

1. Deploy the existing image processing functions to Azure Functions
2. Configure event triggers for blob storage
3. Update connection strings to use Azure Storage instead of Firebase

## Code Migration Steps

### 1. Update Dependencies

Update your `pubspec.yaml` to replace Firebase packages with Azure ones:

```yaml
dependencies:
  # Remove these Firebase packages
  # cloud_firestore
  # firebase_core
  # firebase_analytics
  # firebase_auth
  # firebase_crashlytics
  
  # Add these Azure packages
  http: ^1.1.0
  azure_storage_blob: ^0.1.0
  crypto: ^3.0.3
```

### 2. Update Authentication

1. Replace Firebase Authentication with Azure AD B2C using the `flutter_appauth` package
2. Update login, registration, and user profile screens
3. Update user session management

### 3. Update Database Access

1. Replace Firestore queries with Cosmos DB API calls
2. Update data models to work with Cosmos DB's JSON structure
3. Implement proper error handling for API calls

### 4. Update Storage

1. Replace Firebase Storage with Azure Blob Storage
2. Update file upload/download logic
3. Generate SAS tokens for secure file access

### 5. Update Crash Reporting

1. Replace Firebase Crashlytics with Azure Application Insights
2. Update error handling throughout the app

## Running the Migration

To perform the data migration:

1. Run the app with the new Azure configuration
2. Navigate to the Migration screen
3. Click "Start Migration"
4. Monitor the progress and check logs for any issues
5. Verify data integrity after migration

## Environment Configuration

The app uses different configuration settings based on the environment:

- Development: Uses Azure development resources
- Production: Uses Azure production resources

## Testing

After migration, thoroughly test:

1. Authentication flows
2. Data access and synchronization
3. File uploads and downloads
4. Error reporting
5. All app features to ensure they work with Azure services

## Troubleshooting

Common issues:

1. **Authentication failures**: Check Azure AD B2C configuration and policies
2. **Database access errors**: Verify Cosmos DB connection strings and permissions
3. **Storage errors**: Check Azure Storage account keys and container permissions
4. **Missing data**: Verify the migration process completed successfully

## Support

For migration support, contact the development <NAME_EMAIL>.

## Further Resources

- [Azure AD B2C Documentation](https://docs.microsoft.com/en-us/azure/active-directory-b2c/)
- [Azure Cosmos DB Documentation](https://docs.microsoft.com/en-us/azure/cosmos-db/)
- [Azure Blob Storage Documentation](https://docs.microsoft.com/en-us/azure/storage/blobs/)
- [Azure Application Insights Documentation](https://docs.microsoft.com/en-us/azure/azure-monitor/app/app-insights-overview)
