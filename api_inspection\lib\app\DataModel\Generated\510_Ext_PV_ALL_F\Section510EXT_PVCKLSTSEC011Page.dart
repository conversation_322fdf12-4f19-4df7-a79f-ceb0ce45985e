//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'Section510EXT_PVCKLSTSEC011.dart';

// ignore: camel_case_types
class Section510EXT_PVCKLSTSEC011Page extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final Section510EXT_PVCKLSTSEC011 section510EXT_PVCKLSTSEC011;

  const Section510EXT_PVCKLSTSEC011Page(this.section510EXT_PVCKLSTSEC011,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _Section510EXT_PVCKLSTSEC011PageState();
  }
}

class _Section510EXT_PVCKLSTSEC011PageState
    extends State<Section510EXT_PVCKLSTSEC011Page> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.section510EXT_PVCKLSTSEC011,
        title: "AUXILIARY EQUIPMENT",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC011
                      .attribute510EXT_PVCKLSTSEC011Q001
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC011
                      .attribute510EXT_PVCKLSTSEC011Q002
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC011
                      .attribute510EXT_PVCKLSTSEC011Q003
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC011
                      .attribute510EXT_PVCKLSTSEC011Q004
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC011
                      .attributeAre_threaded_connections_acceptably_engaged_and_leak_free
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC011
                      .attribute510EXT_PVCKLSTSEC011Q006
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC011
                      .attributeAre_threaded_connections_acceptable_for_continued_service
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .section510EXT_PVCKLSTSEC011
                      .attribute510EXT_PVCKLSTSEC011Q008
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
