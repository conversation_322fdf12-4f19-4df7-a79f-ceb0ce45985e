//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionInspection.dart';

// ignore: camel_case_types
class SectionInspectionPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionInspection sectionInspection;

  const SectionInspectionPage(this.sectionInspection, {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionInspectionPageState();
  }
}

class _SectionInspectionPageState extends State<SectionInspectionPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionInspection,
        title: "Inspection",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionInspection.attributeInspection_Code
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionInspection.attributeYear
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionInspection.attributeAddendum
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(widget
                      .sectionInspection.attributeNDE_Examination_Methods
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
