//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class Section510INT_PVCKLSTSEC016 extends DataModelSection {
  @override
  String getDisplayName() => "MISCELLANEOUS";
  Section510INT_PVCKLSTSEC016(DataModelItem? parent)
      : super(parent: parent, sectionName: "MISCELLANEOUS");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC016Q001 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Regardless of attachment type are all gasket sealing surfaces in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC016_Q001",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC016Q002 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Regardless of attachment type is all attachment hardware in acceptable condition for continued service: (nuts & bolts)",
          databaseName: "510_INT-PV_CKLST_SEC016_Q002",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC016Q003 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are auxiliary equipment connections and adjacent shell areas for gauge connections, thermal indicators etc. in acceptable condition for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC016_Q003",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC016Q004 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are there any threaded connections associated with the asset:",
          databaseName: "510_INT-PV_CKLST_SEC016_Q004",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeAre_threaded_connections_acceptably_engaged_and_leak_free =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are threaded connections acceptably engaged and leak free:",
          databaseName: "510_INT-PV_CKLST_SEC016_Q005",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC016Q006 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is threaded connection piping constructed from schedule 80 or greater piping:",
          databaseName: "510_INT-PV_CKLST_SEC016_Q006",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeAre_threaded_connections_acceptable_for_continued_service =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are threaded connections acceptable for continued service:",
          databaseName: "510_INT-PV_CKLST_SEC016_Q007",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510INT_PVCKLSTSEC016Q008 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Were drain connections verified to be free of any foreign material that may cause plugging:",
          databaseName: "510_INT-PV_CKLST_SEC016_Q008",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attribute510INT_PVCKLSTSEC016Q001,
      attribute510INT_PVCKLSTSEC016Q002,
      attribute510INT_PVCKLSTSEC016Q003,
      attribute510INT_PVCKLSTSEC016Q004,
      attributeAre_threaded_connections_acceptably_engaged_and_leak_free,
      attribute510INT_PVCKLSTSEC016Q006,
      attributeAre_threaded_connections_acceptable_for_continued_service,
      attribute510INT_PVCKLSTSEC016Q008,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section510INT_PVCKLSTSEC016";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
