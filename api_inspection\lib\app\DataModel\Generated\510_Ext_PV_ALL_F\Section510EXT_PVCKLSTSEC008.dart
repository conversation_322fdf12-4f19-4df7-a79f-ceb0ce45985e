//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ignore: camel_case_types
class Section510EXT_PVCKLSTSEC008 extends DataModelSection {
  @override
  String getDisplayName() => "PRESSURE RELIEF (PRD/PRV)";
  Section510EXT_PVCKLSTSEC008(DataModelItem? parent)
      : super(parent: parent, sectionName: "PRESSURE RELIEF (PRD/PRV)");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC008Q001 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are PRD’s and or PRV’s set at or below asset MAWP and in acceptable condition for continued service: (Data plate & calibration tag information shall be recorded)",
          databaseName: "510_EXT-PV_CKLST_SEC008_Q001",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC008Q002 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Has an inspection & test of Relief been performed within the required interval:  (i.e. 5 years for typical process services,10 years for clean (non-fouling) and noncorrosive services)",
          databaseName: "510_EXT-PV_CKLST_SEC008_Q002",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC008Q003 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Were any corrosion, erosion, or pitting cells noted on the installed PRD / PRV:",
          databaseName: "510_EXT-PV_CKLST_SEC008_Q003",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC008Q004 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Were any crack like indications noted on the installed PRD / PRV:",
          databaseName: "510_EXT-PV_CKLST_SEC008_Q004",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeWas_any_mechanical_damage_noted_on_the_PRD__PRV =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Was any mechanical damage noted on the PRD / PRV",
          databaseName: "510_EXT-PV_CKLST_SEC008_Q005",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeIs_the_Relief_attachment_achieved_via_flanging =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Is the Relief attachment achieved via flanging:",
          databaseName: "510_EXT-PV_CKLST_SEC008_Q006",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC008Q007 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Were flange fasteners fully engaged: (Any fastener is considered acceptably engaged if the lack of complete engagement is not more than one thread)",
          databaseName: "510_EXT-PV_CKLST_SEC008_Q007",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC008Q008 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are the flange fasteners utilized in alignment with Client specification:",
          databaseName: "510_EXT-PV_CKLST_SEC008_Q008",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC008Q009 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are the flange gaskets utilized in alignment with Client specification:",
          databaseName: "510_EXT-PV_CKLST_SEC008_Q009",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC008Q010 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the Relief attachment achieved via threaded connection(s):",
          databaseName: "510_EXT-PV_CKLST_SEC008_Q010",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC008Q011 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Were the Relief threaded connection(s) in acceptable condition for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC008_Q011",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeIs_the_Relief_valve_vent_piping_routed_to_a_safe_location =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the Relief valve vent piping routed to a safe location:",
          databaseName: "510_EXT-PV_CKLST_SEC008_Q012",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC008Q013 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Was the Relief valve vent piping verified to be free of any foreign material that may cause plugging:",
          databaseName: "510_EXT-PV_CKLST_SEC008_Q013",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC008Q014 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Was the bellows vent verified to be free of any foreign material that may cause plugging:",
          databaseName: "510_EXT-PV_CKLST_SEC008_Q014",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeIs_the_spring_tamper_car_seal_intact =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Is the spring tamper car seal intact:",
          databaseName: "510_EXT-PV_CKLST_SEC008_Q015",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC008Q016 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the manual operation leaver in acceptable condition for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC008_Q016",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeIs_Relief_alignment_acceptable =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Is Relief alignment acceptable:",
          databaseName: "510_EXT-PV_CKLST_SEC008_Q017",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC008Q018 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Were the Relief supports in acceptable condition for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC008_Q018",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeWas_any_excessive_vibration_of_the_Relief_noted =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Was any excessive vibration of the Relief noted:",
          databaseName: "510_EXT-PV_CKLST_SEC008_Q019",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeWas_the_rupture_device_orientation_correct =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName: "Was the rupture device orientation correct:",
          databaseName: "510_EXT-PV_CKLST_SEC008_Q020",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA
      attributeAre_associated_block_valves_car_sealed_in_the_open_position =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Are associated block valves car sealed in the open position:",
          databaseName: "510_EXT-PV_CKLST_SEC008_Q021",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC008Q022 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Was evidence of prior or active leakage noted originating from PRD / PRV:",
          databaseName: "510_EXT-PV_CKLST_SEC008_Q022",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
        PredefinedValueOption("Yes", null, isCommentRequired: true),
      ]);

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute_YesNoNA attribute510EXT_PVCKLSTSEC008Q023 =
      PredefinedValueAttribute_YesNoNA(
          parent: this,
          displayName:
              "Is the PRD / PRV in acceptable condition for continued service:",
          databaseName: "510_EXT-PV_CKLST_SEC008_Q023",
          availableOptions: [
        PredefinedValueOption("N/A", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: true),
        PredefinedValueOption("Yes", null, isCommentRequired: false),
      ]);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      attribute510EXT_PVCKLSTSEC008Q001,
      attribute510EXT_PVCKLSTSEC008Q002,
      attribute510EXT_PVCKLSTSEC008Q003,
      attribute510EXT_PVCKLSTSEC008Q004,
      attributeWas_any_mechanical_damage_noted_on_the_PRD__PRV,
      attributeIs_the_Relief_attachment_achieved_via_flanging,
      attribute510EXT_PVCKLSTSEC008Q007,
      attribute510EXT_PVCKLSTSEC008Q008,
      attribute510EXT_PVCKLSTSEC008Q009,
      attribute510EXT_PVCKLSTSEC008Q010,
      attribute510EXT_PVCKLSTSEC008Q011,
      attributeIs_the_Relief_valve_vent_piping_routed_to_a_safe_location,
      attribute510EXT_PVCKLSTSEC008Q013,
      attribute510EXT_PVCKLSTSEC008Q014,
      attributeIs_the_spring_tamper_car_seal_intact,
      attribute510EXT_PVCKLSTSEC008Q016,
      attributeIs_Relief_alignment_acceptable,
      attribute510EXT_PVCKLSTSEC008Q018,
      attributeWas_any_excessive_vibration_of_the_Relief_noted,
      attributeWas_the_rupture_device_orientation_correct,
      attributeAre_associated_block_valves_car_sealed_in_the_open_position,
      attribute510EXT_PVCKLSTSEC008Q022,
      attribute510EXT_PVCKLSTSEC008Q023,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "Section510EXT_PVCKLSTSEC008";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
