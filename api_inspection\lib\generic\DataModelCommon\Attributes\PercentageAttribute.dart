import 'package:api_inspection/generic/AttributeControls/AttributeStructure.dart';
import 'package:api_inspection/generic/AttributeControls/Percentage/PercentageAttributeView.dart';
import 'package:api_inspection/generic/Common/Types/Percentage.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

import 'AttributeBase.dart';
import 'ChangeLog/ChangeLogEntry.dart';

class PercentageAttribute extends SingleAttributeBase<double> {
  String displayUnit = "%";

  double? getValueFromDb() {
    if (valueChangeLog.entries.isEmpty) return null;
    return valueChangeLog.entries.last.value;
  }

  Percentage? getValue() {
    var value = getValueFromDb();
    if (value != null) {
      return Percentage.from0To1(value);
    } else {
      return null;
    }
  }

  @override
  bool hasData() {
    return getValue() != null;
  }

  void setValue(Percentage? value) {
    if (getValue() == value) return;

    var entry = ChangeLogEntry<double>.newlyCreated(value?.value0To1);
    valueChangeLog.addNewItem(entry);

    notifyListeners();
  }

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  bool allowNegatives;

  PercentageAttribute(
      {required DataModelItem parent,
      required String displayName,
      required this.allowNegatives,
      Percentage? minValue,
      Percentage? maxValue,
      Widget? iconWidget,
      bool areCommentsRequired = false,
      String? databaseName})
      : super(parent, displayName, iconWidget, areCommentsRequired,
            databaseName) {
    valueChangeLog.setConversionMethod(convertDynamicToDouble);
  }

  double? convertDynamicToDouble(dynamic dyn) {
    if (dyn is int) {
      return dyn.toDouble();
    }
    return dyn as double?;
  }

  @override
  Widget buildWidget(
      {IsEditableController? editingController,
      bool showPhotos = true,
      bool showComments = true}) {
    return PercentageAttributeView(
      this,
      editingController: editingController,
      showPhotos: showPhotos,
      showComments: showComments,
    );
  }

  @override
  String getPreviewText() {
    var value = getValue();
    if (value == null) return "";
    return value.value0To100.toString() + " %";
  }
}
