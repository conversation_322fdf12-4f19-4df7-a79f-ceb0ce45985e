//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/UI/CollectionControl.dart';
import 'package:uuid/uuid.dart';
import 'SectionTankRoof.dart';

// ignore: camel_case_types
// ignore: camel_case_types
class SectionTankRoofPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final DataModelCollection<SectionTankRoof> sectionTankRoof;
  const SectionTankRoofPage(this.sectionTankRoof, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionTankRoofPageState();
  }
}

class _SectionTankRoofPageState extends State<SectionTankRoofPage> {
  Widget _cardBuilder(BuildContext context, int number, SectionTankRoof item) {
    return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(number.toString(),
              style: const TextStyle(color: Colors.white, fontSize: 18)),
        ]);
  }

  Widget _editPageBuilder(BuildContext context, SectionTankRoof item) {
    return SectionScaffold(
        section: item,
        title: "Tank Roof",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(item.attributeType
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item
                      .attributeMaterial_Spec_and_Grade
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item
                      .attributeNominal_thickness_roof
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(item
                      .attributeCorrosion_Allowance
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }

  SectionTankRoof _createNewItem() {
    String id = const Uuid().v4();
    var item = SectionTankRoof(id, widget.sectionTankRoof);

    return item;
  }

  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionTankRoof,
        title: "Tank Roof",
        childBuilder: (editableController) {
          return Center(
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: <Widget>[
                Expanded(
                    child: CollectionControl<SectionTankRoof>(
                  cardTitle: "Tank Roof",
                  collection: widget.sectionTankRoof,
                  cardBuilder: _cardBuilder,
                  createNewItem: _createNewItem,
                  editPageBuilder: _editPageBuilder,
                ))
              ]));
        });
  }
}
