//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface;
using CommonDataInterface.Attributes;

namespace APMWebDataInterface.Generated.sectionLeak_Report_F
{
  public class SectionWorkDetail : DataModelItem {

    public override String DisplayName { 
      get {
        return "Work Detail";
      }
    }

    #region [-- Sub Sections --]

    

    #endregion [-- Sub Sections --]

    #region [-- Attributes --]

    public StringAttribute attributeClient;
    public StringAttribute attributeCity;
    public StringAttribute attributeState;
    public StringAttribute attributePostal_Code;
    public PredefinedValueAttribute attributeArea;
    public StringAttribute attributeLease;
    public StringAttribute attributeFacility;
    public StringAttribute attributeJob_Description;
    public StringAttribute attributeClient_Contact;
    public PhoneNumberAttribute attributeClient_Contact_Number;
    public StringAttribute attributePurchase_OrderAFE;
    public StringAttribute attributeClient_Cost_Code;
    public StringAttribute attributeClient_Work_Order;
    public StringAttribute attributeTeam_District;
    public StringAttribute attributeTeam_Project_Number;
    public StringAttribute attributeInspection_Reference;
    public StringAttribute attributeReference_EditionRevision;
    public MultiPredefinedValueAttribute attributeInspection_Type;
    public StringAttribute attributeInspected_By;
    public DateAttribute attributeInspection_Date;
    public StringAttribute attributeInspector_Certificate_Number;
    public StringAttribute attributeReviewed_By;
    public StringAttribute attributeReviewer_Email;
    public StringAttribute attributeReviewer_Certificate_Number;

    #endregion [-- Attributes --]

    internal override String GetDBName() => "SectionWorkDetail";

    public SectionWorkDetail(DataModelItem parent) : base(parent)
    {
            
        attributeClient = new StringAttribute(this, displayName: "Client", databaseName: "LR_Q005"); 
     
        attributeCity = new StringAttribute(this, displayName: "City", databaseName: "LR_Q006"); 
     
        attributeState = new StringAttribute(this, displayName: "State", databaseName: "LR_Q007"); 
     
        attributePostal_Code = new StringAttribute(this, displayName: "Postal Code", databaseName: "LR_Q008"); 
     
        attributeArea = new PredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("Coalinga", null),
          new PredefinedValueOption("Cymric", null),
          new PredefinedValueOption("Kern River", null),
          new PredefinedValueOption("Lost Hills", null),
          new PredefinedValueOption("McKittrick", null),
          new PredefinedValueOption("San Ardo", null)
        }, true, this, "Area", databaseName: "LR_Q009"); 
     
        attributeLease = new StringAttribute(this, displayName: "Lease", databaseName: "LR_Q010"); 
     
        attributeFacility = new StringAttribute(this, displayName: "Facility", databaseName: "LR_Q011"); 
     
        attributeJob_Description = new StringAttribute(this, displayName: "Job Description", databaseName: "LR_Q012"); 
     
        attributeClient_Contact = new StringAttribute(this, displayName: "Client Contact", databaseName: "LR_Q013"); 
     
        attributeClient_Contact_Number = new PhoneNumberAttribute(this, displayName: "Client Contact Number", databaseName: "LR_Q014"); 
     
        attributePurchase_OrderAFE = new StringAttribute(this, displayName: "Purchase Order/AFE", databaseName: "LR_Q015"); 
     
        attributeClient_Cost_Code = new StringAttribute(this, displayName: "Client Cost Code", databaseName: "LR_Q016"); 
     
        attributeClient_Work_Order = new StringAttribute(this, displayName: "Client Work Order", databaseName: "LR_Q017"); 
     
        attributeTeam_District = new StringAttribute(this, displayName: "Team District", databaseName: "LR_Q018"); 
     
        attributeTeam_Project_Number = new StringAttribute(this, displayName: "Team Project Number", databaseName: "LR_Q019"); 
     
        attributeInspection_Reference = new StringAttribute(this, displayName: "Inspection Reference", databaseName: "LR_Q020"); 
     
        attributeReference_EditionRevision = new StringAttribute(this, displayName: "Reference Edition/Revision", databaseName: "LR_Q021"); 
     
        attributeInspection_Type = new MultiPredefinedValueAttribute(new List<PredefinedValueOption>{
          new PredefinedValueOption("MT", null),
          new PredefinedValueOption("PT", null),
          new PredefinedValueOption("RT", null),
          new PredefinedValueOption("UT", null),
          new PredefinedValueOption("VT", null)
        }, true, this, "Inspection Type", databaseName: "LR_Q022"); 
     
        attributeInspected_By = new StringAttribute(this, displayName: "Inspected By", databaseName: "LR_Q023"); 
     
        attributeInspection_Date = new DateAttribute(this, displayName: "Inspection Date", databaseName: "LR_Q024", areCommentsRequired: false); 
     
        attributeInspector_Certificate_Number = new StringAttribute(this, displayName: "Inspector Certificate Number", databaseName: "LR_Q025"); 
     
        attributeReviewed_By = new StringAttribute(this, displayName: "Reviewed By:", databaseName: "LR_Q026"); 
     
        attributeReviewer_Email = new StringAttribute(this, displayName: "Reviewer Email", databaseName: "LR_Q027"); 
     
        attributeReviewer_Certificate_Number = new StringAttribute(this, displayName: "Reviewer Certificate Number", databaseName: "LR_Q028"); 
    }   
    
    public override DataModelItem[] GetChildren() {
        return new DataModelItem[] { 
           attributeClient,
           attributeCity,
           attributeState,
           attributePostal_Code,
           attributeArea,
           attributeLease,
           attributeFacility,
           attributeJob_Description,
           attributeClient_Contact,
           attributeClient_Contact_Number,
           attributePurchase_OrderAFE,
           attributeClient_Cost_Code,
           attributeClient_Work_Order,
           attributeTeam_District,
           attributeTeam_Project_Number,
           attributeInspection_Reference,
           attributeReference_EditionRevision,
           attributeInspection_Type,
           attributeInspected_By,
           attributeInspection_Date,
           attributeInspector_Certificate_Number,
           attributeReviewed_By,
           attributeReviewer_Email,
           attributeReviewer_Certificate_Number,
        };
    }
  }
}
