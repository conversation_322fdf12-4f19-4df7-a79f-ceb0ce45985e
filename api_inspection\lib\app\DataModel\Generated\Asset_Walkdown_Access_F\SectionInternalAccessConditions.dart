//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////

// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhoneNumberAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/LocationAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PhotoAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/IntegerAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DoubleAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/DateAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/PredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/Collection/MultiPredefinedValueAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/StringAttribute.dart';
// ignore: unused_import
import 'package:api_inspection/generic/DataModelCommon/Attributes/BooleanAttribute.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'SectionInternalAccessRequirements.dart';
import 'SectionCleaningRequirements.dart';
import 'SectionInternalCoatingLiner.dart';

// ignore: camel_case_types
class SectionInternalAccessConditions extends DataModelSection {
  @override
  String getDisplayName() => "Internal Access Conditions";
  SectionInternalAccessConditions(DataModelItem? parent)
      : super(parent: parent, sectionName: "Internal Access Conditions");

  // ignore: non_constant_identifier_names
  late PredefinedValueAttribute attributeIs_the_asset_out_of_service =
      PredefinedValueAttribute(
          parent: this,
          hasOtherOption: false,
          displayName: "Is the asset out of service?",
          databaseName: "AWA_Q305",
          availableOptions: [
        PredefinedValueOption("Yes", null, isCommentRequired: false),
        PredefinedValueOption("No", null, isCommentRequired: false),
      ]);

// ignore: non_constant_identifier_names
  late SectionInternalAccessRequirements sectionInternalAccessRequirements =
      SectionInternalAccessRequirements(this);
  // ignore: non_constant_identifier_names
  late SectionCleaningRequirements sectionCleaningRequirements =
      SectionCleaningRequirements(this);
  // ignore: non_constant_identifier_names
  late SectionInternalCoatingLiner sectionInternalCoatingLiner =
      SectionInternalCoatingLiner(this);

  @override
  List<DataModelItem> getChildren() {
    var children = super.getChildren().toList();
    children.addAll([
      sectionInternalAccessRequirements,
      sectionCleaningRequirements,
      sectionInternalCoatingLiner,
      attributeIs_the_asset_out_of_service,
    ]);
    return children;
  }

  @override
  String getDBName() {
    return "SectionInternalAccessConditions";
  }

  @override
  void saveDirectItems(WriteBatch batch) async {}

  @override
  bool updateDirectPropertiesFromMapEntry(MapEntry entry) {
    return false;
  }
}
