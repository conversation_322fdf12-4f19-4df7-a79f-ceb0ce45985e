//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'SectionOperatingDesignConditions.dart';
import 'SectionShellSidePage.dart';
import 'SectionTubeSidePage.dart';
import 'SectionDimensionsPage.dart';

// ignore: camel_case_types
class SectionOperatingDesignConditionsPage extends StatefulWidget {
  // ignore: non_constant_identifier_names
  final SectionOperatingDesignConditions sectionOperatingDesignConditions;

  const SectionOperatingDesignConditionsPage(
      this.sectionOperatingDesignConditions,
      {Key? key})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _SectionOperatingDesignConditionsPageState();
  }
}

class _SectionOperatingDesignConditionsPageState
    extends State<SectionOperatingDesignConditionsPage> {
  @override
  Widget build(BuildContext context) {
    return SectionScaffold(
        section: widget.sectionOperatingDesignConditions,
        title: "Operating/Design Conditions",
        childBuilder: (editableController) {
          return SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  AttributePadding.WithStdPadding(widget
                      .sectionOperatingDesignConditions
                      .attributeOperating_Temperature
                      .buildWidget(editingController: editableController)),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget
                            .sectionOperatingDesignConditions.sectionShellSide,
                        onPressed: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => SectionShellSidePage(
                                      widget.sectionOperatingDesignConditions
                                          .sectionShellSide))).then(
                              (value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget
                            .sectionOperatingDesignConditions.sectionTubeSide,
                        onPressed: () {
                          Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => SectionTubeSidePage(
                                          widget
                                              .sectionOperatingDesignConditions
                                              .sectionTubeSide)))
                              .then((value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(
                    TeamSectionButton(
                        section: widget
                            .sectionOperatingDesignConditions.sectionDimensions,
                        onPressed: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => SectionDimensionsPage(
                                      widget.sectionOperatingDesignConditions
                                          .sectionDimensions))).then(
                              (value) => setState(() {}));
                        }),
                  ),
                  AttributePadding.WithStdPadding(widget
                      .sectionOperatingDesignConditions
                      .attributeOperation_Status
                      .buildWidget(editingController: editableController)),
                ]),
          );
        });
  }
}
