﻿using System;
using System.Collections.Generic;
using System.Linq;
using CommonDataInterface.DatabaseHelpers;

namespace CommonDataInterface
{
  class MediaCache
  {
    public int? width;
    public int? height;
    public int? quality;
    public bool pad;


    private static List<MediaCache> _caches = new List<MediaCache>();
    public static MediaCache Global(int width, int height, int quality, bool pad)
    {
      var cache = _caches.FirstOrDefault(a => a.width == width && a.height == height && a.quality == quality && a.pad == pad);
      if (cache == null) {
        cache = new MediaCache();
        cache.height = height;
        cache.width = width;
        cache.quality = quality;
        cache.pad = pad;
        _caches.Add(cache);
      }

      return cache;
    }

    private object _mediaLock = new object();
    Dictionary<string, byte[]> _mediaMap = new Dictionary<string, byte[]>();

    public void AddEntry(String mediaName, String extension, int version, byte[] data)
    {
      lock (_mediaLock) {
        _mediaMap[mediaName + "|" + extension + "|" + version] = data;
      }
    }

    public byte[] FindEntry(String mediaName, String extension, int version) {
      lock (_mediaLock) {
        if (_mediaMap.ContainsKey(mediaName + "|" + extension + "|" + version)) {
          return _mediaMap[mediaName + "|" + extension + "|" + version];
        }

        return null;
      }
    }
    



    public static MediaEntry FromMapEntry(String mediaName, Dictionary<string, object> map, DataModelItem parent)
    {
      if (!map.ContainsKey("E"))
        return null;
      var extension = map["E"] as String;

      MediaEntry entry = new MediaEntry(mediaName, extension, parent);
      foreach (var item in map) {
        entry.UpdateDirectPropertiesFromMapEntry(item);
      }

      return entry;
      
    }

  }
}