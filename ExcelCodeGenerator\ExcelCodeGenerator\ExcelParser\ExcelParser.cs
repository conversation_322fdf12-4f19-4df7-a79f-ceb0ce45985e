﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using CheckListGen;
using ExcelDataReader;

namespace ExcelCodeGenerator.ExcelParser
{
    internal class ExcelParser
    {
        public static List<Section> BuildForms(string excelDocumentPath, string[] tableNames)
        {
            var parser = new ExcelParser();

            return parser.DoBuildForms(excelDocumentPath, tableNames);
        }


        private List<Section> DoBuildForms(string excelDocumentPath, IEnumerable<string> tableNames)
        {
            var forms = new List<Section>();
            using (var stream = File.Open(excelDocumentPath, FileMode.Open, FileAccess.Read))
            {
                using (var reader = ExcelReaderFactory.CreateReader(stream))
                {
                    var result = reader.AsDataSet();
                    foreach (var tableName in tableNames)
                    {
                        var table = result.Tables[tableName];

                        if (table == null) throw new Exception($"Table with name {tableName} does not exist");

                        var excelForm = ReadTable(table);
                        var form = BuildFormFromExcel(excelForm);
                        forms.Add(form);
                    }
                }
            }

            return forms;
        }


        private Section BuildFormFromExcel(ExcelForm excelForm)
        {
            var form = new Section
            {
                Name = excelForm.Name,
                DataName = excelForm.Name
            };

            form.DisplayName = form.Name.EndsWith("-F") ? form.Name.Substring(0, form.Name.Length - 2) : form.Name;

            form.IsCollection = false;
            form.IsInline = false;
            form.Order = 1;

            form.Children = BuildChildren(excelForm, "root");
            return form;
        }

        private static List<IChildItem> BuildChildren(ExcelForm excelForm, string parentSectionId)
        {
            var matchingSections = excelForm.Sections.Where(a =>
                string.Equals(a.ParentId, parentSectionId, StringComparison.InvariantCultureIgnoreCase)).ToArray();

            var returnSections = new List<IChildItem>();
            foreach (var section in matchingSections)
            {
                var builtSection = new Section
                {
                    Name = section.Name,
                    DataName = section.DataName,
                    DisplayName = section.Name,
                    Order = section.Order,
                    IsCollection = section.IsCollection,
                    IsInline = section.IsInline,
                    Decorators = section.Decorators ?? Array.Empty<string>()
                };

                builtSection.Children.AddRange(BuildChildren(excelForm, section.Id));

                returnSections.Add(builtSection);
            }

            var matchingQuestions = excelForm.Questions.Where(a =>
                string.Equals(a.SectionId, parentSectionId, StringComparison.InvariantCultureIgnoreCase)).ToArray();
            returnSections.AddRange(matchingQuestions.Select(question => question.Question));

            return returnSections.ToList();
        }


        private static ExcelForm ReadTable(DataTable table)
        {
            var parsers = new IExcelParser[]
            {
                new Version1Parser(),
                new Version2Parser()
            };

            var parser = parsers.FirstOrDefault(a => a.IsTableInFormat(table));

            if (parser == null)
                throw new Exception("Failed to find parser to parse table: " + table.TableName);

            return parser.ReadTable(table);
        }
    }
}