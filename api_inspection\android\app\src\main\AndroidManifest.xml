<manifest
        xmlns:android="http://schemas.android.com/apk/res/android"
        package="com.example.api_inspection"
        xmlns:tools="http://schemas.android.com/tools">
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <application
            android:label="API Inspection"
            android:name="${applicationName}"
            android:icon="@mipmap/launcher_icon">
        <activity
                android:name=".MainActivity"
                android:exported="true"
                android:launchMode="singleTop"
                android:theme="@style/LaunchTheme"
                android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
                android:hardwareAccelerated="true"
                android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
                    android:name="io.flutter.embedding.android.NormalTheme"
                    android:resource="@style/NormalTheme" />
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
                android:name="flutterEmbedding"
                android:value="2" />

        <provider
                android:name="androidx.startup.InitializationProvider"
                android:authorities="${applicationId}.androidx-startup"
                android:exported="false"
                tools:node="merge">

            <!-- If you are using androidx.startup to initialize other components -->
            <meta-data
                    android:name="androidx.work.WorkManagerInitializer"
                    android:value="androidx.startup"
                    tools:node="remove" />
        </provider>

        <provider
                android:name="com.bluechilli.flutteruploader.FlutterUploaderInitializer"
                android:authorities="${applicationId}.flutter-upload-init"
                android:exported="false">
            <!-- changes this number to configure the maximum number of concurrent tasks -->
            <meta-data
                    android:name="com.bluechilli.flutterupload.MAX_CONCURRENT_TASKS"
                    android:value="2" />

            <!-- changes this number to configure connection timeout for the upload http request -->
            <meta-data
                    android:name="com.bluechilli.flutteruploader.UPLOAD_CONNECTION_TIMEOUT_IN_SECONDS"
                    android:value="3600" />
        </provider>
    </application>
</manifest>