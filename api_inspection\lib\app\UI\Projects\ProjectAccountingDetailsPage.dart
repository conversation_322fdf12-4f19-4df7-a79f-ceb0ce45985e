import 'package:api_inspection/generic/AppStyle.dart';
import 'package:flutter/material.dart';
import 'package:api_inspection/app/DataModel/project.dart';
import 'package:api_inspection/generic/UIControls/attributePadding.dart';

class ProjectAccountingDetailsPage extends StatelessWidget {
  final ProjectAccountingDetails accountingDetails;
  const ProjectAccountingDetailsPage(this.accountingDetails, {Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: const Color.fromARGB(255, 24, 28, 32),
        appBar: AppBar(
          title: Text(
            "Accounting Details",
            style: TextStyle(fontSize: AppStyle.global.toolBarFontSize),
          ),
          toolbarHeight: AppStyle.global.toolBarHeight,
        ),
        body: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              AttributePadding.WithStdPadding(
                  accountingDetails.apmProjectNumber.buildWidget()),
              AttributePadding.WithStdPadding(
                  accountingDetails.teamDistrictNumber.buildWidget()),
              AttributePadding.WithStdPadding(
                  accountingDetails.teamProjectNumber.buildWidget()),
              AttributePadding.WithStdPadding(
                  accountingDetails.purchaseOrderAFE.buildWidget()),
              AttributePadding.WithStdPadding(
                  accountingDetails.workOrderNumber.buildWidget()),
              AttributePadding.WithStdPadding(
                  accountingDetails.projectNumber.buildWidget()),
            ],
          ),
        ));
  }
}
