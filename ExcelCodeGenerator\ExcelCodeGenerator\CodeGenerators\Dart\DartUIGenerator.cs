﻿using System;
using System.IO;
using System.Linq;
using CheckListGen;

namespace ExcelCodeGenerator.CodeGenerators.Dart
{
    public class DartUIGenerator
    {
        private DartUIGenerator()
        {
        }

        public static void BuildSectionUIFile(Section section, string outputDirectory)
        {
            if (section.IsCollection)
                BuildSectionUIFile_Collection(section, outputDirectory);
            else
                BuildSectionUIFile_NonCollection(section, outputDirectory);
        }


        private static void BuildSectionUIFile_Collection(Section section, string outputDirectory)
        {
            var sections = section.Children.OfType<Section>().ToArray();

            var fileContents =
                @"//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import 'package:api_inspection/generic/DataModelCommon/DataModelItem.dart';
import 'package:api_inspection/generic/UI/CollectionControl.dart';
import 'package:uuid/uuid.dart';
import '" + section.DartClassName + @".dart';
" +
                sections.Select(a => "import '" + a.DartUIClassName + ".dart';")
                    .AggregateEXT((a, b) => a + Environment.NewLine + b)
                +
                @"


// ignore: camel_case_types
// ignore: camel_case_types
class " + section.DartUIClassName + @" extends StatefulWidget  {
  
  // ignore: non_constant_identifier_names
  final DataModelCollection<" + section.DartClassName + "> " + section.DartVariableName + @";
  " + section.DartUIClassName + "(this." + section.DartVariableName + @", {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _" + section.DartUIClassName + @"State();
  }


}
class _" + section.DartUIClassName + @"State extends State<" + section.DartUIClassName + @"> {

  
  Widget _cardBuilder(BuildContext context, int number, " + section.DartClassName + @" item) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, mainAxisAlignment: MainAxisAlignment.center, 
      children:[ 
        " + BuildCardViewWidgets(section) + @"
      ]
    );
  }

  
  Widget _editPageBuilder(BuildContext context, " + section.DartClassName + @" item) {
    return SectionScaffold(section: item, title: """ + section.DisplayName + @""", childBuilder: (editableController) {
        return SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
             " + section.Children.OrderBy(a => a.Order).Select(a => BuildChildUILineWithoutParent(a, "item"))
                    .AggregateEXT((a, b) => a + "\r\n             " + b) + @"          
          ]),
        );
      }  
    );
  }


  
  " + section.DartClassName + @" _createNewItem(){
    String id = Uuid().v4();
    var item = " + section.DartClassName + @"(id, widget." + section.DartVariableName + @");

    return item;    
  }

  @override
  Widget build(BuildContext context) {
  return SectionScaffold(section: widget." + section.DartVariableName + @", title: """ + section.DisplayName +
                @""", childBuilder: (editableController) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[               
            Expanded(child: 
              CollectionControl<" + section.DartClassName + @">(
                cardTitle: """ + section.Name + @""",
                collection: widget." + section.DartVariableName + @",
                cardBuilder: _cardBuilder,
                createNewItem: _createNewItem,
                editPageBuilder: _editPageBuilder,
              )
            )
          ])
      );}
    );
  }

}";

            File.WriteAllText(outputDirectory + "/" + section.DartUIClassName + ".dart", fileContents);
        }

        private static void BuildSectionUIFile_NonCollection(Section section, string outputDirectory)
        {
            var sections = section.Children.OfType<Section>().ToArray();

            var fileContents =
                @"//////////////////////////////////////////////////////
//       GENERATED FILE - DO NOT MODIFY DIRECTLY    //
//////////////////////////////////////////////////////
import 'package:api_inspection/generic/UIControls/sectionScaffold.dart';
// ignore: unused_import
import 'package:api_inspection/generic/AppStyle.dart';
// ignore: unused_import
import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/TeamToggleButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/SectionButton.dart';
// ignore: unused_import
import 'package:api_inspection/generic/UIControls/attributePadding.dart';
import '" + section.DartClassName + @".dart';
" +
                sections.Select(a => "import '" + a.DartUIClassName + ".dart';")
                    .AggregateEXT((a, b) => a + Environment.NewLine + b)
                +
                @"




// ignore: camel_case_types
class " + section.DartUIClassName + @" extends StatefulWidget  {
  
  // ignore: non_constant_identifier_names
  final " + section.DartClassName + " " + section.DartVariableName + @";


  " + section.DartUIClassName + @"(this." + section.DartVariableName + @", {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _" + section.DartUIClassName + @"State();
  }


}
class _" + section.DartUIClassName + @"State extends State<" + section.DartUIClassName + @"> {

  @override
  Widget build(BuildContext context) {
  return SectionScaffold(section: widget." + section.DartVariableName + @", title: """ + section.DisplayName +
                @""", childBuilder: (editableController) {
      return SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
             " + section.Children.OrderBy(a => a.Order).Select(a => BuildChildUILine(a, section.DartVariableName))
                    .AggregateEXT((a, b) => a + "\r\n             " + b) + @"
      
        ]),
      );}
    );
  }

}";

            File.WriteAllText(outputDirectory + "/" + section.DartUIClassName + ".dart", fileContents);
        }

        private static string BuildChildUILineWithoutParent(IChildItem child, string parentVariableName)
        {
            switch (child)
            {
                case Question question:
                    return "AttributePadding.WithStdPadding(" + parentVariableName + "." + question.DartVariableName +
                           ".buildWidget(editingController: editableController)),";
                case Section section:
                    return @"                 AttributePadding.WithStdPadding(
                   TeamSectionButton(section: " + parentVariableName + "." + section.DartVariableName +
                           @", onPressed: (){ Navigator.push(context, new MaterialPageRoute(builder: (context)=> " +
                           section.DartUIClassName + @"(widget." + parentVariableName + "." +
                           section.DartVariableName +
                           @"))).then((value) => setState((){})); }),
                 ),";
                default:
                    throw new Exception("Found invalid child type");
            }
        }

        private static string BuildChildUILine(IChildItem child, string parentVariableName)
        {
            switch (child)
            {
                case Question question:
                    return "AttributePadding.WithStdPadding(widget." + parentVariableName + "." +
                           question.DartVariableName + ".buildWidget(editingController: editableController)),";
                case Section section:
                    return @"                 AttributePadding.WithStdPadding(
                   TeamSectionButton(section: widget." + parentVariableName + "." + section.DartVariableName +
                           @", onPressed: (){ Navigator.push(context, new MaterialPageRoute(builder: (context)=> " +
                           section.DartUIClassName + @"(widget." + parentVariableName + "." +
                           section.DartVariableName +
                           @"))).then((value) => setState((){})); }),
                 ),";
                default:
                    throw new Exception("Found invalid child type");
            }
        }

        private static string BuildCardViewWidgets(Section section)
        {
            var rtn = "";
            var questions = section.Children.OfType<Question>().Where(a => a.Decorators.Contains("ShowOnCard"))
                .ToArray();

            if (section.Decorators.Any(a => a.ToLower().Contains("showcountoncard")) || questions.Length == 0)
                rtn +=
                    "Text(number.toString(), style: TextStyle(color: Colors.white, fontSize: AppStyle.global.fontSize18)), \r\n        ";

            return questions.Aggregate(rtn,
                (current, question) => current + "Text(\"" + question.DisplayText + ": \" + (item." +
                                       question.DartVariableName + ".getPreviewText() == \"\" ? \"Not set\" : item." +
                                       question.DartVariableName +
                                       ".getPreviewText()), style: TextStyle(color: Colors.white, fontSize: 18)), \r\n        ");
        }
    }
}