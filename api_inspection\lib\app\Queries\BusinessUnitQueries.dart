import 'dart:async';
import 'package:api_inspection/app/Queries/Queries.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:api_inspection/generic/DataModelCommon/DatabaseInterfaces/FirebaseDatabaseHelper.dart';
import 'package:api_inspection/generic/UIControls/listener.dart';

import '../../generic/AppRoot.dart';
import '../APMRoot.dart';
import '../DataModel/businessUnit.dart';

class BusinessUnitQueries {
  late Queries _parent;

  ListenerWrapper businessUnitsChangedListener = ListenerWrapper();
  ListenerWrapper selectedBusinessUnitIdListener = ListenerWrapper();

  List<BusinessUnit> businessUnits = [];
  List<StreamSubscription<QuerySnapshot<Map<String, dynamic>>>>
      businessUnitSubscriptions = [];

  Future dispose() async {
    for (var element in businessUnitSubscriptions) {
      await element.cancel();
    }
    businessUnitsChangedListener.dispose();
    selectedBusinessUnitIdListener.dispose();
  }

  void initialize(Queries parent) {
    _parent = parent;
    startQuery();
  }

  void startQuery() {
    var databaseReference = FirebaseDatabaseHelper.global().databaseReference;

    final buIdBatches = AppRoot.global().businessIdsBatches;
    if (buIdBatches != null) {
      for (var buIdBatch in buIdBatches) {
        businessUnitSubscriptions.add(databaseReference
            .collection("businessUnits")
            .where(FieldPath.documentId, whereIn: buIdBatch.toList())
            .snapshots()
            .listen(_onBusinessUnitsUpdated));
      }
    }
  }

  void setSelectedBusinessUnitId(String? buId) {
    if (buId != null) {
      AppRoot.global()
          .sharedPreferences
          .setString(selectedBusinessUnitIdKey, buId);
    } else {
      AppRoot.global().sharedPreferences.remove(selectedBusinessUnitIdKey);
    }
    if (APMRoot.global.selectedBusinessUnitId != buId) {
      APMRoot.global.selectedBusinessUnitId = buId;
      selectedBusinessUnitIdListener.notifyListeners();
    }
  }

  void _onBusinessUnitsUpdated(QuerySnapshot<Map<String, dynamic>> snapshot) {
    List<BusinessUnit> newBusinessUnits = [];
    for (var item in snapshot.docs) {
      var businessUnit = BusinessUnitCache.findEntry(item.id);
      businessUnit.updateFromMap(item.data());
      businessUnit.setShouldDownloadPhotos(false);

      newBusinessUnits.add(businessUnit);
    }

    for (var element in newBusinessUnits) {
      var index = businessUnits.indexWhere((x) => x.id == element.id);
      if (index >= 0) {
        businessUnits[index] = element;
      } else {
        businessUnits.add(element);
      }
    }

    var user = AppRoot.global().currentUser!;
    var effectiveBusinessUnitIds = businessUnits
        .where((element) => user.effectiveBusinessUnitIds.contains(element.id));
    var userHasSelectedBU = effectiveBusinessUnitIds
        .map((bu) => bu.id)
        .contains(APMRoot.global.selectedBusinessUnitId);

    if (!userHasSelectedBU) {
      setSelectedBusinessUnitId(
          businessUnits.isNotEmpty ? businessUnits.first.id : null);
    }

    businessUnitsChangedListener.notifyListeners();
  }
}
